#!/usr/bin/env python3
"""
Test the simplified reservations functionality
"""

import requests
import json
from datetime import datetime

TARGET_ITEM = "8010-60-V00-0113"
TARGET_SITE = "LCVKWT"

def test_reservations_simple():
    """Test the simplified reservations functionality."""
    print(f"🔍 Testing Simplified Reservations for Item: {TARGET_ITEM}")
    print("=" * 60)
    
    try:
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/availability/{TARGET_ITEM}",
            params={'siteid': TARGET_SITE},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                # Check reservations
                reservations = data.get('reservations', [])
                print(f"\n🔒 Reservations Found: {len(reservations)}")
                
                if reservations:
                    print("\nReservations Details:")
                    for i, res in enumerate(reservations, 1):
                        print(f"\n--- Reservation {i} ---")
                        print(f"Request: {res.get('request_num', 'N/A')}")
                        print(f"Type: {res.get('reservation_type', 'N/A')}")
                        print(f"Item: {res.get('item_num', 'N/A')}")
                        print(f"Location: {res.get('location', 'N/A')}")
                        print(f"Storeroom: {res.get('storeroom', 'N/A')}")
                        print(f"Reserved Qty: {res.get('reserved_quantity', 'N/A')}")
                        print(f"Work Order: {res.get('work_order', 'N/A')}")
                        print(f"Required Date: {res.get('required_date', 'N/A')}")
                        print(f"Description: {res.get('description', 'N/A')[:50]}...")
                else:
                    print("   No reservations found")
                
                # Check inventory locations for reference
                inventory_records = data.get('inventory_records', [])
                print(f"\n📦 Inventory Records: {len(inventory_records)}")
                
                all_locations = set()
                for record in inventory_records:
                    # Main location
                    if record.get('location'):
                        all_locations.add(record.get('location'))
                    
                    # Balance locations
                    balance_details = record.get('balance_details', [])
                    for balance in balance_details:
                        if balance.get('location'):
                            all_locations.add(balance.get('location'))
                
                print(f"   Unique locations in inventory: {sorted(all_locations)}")
                
                # Summary
                summary = data.get('availability_summary', {})
                print(f"\n📊 Summary:")
                print(f"   Total Reservations: {summary.get('total_reservations', 0)}")
                print(f"   Total POs: {summary.get('total_purchase_orders', 0)}")
                print(f"   Total PRs: {summary.get('total_purchase_requisitions', 0)}")
                
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Testing Simplified Reservations")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_reservations_simple()
    
    print(f"\n{'='*60}")
    print("🏁 Test Complete")
    print(f"{'='*60}")
    print("\n💡 Expected Result:")
    print("- Should find reservations using straightforward matching:")
    print("  inventory.location = INVRESERVE.location")
    print("  inventory.itemnum = INVRESERVE.itemnum") 
    print("  inventory.siteid = INVRESERVE.siteid")
    print("- No complex assumptions or fallbacks")
    print("- Direct field mapping from MXAPIINVRES")
