#!/usr/bin/env python3
"""
Test script to determine correct relationship names and API methods for asset-related workorders and service requests.
"""

import sys
import os
import json
import time
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from auth import MaximoTokenManager

def test_asset_relationships():
    """Test different relationship names and API methods for assets."""
    
    # Initialize token manager
    DEFAULT_MAXIMO_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    token_manager = MaximoTokenManager(DEFAULT_MAXIMO_URL)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated. Please login first.")
        return False
    
    print("✅ Authenticated with session")
    print("=" * 80)
    
    # First, let's find some actual assets in the system
    print("🔍 Finding available assets in the system...")
    available_assets = find_available_assets(token_manager)

    if not available_assets:
        print("❌ No assets found in the system")
        return False

    # Use the first few available assets for testing
    test_assets = available_assets[:2]  # Test with first 2 assets found
    
    for asset in test_assets:
        assetnum = asset["assetnum"]
        siteid = asset["siteid"]
        
        print(f"\n🔍 Testing Asset: {assetnum} in Site: {siteid}")
        print("=" * 60)
        
        # Test 1: Get asset details first to verify it exists
        if not test_asset_exists(token_manager, assetnum, siteid):
            print(f"⚠️ Asset {assetnum} not found in site {siteid}, skipping...")
            continue
        
        # Test 2: Test different workorder relationship names
        test_workorder_relationships(token_manager, assetnum, siteid)
        
        # Test 3: Test service request methods
        test_service_request_methods(token_manager, assetnum, siteid)
        
        # Test 4: Test relationship traversal approach
        test_relationship_traversal(token_manager, assetnum, siteid)
        
        print("\n" + "=" * 60)

def find_available_assets(token_manager):
    """Find available assets in the system."""
    try:
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiasset"

        params = {
            "oslc.select": "assetnum,siteid,description,status",
            "oslc.where": 'status!="DECOMMISSIONED"',
            "oslc.pageSize": "10",
            "lean": "1"
        }

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content (first 500 chars): {response.text[:500]}")

        if response.status_code == 200:
            try:
                data = response.json()
            except Exception as json_error:
                print(f"❌ JSON parsing error: {json_error}")
                print(f"Raw response: {response.text}")
                return []

            if 'member' in data and len(data['member']) > 0:
                assets = []
                for asset_data in data['member']:
                    assets.append({
                        "assetnum": asset_data.get('assetnum', ''),
                        "siteid": asset_data.get('siteid', ''),
                        "description": asset_data.get('description', ''),
                        "status": asset_data.get('status', '')
                    })
                print(f"✅ Found {len(assets)} available assets")
                for asset in assets[:5]:  # Show first 5
                    print(f"   - {asset['assetnum']} ({asset['siteid']}) - {asset['description'][:50]}...")
                return assets
            else:
                print("❌ No assets found in response")
                return []
        else:
            print(f"❌ API error finding assets: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return []

    except Exception as e:
        print(f"❌ Error finding assets: {e}")
        return []

def test_asset_exists(token_manager, assetnum, siteid):
    """Test if asset exists and get its details."""
    try:
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiasset"
        
        params = {
            "oslc.select": "assetnum,siteid,description,status",
            "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
            "oslc.pageSize": "1",
            "lean": "1"
        }
        
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'member' in data and len(data['member']) > 0:
                asset_data = data['member'][0]
                print(f"✅ Asset found: {asset_data.get('description', 'No description')}")
                print(f"   Status: {asset_data.get('status', 'Unknown')}")
                return True
            else:
                print(f"❌ Asset {assetnum} not found in site {siteid}")
                return False
        else:
            print(f"❌ API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking asset: {e}")
        return False

def test_workorder_relationships(token_manager, assetnum, siteid):
    """Test different workorder relationship names."""
    print("\n🔧 Testing Workorder Relationships:")
    print("-" * 40)
    
    # List of possible relationship names to test
    relationship_names = [
        "workorder",
        "openwo", 
        "allwo",
        "workorders",
        "relatedworkorder",
        "assetworkorder"
    ]
    
    base_url = getattr(token_manager, 'base_url', '')
    api_url = f"{base_url}/oslc/os/mxapiasset"
    
    for rel_name in relationship_names:
        try:
            print(f"\n  Testing rel.{rel_name}...")
            
            params = {
                "oslc.select": f"assetnum,siteid,rel.{rel_name}{{wonum,description,status,priority}}",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.pageSize": "1",
                "lean": "1"
            }
            
            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'member' in data and len(data['member']) > 0:
                    asset_data = data['member'][0]
                    if rel_name in asset_data:
                        workorders = asset_data[rel_name]
                        if isinstance(workorders, list) and len(workorders) > 0:
                            print(f"    ✅ SUCCESS: Found {len(workorders)} workorders")
                            print(f"    Sample: {workorders[0].get('wonum', 'No wonum')} - {workorders[0].get('description', 'No description')[:50]}...")
                        else:
                            print(f"    ⚠️ Relationship exists but no workorders found")
                    else:
                        print(f"    ❌ Relationship '{rel_name}' not found in response")
                else:
                    print(f"    ❌ No asset data returned")
            else:
                print(f"    ❌ API error: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error testing {rel_name}: {e}")

def test_service_request_methods(token_manager, assetnum, siteid):
    """Test different service request API methods."""
    print("\n🎫 Testing Service Request Methods:")
    print("-" * 40)
    
    base_url = getattr(token_manager, 'base_url', '')
    
    # Method 1: Test MXAPISR endpoint directly
    print("\n  Method 1: Direct MXAPISR query...")
    try:
        api_url = f"{base_url}/oslc/os/mxapisr"
        params = {
            "oslc.select": "ticketid,description,status,priority,reportdate,assetnum,siteid",
            "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
            "oslc.pageSize": "10",
            "lean": "1"
        }
        
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'member' in data and len(data['member']) > 0:
                print(f"    ✅ SUCCESS: Found {len(data['member'])} service requests via MXAPISR")
                for sr in data['member'][:3]:  # Show first 3
                    print(f"    - {sr.get('ticketid', 'No ID')} - {sr.get('description', 'No description')[:50]}...")
            else:
                print(f"    ⚠️ MXAPISR accessible but no service requests found for this asset")
        else:
            print(f"    ❌ MXAPISR API error: {response.status_code}")
            
    except Exception as e:
        print(f"    ❌ Error testing MXAPISR: {e}")
    
    # Method 2: Test relationship from asset
    print("\n  Method 2: Asset relationship query...")
    relationship_names = [
        "servicerequest",
        "sr", 
        "ticket",
        "tickets",
        "relatedsr",
        "assetsr"
    ]
    
    api_url = f"{base_url}/oslc/os/mxapiasset"
    
    for rel_name in relationship_names:
        try:
            print(f"\n    Testing rel.{rel_name}...")
            
            params = {
                "oslc.select": f"assetnum,siteid,rel.{rel_name}{{ticketid,description,status,priority}}",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.pageSize": "1",
                "lean": "1"
            }
            
            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'member' in data and len(data['member']) > 0:
                    asset_data = data['member'][0]
                    if rel_name in asset_data:
                        srs = asset_data[rel_name]
                        if isinstance(srs, list) and len(srs) > 0:
                            print(f"      ✅ SUCCESS: Found {len(srs)} service requests")
                            print(f"      Sample: {srs[0].get('ticketid', 'No ID')} - {srs[0].get('description', 'No description')[:50]}...")
                        else:
                            print(f"      ⚠️ Relationship exists but no service requests found")
                    else:
                        print(f"      ❌ Relationship '{rel_name}' not found in response")
                        
        except Exception as e:
            print(f"      ❌ Error testing {rel_name}: {e}")

def test_relationship_traversal(token_manager, assetnum, siteid):
    """Test relationship traversal approach."""
    print("\n🔗 Testing Relationship Traversal:")
    print("-" * 40)

    # First get the asset REST ID
    try:
        base_url = getattr(token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiasset"

        params = {
            "oslc.select": "assetnum,siteid",
            "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            data = response.json()
            if 'member' in data and len(data['member']) > 0:
                asset_href = data['member'][0].get('href', '')
                if asset_href:
                    # Extract REST ID from href
                    rest_id = asset_href.split('/')[-1]
                    print(f"  Asset REST ID: {rest_id}")

                    # Test traversal to workorders
                    test_traversal_workorders(token_manager, rest_id)

                    # Test traversal to service requests
                    test_traversal_service_requests(token_manager, rest_id)
                else:
                    print("  ❌ No href found in asset response")
            else:
                print("  ❌ No asset data for traversal test")
        else:
            print(f"  ❌ Error getting asset for traversal: {response.status_code}")

    except Exception as e:
        print(f"  ❌ Error in relationship traversal setup: {e}")

def test_traversal_workorders(token_manager, rest_id):
    """Test workorder traversal."""
    print("\n    Testing workorder traversal...")

    base_url = getattr(token_manager, 'base_url', '')
    traversal_paths = [
        "workorder",
        "openwo",
        "allwo"
    ]

    for path in traversal_paths:
        try:
            api_url = f"{base_url}/oslc/os/mxapiasset/{rest_id}/{path}"
            params = {
                "oslc.select": "wonum,description,status,priority",
                "oslc.pageSize": "5",
                "lean": "1"
            }

            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                if 'member' in data and len(data['member']) > 0:
                    print(f"      ✅ SUCCESS: /{path} found {len(data['member'])} workorders")
                    for wo in data['member'][:2]:  # Show first 2
                        print(f"        - {wo.get('wonum', 'No wonum')} - {wo.get('description', 'No description')[:40]}...")
                else:
                    print(f"      ⚠️ /{path} accessible but no workorders found")
            else:
                print(f"      ❌ /{path} error: {response.status_code}")

        except Exception as e:
            print(f"      ❌ Error testing /{path}: {e}")

def test_traversal_service_requests(token_manager, rest_id):
    """Test service request traversal."""
    print("\n    Testing service request traversal...")

    base_url = getattr(token_manager, 'base_url', '')
    traversal_paths = [
        "servicerequest",
        "sr",
        "ticket"
    ]

    for path in traversal_paths:
        try:
            api_url = f"{base_url}/oslc/os/mxapiasset/{rest_id}/{path}"
            params = {
                "oslc.select": "ticketid,description,status,priority",
                "oslc.pageSize": "5",
                "lean": "1"
            }

            response = token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                if 'member' in data and len(data['member']) > 0:
                    print(f"      ✅ SUCCESS: /{path} found {len(data['member'])} service requests")
                    for sr in data['member'][:2]:  # Show first 2
                        print(f"        - {sr.get('ticketid', 'No ID')} - {sr.get('description', 'No description')[:40]}...")
                else:
                    print(f"      ⚠️ /{path} accessible but no service requests found")
            else:
                print(f"      ❌ /{path} error: {response.status_code}")

        except Exception as e:
            print(f"      ❌ Error testing /{path}: {e}")

if __name__ == "__main__":
    print("🔍 TESTING ASSET RELATIONSHIPS AND API METHODS")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_asset_relationships()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ Testing completed successfully")
    else:
        print("❌ Testing completed with errors")
    print("=" * 80)
