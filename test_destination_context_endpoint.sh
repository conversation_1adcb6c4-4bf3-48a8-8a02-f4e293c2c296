#!/bin/bash

# Test Destination Context Endpoint
# ==================================

echo "🚀 TESTING DESTINATION SITE CONTEXT ENDPOINT"
echo "============================================"

echo "🎯 OBJECTIVE: Get 204 success using destination site context structure"
echo "📋 STRATEGY: Use new endpoint with destination site in top-level record"
echo "🔍 KEY: Test your exact structure with IKWAJ siteid and KWAJ-1058 location"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_destination_context() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting to destination context endpoint..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item-destination-context \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Destination context transfer worked with 204 status!"
        echo "$payload" > "destination_context_endpoint_success_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: destination_context_endpoint_success_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING DESTINATION CONTEXT WITH YOUR EXACT STRUCTURE"
echo "========================================================"

# Test 1: Your exact structure - CMW-AJ → KWAJ-1058
test_destination_context "Your Exact Structure - CMW-AJ → KWAJ-1058" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: RIP001 → KWAJ-1058 with destination context
test_destination_context "RIP001 → KWAJ-1058 with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: CMW-AJ → KWAJ-1115 with destination context
test_destination_context "CMW-AJ → KWAJ-1115 with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: Small quantity with destination context
test_destination_context "Small Quantity with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 0.1,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Same units (RO to RO)
test_destination_context "Same Units (RO to RO)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 6: EA to EA units
test_destination_context "EA to EA Units" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "EA",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 7: Minimal fields with destination context
test_destination_context "Minimal Fields with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA"
}'

# Test 8: Different bins with destination context
test_destination_context "Different Bins with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "28-800-0004",
    "to_bin": "58-A-A01-1",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 9: Different lots with destination context
test_destination_context "Different Lots with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "LOT123",
    "to_lot": "TEST",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 10: CMW-BU source with destination context
test_destination_context "CMW-BU → KWAJ-1058 with Destination Context" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-BU",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 DESTINATION CONTEXT ENDPOINT TEST SUMMARY"
echo "==========================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! DESTINATION CONTEXT APPROACH WORKS!"
    echo "==============================================="
    echo "💾 Check destination_context_endpoint_success_*.json files for working patterns"
    echo ""
    echo "📋 Working curl commands for destination context:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "destination_context_endpoint_success_$i.json" ]; then
            echo ""
            echo "# Working Destination Context Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item-destination-context \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat destination_context_endpoint_success_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
    
    echo ""
    echo "🎯 KEY SUCCESS FACTORS:"
    echo "• Destination site context in top-level record (IKWAJ)"
    echo "• Destination location in top-level record (KWAJ-1058)"
    echo "• Include to_issue_unit: EA for unit conversion"
    echo "• Complete field validation with DEFAULT values"
    echo "• A1 condition codes work properly"
    
    echo ""
    echo "🔧 IMPLEMENTATION CONFIRMED:"
    echo "• Your exact structure works with destination site context"
    echo "• Top-level siteid: IKWAJ (destination site)"
    echo "• Top-level location: KWAJ-1058 (destination storeroom)"
    echo "• toissueunit: EA handles unit conversion properly"
    
else
    echo ""
    echo "❌ No destination context success found"
    echo "🔄 Check if Flask application is running and endpoint is available"
    echo ""
    echo "💡 TROUBLESHOOTING:"
    echo "• Ensure Flask app is restarted after adding new endpoint"
    echo "• Check Flask logs for any errors"
    echo "• Verify authentication is working"
fi

echo ""
echo "🎯 NEXT STEPS IF SUCCESSFUL:"
echo "• Update main inventory transfer service to use destination context"
echo "• Implement proper storeroom validation for destination context"
echo "• Test with more item/location combinations"
echo "• Deploy to production with destination context approach"
