#!/usr/bin/env python3
"""
Test script for the new View Item Availability feature.

This script tests:
1. Backend API endpoint functionality
2. Authentication integration
3. Response format and data structure
4. Error handling

Author: Augment Agent
Date: 2025-07-16
"""

import sys
import os
import json
import requests
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.auth.token_manager import Maximo<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_availability_api():
    """Test the new availability API endpoint."""
    print("🔍 TESTING VIEW ITEM AVAILABILITY FEATURE")
    print("=" * 80)
    
    # Initialize token manager
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated - please login first")
        return False
        
    print("✅ Authenticated successfully")
    
    # Test parameters - using known working item from codebase analysis
    test_itemnum = "5975-60-V00-0001"
    test_siteid = "LCVKWT"
    
    print(f"📋 Testing with Item: {test_itemnum}, Site: {test_siteid}")
    
    # Test the API endpoint directly
    api_url = f"http://127.0.0.1:5010/api/inventory/availability/{test_itemnum}"
    params = {"siteid": test_siteid}
    
    try:
        print(f"🔗 Making request to: {api_url}")
        print(f"📋 Parameters: {params}")
        
        # Use the token manager's session for authentication
        response = requests.get(
            api_url,
            params=params,
            cookies=token_manager.session.cookies,
            timeout=(3.05, 15)
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        print(f"📊 Raw Response: {response.text[:500]}...")

        if response.status_code == 200:
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"📊 Full Response Text: {response.text}")
                return False
            
            if data.get('success'):
                print("✅ API call successful!")
                print("\n📊 AVAILABILITY SUMMARY:")
                summary = data.get('availability_summary', {})
                print(f"  • Total Available Balance: {summary.get('total_available_balance', 'N/A')}")
                print(f"  • Total Current Balance: {summary.get('total_current_balance', 'N/A')}")
                print(f"  • Total Reserved Quantity: {summary.get('total_reserved_quantity', 'N/A')}")
                print(f"  • Total Locations: {summary.get('total_locations', 'N/A')}")
                
                print("\n📋 INVENTORY RECORDS:")
                records = data.get('inventory_records', [])
                if records:
                    for i, record in enumerate(records[:3]):  # Show first 3 records
                        print(f"  Record {i+1}:")
                        print(f"    • Location: {record.get('location', 'N/A')}")
                        print(f"    • Available: {record.get('avblbalance', 'N/A')}")
                        print(f"    • Current: {record.get('curbaltotal', 'N/A')}")
                        print(f"    • Reserved: {record.get('reservedqty', 'N/A')}")
                        print(f"    • Status: {record.get('status', 'N/A')}")
                    
                    if len(records) > 3:
                        print(f"    ... and {len(records) - 3} more records")
                else:
                    print("  No inventory records found")
                
                print("\n📊 METADATA:")
                metadata = data.get('metadata', {})
                print(f"  • API Endpoint: {metadata.get('api_endpoint', 'N/A')}")
                print(f"  • Record Count: {metadata.get('record_count', 'N/A')}")
                print(f"  • Query Timestamp: {metadata.get('query_timestamp', 'N/A')}")
                
                return True
            else:
                print(f"❌ API returned error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        return False

def test_ui_integration():
    """Test UI integration by checking if the button and modal are properly implemented."""
    print("\n🖥️  TESTING UI INTEGRATION")
    print("=" * 50)
    
    # Check if the JavaScript file contains the new methods
    js_file_path = "frontend/static/js/inventory_management.js"
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        # Check for required methods and elements
        required_elements = [
            "openAvailabilityModal",
            "fetchAvailabilityData",
            "displayAvailabilityData",
            "createAvailabilityModal",
            "View Availability",
            "btn-success",
            "fas fa-chart-bar"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in js_content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing UI elements: {missing_elements}")
            return False
        else:
            print("✅ All required UI elements found in JavaScript")
            
        # Check button implementation
        if "window.inventoryManager.openAvailabilityModal" in js_content:
            print("✅ Button click handler properly implemented")
        else:
            print("❌ Button click handler not found")
            return False
            
        # Check modal creation
        if "availabilityModal" in js_content and "modal-xl" in js_content:
            print("✅ Modal creation properly implemented")
        else:
            print("❌ Modal creation not properly implemented")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking UI integration: {str(e)}")
        return False

def main():
    """Run all tests."""
    print(f"🚀 STARTING AVAILABILITY FEATURE TESTS")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test API functionality
    api_success = test_availability_api()
    
    # Test UI integration
    ui_success = test_ui_integration()
    
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"API Endpoint Test: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"UI Integration Test: {'✅ PASSED' if ui_success else '❌ FAILED'}")
    
    overall_success = api_success and ui_success
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 The View Item Availability feature is ready for use!")
        print("📋 To test manually:")
        print("   1. Go to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for an item (e.g., '5975-60-V00-0001')")
        print("   3. Click the green 'View Availability' button")
        print("   4. Verify the modal shows availability data from Maximo")
    else:
        print("\n⚠️  Please fix the failing tests before using the feature.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
