#!/usr/bin/env python3
"""
Test the reservations functionality for item 8010-60-V00-0113
"""

import requests
import json
from datetime import datetime

# Configuration
TARGET_ITEM = "8010-60-V00-0113"
TARGET_SITE = "LCVKWT"  # Default site, can be changed if needed

def test_flask_app_item():
    """Test the Flask application with the specific item."""
    print(f"🔍 Testing Flask Application for Item: {TARGET_ITEM}")
    print("=" * 60)
    
    try:
        # Test the availability endpoint
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/availability/{TARGET_ITEM}",
            params={'siteid': TARGET_SITE},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                # Print summary
                summary = data.get('availability_summary', {})
                print(f"\n📊 Availability Summary:")
                print(f"   Total Available Balance: {summary.get('total_available_balance', 0)}")
                print(f"   Total Current Balance: {summary.get('total_current_balance', 0)}")
                print(f"   Total Locations: {summary.get('total_locations', 0)}")
                print(f"   Total Purchase Orders: {summary.get('total_purchase_orders', 0)}")
                print(f"   Total Purchase Requisitions: {summary.get('total_purchase_requisitions', 0)}")
                print(f"   Total Reservations: {summary.get('total_reservations', 0)}")
                
                # Print inventory records
                inventory_records = data.get('inventory_records', [])
                print(f"\n📦 Inventory Records: {len(inventory_records)}")
                if inventory_records:
                    for i, record in enumerate(inventory_records[:3], 1):  # Show first 3
                        print(f"   Record {i}: Location={record.get('location', 'N/A')}, Available={record.get('available_balance', 0)}")
                
                # Print reservations
                reservations = data.get('reservations', [])
                print(f"\n🔒 Reservations: {len(reservations)}")
                if reservations:
                    print("\nReservations Details:")
                    for i, reservation in enumerate(reservations, 1):
                        print(f"\n--- Reservation {i} ---")
                        print(f"Request: {reservation.get('request_num', 'N/A')}")
                        print(f"Type: {reservation.get('reservation_type', 'N/A')}")
                        print(f"Item: {reservation.get('item_num', 'N/A')}")
                        print(f"Reserved Qty: {reservation.get('reserved_quantity', 'N/A')}")
                        print(f"Work Order: {reservation.get('work_order', 'N/A')}")
                        print(f"Storeroom: {reservation.get('storeroom', 'N/A')}")
                        print(f"Required Date: {reservation.get('required_date', 'N/A')}")
                        print(f"Description: {reservation.get('description', 'N/A')[:50]}...")
                else:
                    print("   No reservations found for this item")
                
                # Print purchase orders
                purchase_orders = data.get('purchase_orders', [])
                print(f"\n🛒 Purchase Orders: {len(purchase_orders)}")
                if purchase_orders:
                    for i, po in enumerate(purchase_orders[:3], 1):  # Show first 3
                        print(f"   PO {i}: {po.get('ponum', 'N/A')} - Status: {po.get('status', 'N/A')} - Qty: {po.get('quantity_ordered', 0)}")
                
                # Print purchase requisitions
                purchase_reqs = data.get('purchase_requisitions', [])
                print(f"\n📋 Purchase Requisitions: {len(purchase_reqs)}")
                if purchase_reqs:
                    for i, pr in enumerate(purchase_reqs[:3], 1):  # Show first 3
                        print(f"   PR {i}: {pr.get('prnum', 'N/A')} - Status: {pr.get('status', 'N/A')} - Qty: {pr.get('quantity_ordered', 0)}")
                
            else:
                print(f"❌ Flask error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_inventory_search():
    """Test if the item exists in inventory search."""
    print(f"\n🔍 Testing Inventory Search for Item: {TARGET_ITEM}")
    print("=" * 60)
    
    try:
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/management/search",
            params={
                'siteid': TARGET_SITE,
                'q': TARGET_ITEM,
                'limit': 10,
                'page': 0
            },
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                items = data.get('items', [])
                print(f"Items found: {len(items)}")
                
                if items:
                    for i, item in enumerate(items, 1):
                        print(f"\n--- Item {i} ---")
                        print(f"Item Number: {item.get('itemnum', 'N/A')}")
                        print(f"Description: {item.get('description', 'N/A')[:100]}...")
                        print(f"Status: {item.get('status', 'N/A')}")
                        print(f"Total Balance: {item.get('total_balance', 0)}")
                        print(f"Available Balance: {item.get('available_balance', 0)}")
                else:
                    print("No items found in inventory search")
            else:
                print(f"❌ Search error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Testing Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_inventory_search()
    test_flask_app_item()
    
    print(f"\n{'='*60}")
    print("🏁 Test Complete")
    print(f"{'='*60}")
    print("\n💡 Next Steps:")
    print("1. If item found: Check the Reservations tab in the web interface")
    print("2. If no reservations: Item may not have any active reservations")
    print("3. Try searching for the item in the inventory management interface")
    print("4. Click on the item to view detailed availability including reservations")
