#!/bin/bash

# Debug Payload Test - Show Exact Working Combination
# ===================================================

echo "🚀 DEBUG PAYLOAD TEST - SHOW WORKING COMBINATION"
echo "==============================================="

echo "🎯 OBJECTIVE: Show exact payload being sent and test working combination"
echo "📋 STRATEGY: Use the confirmed working pattern from terminal testing"
echo "🔍 WORKING PATTERN: CMW-AJ → CMW-BU (LCVKWT storerooms for both)"
echo ""

# Function to test and show detailed payload
test_with_payload_debug() {
    local test_name="$1"
    local endpoint="$2"
    local payload="$3"
    
    echo "📋 TEST: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    echo "🔗 Endpoint: $endpoint"
    echo ""
    
    echo "📋 PAYLOAD BEING SENT:"
    echo "$payload" | jq '.' 2>/dev/null || echo "$payload"
    echo ""
    
    echo "🔄 Submitting to Flask application..."
    
    response=$(curl -X POST "http://127.0.0.1:5010$endpoint" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 RESPONSE:"
    echo "$response"
    echo ""
    
    # Check for success
    if echo "$response" | grep -q '"success": true'; then
        if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
            echo "🎉 SUCCESS! Transfer worked with 204 status!"
            return 0
        else
            echo "✅ SUCCESS! Transfer accepted by system"
            return 0
        fi
    elif echo "$response" | grep -q '"success": false'; then
        echo "❌ FAILED! Check error details above"
        return 1
    elif echo "$response" | grep -q "401"; then
        echo "🔐 AUTHENTICATION REQUIRED - Please login first"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
}

echo "🚀 TESTING CONFIRMED WORKING COMBINATIONS"
echo "========================================"

# Test 1: CONFIRMED WORKING PATTERN - CMW-AJ → CMW-BU (Source Site Context)
echo "🎯 This is the CONFIRMED WORKING pattern from our terminal testing:"
test_with_payload_debug "CONFIRMED WORKING: CMW-AJ → CMW-BU (Source Context)" \
    "/api/inventory/transfer-same-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-BU",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

echo ""
echo "$(printf '=%.0s' {1..80})"

# Test 2: CONFIRMED WORKING PATTERN - Destination Context (Your Requested Structure)
echo "🎯 This is the CONFIRMED WORKING destination context pattern:"
test_with_payload_debug "CONFIRMED WORKING: CMW-AJ → KWAJ-1058 (Destination Context)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

echo ""
echo "$(printf '=%.0s' {1..80})"

# Test 3: Show what the backend is actually building
echo "🔧 Let's also test the original endpoint to see what it builds:"
test_with_payload_debug "ORIGINAL ENDPOINT: Auto-Detection" \
    "/api/inventory/transfer-current-item" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-BU",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

echo ""
echo "📊 PAYLOAD ANALYSIS SUMMARY"
echo "=========================="
echo ""
echo "🎯 CONFIRMED WORKING PATTERNS FROM TERMINAL TESTING:"
echo ""
echo "1. ✅ CMW-AJ → CMW-BU (Both LCVKWT storerooms)"
echo "   - Uses source site context (LCVKWT)"
echo "   - Both storerooms exist in LCVKWT"
echo "   - Returns 204 success status"
echo ""
echo "2. ✅ CMW-AJ → KWAJ-1058 (Destination context)"
echo "   - Uses destination site context (IKWAJ)"
echo "   - Includes toissueunit for conversion"
echo "   - Returns 204 success status"
echo ""
echo "🔧 BACKEND PAYLOAD STRUCTURE:"
echo ""
echo "For Same Site (Source Context):"
echo '{'
echo '  "_action": "AddChange",'
echo '  "itemnum": "5975-60-V00-0529",'
echo '  "siteid": "LCVKWT",           // SOURCE site'
echo '  "location": "CMW-AJ",         // SOURCE location'
echo '  "matrectrans": [{'
echo '    "fromsiteid": "LCVKWT",'
echo '    "tositeid": "IKWAJ",'
echo '    "fromstoreloc": "CMW-AJ",'
echo '    "tostoreloc": "CMW-BU"'
echo '  }]'
echo '}'
echo ""
echo "For Cross Site (Destination Context):"
echo '{'
echo '  "_action": "AddChange",'
echo '  "itemnum": "5975-60-V00-0529",'
echo '  "siteid": "IKWAJ",            // DESTINATION site'
echo '  "location": "KWAJ-1058",      // DESTINATION location'
echo '  "matrectrans": [{'
echo '    "fromsiteid": "LCVKWT",'
echo '    "tositeid": "IKWAJ",'
echo '    "fromstoreloc": "CMW-AJ",'
echo '    "tostoreloc": "KWAJ-1058",'
echo '    "toissueunit": "EA"         // Unit conversion'
echo '  }]'
echo '}'
echo ""
echo "🎯 NEXT STEPS:"
echo "1. Check if authentication is working (login to the app first)"
echo "2. Use the exact working combinations shown above"
echo "3. Check Flask logs for detailed payload information"
echo "4. Verify the backend is building the correct Maximo API payload"
echo ""
echo "💡 TIP: If authentication fails, go to http://127.0.0.1:5010 and login first!"
