#!/usr/bin/env python3
"""
Test script to verify the toggle button fixes and hover animations.
This script tests the three issues that were addressed:
1. Cost data section toggle button visibility
2. Toggle button text conflicts
3. Elegant hover animations
"""
import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_ITEM = "5975-60-V00-0529"
TEST_SITE = "LCVKWT"

def test_css_improvements():
    """Test CSS improvements for toggle buttons and animations"""
    print("🔧 Testing CSS Improvements")
    print("=" * 50)
    
    try:
        # Test if the CSS file loads and contains the improved styles
        response = requests.get(f"{BASE_URL}/static/css/style.css", timeout=30)
        
        if response.status_code == 200:
            print("✅ CSS file loads successfully")
            
            css_content = response.text
            
            # Check for toggle button visibility fixes
            visibility_checks = [
                ('cost-toggle-btn', 'Cost toggle button styling'),
                ('balance-toggle-btn', 'Balance toggle button styling'),
                ('opacity: 1 !important', 'Button opacity fix'),
                ('visibility: visible !important', 'Button visibility fix'),
                ('display: inline-block !important', 'Button display fix'),
                ('z-index: 10', 'Button z-index fix'),
            ]
            
            print(f"\n🔍 Toggle button visibility checks:")
            visibility_passed = 0
            for check_item, description in visibility_checks:
                if check_item in css_content:
                    print(f"  ✅ {description}")
                    visibility_passed += 1
                else:
                    print(f"  ❌ {description}")
            
            # Check for hover animations
            animation_checks = [
                ('transition: all 0.3s cubic-bezier', 'Smooth transitions'),
                ('transform: translateY(-2px)', 'Hover lift effect'),
                ('box-shadow:', 'Shadow effects'),
                ('@keyframes pulse', 'Pulse animation'),
                ('@keyframes fadeInUp', 'Fade in animation'),
                ('prefers-reduced-motion', 'Accessibility motion preferences'),
                ('.inventory-item:hover', 'Inventory item hover'),
                ('.mobile-cost-item:hover', 'Mobile cost item hover'),
                ('.balance-records-table .table tbody tr:hover', 'Balance table row hover'),
            ]
            
            print(f"\n🔍 Hover animation checks:")
            animation_passed = 0
            for check_item, description in animation_checks:
                if check_item in css_content:
                    print(f"  ✅ {description}")
                    animation_passed += 1
                else:
                    print(f"  ❌ {description}")
            
            # Check for dark theme support
            dark_theme_checks = [
                ('[data-bs-theme="dark"]', 'Dark theme support'),
                ('dark"] .inventory-field:hover', 'Dark theme field hover'),
                ('dark"] .cost-data-table', 'Dark theme cost table'),
                ('dark"] .balance-records-table', 'Dark theme balance table'),
            ]
            
            print(f"\n🔍 Dark theme animation checks:")
            dark_theme_passed = 0
            for check_item, description in dark_theme_checks:
                if check_item in css_content:
                    print(f"  ✅ {description}")
                    dark_theme_passed += 1
                else:
                    print(f"  ❌ {description}")
            
            total_checks = len(visibility_checks) + len(animation_checks) + len(dark_theme_checks)
            total_passed = visibility_passed + animation_passed + dark_theme_passed
            
            print(f"\n📊 CSS Summary: {total_passed}/{total_checks} checks passed")
            print(f"  - Visibility fixes: {visibility_passed}/{len(visibility_checks)}")
            print(f"  - Hover animations: {animation_passed}/{len(animation_checks)}")
            print(f"  - Dark theme support: {dark_theme_passed}/{len(dark_theme_checks)}")
            
            return total_passed >= (total_checks * 0.8)  # 80% pass rate
        else:
            print(f"❌ CSS load failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_javascript_improvements():
    """Test JavaScript improvements for toggle functionality"""
    print("\n🔧 Testing JavaScript Improvements")
    print("=" * 50)
    
    try:
        # Test if the JavaScript file loads and contains the improved functions
        response = requests.get(f"{BASE_URL}/static/js/inventory_management.js", timeout=30)
        
        if response.status_code == 200:
            print("✅ JavaScript file loads successfully")
            
            js_content = response.text
            
            # Check for toggle function improvements
            toggle_checks = [
                ('handleCostToggle', 'Cost toggle handler function'),
                ('updateCostToggleButton', 'Cost toggle button update function'),
                ('initializeCostToggleHandlers', 'Cost toggle event handlers'),
                ('updateBalanceToggleButton', 'Balance toggle button update function'),
                ('aria-expanded', 'ARIA accessibility support'),
                ('Show Costs', 'Show costs text'),
                ('Hide Costs', 'Hide costs text'),
                ('Show Balances', 'Show balances text'),
                ('Hide Balances', 'Hide balances text'),
            ]
            
            print(f"\n🔍 Toggle functionality checks:")
            toggle_passed = 0
            for check_item, description in toggle_checks:
                if check_item in js_content:
                    print(f"  ✅ {description}")
                    toggle_passed += 1
                else:
                    print(f"  ❌ {description}")
            
            # Check for event handling improvements
            event_checks = [
                ('shown.bs.collapse', 'Bootstrap collapse shown event'),
                ('hidden.bs.collapse', 'Bootstrap collapse hidden event'),
                ('cost-toggle-btn', 'Cost toggle button class'),
                ('balance-toggle-btn', 'Balance toggle button class'),
                ('Bootstrap.Collapse.getInstance', 'Bootstrap collapse instance management'),
            ]
            
            print(f"\n🔍 Event handling checks:")
            event_passed = 0
            for check_item, description in event_checks:
                if check_item in js_content:
                    print(f"  ✅ {description}")
                    event_passed += 1
                else:
                    print(f"  ❌ {description}")
            
            total_checks = len(toggle_checks) + len(event_checks)
            total_passed = toggle_passed + event_passed
            
            print(f"\n📊 JavaScript Summary: {total_passed}/{total_checks} checks passed")
            print(f"  - Toggle functionality: {toggle_passed}/{len(toggle_checks)}")
            print(f"  - Event handling: {event_passed}/{len(event_checks)}")
            
            return total_passed >= (total_checks * 0.8)  # 80% pass rate
        else:
            print(f"❌ JavaScript load failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_inventory_data_availability():
    """Test that inventory data is available for testing toggle functionality"""
    print("\n🔧 Testing Inventory Data Availability")
    print("=" * 50)
    
    try:
        # Get inventory data from the search API
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item = data['items'][0]
                
                print(f"✅ Found inventory item: {item.get('itemnum')}")
                
                # Check for data that would trigger toggle sections
                has_cost_data = any([
                    item.get('avgcost'),
                    item.get('stdcost'),
                    item.get('lastcost'),
                    item.get('unitcost')
                ])
                
                has_balance_data = item.get('invbalances') and len(item.get('invbalances', [])) > 0
                
                print(f"\n🔍 Toggle section data availability:")
                print(f"  {'✅' if has_cost_data else '❌'} Cost data available: {has_cost_data}")
                print(f"  {'✅' if has_balance_data else '❌'} Balance data available: {has_balance_data}")
                
                if has_cost_data:
                    print(f"    - Average Cost: ${item.get('avgcost', 'N/A')}")
                    print(f"    - Standard Cost: ${item.get('stdcost', 'N/A')}")
                    print(f"    - Last Cost: ${item.get('lastcost', 'N/A')}")
                
                if has_balance_data:
                    balance_count = len(item.get('invbalances', []))
                    print(f"    - Balance records: {balance_count}")
                
                return has_cost_data or has_balance_data
            else:
                print(f"❌ No inventory items found")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_ui_integration():
    """Test UI integration and page loading"""
    print("\n🔧 Testing UI Integration")
    print("=" * 50)
    
    try:
        # Test if the inventory management page loads
        response = requests.get(f"{BASE_URL}/inventory-management", timeout=30)
        
        if response.status_code == 200:
            print("✅ Inventory management page loads successfully")
            
            # Check if the page contains the expected updates
            content = response.text
            
            checks = [
                ('inventory_management.js', 'JavaScript file included'),
                ('style.css', 'CSS file included'),
                ('cost-data-section', 'Cost data section'),
                ('balance-records-section', 'Balance records section'),
                ('btn-outline-info', 'Cost toggle button styling'),
                ('btn-outline-primary', 'Balance toggle button styling'),
            ]
            
            print(f"\n🔍 Page content checks:")
            all_checks_passed = True
            
            for check_item, description in checks:
                if check_item in content:
                    print(f"  ✅ {description}")
                else:
                    print(f"  ❌ {description}")
                    all_checks_passed = False
            
            return all_checks_passed
        else:
            print(f"❌ Page load failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 TOGGLE BUTTONS AND ANIMATIONS TEST")
    print("=" * 60)
    print(f"🎯 Target Item: {TEST_ITEM}")
    print(f"🏢 Target Site: {TEST_SITE}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    results = []
    
    # Test 1: CSS improvements
    css_ok = test_css_improvements()
    results.append(("CSS Improvements (Visibility & Animations)", css_ok))
    
    # Test 2: JavaScript improvements
    js_ok = test_javascript_improvements()
    results.append(("JavaScript Improvements (Toggle Logic)", js_ok))
    
    # Test 3: Inventory data availability
    data_ok = test_inventory_data_availability()
    results.append(("Inventory Data Availability", data_ok))
    
    # Test 4: UI integration
    ui_ok = test_ui_integration()
    results.append(("UI Integration", ui_ok))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! All three issues have been resolved:")
        print("   ✅ Issue 1: Cost data section toggle button visibility - FIXED")
        print("   ✅ Issue 2: Toggle button text conflicts - FIXED")
        print("   ✅ Issue 3: Elegant hover animations - IMPLEMENTED")
        print("\n📝 Manual Testing Steps:")
        print("   1. Navigate to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for an inventory item")
        print("   3. Test toggle buttons for both cost data and balance sections")
        print("   4. Verify button text changes correctly (Show/Hide)")
        print("   5. Test hover animations on all interactive elements")
        print("   6. Check responsiveness on mobile and desktop")
        print("   7. Verify accessibility with reduced motion preferences")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
