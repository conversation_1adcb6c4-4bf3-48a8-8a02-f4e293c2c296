#!/usr/bin/env python3
"""
Investigate API Access Issue
============================

Investigate why the API endpoint returns 403 Forbidden and explore
alternative authentication methods including API key.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import requests

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def investigate_api_access():
    """Investigate API access issues."""
    print("🔍 INVESTIGATING API ACCESS ISSUES")
    print("=" * 50)
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    
    # Initialize token manager
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return
    
    print("✅ Authenticated with session")
    
    # Test basic API endpoint access
    api_endpoint = f"{base_url}/api/os/mxapiinventory"
    
    print(f"\n🔗 Testing API endpoint: {api_endpoint}")
    
    try:
        # Test GET request first
        response = token_manager.session.get(
            api_endpoint,
            headers={"Accept": "application/json"},
            timeout=(3.05, 30)
        )
        
        print(f"📊 GET Status: {response.status_code}")
        print(f"📄 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        print(f"📏 Response Length: {len(response.text) if response.text else 0} chars")
        
        if response.status_code == 403:
            print("\n❌ 403 FORBIDDEN ERROR DETAILS:")
            print("-" * 30)
            try:
                error_data = response.json()
                print(f"Error Response: {json.dumps(error_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"Error Text: {response.text}")
        
        elif response.status_code == 200:
            print("✅ API endpoint accessible via GET")
            
    except Exception as e:
        print(f"❌ Error testing API endpoint: {str(e)}")
    
    # Test with API key if available
    print(f"\n🔑 Testing with API Key")
    print("-" * 30)
    
    try:
        api_key = token_manager.get_api_key()
        if api_key:
            print(f"✅ API key obtained: {api_key[:10]}...{api_key[-10:]}")
            
            # Test with API key
            headers = {
                'Accept': 'application/json',
                'apikey': api_key
            }
            
            response = requests.get(
                api_endpoint,
                headers=headers,
                timeout=(3.05, 30)
            )
            
            print(f"📊 API Key GET Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ API endpoint accessible with API key")
                
                # Test transfer method with API key
                transfer_url = f"{api_endpoint}/transfercurrentitem"
                
                payload = {
                    "itemnum": "5975-60-V00-0529",
                    "fromsiteid": "LCVKWT",
                    "tositeid": "IKWAJ",
                    "quantity": 1.0
                }
                
                print(f"\n🧪 Testing transfer method with API key")
                print(f"URL: {transfer_url}")
                
                post_response = requests.post(
                    transfer_url,
                    json=payload,
                    headers={
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'apikey': api_key
                    },
                    timeout=(3.05, 30)
                )
                
                print(f"📊 Transfer POST Status: {post_response.status_code}")
                print(f"📄 Content-Type: {post_response.headers.get('Content-Type', 'Unknown')}")
                
                if post_response.status_code in [200, 201, 204]:
                    print("✅ Transfer method accessible with API key!")
                    try:
                        data = post_response.json()
                        print(f"Response: {json.dumps(data, indent=2)}")
                    except json.JSONDecodeError:
                        print(f"Response text: {post_response.text}")
                        
                elif post_response.status_code == 400:
                    print("⚠️  Bad Request - Method exists but payload invalid")
                    try:
                        error_data = post_response.json()
                        print(f"Error: {json.dumps(error_data, indent=2)}")
                    except json.JSONDecodeError:
                        print(f"Error text: {post_response.text}")
                        
                else:
                    print(f"❌ Transfer method failed: {post_response.status_code}")
                    print(f"Response: {post_response.text[:300]}")
            
            else:
                print(f"❌ API endpoint not accessible with API key: {response.status_code}")
                
        else:
            print("❌ No API key available")
            
    except Exception as e:
        print(f"❌ Error testing with API key: {str(e)}")
    
    # Test OSLC endpoint for comparison
    print(f"\n🔗 Testing OSLC endpoint for comparison")
    print("-" * 40)
    
    oslc_endpoint = f"{base_url}/oslc/os/mxapiinventory"
    
    try:
        response = token_manager.session.get(
            oslc_endpoint,
            headers={"Accept": "application/json"},
            timeout=(3.05, 30)
        )
        
        print(f"📊 OSLC GET Status: {response.status_code}")
        print(f"📄 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        
        if response.status_code == 200:
            print("✅ OSLC endpoint accessible")
        else:
            print(f"❌ OSLC endpoint issue: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing OSLC endpoint: {str(e)}")

def main():
    """Main function."""
    investigate_api_access()

if __name__ == "__main__":
    main()
