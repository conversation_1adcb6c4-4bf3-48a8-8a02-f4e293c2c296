#!/usr/bin/env python3
"""
Maximo Integration Explorer™ - Comprehensive Inventory Service Discovery

This script systematically discovers and documents ALL inventory service methods available
in IBM Maximo, including both SOAP/WSDL and REST/OSLC endpoints.

Features:
1. WSDL Inspection - Fetches and parses InventoryService WSDL
2. REST/OSLC Metadata Discovery - Explores MXAPIINVENTORY capabilities
3. Authentication Testing - Both session and API key methods
4. Method Documentation - Complete payload structures and examples
5. Consolidated Report - JSON/Markdown output for integration modules

Author: Maximo Integration Explorer™
Date: 2025-01-16
"""

import sys
import os
import json
import time
import requests
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin
import base64

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError:
    print("❌ Could not import MaximoTokenManager. Please ensure backend is properly configured.")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

class MaximoInventoryServiceExplorer:
    """Comprehensive Maximo Inventory Service Discovery Tool"""
    
    def __init__(self):
        """Initialize the explorer."""
        self.base_url = BASE_URL
        self.token_manager = None
        self.api_key = None
        self.discovery_results = {
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'soap_wsdl': {},
            'rest_oslc': {},
            'authentication': {},
            'consolidated_methods': []
        }
        
    def initialize_authentication(self):
        """Initialize both session and API key authentication."""
        print("🔐 Initializing Authentication Methods")
        print("=" * 80)
        
        # Initialize token manager for session authentication
        self.token_manager = MaximoTokenManager(self.base_url)
        
        if not self.token_manager.is_logged_in():
            print("❌ Session authentication not available")
            print("💡 Please login through the web interface first")
            return False
            
        print("✅ Session authentication available")
        
        # Get API key
        try:
            self.api_key = self.token_manager.get_api_key()
            if self.api_key:
                print(f"✅ API key obtained: {self.api_key[:10]}...{self.api_key[-10:]}")
                self.discovery_results['authentication']['api_key'] = f"{self.api_key[:10]}...{self.api_key[-10:]}"
            else:
                print("❌ API key not available")
        except Exception as e:
            print(f"❌ Error getting API key: {str(e)}")
            
        self.discovery_results['authentication']['session_available'] = True
        self.discovery_results['authentication']['api_key_available'] = bool(self.api_key)
        
        return True
        
    def discover_soap_wsdl_services(self):
        """Fetch and parse the InventoryService WSDL."""
        print("\n🌐 SOAP/WSDL Service Discovery")
        print("=" * 80)
        
        wsdl_url = f"{self.base_url}/services/InventoryService?wsdl"
        print(f"📋 Fetching WSDL from: {wsdl_url}")
        
        try:
            # Try with session authentication first
            response = self.token_manager.session.get(
                wsdl_url,
                timeout=(3.05, 30),
                headers={"Accept": "text/xml, application/xml"}
            )
            
            if response.status_code == 200:
                print("✅ WSDL fetched successfully")
                self._parse_wsdl_content(response.text)
            else:
                print(f"❌ WSDL fetch failed: {response.status_code}")
                self.discovery_results['soap_wsdl']['error'] = f"HTTP {response.status_code}"
                
        except Exception as e:
            print(f"❌ WSDL fetch error: {str(e)}")
            self.discovery_results['soap_wsdl']['error'] = str(e)
            
    def _parse_wsdl_content(self, wsdl_content: str):
        """Parse WSDL XML content to extract operations."""
        try:
            root = ET.fromstring(wsdl_content)
            
            # Define namespaces
            namespaces = {
                'wsdl': 'http://schemas.xmlsoap.org/wsdl/',
                'soap': 'http://schemas.xmlsoap.org/wsdl/soap/',
                'tns': 'http://www.ibm.com/maximo'
            }
            
            operations = []
            
            # Find all operations in portType
            for porttype in root.findall('.//wsdl:portType', namespaces):
                porttype_name = porttype.get('name', 'Unknown')
                print(f"📦 Found PortType: {porttype_name}")
                
                for operation in porttype.findall('wsdl:operation', namespaces):
                    op_name = operation.get('name')
                    
                    # Get input/output messages
                    input_elem = operation.find('wsdl:input', namespaces)
                    output_elem = operation.find('wsdl:output', namespaces)
                    
                    op_info = {
                        'name': op_name,
                        'porttype': porttype_name,
                        'input_message': input_elem.get('message') if input_elem is not None else None,
                        'output_message': output_elem.get('message') if output_elem is not None else None
                    }
                    
                    operations.append(op_info)
                    print(f"  🔧 Operation: {op_name}")
                    
            self.discovery_results['soap_wsdl']['operations'] = operations
            self.discovery_results['soap_wsdl']['total_operations'] = len(operations)
            
            # Find service endpoints
            services = []
            for service in root.findall('.//wsdl:service', namespaces):
                service_name = service.get('name')
                for port in service.findall('.//wsdl:port', namespaces):
                    soap_address = port.find('.//soap:address', namespaces)
                    if soap_address is not None:
                        endpoint_url = soap_address.get('location')
                        services.append({
                            'service_name': service_name,
                            'endpoint_url': endpoint_url
                        })
                        print(f"🌐 Service Endpoint: {endpoint_url}")
                        
            self.discovery_results['soap_wsdl']['services'] = services
            
        except ET.ParseError as e:
            print(f"❌ WSDL parsing error: {str(e)}")
            self.discovery_results['soap_wsdl']['parse_error'] = str(e)
            
    def discover_rest_oslc_metadata(self):
        """Discover REST/OSLC metadata and capabilities."""
        print("\n🔗 REST/OSLC Metadata Discovery")
        print("=" * 80)
        
        endpoint_url = f"{self.base_url}/api/os/MXAPIINVENTORY"
        print(f"📋 Exploring endpoint: {endpoint_url}")
        
        # Test OPTIONS method
        self._test_options_method(endpoint_url)
        
        # Test schema discovery
        self._test_schema_discovery(endpoint_url)
        
        # Test basic GET capabilities
        self._test_basic_get_capabilities(endpoint_url)
        
        # Discover web service methods
        self._discover_wsmethods(endpoint_url)
        
    def _test_options_method(self, endpoint_url: str):
        """Test OPTIONS method to discover supported HTTP methods."""
        print("\n📋 Testing OPTIONS method...")
        
        try:
            response = self.token_manager.session.options(
                endpoint_url,
                timeout=(3.05, 15)
            )
            
            print(f"  Status: {response.status_code}")
            
            if 'Allow' in response.headers:
                allowed_methods = response.headers['Allow']
                print(f"  ✅ Allowed Methods: {allowed_methods}")
                self.discovery_results['rest_oslc']['allowed_methods'] = allowed_methods.split(', ')
            else:
                print("  ❌ No Allow header found")
                
            # Store all response headers
            self.discovery_results['rest_oslc']['options_headers'] = dict(response.headers)
            
        except Exception as e:
            print(f"  ❌ OPTIONS test failed: {str(e)}")
            
    def _test_schema_discovery(self, endpoint_url: str):
        """Test schema discovery using ?_schema parameter."""
        print("\n📋 Testing schema discovery...")
        
        schema_url = f"{endpoint_url}?_schema"
        
        try:
            response = self.token_manager.session.get(
                schema_url,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    schema_data = response.json()
                    print(f"  ✅ Schema retrieved - Keys: {list(schema_data.keys())}")
                    self.discovery_results['rest_oslc']['schema'] = schema_data
                except:
                    print(f"  ❌ Non-JSON schema response")
                    self.discovery_results['rest_oslc']['schema_raw'] = response.text[:500]
            else:
                print(f"  ❌ Schema discovery failed: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Schema discovery error: {str(e)}")

    def _test_basic_get_capabilities(self, endpoint_url: str):
        """Test basic GET capabilities and response structure."""
        print("\n📋 Testing basic GET capabilities...")

        try:
            response = self.token_manager.session.get(
                endpoint_url,
                params={"oslc.select": "itemnum,siteid,status", "oslc.pageSize": "1"},
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )

            print(f"  Status: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  ✅ JSON Response - Structure: {list(data.keys())}")

                    # Store response structure info
                    self.discovery_results['rest_oslc']['response_structure'] = {
                        'root_keys': list(data.keys()),
                        'has_member': 'member' in data,
                        'total_count': data.get('responseInfo', {}).get('totalCount', 'Unknown')
                    }

                    if 'member' in data and data['member']:
                        sample_record = data['member'][0]
                        print(f"  📦 Sample record fields: {list(sample_record.keys())[:10]}...")
                        self.discovery_results['rest_oslc']['sample_fields'] = list(sample_record.keys())

                except Exception as parse_error:
                    print(f"  ❌ JSON parsing failed: {str(parse_error)}")
            else:
                print(f"  ❌ GET test failed: {response.status_code}")

        except Exception as e:
            print(f"  ❌ GET test error: {str(e)}")

    def _discover_wsmethods(self, endpoint_url: str):
        """Discover available web service methods."""
        print("\n📋 Discovering web service methods...")

        # Common inventory-related wsmethods to test
        potential_wsmethods = [
            'ADDCHANGE', 'ADDITEM', 'ADJUSTCURRENTBALANCE', 'ADJUSTPHYSICALCOUNT',
            'TRANSFERCURITEM', 'ISSUECURITEM', 'RECEIVECURITEM', 'RETURNCURITEM',
            'CREATE', 'UPDATE', 'DELETE', 'SAVE', 'VALIDATE',
            'GETINVENTORY', 'GETBALANCE', 'GETAVAILABILITY',
            'PROCESSREQUEST', 'SUBMITREQUEST', 'APPROVEREQUEST'
        ]

        discovered_methods = {}

        for wsmethod in potential_wsmethods:
            print(f"  🔍 Testing wsmethod: {wsmethod}")

            test_url = f"{endpoint_url}?action=wsmethod:{wsmethod}"

            try:
                # Test with minimal payload
                response = self.token_manager.session.post(
                    test_url,
                    json={"test": "discovery"},
                    timeout=(3.05, 15),
                    headers={
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    }
                )

                method_info = {
                    'status_code': response.status_code,
                    'exists': self._analyze_method_existence(response),
                    'response_preview': self._get_response_preview(response)
                }

                if method_info['exists']:
                    print(f"    ✅ Method exists - Status: {response.status_code}")
                else:
                    print(f"    ❌ Method not found - Status: {response.status_code}")

                discovered_methods[wsmethod] = method_info

            except Exception as e:
                discovered_methods[wsmethod] = {
                    'status_code': None,
                    'exists': False,
                    'error': str(e)
                }
                print(f"    ❌ Error testing {wsmethod}: {str(e)}")

        self.discovery_results['rest_oslc']['wsmethods'] = discovered_methods

        # Count discovered methods
        valid_methods = [m for m, info in discovered_methods.items() if info.get('exists', False)]
        print(f"\n📊 Discovered {len(valid_methods)} valid wsmethods: {', '.join(valid_methods)}")

    def _analyze_method_existence(self, response) -> bool:
        """Analyze response to determine if method exists."""
        # Method likely exists if we get 200, 400, or 422 (validation errors)
        if response.status_code in [200, 400, 422]:
            return True

        # Check response content for method-not-found indicators
        try:
            response_text = response.text.lower()
            not_found_indicators = [
                'method not found',
                'unknown method',
                'invalid action',
                'wsmethod not found'
            ]

            for indicator in not_found_indicators:
                if indicator in response_text:
                    return False

            # If we get here and status is 500, method might exist but have other issues
            return response.status_code != 404

        except:
            return False

    def _get_response_preview(self, response) -> str:
        """Get a preview of the response content."""
        try:
            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()
                return json.dumps(data, indent=2)[:500] + "..." if len(str(data)) > 500 else json.dumps(data, indent=2)
            else:
                return response.text[:500] + "..." if len(response.text) > 500 else response.text
        except:
            return f"Status: {response.status_code}, Content-Type: {response.headers.get('content-type', 'Unknown')}"

    def test_with_api_key(self):
        """Test the same discoveries using API key authentication."""
        if not self.api_key:
            print("\n❌ API key not available, skipping API key tests")
            return

        print("\n🔑 Testing with API Key Authentication")
        print("=" * 80)

        endpoint_url = f"{self.base_url}/api/os/MXAPIINVENTORY"
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }

        # Test basic GET with API key
        try:
            response = requests.get(
                endpoint_url,
                params={"oslc.select": "itemnum,siteid", "oslc.pageSize": "1"},
                headers=headers,
                timeout=(3.05, 15)
            )

            print(f"📋 API Key GET test - Status: {response.status_code}")

            if response.status_code == 200:
                print("✅ API key authentication working")
                self.discovery_results['authentication']['api_key_working'] = True
            else:
                print(f"❌ API key authentication failed: {response.status_code}")
                self.discovery_results['authentication']['api_key_working'] = False

        except Exception as e:
            print(f"❌ API key test error: {str(e)}")
            self.discovery_results['authentication']['api_key_working'] = False

    def consolidate_methods(self):
        """Consolidate all discovered methods into a unified structure."""
        print("\n📊 Consolidating Discovery Results")
        print("=" * 80)

        consolidated = []

        # Add SOAP/WSDL operations
        soap_operations = self.discovery_results.get('soap_wsdl', {}).get('operations', [])
        for op in soap_operations:
            consolidated.append({
                'name': op['name'],
                'type': 'SOAP/WSDL',
                'endpoint': f"{self.base_url}/services/InventoryService",
                'method': 'POST',
                'authentication': ['Session', 'Basic Auth'],
                'input_message': op.get('input_message'),
                'output_message': op.get('output_message'),
                'description': f"SOAP operation from InventoryService WSDL"
            })

        # Add REST/OSLC methods
        rest_methods = self.discovery_results.get('rest_oslc', {}).get('allowed_methods', [])
        for method in rest_methods:
            consolidated.append({
                'name': f"REST_{method}",
                'type': 'REST/OSLC',
                'endpoint': f"{self.base_url}/api/os/MXAPIINVENTORY",
                'method': method,
                'authentication': ['Session', 'API Key'],
                'description': f"Standard REST {method} operation"
            })

        # Add discovered wsmethods
        wsmethods = self.discovery_results.get('rest_oslc', {}).get('wsmethods', {})
        for wsmethod, info in wsmethods.items():
            if info.get('exists', False):
                consolidated.append({
                    'name': wsmethod,
                    'type': 'WSMethod',
                    'endpoint': f"{self.base_url}/api/os/MXAPIINVENTORY?action=wsmethod:{wsmethod}",
                    'method': 'POST',
                    'authentication': ['Session', 'API Key'],
                    'status_code': info.get('status_code'),
                    'description': f"Web service method for inventory operations"
                })

        self.discovery_results['consolidated_methods'] = consolidated

        print(f"✅ Consolidated {len(consolidated)} total methods:")
        for method in consolidated:
            print(f"  🔧 {method['name']} ({method['type']})")

    def generate_integration_report(self):
        """Generate a comprehensive integration report."""
        print("\n📋 Generating Integration Report")
        print("=" * 80)

        # Save JSON report
        json_filename = f"maximo_inventory_discovery_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_filename, 'w') as f:
            json.dump(self.discovery_results, f, indent=2, default=str)
        print(f"✅ JSON report saved: {json_filename}")

        # Generate markdown report
        markdown_filename = f"maximo_inventory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self._generate_markdown_report(markdown_filename)
        print(f"✅ Markdown report saved: {markdown_filename}")

        return json_filename, markdown_filename

    def _generate_markdown_report(self, filename: str):
        """Generate a markdown report."""
        with open(filename, 'w') as f:
            f.write("# Maximo Inventory Service Discovery Report\n\n")
            f.write(f"**Generated:** {self.discovery_results['timestamp']}\n")
            f.write(f"**Base URL:** {self.discovery_results['base_url']}\n\n")

            # Authentication section
            f.write("## Authentication Methods\n\n")
            auth = self.discovery_results.get('authentication', {})
            f.write(f"- **Session Authentication:** {'✅ Available' if auth.get('session_available') else '❌ Not Available'}\n")
            f.write(f"- **API Key Authentication:** {'✅ Available' if auth.get('api_key_available') else '❌ Not Available'}\n")
            if auth.get('api_key'):
                f.write(f"- **API Key:** {auth['api_key']}\n")
            f.write("\n")

            # SOAP/WSDL section
            f.write("## SOAP/WSDL Services\n\n")
            soap = self.discovery_results.get('soap_wsdl', {})
            if 'operations' in soap:
                f.write(f"**Total Operations:** {len(soap['operations'])}\n\n")
                f.write("| Operation | Input Message | Output Message |\n")
                f.write("|-----------|---------------|----------------|\n")
                for op in soap['operations']:
                    f.write(f"| {op['name']} | {op.get('input_message', 'N/A')} | {op.get('output_message', 'N/A')} |\n")
            else:
                f.write("❌ WSDL discovery failed\n")
            f.write("\n")

            # REST/OSLC section
            f.write("## REST/OSLC Methods\n\n")
            rest = self.discovery_results.get('rest_oslc', {})
            if 'allowed_methods' in rest:
                f.write(f"**Allowed HTTP Methods:** {', '.join(rest['allowed_methods'])}\n\n")

            # WSMethods section
            if 'wsmethods' in rest:
                f.write("### Discovered WSMethods\n\n")
                f.write("| Method | Status | Exists | Description |\n")
                f.write("|--------|--------|--------|--------------|\n")
                for method, info in rest['wsmethods'].items():
                    exists = "✅" if info.get('exists') else "❌"
                    status = info.get('status_code', 'N/A')
                    f.write(f"| {method} | {status} | {exists} | Inventory operation |\n")
            f.write("\n")

            # Consolidated methods section
            f.write("## All Available Methods\n\n")
            consolidated = self.discovery_results.get('consolidated_methods', [])
            f.write(f"**Total Methods:** {len(consolidated)}\n\n")
            f.write("| Name | Type | Method | Endpoint | Authentication |\n")
            f.write("|------|------|--------|----------|----------------|\n")
            for method in consolidated:
                auth_str = ', '.join(method.get('authentication', []))
                f.write(f"| {method['name']} | {method['type']} | {method['method']} | {method['endpoint']} | {auth_str} |\n")
            f.write("\n")

            # Integration examples
            f.write("## Integration Examples\n\n")
            f.write("### Session Authentication\n")
            f.write("```python\n")
            f.write("from backend.auth.token_manager import MaximoTokenManager\n")
            f.write(f"token_manager = MaximoTokenManager('{self.base_url}')\n")
            f.write("response = token_manager.session.get(endpoint_url, params=params)\n")
            f.write("```\n\n")

            if auth.get('api_key_available'):
                f.write("### API Key Authentication\n")
                f.write("```python\n")
                f.write("headers = {'Accept': 'application/json', 'apikey': 'YOUR_API_KEY'}\n")
                f.write("response = requests.get(endpoint_url, headers=headers, params=params)\n")
                f.write("```\n\n")


def main():
    """Main execution function."""
    print("🚀 Maximo Integration Explorer™")
    print("=" * 80)
    print("Comprehensive Inventory Service Discovery")
    print("Author: Maximo Integration Explorer™")
    print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # Initialize explorer
    explorer = MaximoInventoryServiceExplorer()

    # Step 1: Initialize authentication
    if not explorer.initialize_authentication():
        print("❌ Authentication initialization failed. Exiting.")
        return

    # Step 2: Discover SOAP/WSDL services
    explorer.discover_soap_wsdl_services()

    # Step 3: Discover REST/OSLC metadata
    explorer.discover_rest_oslc_metadata()

    # Step 4: Test with API key
    explorer.test_with_api_key()

    # Step 5: Consolidate results
    explorer.consolidate_methods()

    # Step 6: Generate reports
    json_file, markdown_file = explorer.generate_integration_report()

    # Final summary
    print("\n🎉 Discovery Complete!")
    print("=" * 80)
    print(f"📄 JSON Report: {json_file}")
    print(f"📄 Markdown Report: {markdown_file}")
    print("\n💡 Next Steps:")
    print("   1. Review the generated reports")
    print("   2. Test specific methods with your data")
    print("   3. Implement methods in your Python integration module")
    print("   4. Use the consolidated method list for reference")


if __name__ == "__main__":
    main()
