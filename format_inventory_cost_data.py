#!/usr/bin/env python3
"""
Format inventory cost data from Maximo MXAPIINVENTORY API to match expected JSON structure.
This script processes the raw API response and formats it according to user requirements.
"""
import json
import sys
from typing import List, Dict, Any

def format_inventory_cost_data(raw_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Format raw MXAPIINVENTORY data to match expected structure.
    
    Expected format:
    [
      {
        "itemnum": "5975-60-V00-0529",
        "itemsetid": "ITEMSET", 
        "siteid": "LCVKWT",
        "location": "RIP001",
        "invcost": [
          {
            "avgcost": "43.00",
            "stdcost": 30.00,
            "conditioncode": "A1"
          }
        ]
      }
    ]
    """
    formatted_data = []
    
    if 'member' not in raw_data:
        return formatted_data
    
    for item in raw_data['member']:
        # Extract basic inventory information
        formatted_item = {
            "itemnum": item.get("itemnum", ""),
            "itemsetid": item.get("itemsetid", ""),
            "siteid": item.get("siteid", ""),
            "location": item.get("location", ""),
            "invcost": []
        }
        
        # Process invcost array
        if 'invcost' in item and isinstance(item['invcost'], list):
            for cost_record in item['invcost']:
                formatted_cost = {
                    "avgcost": cost_record.get("avgcost", 0),
                    "stdcost": cost_record.get("stdcost", 0),
                    "lastcost": cost_record.get("lastcost", 0),
                    "conditioncode": cost_record.get("conditioncode", ""),
                    "orgid": cost_record.get("orgid", ""),
                    "invcostid": cost_record.get("invcostid", 0),
                    "condrate": cost_record.get("condrate", 0)
                }
                formatted_item["invcost"].append(formatted_cost)
        
        formatted_data.append(formatted_item)
    
    return formatted_data

def main():
    """Main function to process inventory cost data files."""
    
    # Process page 1 data
    print("🔧 Processing inventory cost data for item 5975-60-V00-0529")
    print("=" * 60)
    
    try:
        # Load page 1 data
        with open('inventory_cost_data_page1.json', 'r') as f:
            page1_data = json.load(f)
        
        # Load page 2 data
        with open('inventory_cost_data_page2.json', 'r') as f:
            page2_data = json.load(f)
        
        # Combine all data
        combined_data = {
            "member": page1_data.get("member", []) + page2_data.get("member", [])
        }
        
        # Format the data
        formatted_data = format_inventory_cost_data(combined_data)
        
        print(f"✅ Successfully processed {len(formatted_data)} inventory records")
        print(f"📊 Total cost records found: {sum(len(item['invcost']) for item in formatted_data)}")
        
        # Display summary by location
        print("\n📍 Inventory locations found:")
        locations = {}
        for item in formatted_data:
            location = item['location']
            if location not in locations:
                locations[location] = []
            locations[location].append(item)
        
        for location, items in locations.items():
            cost_count = sum(len(item['invcost']) for item in items)
            print(f"  • {location}: {len(items)} inventory record(s), {cost_count} cost record(s)")
        
        # Find RIP001 location specifically (as mentioned in user's example)
        rip001_items = [item for item in formatted_data if item['location'] == 'RIP001']
        if rip001_items:
            print(f"\n🎯 Found RIP001 location with {len(rip001_items)} record(s)")
            for item in rip001_items:
                if item['invcost']:
                    cost = item['invcost'][0]
                    print(f"   - avgcost: {cost['avgcost']}, stdcost: {cost['stdcost']}, conditioncode: {cost['conditioncode']}")
        
        # Save formatted data
        with open('formatted_inventory_cost_data.json', 'w') as f:
            json.dump(formatted_data, f, indent=2)
        
        print(f"\n💾 Formatted data saved to: formatted_inventory_cost_data.json")
        
        # Display sample of formatted data
        print("\n📋 Sample formatted data (first 2 records):")
        sample_data = formatted_data[:2]
        print(json.dumps(sample_data, indent=2))
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ Error: Required data file not found: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON in data file: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
