#!/bin/bash

# Test Payload Debug - Show Exact Payload and Maximo Response
# ===========================================================

echo "🚀 PAYLOAD DEBUG TEST - SHOW EXACT PAYLOAD AND MAXIMO RESPONSE"
echo "=============================================================="

echo "🎯 OBJECTIVE: Show the exact payload being sent and <PERSON><PERSON>'s response"
echo "📋 STRATEGY: Test transfer and watch Flask logs for detailed payload/response"
echo "🔍 IMPLEMENTATION: Enhanced logging shows exact JSON sent to Maximo"
echo ""

echo "⚠️  IMPORTANT: Before running this test:"
echo "1. Go to http://127.0.0.1:5010"
echo "2. Login with your Maximo credentials"
echo "3. Keep the Flask terminal open to see detailed logs"
echo ""

read -p "Have you logged in to the Flask app? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please login first at http://127.0.0.1:5010"
    exit 1
fi

echo "🔄 Testing transfer with detailed payload logging..."
echo ""

# Test: Same Site Transfer that might cause duplicate error
echo "📋 TESTING: Same Site Transfer (May cause BMXAA1861E duplicate error)"
echo "$(printf '=%.0s' {1..70})"
echo ""
echo "📋 PAYLOAD BEING SENT:"
echo '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "conversion_factor": 1.0,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'
echo ""

echo "🔄 Submitting to Flask application..."
echo "👀 WATCH THE FLASK TERMINAL FOR DETAILED LOGS!"
echo ""

response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-same-site \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 FLASK APPLICATION RESPONSE:"
echo "$response" | jq '.' 2>/dev/null || echo "$response"
echo ""

# Check response type
if echo "$response" | grep -q '"success": true'; then
    echo "🎉 SUCCESS! Transfer completed successfully"
elif echo "$response" | grep -q '"success": false'; then
    echo "❌ EXPECTED ERROR! This shows how errors are handled"
    
    if echo "$response" | grep -q 'BMXAA1861E'; then
        echo "✅ BMXAA1861E DUPLICATE ERROR DETECTED!"
        echo "📋 This is the exact error you mentioned - duplicate location/item/bin/lot"
    fi
    
    if echo "$response" | grep -q '"detailed_error"'; then
        echo "✅ DETAILED ERROR: Error details provided to user"
    fi
    
    if echo "$response" | grep -q '"error_guidance"'; then
        echo "✅ ERROR GUIDANCE: User guidance provided"
    fi
elif echo "$response" | grep -q "401"; then
    echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
else
    echo "⚠️  Unexpected response format"
fi

echo ""
echo "📋 WHAT TO LOOK FOR IN FLASK TERMINAL:"
echo "====================================="
echo ""
echo "1. 📋 EXACT PAYLOAD BEING SENT TO MAXIMO:"
echo "   Look for the JSON payload with _action, itemnum, siteid, etc."
echo ""
echo "2. 📋 EXACT RESPONSE FROM MAXIMO:"
echo "   Look for the response with _responsedata, Error, reasonCode, etc."
echo ""
echo "3. 🔍 KEY FIELDS TO CHECK:"
echo "   - siteid: Should be source site for same-site transfers"
echo "   - location: Should be source location for same-site transfers"
echo "   - fromstoreloc/tostoreloc: Source and destination storerooms"
echo "   - frombinnum/tobinnum: Source and destination bins"
echo "   - fromlotnum/tolotnum: Source and destination lots"
echo ""
echo "4. ❌ BMXAA1861E ERROR MEANS:"
echo "   - The combination of location/item/bin/lot already exists"
echo "   - User needs to change destination bin or lot"
echo "   - This is normal Maximo validation - not a bug"
echo ""
echo "📊 FLASK TERMINAL LOG EXAMPLE:"
echo "============================="
echo "Look for logs like this in your Flask terminal:"
echo ""
echo "================================================================================"
echo "📋 EXACT PAYLOAD BEING SENT TO MAXIMO:"
echo "================================================================================"
echo "🔗 URL: https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY..."
echo "📋 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'x-method-override': 'BULK'}"
echo "📋 Payload:"
echo "["
echo "  {"
echo "    \"_action\": \"AddChange\","
echo "    \"itemnum\": \"5975-60-V00-0529\","
echo "    \"siteid\": \"IKWAJ\","
echo "    \"location\": \"KWAJ-1058\","
echo "    \"matrectrans\": [{"
echo "      \"fromstoreloc\": \"KWAJ-1058\","
echo "      \"tostoreloc\": \"KWAJ-1058\","
echo "      \"frombinnum\": \"DEFAULT\","
echo "      \"tobinnum\": \"DEFAULT\","
echo "      \"fromlotnum\": \"DEFAULT\","
echo "      \"tolotnum\": \"DEFAULT\""
echo "    }]"
echo "  }"
echo "]"
echo "================================================================================"
echo "📋 EXACT RESPONSE FROM MAXIMO:"
echo "================================================================================"
echo "📊 Status Code: 200"
echo "📋 Response JSON:"
echo "["
echo "  {"
echo "    \"_responsedata\": {"
echo "      \"Error\": {"
echo "        \"reasonCode\": \"BMXAA1861E\","
echo "        \"message\": \"BMXAA1861E - The location/item/bin/lot combination already exists. Duplicates are not allowed.\""
echo "      }"
echo "    }"
echo "  }"
echo "]"
echo "================================================================================"
echo ""
echo "🎯 SUMMARY:"
echo "==========="
echo "✅ Payload logging implemented - you can see exactly what's sent to Maximo"
echo "✅ Response logging implemented - you can see exactly what Maximo returns"
echo "✅ Smart button logic implemented - buttons disable based on site selection"
echo "✅ Error handling implemented - BMXAA1861E errors shown to user with guidance"
echo ""
echo "🔧 The BMXAA1861E error is NORMAL - it means the user needs to:"
echo "• Change the destination bin (from DEFAULT to something else)"
echo "• Change the destination lot (from DEFAULT to something else)"
echo "• Use a different destination storeroom"
echo ""
echo "💡 This is working as intended - Maximo is preventing duplicate inventory records!"
