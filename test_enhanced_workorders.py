#!/usr/bin/env python3
"""
Test the enhanced workorder service to see what work orders are available
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager
from backend.services.enhanced_workorder_service import EnhancedWorkOrderService
from backend.services.enhanced_profile_service import EnhancedProfileService

def test_enhanced_workorders():
    """Test the enhanced workorder service"""
    
    print("🔧 TESTING ENHANCED WORKORDER SERVICE")
    print("=" * 50)
    
    try:
        # Initialize token manager with base URL
        base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
        token_manager = MaximoTokenManager(base_url)
        
        # Initialize enhanced profile service
        enhanced_profile_service = EnhancedProfileService(token_manager)

        # Initialize enhanced workorder service
        enhanced_wo_service = EnhancedWorkOrderService(token_manager, enhanced_profile_service)
        
        # Get work orders
        print("\n1️⃣ Getting all assigned work orders...")
        workorders, stats = enhanced_wo_service.get_assigned_workorders()
        
        print(f"Found {len(workorders)} work orders")
        print(f"Performance stats: {stats}")
        
        # Filter for WMATL and PISSUE statuses
        wmatl_pissue_wos = [wo for wo in workorders if wo.get('status') in ['WMATL', 'PISSUE']]
        print(f"\n2️⃣ Work orders with WMATL/PISSUE status: {len(wmatl_pissue_wos)}")
        
        for wo in wmatl_pissue_wos[:5]:  # Show first 5
            print(f"   - {wo.get('wonum')} - {wo.get('description', 'No desc')[:50]} ({wo.get('status')}) - Site: {wo.get('siteid')}")
        
        # Check specific sites
        test_sites = ["LCVKWT", "IKWAJ"]
        for site in test_sites:
            site_wos = [wo for wo in wmatl_pissue_wos if wo.get('siteid') == site]
            print(f"\n3️⃣ WMATL/PISSUE work orders for site {site}: {len(site_wos)}")
            
            for wo in site_wos[:3]:  # Show first 3
                print(f"   - {wo.get('wonum')} - {wo.get('description', 'No desc')[:50]} ({wo.get('status')})")
        
        # Show all unique statuses
        all_statuses = set(wo.get('status') for wo in workorders if wo.get('status'))
        print(f"\n4️⃣ All available work order statuses: {sorted(all_statuses)}")
        
        # Show work orders by status count
        status_counts = {}
        for wo in workorders:
            status = wo.get('status', 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"\n5️⃣ Work order counts by status:")
        for status, count in sorted(status_counts.items()):
            print(f"   {status}: {count}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_workorders()
