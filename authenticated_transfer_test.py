#!/usr/bin/env python3
"""
Test transfer endpoints using authenticated session from Flask app
"""

import requests
import json
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Import the token manager to get authenticated session
try:
    from backend.services.token_manager import TokenManager
    token_manager = TokenManager()
except ImportError:
    print("❌ Could not import TokenManager")
    sys.exit(1)

def test_endpoints_with_auth():
    """Test various transfer endpoints with authenticated session"""
    
    print("🔍 Testing Transfer Endpoints with Authenticated Session")
    print("=" * 60)
    
    # Get authenticated session
    try:
        session = token_manager.get_authenticated_session()
        base_url = token_manager.base_url
        print(f"✅ Got authenticated session for: {base_url}")
    except Exception as e:
        print(f"❌ Failed to get authenticated session: {e}")
        return
    
    # Test payload from terminal logs
    payload = {
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058",
        "transdate": "2025-07-15T19:51:28+00:00",
        "issueunit": "RO",
        "frombinnum": "28-800-0004",
        "tobinnum": "1058-TEMP",
        "fromlotnum": "TEST",
        "tolotnum": "TEST",
        "fromconditioncode": "A1",
        "toconditioncode": "A1"
    }
    
    # Headers
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test endpoints in order of likelihood
    endpoints_to_test = [
        ("MXAPIMATRECTRANS", f"{base_url}/oslc/os/mxapimatrectrans"),
        ("MXAPITRANSFER", f"{base_url}/oslc/os/mxapitransfer"),
        ("MXAPIINVTRANS", f"{base_url}/oslc/os/mxapiinvtrans"),
        ("MXAPIINVENTORY with action", f"{base_url}/oslc/os/mxapiinventory?action=TransferCurrentItem"),
        ("MXAPIINVUSE", f"{base_url}/oslc/os/mxapiinvuse"),
        ("MXAPIINVRESERVE", f"{base_url}/oslc/os/mxapiinvreserve")
    ]
    
    results = []
    
    for name, endpoint in endpoints_to_test:
        print(f"\n🔧 Testing {name}")
        print(f"Endpoint: {endpoint}")
        
        try:
            response = session.post(
                endpoint,
                headers=headers,
                json=payload,
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"✅ SUCCESS: {name}")
                    print(f"Response: {json.dumps(response_data, indent=2)[:500]}...")
                    results.append((name, "SUCCESS", response.status_code, response_data))
                except:
                    print(f"✅ SUCCESS: {name} (non-JSON response)")
                    results.append((name, "SUCCESS", response.status_code, response.text[:200]))
                    
            elif response.status_code == 201:
                try:
                    response_data = response.json()
                    print(f"✅ CREATED: {name}")
                    print(f"Response: {json.dumps(response_data, indent=2)[:500]}...")
                    results.append((name, "CREATED", response.status_code, response_data))
                except:
                    print(f"✅ CREATED: {name} (non-JSON response)")
                    results.append((name, "CREATED", response.status_code, response.text[:200]))
                    
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    print(f"❌ BAD REQUEST: {name}")
                    print(f"Error: {error_data}")
                    results.append((name, "BAD_REQUEST", response.status_code, error_data))
                except:
                    print(f"❌ BAD REQUEST: {name} (non-JSON error)")
                    results.append((name, "BAD_REQUEST", response.status_code, response.text[:200]))
                    
            elif response.status_code == 404:
                print(f"❌ NOT FOUND: {name}")
                results.append((name, "NOT_FOUND", response.status_code, "Endpoint does not exist"))
                
            else:
                print(f"⚠️ OTHER: {name} - Status {response.status_code}")
                results.append((name, "OTHER", response.status_code, response.text[:200]))
                
        except requests.exceptions.Timeout:
            print(f"⏰ TIMEOUT: {name}")
            results.append((name, "TIMEOUT", 0, "Request timed out"))
            
        except Exception as e:
            print(f"❌ ERROR: {name} - {e}")
            results.append((name, "ERROR", 0, str(e)))
    
    # Summary
    print(f"\n📋 SUMMARY")
    print("=" * 60)
    
    for name, status, code, data in results:
        print(f"{name:20} | {status:12} | {code:3} | {str(data)[:50]}")
    
    # Find the working endpoint
    working_endpoints = [r for r in results if r[1] in ["SUCCESS", "CREATED"]]
    if working_endpoints:
        print(f"\n🎯 WORKING ENDPOINTS:")
        for name, status, code, data in working_endpoints:
            print(f"✅ {name} - Use this endpoint!")
    else:
        print(f"\n⚠️ NO WORKING ENDPOINTS FOUND")
        print("Need to investigate alternative approaches")
    
    return results

if __name__ == "__main__":
    test_endpoints_with_auth()
