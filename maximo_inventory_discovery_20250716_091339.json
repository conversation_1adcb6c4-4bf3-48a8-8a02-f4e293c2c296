{"timestamp": "2025-07-16T09:13:29.033176", "base_url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo", "soap_wsdl": {"error": "HTTP 404"}, "rest_oslc": {"options_headers": {"Date": "Wed, 16 Jul 2025 03:43:33 GMT", "Content-Length": "0", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding"}, "wsmethods": {"ADDCHANGE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "ADDITEM": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "ADJUSTCURRENTBALANCE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "ADJUSTPHYSICALCOUNT": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "TRANSFERCURITEM": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "ISSUECURITEM": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "RECEIVECURITEM": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "RETURNCURITEM": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "CREATE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "UPDATE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "DELETE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "SAVE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "VALIDATE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "GETINVENTORY": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "GETBALANCE": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "GETAVAILABILITY": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "PROCESSREQUEST": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "SUBMITREQUEST": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}, "APPROVEREQUEST": {"status_code": 403, "exists": true, "response_preview": "{\n  \"Error\": {\n    \"extendedError\": {\n      \"moreInfo\": {\n        \"href\": \"https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E\"\n      }\n    },\n    \"reasonCode\": \"BMXAA7901E\",\n    \"message\": \"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\n    \"statusCode\": \"403\"\n  }\n}"}}}, "authentication": {"session_available": true, "api_key_available": false}, "consolidated_methods": [{"name": "ADDCHANGE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADDCHANGE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "ADDITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADDITEM", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "ADJUSTCURRENTBALANCE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADJUSTCURRENTBALANCE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "ADJUSTPHYSICALCOUNT", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADJUSTPHYSICALCOUNT", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "TRANSFERCURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:TRANSFERCURITEM", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "ISSUECURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ISSUECURITEM", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "RECEIVECURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:RECEIVECURITEM", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "RETURNCURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:RETURNCURITEM", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "CREATE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:CREATE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "UPDATE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:UPDATE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "DELETE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:DELETE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "SAVE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:SAVE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "VALIDATE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:VALIDATE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "GETINVENTORY", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:GETINVENTORY", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "GETBALANCE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:GETBALANCE", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "GETAVAILABILITY", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:GETAVAILABILITY", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "PROCESSREQUEST", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:PROCESSREQUEST", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "SUBMITREQUEST", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:SUBMITREQUEST", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}, {"name": "APPROVEREQUEST", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:APPROVEREQUEST", "method": "POST", "authentication": ["Session", "API Key"], "status_code": 403, "description": "Web service method for inventory operations"}]}