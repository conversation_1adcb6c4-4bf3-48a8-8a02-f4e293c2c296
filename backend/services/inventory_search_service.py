#!/usr/bin/env python3
"""
Inventory Search Service

This service handles searching inventory items using MXAPIINVENTORY and MXAPIITEM APIs.
It provides comprehensive inventory search functionality with site-aware filtering.

Author: Augment Agent
Date: 2025-01-27
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
import requests

logger = logging.getLogger(__name__)

class InventorySearchService:
    """
    Service for searching inventory items from Maximo MXAPIINVENTORY and MXAPIITEM APIs.

    This service provides:
    - Inventory search by item number (partial/full)
    - Inventory search by description (partial/full)
    - Site-aware inventory filtering
    - Enhanced item data from MXAPIITEM when needed
    - Efficient API calls with proper error handling
    """

    def __init__(self, token_manager):
        """
        Initialize the inventory search service.

        Args:
            token_manager: Maximo token manager for API authentication
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        self._search_cache = {}
        self._cache_timeout = 300  # 5 minutes cache timeout

    def search_inventory_items(self, search_term: str, site_id: str, limit: int = 20) -> Tuple[List[Dict], Dict]:
        """
        Search inventory items using MXAPIITEM endpoint with partial description, partial itemnum, and exact itemnum support.
        Uses status != 'OBSOLETE' filter as requested.

        Args:
            search_term (str): Search term for item number or description (supports partial matching)
            site_id (str): Site ID (kept for compatibility but not used in MXAPIITEM search)
            limit (int): Maximum number of results to return

        Returns:
            Tuple[List[Dict], Dict]: (inventory_items, metadata)
        """
        start_time = time.time()
        
        if not search_term or not search_term.strip():
            return [], {'load_time': 0, 'source': 'empty', 'count': 0}

        search_term = search_term.strip()
        
        # Check cache first
        cache_key = f"{search_term}_{site_id}_{limit}"
        if self._is_cache_valid(cache_key):
            self.logger.info(f"🔍 INVENTORY: Using cached search results for '{search_term}'")
            return self._search_cache[cache_key]['data'], {
                'load_time': time.time() - start_time,
                'source': 'cache'
            }

        try:
            # Step 1: Search MXAPIITEM for comprehensive item data (primary dataset)
            self.logger.info(f"🔍 INVENTORY: Step 1 - Searching MXAPIITEM for '{search_term}' (limit: {limit * 3})")
            mxapiitem_results = self._search_mxapiitem_comprehensive(search_term, limit * 3)  # Get more items for filtering

            if not mxapiitem_results:
                self.logger.info(f"🔍 INVENTORY: No items found in MXAPIITEM")
                return [], {
                    'load_time': time.time() - start_time,
                    'source': 'mxapiitem_empty',
                    'count': 0
                }

            # Step 2: Filter through MXAPIINVENTORY with site_id and status != 'OBSOLETE'
            self.logger.info(f"🔍 INVENTORY: Step 2 - Filtering {len(mxapiitem_results)} items through MXAPIINVENTORY for site '{site_id}'")
            filtered_items = self._filter_through_mxapiinventory(mxapiitem_results, site_id)

            # Step 3: Sort by relevance and limit results
            filtered_items = self._sort_by_relevance(filtered_items, search_term)
            final_items = filtered_items[:limit]

            # Cache the results
            self._search_cache[cache_key] = {
                'data': final_items,
                'timestamp': time.time()
            }

            load_time = time.time() - start_time
            self.logger.info(f"🔍 INVENTORY: Found {len(final_items)} items after filtering through inventory in {load_time:.3f}s")

            return final_items, {
                'load_time': load_time,
                'source': 'mxapiitem_filtered_inventory',
                'count': len(final_items),
                'mxapiitem_count': len(mxapiitem_results),
                'filtered_count': len(filtered_items)
            }

        except Exception as e:
            self.logger.error(f"🔍 INVENTORY: Search failed for '{search_term}': {str(e)}")
            return [], {
                'load_time': time.time() - start_time,
                'source': 'error',
                'error': str(e)
            }

    def _search_inventory_primary(self, search_term: str, site_id: str, limit: int) -> List[Dict]:
        """
        Primary search using MXAPIINVENTORY endpoint.

        Args:
            search_term (str): Search term
            site_id (str): Site ID for filtering
            limit (int): Maximum results

        Returns:
            List[Dict]: Inventory items from MXAPIINVENTORY
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Build search filters using proper OSLC syntax (no parentheses)
        # OSLC doesn't support parentheses grouping, so we need to use multiple conditions

        # Build search filter for MXAPIINVENTORY with partial search
        # RESTRICTED TO CURRENT WORK ORDER SITE ID ONLY - no cross-site search
        # Use the correct OSLC syntax: ="%term%" for partial search (from reference implementation)
        search_term_clean = search_term.replace('"', '\\"')
        oslc_filter = f'itemnum="%{search_term_clean}%" and siteid="{site_id}" and status="ACTIVE"'

        # Select ONLY the fields that actually exist in MXAPIINVENTORY
        # Based on actual API response analysis for item 5975-60-V00-0001
        select_fields = [
            # Core inventory fields (confirmed available)
            "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
            "issueunit", "orderunit", "curbaltotal", "avblbalance",
            "costtype", "conditioncode", "inventoryid", "orgid",
            "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty",
            "opstime", "deliverytime", "admimtime", "statusdate",
            "issue1yrago", "issue2yrago", "issue3yrago", "issueytd",
            "expiredqty", "invreserveqty", "shippedqty"
        ]
        # Note: description, abc, vendor, manufacturer, modelnum, rotating, binnum,
        # storeloc, conditionenabled, physcnt, physcntdate, unitcost, currencycode
        # are NOT available as direct fields in MXAPIINVENTORY

        # Add nested array fields - these will be accessed via array[0].field in processing
        nested_fields = [
            "invcost", "invbalances", "itemcondition", "invvendor"
        ]
        # Note: invcost.* and invbalances.* dot notation doesn't work
        # The actual data is in nested arrays that need special processing

        all_fields = select_fields + nested_fields

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": oslc_filter,
            "oslc.pageSize": str(limit),
            "lean": "1"
        }

        self.logger.info(f"🔍 INVENTORY: Searching MXAPIINVENTORY for '{search_term}'")
        self.logger.info(f"🔍 INVENTORY: API URL: {api_url}")
        self.logger.info(f"🔍 INVENTORY: Filter: {oslc_filter}")
        self.logger.info(f"🔍 INVENTORY: OSLC Select: {params['oslc.select']}")

        response = self.token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        self.logger.info(f"🔍 INVENTORY: Response status: {response.status_code}")

        if response.status_code != 200:
            self.logger.error(f"🔍 INVENTORY: API call failed with status {response.status_code}")
            self.logger.error(f"🔍 INVENTORY: Response text: {response.text}")
            raise Exception(f"API call failed: {response.status_code}")

        try:
            # Debug: Log the actual response content
            response_text = response.text
            self.logger.info(f"🔍 INVENTORY: Response content length: {len(response_text)}")
            self.logger.info(f"🔍 INVENTORY: Response content (first 200 chars): {response_text[:200]}")

            if not response_text.strip():
                self.logger.warning("🔍 INVENTORY: Empty response - no inventory items found")
                return []

            data = response.json()
            self.logger.info(f"🔍 INVENTORY: JSON keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

            items = data.get('member', data.get('rdfs:member', []))

            self.logger.info(f"🔍 INVENTORY: Raw response contains {len(items)} items")

            # Clean and process inventory data
            processed_items = []
            for item in items:
                processed_item = self._clean_inventory_data(item)
                if processed_item:
                    processed_items.append(processed_item)

            self.logger.info(f"🔍 INVENTORY: Processed {len(processed_items)} inventory items with comprehensive data")
            return processed_items

        except Exception as e:
            self.logger.error(f"🔍 INVENTORY: Error parsing API response: {str(e)}")
            self.logger.error(f"🔍 INVENTORY: Raw response: {response.text[:500]}")
            raise Exception(f"Error parsing inventory data: {str(e)}")

    def _enhance_with_item_data(self, inventory_items: List[Dict], site_id: str) -> List[Dict]:
        """
        Enhance inventory items with additional data from MXAPIITEM.

        Args:
            inventory_items (List[Dict]): Items from MXAPIINVENTORY
            site_id (str): Site ID

        Returns:
            List[Dict]: Enhanced inventory items
        """
        if not inventory_items:
            return inventory_items

        enhanced_items = []
        
        for inv_item in inventory_items:
            itemnum = inv_item.get('itemnum')
            if not itemnum:
                enhanced_items.append(inv_item)
                continue

            try:
                # Get additional item data from MXAPIITEM (always fetch description from item master)
                item_data = self._get_item_details(itemnum)

                # Merge data (inventory data takes precedence for quantities/costs, item master for description)
                enhanced_item = {**inv_item}  # Start with inventory data
                enhanced_item['description'] = item_data.get('description', '')  # Always use item master description
                enhanced_item['itemtype'] = item_data.get('itemtype', enhanced_item.get('itemtype', ''))
                enhanced_item['data_source'] = 'inventory_enhanced'
                enhanced_item['is_direct_issue'] = False  # Items in inventory are NOT direct issue
                enhanced_items.append(enhanced_item)
                
            except Exception as e:
                self.logger.warning(f"🔍 INVENTORY: Failed to enhance item {itemnum}: {str(e)}")
                inv_item['data_source'] = 'inventory_only'
                inv_item['is_direct_issue'] = False  # Items in inventory are NOT direct issue
                enhanced_items.append(inv_item)

        return enhanced_items

    def _get_item_details(self, itemnum: str) -> Dict:
        """
        Get item details from MXAPIITEM.

        Args:
            itemnum (str): Item number

        Returns:
            Dict: Item details from MXAPIITEM
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiitem"

        # Select fields from MXAPIITEM that actually exist (based on testing)
        select_fields = [
            "itemnum", "description", "issueunit", "orderunit", "itemsetid"
            # Note: conditioncode, nsn, commoditygroup, commodity may not exist
        ]

        params = {
            "oslc.select": ",".join(select_fields),
            "oslc.where": f'itemnum="{itemnum}" and status="ACTIVE"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        response = self.token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 15),
            headers={"Accept": "application/json"}
        )

        if response.status_code == 200:
            try:
                data = response.json()
                items = data.get('member', [])
                if items:
                    return self._clean_item_data(items[0])
            except Exception as e:
                self.logger.warning(f"🔍 INVENTORY: Failed to parse item data for {itemnum}: {str(e)}")

        return {}

    def _search_item_master_for_direct_issue(self, search_term: str, site_id: str, limit: int) -> List[Dict]:
        """
        Search MXAPIITEM for items not in inventory (direct issue items).

        Args:
            search_term (str): Search term
            site_id (str): Site ID (for reference)
            limit (int): Maximum results

        Returns:
            List[Dict]: Items from MXAPIITEM marked as direct issue
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiitem"

        # Search MXAPIITEM for partial itemnum and description matches (ACTIVE status)
        # Use the correct OSLC syntax: ="%term%" for partial search (from reference implementation)
        search_term_clean = search_term.replace('"', '\\"')

        # Search strategies - ONLY ACTIVE status items (no PENDOBS or other statuses)
        search_filters = [
            # 1. Exact item number match
            f'itemnum="{search_term_clean}" and status="ACTIVE"',
            # 2. Partial item number match using LIKE pattern
            f'itemnum="%{search_term_clean}%" and status="ACTIVE"',
            # 3. Partial description match using LIKE pattern
            f'description="%{search_term_clean}%" and status="ACTIVE"'
        ]

        # Select fields from MXAPIITEM that actually exist (based on testing)
        select_fields = [
            "itemnum", "description", "issueunit", "orderunit", "itemsetid", "itemtype", "status"
        ]

        all_items = []
        found_item_nums = set()  # Track found items to avoid duplicates

        # Try each search filter
        for i, oslc_filter in enumerate(search_filters):
            self.logger.info(f"🔍 ITEM MASTER: Try #{i+1} - Filter: {oslc_filter}")

            params = {
                "oslc.select": ",".join(select_fields),
                "oslc.where": oslc_filter,
                "oslc.pageSize": str(limit),
                "lean": "1"
            }

            try:
                response = self.token_manager.session.get(
                    api_url,
                    params=params,
                    timeout=(5.0, 30),
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    items = data.get('member', [])
                    self.logger.info(f"🔍 ITEM MASTER: Found {len(items)} items with filter #{i+1}")

                    # Add unique items only
                    for item in items:
                        itemnum = item.get('itemnum', '')
                        if itemnum and itemnum not in found_item_nums:
                            all_items.append(item)
                            found_item_nums.add(itemnum)

                    # If we found enough items, stop searching
                    if len(all_items) >= limit:
                        break

                else:
                    self.logger.error(f"🔍 ITEM MASTER: API call failed with status {response.status_code} for filter #{i+1}")

            except Exception as e:
                self.logger.error(f"🔍 ITEM MASTER: Error with filter #{i+1}: {str(e)}")

        # Convert to direct issue format
        direct_issue_items = []
        for item in all_items:
            direct_issue_item = self._convert_to_direct_issue_item(item, site_id)
            if direct_issue_item:
                direct_issue_items.append(direct_issue_item)

        # NO PENDOBS SEARCH - Only ACTIVE status items allowed
        # Direct issue items are only those found in MXAPIITEM with ACTIVE status
        # that are NOT found in MXAPIINVENTORY

        return direct_issue_items

    # REMOVED: _search_item_master_pendobs method
    # Only ACTIVE status items are allowed - no PENDOBS search

    def _convert_to_direct_issue_item(self, item_data: Dict, site_id: str) -> Dict:
        """
        Convert MXAPIITEM data to direct issue item format.

        Args:
            item_data (Dict): Raw item data from MXAPIITEM
            site_id (str): Site ID for reference

        Returns:
            Dict: Formatted direct issue item
        """
        return {
            'itemnum': item_data.get('itemnum', ''),
            'siteid': site_id,  # Reference site, not actual inventory location
            'location': 'DIRECT ISSUE',  # Highlight as direct issue
            'description': item_data.get('description', ''),
            'issueunit': item_data.get('issueunit', 'EA'),
            'orderunit': item_data.get('orderunit', 'EA'),
            'curbaltotal': 0.0,  # No inventory balance
            'avblbalance': 0.0,  # No available balance
            'status': item_data.get('status', 'ACTIVE'),
            'itemtype': item_data.get('itemtype', ''),
            'itemsetid': item_data.get('itemsetid', ''),
            'avgcost': 0.0,  # No cost data from item master
            'lastcost': 0.0,
            'stdcost': 0.0,
            'currency': '',  # No hardcoded currency
            'data_source': 'direct_issue',  # Mark as direct issue item
            'is_direct_issue': True  # Flag for UI highlighting
        }

    def _clean_inventory_data(self, raw_item: Dict) -> Dict:
        """Clean and normalize inventory data."""
        if not raw_item:
            return {}

        # Process invcost data
        cost_data = self._process_cost_data(raw_item.get('invcost', []))

        cleaned_item = {
            'itemnum': raw_item.get('itemnum', ''),
            'siteid': raw_item.get('siteid', ''),
            'location': raw_item.get('location', ''),
            'description': raw_item.get('description', ''),
            'issueunit': raw_item.get('issueunit', 'EA'),
            'orderunit': raw_item.get('orderunit', 'EA'),
            'curbaltotal': float(raw_item.get('curbaltotal', 0)),
            'avblbalance': float(raw_item.get('avblbalance', 0)),
            'status': raw_item.get('status', ''),
            'abc': raw_item.get('abc', ''),
            'vendor': raw_item.get('vendor', ''),
            'manufacturer': raw_item.get('manufacturer', ''),
            'modelnum': raw_item.get('modelnum', ''),
            'itemtype': raw_item.get('itemtype', ''),
            'rotating': raw_item.get('rotating', False),
            'conditioncode': raw_item.get('conditioncode', ''),
            'itemsetid': raw_item.get('itemsetid', ''),
            **cost_data
        }

        return cleaned_item

    def _clean_item_data(self, raw_item: Dict) -> Dict:
        """Clean and normalize item master data."""
        if not raw_item:
            return {}

        # Only include fields that have actual values
        cleaned_data = {}
        if raw_item.get('itemnum'):
            cleaned_data['itemnum'] = raw_item['itemnum']
        if raw_item.get('description'):
            cleaned_data['description'] = raw_item['description']
        if raw_item.get('issueunit'):
            cleaned_data['issueunit'] = raw_item['issueunit']
        if raw_item.get('orderunit'):
            cleaned_data['orderunit'] = raw_item['orderunit']
        if raw_item.get('conditioncode'):
            cleaned_data['conditioncode'] = raw_item['conditioncode']
        if raw_item.get('itemsetid'):
            cleaned_data['itemsetid'] = raw_item['itemsetid']
        if raw_item.get('nsn'):
            cleaned_data['nsn'] = raw_item['nsn']
        if raw_item.get('commoditygroup'):
            cleaned_data['commoditygroup'] = raw_item['commoditygroup']
        if raw_item.get('commodity'):
            cleaned_data['commodity'] = raw_item['commodity']

        return cleaned_data

    def _process_cost_data(self, invcost_data) -> Dict:
        """Process cost data from invcost table."""
        cost_info = {
            'avgcost': 0.0,
            'lastcost': 0.0,
            'stdcost': 0.0,
            'currency': ''  # No hardcoded currency - get from actual data
        }

        if not invcost_data:
            return cost_info

        # Handle both dict and list formats
        if isinstance(invcost_data, dict):
            # Single cost record as dict
            cost_info['avgcost'] = float(invcost_data.get('avgcost', 0) or 0)
            cost_info['lastcost'] = float(invcost_data.get('lastcost', 0) or 0)
            cost_info['stdcost'] = float(invcost_data.get('stdcost', 0) or 0)
            cost_info['currency'] = invcost_data.get('currencycode', '') or ''
        elif isinstance(invcost_data, list):
            # Multiple cost records as list
            for cost_record in invcost_data:
                if not isinstance(cost_record, dict):
                    continue

                cost_type = cost_record.get('costtype', '').upper()
                # Try different field names for cost value
                cost_value = cost_record.get('avgcost') or cost_record.get('lastcost') or cost_record.get('stdcost') or cost_record.get('cost', 0)
                cost_value = float(cost_value or 0)
                currency = cost_record.get('currencycode', '')

                if cost_type == 'AVERAGE' or 'avgcost' in cost_record:
                    cost_info['avgcost'] = cost_value
                elif cost_type == 'LAST' or 'lastcost' in cost_record:
                    cost_info['lastcost'] = cost_value
                elif cost_type == 'STANDARD' or 'stdcost' in cost_record:
                    cost_info['stdcost'] = cost_value

                # Use the first currency found
                if currency and not cost_info['currency']:
                    cost_info['currency'] = currency

        return cost_info

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self._search_cache:
            return False
        
        cache_entry = self._search_cache[cache_key]
        return (time.time() - cache_entry['timestamp']) < self._cache_timeout

    def clear_cache(self):
        """Clear the search cache."""
        self._search_cache.clear()
        self.logger.info("🔍 INVENTORY: Search cache cleared")

    def get_cache_stats(self) -> Dict:
        """Get cache statistics."""
        return {
            'entries': len(self._search_cache),
            'timeout_seconds': self._cache_timeout
        }

    def _process_inventory_data(self, items: List[Dict]) -> List[Dict]:
        """
        Process inventory data to extract ALL nested array fields into comprehensive flat structure.
        Maps all MXAPIINVENTORY fields and related table data as per user requirements.

        Args:
            items (List[Dict]): Raw inventory items from MXAPIINVENTORY

        Returns:
            List[Dict]: Processed inventory items with ALL flattened nested data
        """
        processed_items = []

        for item in items:
            processed_item = item.copy()

            # Process INVCOST array - extract ALL cost fields
            if 'invcost' in item and isinstance(item['invcost'], list) and item['invcost']:
                cost_data = item['invcost'][0]  # Get first cost record
                # Map all INVCOST fields as per user requirements
                processed_item['invcost_avgcost'] = cost_data.get('avgcost')
                processed_item['invcost_lastcost'] = cost_data.get('lastcost')
                processed_item['invcost_stdcost'] = cost_data.get('stdcost')
                processed_item['invcost_conditioncode'] = cost_data.get('conditioncode')
                processed_item['invcost_orgid'] = cost_data.get('orgid')
                processed_item['invcost_invcostid'] = cost_data.get('invcostid')
                processed_item['invcost_condrate'] = cost_data.get('condrate')

                # Also keep old field names for backward compatibility
                processed_item['avgcost'] = cost_data.get('avgcost')
                processed_item['lastcost'] = cost_data.get('lastcost')
                processed_item['stdcost'] = cost_data.get('stdcost')

            # Process INVBALANCES array - extract ALL balance fields
            if 'invbalances' in item and isinstance(item['invbalances'], list) and item['invbalances']:
                balance_data = item['invbalances'][0]  # Get first balance record
                # Map all INVBALANCES fields as per user requirements
                processed_item['invbalances_curbal'] = balance_data.get('curbal')
                processed_item['invbalances_physcnt'] = balance_data.get('physcnt')
                processed_item['invbalances_physcntdate'] = balance_data.get('physcntdate')
                processed_item['invbalances_conditioncode'] = balance_data.get('conditioncode')
                processed_item['invbalances_reconciled'] = balance_data.get('reconciled')
                processed_item['invbalances_invbalancesid'] = balance_data.get('invbalancesid')
                processed_item['invbalances_stagedcurbal'] = balance_data.get('stagedcurbal')
                processed_item['invbalances_expiredqty'] = balance_data.get('expiredqty')
                processed_item['invbalances_stagingbin'] = balance_data.get('stagingbin')
                processed_item['invbalances_lotnum'] = balance_data.get('lotnum')
                processed_item['invbalances_binnum'] = balance_data.get('binnum')
                processed_item['invbalances_location'] = balance_data.get('location')
                processed_item['invbalances_unitcost'] = balance_data.get('unitcost')

                # Also keep old field names for backward compatibility
                processed_item['curbal'] = balance_data.get('curbal')
                processed_item['physcnt'] = balance_data.get('physcnt')
                processed_item['physcntdate'] = balance_data.get('physcntdate')

            # Process INVVENDOR array - extract ALL vendor fields
            if 'invvendor' in item and isinstance(item['invvendor'], list) and item['invvendor']:
                vendor_data = item['invvendor'][0]  # Get first vendor record
                # Map all INVVENDOR fields as per user requirements
                processed_item['invvendor_vendor'] = vendor_data.get('vendor')
                processed_item['invvendor_manufacturer'] = vendor_data.get('manufacturer')
                processed_item['invvendor_modelnum'] = vendor_data.get('modelnum')
                processed_item['invvendor_currencycode'] = vendor_data.get('currencycode')
                processed_item['invvendor_contractnum'] = vendor_data.get('contractnum')
                processed_item['invvendor_invvendorid'] = vendor_data.get('invvendorid')

                # Also keep old field names for backward compatibility
                processed_item['vendor'] = vendor_data.get('vendor')
                processed_item['manufacturer'] = vendor_data.get('manufacturer')
                processed_item['modelnum'] = vendor_data.get('modelnum')
                processed_item['currency'] = vendor_data.get('currencycode')

            # Process ITEMCONDITION array - extract condition details
            if 'itemcondition' in item and isinstance(item['itemcondition'], list) and item['itemcondition']:
                condition_data = item['itemcondition'][0]  # Get first condition record
                processed_item['itemcondition_description'] = condition_data.get('description')
                processed_item['itemcondition_conditioncode'] = condition_data.get('conditioncode')
                processed_item['itemcondition_condrate'] = condition_data.get('condrate')
                processed_item['itemcondition_itemconditionid'] = condition_data.get('itemconditionid')

            processed_items.append(processed_item)

        return processed_items

    def _get_item_data_from_mxapiitem(self, itemnum: str) -> Dict:
        """
        Get ITEM table data from MXAPIITEM endpoint.
        This includes description, rotating, lottype, conditionenabled, etc.

        Args:
            itemnum (str): Item number to look up

        Returns:
            Dict: Item data from MXAPIITEM or empty dict if not found
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiitem"

            # Select ALL available ITEM fields
            item_fields = [
                "itemnum", "description", "status", "itemtype", "itemsetid",
                "issueunit", "orderunit", "rotating", "lottype", "conditionenabled",
                "unitcost", "commoditygroup", "commodity", "glaccount",
                "controlacc", "shrinkageacc", "abctype", "statusdate",
                "changeby", "changedate", "hasld", "langcode"
            ]

            params = {
                "oslc.select": ",".join(item_fields),
                "oslc.where": f'itemnum="{itemnum}" and status!="OBSOLETE"',
                "oslc.pageSize": "1",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])
                if items:
                    item_data = items[0]
                    # Prefix all ITEM fields to distinguish from INVENTORY fields
                    prefixed_item_data = {}
                    for key, value in item_data.items():
                        prefixed_item_data[f'item_{key}'] = value
                    return prefixed_item_data

        except Exception as e:
            self.logger.warning(f"⚠️ INVENTORY: Failed to get item data for {itemnum}: {str(e)}")

        return {}

    def _enhance_inventory_with_item_data(self, inventory_items: List[Dict]) -> List[Dict]:
        """
        Enhance inventory items with ITEM data from MXAPIITEM.

        Args:
            inventory_items (List[Dict]): Processed inventory items from MXAPIINVENTORY

        Returns:
            List[Dict]: Enhanced inventory items with ITEM data included
        """
        enhanced_items = []

        for inv_item in inventory_items:
            enhanced_item = inv_item.copy()

            # Get ITEM data from MXAPIITEM
            itemnum = inv_item.get('itemnum')
            if itemnum:
                item_data = self._get_item_data_from_mxapiitem(itemnum)
                enhanced_item.update(item_data)

            enhanced_items.append(enhanced_item)

        return enhanced_items

    def _search_mxapiitem_comprehensive(self, search_term: str, limit: int) -> List[Dict]:
        """
        Comprehensive search using MXAPIITEM endpoint with multiple search strategies.
        Supports partial description, partial itemnum, and exact itemnum searches.
        Uses status != 'OBSOLETE' filter as requested.

        Args:
            search_term (str): Search term for item number or description
            limit (int): Maximum number of results to return

        Returns:
            List[Dict]: Items found from MXAPIITEM
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiitem"

        # Clean search term for OSLC query
        search_term_clean = search_term.replace('"', '\\"')

        # Search strategies - ONLY non-OBSOLETE status items
        search_filters = [
            # 1. Exact item number match
            f'itemnum="{search_term_clean}" and status!="OBSOLETE"',
            # 2. Partial item number match using LIKE pattern
            f'itemnum="%{search_term_clean}%" and status!="OBSOLETE"',
            # 3. Partial description match using LIKE pattern
            f'description="%{search_term_clean}%" and status!="OBSOLETE"'
        ]

        # Select fields from MXAPIITEM
        select_fields = [
            "itemnum", "description", "issueunit", "orderunit", "itemsetid",
            "itemtype", "status", "unitcost", "rotating", "conditionenabled",
            "lottype", "commoditygroup", "commodity", "abctype"
        ]

        all_items = []
        found_item_nums = set()  # Track found items to avoid duplicates

        for i, oslc_filter in enumerate(search_filters):
            try:
                params = {
                    "oslc.select": ",".join(select_fields),
                    "oslc.where": oslc_filter,
                    "oslc.pageSize": str(limit * 2),  # Get more items to account for filtering
                    "lean": "1"
                }

                self.logger.info(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1}: {oslc_filter}")

                response = self.token_manager.session.get(
                    api_url,
                    params=params,
                    timeout=(3.05, 10),
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    items = data.get('member', [])

                    for item in items:
                        itemnum = item.get('itemnum', '')
                        if itemnum and itemnum not in found_item_nums:
                            found_item_nums.add(itemnum)

                            # Process the item data
                            processed_item = {
                                'itemnum': itemnum,
                                'description': item.get('description', ''),
                                'status': item.get('status', ''),
                                'itemtype': item.get('itemtype', ''),
                                'issueunit': item.get('issueunit', ''),
                                'orderunit': item.get('orderunit', ''),
                                'unitcost': item.get('unitcost', 0),
                                'rotating': item.get('rotating', False),
                                'conditionenabled': item.get('conditionenabled', False),
                                'lottype': item.get('lottype', ''),
                                'commoditygroup': item.get('commoditygroup', ''),
                                'commodity': item.get('commodity', ''),
                                'abctype': item.get('abctype', ''),
                                'itemsetid': item.get('itemsetid', ''),
                                'source': 'mxapiitem'
                            }
                            all_items.append(processed_item)

                            # Stop if we have enough items
                            if len(all_items) >= limit:
                                break
                else:
                    self.logger.warning(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1} failed: {response.status_code}")

            except Exception as e:
                self.logger.error(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1} error: {str(e)}")
                continue

            # Stop if we have enough items
            if len(all_items) >= limit:
                break

        self.logger.info(f"🔍 INVENTORY: MXAPIITEM comprehensive search found {len(all_items)} items")
        return all_items

    def _filter_through_mxapiinventory(self, mxapiitem_results: List[Dict], site_id: str) -> List[Dict]:
        """
        Filter MXAPIITEM results through MXAPIINVENTORY to ensure items exist in inventory for the specified site.
        Uses OSLC endpoint with siteid and status != 'OBSOLETE' filters.

        Args:
            mxapiitem_results (List[Dict]): Items from MXAPIITEM search
            site_id (str): Site ID to filter inventory

        Returns:
            List[Dict]: Items that exist in both MXAPIITEM and MXAPIINVENTORY for the site
        """
        if not mxapiitem_results or not site_id:
            return []

        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Extract item numbers from MXAPIITEM results
        item_numbers = [item.get('itemnum') for item in mxapiitem_results if item.get('itemnum')]

        if not item_numbers:
            return []

        self.logger.info(f"🔍 INVENTORY: Filtering {len(item_numbers)} items through MXAPIINVENTORY for site '{site_id}'")

        filtered_items = []
        inventory_data_map = {}

        # Process items one by one to avoid OSLC filter complexity issues
        # OSLC doesn't support parentheses grouping, so we need to query each item individually
        for itemnum in item_numbers:
            if not itemnum:
                continue

            # Build simple OSLC filter for single item
            oslc_filter = f'siteid="{site_id}" and status!="OBSOLETE" and itemnum="{itemnum}"'

            # Select comprehensive inventory fields
            select_fields = [
                "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
                "issueunit", "orderunit", "curbaltotal", "avblbalance",
                "costtype", "conditioncode", "inventoryid", "orgid",
                "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty"
            ]

            # Add nested array fields for comprehensive data
            nested_fields = ["invcost", "invbalances", "itemcondition", "invvendor"]
            all_fields = select_fields + nested_fields

            params = {
                "oslc.select": ",".join(all_fields),
                "oslc.where": oslc_filter,
                "oslc.pageSize": "10",
                "lean": "1"
            }

            try:
                self.logger.info(f"🔍 INVENTORY: Checking item '{itemnum}' in MXAPIINVENTORY for site '{site_id}'")

                response = self.token_manager.session.get(
                    api_url,
                    params=params,
                    timeout=(3.05, 15),
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    inventory_items = data.get('member', [])

                    # Process and map inventory data
                    for inv_item in inventory_items:
                        found_itemnum = inv_item.get('itemnum')
                        if found_itemnum:
                            # Process nested inventory data
                            processed_inv_item = self._process_single_inventory_item(inv_item)
                            inventory_data_map[found_itemnum] = processed_inv_item

                    if inventory_items:
                        self.logger.info(f"🔍 INVENTORY: Found item '{itemnum}' in inventory")
                    else:
                        self.logger.info(f"🔍 INVENTORY: Item '{itemnum}' not found in inventory for site '{site_id}'")
                else:
                    self.logger.warning(f"🔍 INVENTORY: Item '{itemnum}' query failed with status {response.status_code}")
                    # Log the response text for debugging
                    try:
                        error_text = response.text[:500]  # First 500 chars
                        self.logger.warning(f"🔍 INVENTORY: Error response: {error_text}")
                    except:
                        pass

            except Exception as e:
                self.logger.error(f"🔍 INVENTORY: Item '{itemnum}' query error: {str(e)}")
                continue

        # Merge MXAPIITEM data with MXAPIINVENTORY data for items that exist in inventory
        for item in mxapiitem_results:
            itemnum = item.get('itemnum')
            if itemnum and itemnum in inventory_data_map:
                # Merge item data with inventory data
                merged_item = item.copy()
                inventory_data = inventory_data_map[itemnum]

                # Add inventory-specific fields
                merged_item.update({
                    'siteid': inventory_data.get('siteid'),
                    'location': inventory_data.get('location'),
                    'curbaltotal': inventory_data.get('curbaltotal'),
                    'avblbalance': inventory_data.get('avblbalance'),
                    'inventory_status': inventory_data.get('status'),
                    'inventoryid': inventory_data.get('inventoryid'),
                    'maxlevel': inventory_data.get('maxlevel'),
                    'minlevel': inventory_data.get('minlevel'),
                    'reorder': inventory_data.get('reorder'),
                    'reservedqty': inventory_data.get('reservedqty'),
                    'source': 'mxapiitem_filtered_inventory'
                })

                # Add cost data if available
                if inventory_data.get('invcost_avgcost') is not None:
                    merged_item['avgcost'] = inventory_data.get('invcost_avgcost')
                if inventory_data.get('invcost_lastcost') is not None:
                    merged_item['lastcost'] = inventory_data.get('invcost_lastcost')
                if inventory_data.get('invcost_stdcost') is not None:
                    merged_item['stdcost'] = inventory_data.get('invcost_stdcost')

                # Add balance data if available
                if inventory_data.get('invbalances_physcnt') is not None:
                    merged_item['physcnt'] = inventory_data.get('invbalances_physcnt')
                if inventory_data.get('invbalances_binnum'):
                    merged_item['binnum'] = inventory_data.get('invbalances_binnum')

                # Add vendor data if available
                if inventory_data.get('invvendor_vendor'):
                    merged_item['vendor'] = inventory_data.get('invvendor_vendor')
                if inventory_data.get('invvendor_manufacturer'):
                    merged_item['manufacturer'] = inventory_data.get('invvendor_manufacturer')

                filtered_items.append(merged_item)

        self.logger.info(f"🔍 INVENTORY: Filtered result: {len(filtered_items)} items exist in inventory for site '{site_id}'")
        return filtered_items

    def _process_single_inventory_item(self, inv_item: Dict) -> Dict:
        """
        Process a single inventory item to extract nested array data.

        Args:
            inv_item (Dict): Raw inventory item from MXAPIINVENTORY

        Returns:
            Dict: Processed inventory item with flattened nested data
        """
        processed_item = inv_item.copy()

        # Process INVCOST array
        if 'invcost' in inv_item and isinstance(inv_item['invcost'], list) and inv_item['invcost']:
            cost_data = inv_item['invcost'][0]
            processed_item['invcost_avgcost'] = cost_data.get('avgcost')
            processed_item['invcost_lastcost'] = cost_data.get('lastcost')
            processed_item['invcost_stdcost'] = cost_data.get('stdcost')

        # Process INVBALANCES array
        if 'invbalances' in inv_item and isinstance(inv_item['invbalances'], list) and inv_item['invbalances']:
            balance_data = inv_item['invbalances'][0]
            processed_item['invbalances_physcnt'] = balance_data.get('physcnt')
            processed_item['invbalances_binnum'] = balance_data.get('binnum')
            processed_item['invbalances_unitcost'] = balance_data.get('unitcost')

        # Process INVVENDOR array
        if 'invvendor' in inv_item and isinstance(inv_item['invvendor'], list) and inv_item['invvendor']:
            vendor_data = inv_item['invvendor'][0]
            processed_item['invvendor_vendor'] = vendor_data.get('vendor')
            processed_item['invvendor_manufacturer'] = vendor_data.get('manufacturer')
            processed_item['invvendor_modelnum'] = vendor_data.get('modelnum')

        return processed_item

    def _sort_by_relevance(self, items: List[Dict], search_term: str) -> List[Dict]:
        """
        Sort items by relevance to search term.
        Exact matches first, then partial matches.

        Args:
            items (List[Dict]): Items to sort
            search_term (str): Original search term

        Returns:
            List[Dict]: Sorted items
        """
        search_term_lower = search_term.lower()

        def relevance_score(item):
            itemnum = item.get('itemnum', '').lower()
            description = item.get('description', '').lower()

            # Exact itemnum match gets highest score
            if itemnum == search_term_lower:
                return 1
            # Exact description match gets second highest
            elif description == search_term_lower:
                return 2
            # Itemnum starts with search term
            elif itemnum.startswith(search_term_lower):
                return 3
            # Description starts with search term
            elif description.startswith(search_term_lower):
                return 4
            # Itemnum contains search term
            elif search_term_lower in itemnum:
                return 5
            # Description contains search term
            elif search_term_lower in description:
                return 6
            # Default score
            else:
                return 7

        return sorted(items, key=relevance_score)

    def _remove_duplicates(self, items: List[Dict]) -> List[Dict]:
        """
        Remove duplicate items based on itemnum.

        Args:
            items (List[Dict]): Items to deduplicate

        Returns:
            List[Dict]: Unique items
        """
        seen_items = set()
        unique_items = []

        for item in items:
            itemnum = item.get('itemnum', '')
            if itemnum and itemnum not in seen_items:
                seen_items.add(itemnum)
                unique_items.append(item)

        return unique_items
