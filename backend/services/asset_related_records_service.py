"""
Asset Related Records Service

Provides unified access to workorders and service requests related to assets.
Follows the same patterns as enhanced_workorder_service.py and asset_management_service.py.
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import hashlib
import os

# Configure logging
logger = logging.getLogger(__name__)

class AssetRelatedRecordsService:
    """Service for fetching workorders and service requests related to assets."""
    
    def __init__(self, token_manager):
        """Initialize the service with token manager."""
        self.token_manager = token_manager
        self.memory_cache = {}
        self.cache_duration = timedelta(minutes=5)  # 5-minute cache
        
        # Disk cache directory
        self.cache_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'cache', 'asset_related_records')
        os.makedirs(self.cache_dir, exist_ok=True)
        
        logger.info("🔧 INIT: AssetRelatedRecordsService initialized")
        logger.info("🔧 INIT: token_manager: ✅ Available" if token_manager else "🔧 INIT: token_manager: ❌ Not Available")
    
    def get_related_records(self, assetnum: str, siteid: str) -> Dict[str, Any]:
        """
        Get both workorders and service requests related to an asset.
        
        Args:
            assetnum: Asset number
            siteid: Site ID
            
        Returns:
            Dictionary containing workorders and service requests
        """
        try:
            logger.info(f"🔍 RELATED: Getting related records for asset {assetnum} in site {siteid}")
            
            # Check cache first
            cache_key = f"{assetnum}_{siteid}"
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"✅ RELATED: Using cached data for {assetnum}")
                return cached_result
            
            # Fetch fresh data
            start_time = time.time()
            
            # Get workorders and service requests in parallel
            workorders = self._get_related_workorders(assetnum, siteid)
            service_requests = self._get_related_service_requests(assetnum, siteid)
            
            result = {
                'assetnum': assetnum,
                'siteid': siteid,
                'workorders': workorders,
                'service_requests': service_requests,
                'summary': {
                    'workorder_count': len(workorders),
                    'service_request_count': len(service_requests),
                    'total_count': len(workorders) + len(service_requests)
                },
                'timestamp': datetime.now().isoformat(),
                'fetch_time': round(time.time() - start_time, 2)
            }
            
            # Cache the result
            self._save_to_cache(cache_key, result)
            
            logger.info(f"✅ RELATED: Found {result['summary']['workorder_count']} workorders and {result['summary']['service_request_count']} service requests for {assetnum} in {result['fetch_time']}s")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ RELATED: Error getting related records for {assetnum}: {e}")
            return {
                'assetnum': assetnum,
                'siteid': siteid,
                'workorders': [],
                'service_requests': [],
                'summary': {'workorder_count': 0, 'service_request_count': 0, 'total_count': 0},
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _get_related_workorders(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Get workorders related to the asset using OSLC relationship queries."""
        try:
            logger.info(f"🔧 WORKORDERS: Fetching workorders for asset {assetnum}")
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiasset"
            
            # Try different relationship names based on testing results
            relationship_names = ["workorder", "openwo", "allwo"]
            
            for rel_name in relationship_names:
                try:
                    params = {
                        "oslc.select": f"assetnum,siteid,rel.{rel_name}{{wonum,description,status,priority,worktype,reportdate,schedstart,assetnum,siteid}}",
                        "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                        "oslc.pageSize": "1",
                        "lean": "1"
                    }
                    
                    response = self.token_manager.session.get(
                        api_url,
                        params=params,
                        timeout=(5.0, 30),
                        headers={"Accept": "application/json"}
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if 'member' in data and len(data['member']) > 0:
                            asset_data = data['member'][0]
                            if rel_name in asset_data and isinstance(asset_data[rel_name], list):
                                workorders = asset_data[rel_name]
                                if workorders:
                                    logger.info(f"✅ WORKORDERS: Found {len(workorders)} workorders using rel.{rel_name}")
                                    return self._process_workorders(workorders)
                    
                except Exception as e:
                    logger.warning(f"⚠️ WORKORDERS: Failed to fetch using rel.{rel_name}: {e}")
                    continue
            
            # Fallback: Try direct MXAPIWODETAIL query
            logger.info("🔧 WORKORDERS: Trying fallback direct query")
            return self._get_workorders_direct(assetnum, siteid)
            
        except Exception as e:
            logger.error(f"❌ WORKORDERS: Error fetching workorders: {e}")
            return []
    
    def _get_workorders_direct(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fallback method to get workorders directly from MXAPIWODETAIL."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            params = {
                "oslc.select": "wonum,description,status,priority,worktype,reportdate,schedstart,assetnum,siteid",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}" and historyflag=0',
                "oslc.pageSize": "50",
                "lean": "1"
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'member' in data:
                    workorders = data['member']
                    logger.info(f"✅ WORKORDERS: Found {len(workorders)} workorders via direct query")
                    return self._process_workorders(workorders)
            
            logger.warning(f"⚠️ WORKORDERS: Direct query returned status {response.status_code}")
            return []
            
        except Exception as e:
            logger.error(f"❌ WORKORDERS: Error in direct query: {e}")
            return []
    
    def _get_related_service_requests(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Get service requests related to the asset."""
        try:
            logger.info(f"🎫 SERVICE REQUESTS: Fetching service requests for asset {assetnum}")
            
            # Try direct MXAPISR query first (most reliable based on testing)
            service_requests = self._get_service_requests_direct(assetnum, siteid)
            if service_requests:
                return service_requests
            
            # Fallback: Try relationship queries
            return self._get_service_requests_relationship(assetnum, siteid)
            
        except Exception as e:
            logger.error(f"❌ SERVICE REQUESTS: Error fetching service requests: {e}")
            return []
    
    def _get_service_requests_direct(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Get service requests directly from MXAPISR."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapisr"
            
            params = {
                "oslc.select": "ticketid,description,status,priority,reportdate,reportedby,assetnum,siteid",
                "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                "oslc.pageSize": "50",
                "lean": "1"
            }
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'member' in data:
                    service_requests = data['member']
                    logger.info(f"✅ SERVICE REQUESTS: Found {len(service_requests)} service requests via direct query")
                    return self._process_service_requests(service_requests)
            
            logger.warning(f"⚠️ SERVICE REQUESTS: Direct query returned status {response.status_code}")
            return []
            
        except Exception as e:
            logger.error(f"❌ SERVICE REQUESTS: Error in direct query: {e}")
            return []
    
    def _get_service_requests_relationship(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Get service requests using asset relationship queries."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiasset"
            
            relationship_names = ["servicerequest", "sr", "ticket"]
            
            for rel_name in relationship_names:
                try:
                    params = {
                        "oslc.select": f"assetnum,siteid,rel.{rel_name}{{ticketid,description,status,priority,reportdate,reportedby}}",
                        "oslc.where": f'assetnum="{assetnum}" and siteid="{siteid}"',
                        "oslc.pageSize": "1",
                        "lean": "1"
                    }
                    
                    response = self.token_manager.session.get(
                        api_url,
                        params=params,
                        timeout=(5.0, 30),
                        headers={"Accept": "application/json"}
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if 'member' in data and len(data['member']) > 0:
                            asset_data = data['member'][0]
                            if rel_name in asset_data and isinstance(asset_data[rel_name], list):
                                service_requests = asset_data[rel_name]
                                if service_requests:
                                    logger.info(f"✅ SERVICE REQUESTS: Found {len(service_requests)} service requests using rel.{rel_name}")
                                    return self._process_service_requests(service_requests)
                    
                except Exception as e:
                    logger.warning(f"⚠️ SERVICE REQUESTS: Failed to fetch using rel.{rel_name}: {e}")
                    continue
            
            return []
            
        except Exception as e:
            logger.error(f"❌ SERVICE REQUESTS: Error in relationship query: {e}")
            return []

    def _process_workorders(self, workorders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process and standardize workorder data."""
        processed = []
        for wo in workorders:
            try:
                processed_wo = {
                    'wonum': wo.get('wonum', ''),
                    'description': wo.get('description', ''),
                    'status': wo.get('status', ''),
                    'priority': wo.get('priority', ''),
                    'worktype': wo.get('worktype', ''),
                    'reportdate': wo.get('reportdate', ''),
                    'schedstart': wo.get('schedstart', ''),
                    'assetnum': wo.get('assetnum', ''),
                    'siteid': wo.get('siteid', ''),
                    'type': 'workorder'
                }
                processed.append(processed_wo)
            except Exception as e:
                logger.warning(f"⚠️ WORKORDERS: Error processing workorder {wo}: {e}")
                continue

        return processed

    def _process_service_requests(self, service_requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process and standardize service request data."""
        processed = []
        for sr in service_requests:
            try:
                processed_sr = {
                    'ticketid': sr.get('ticketid', ''),
                    'description': sr.get('description', ''),
                    'status': sr.get('status', ''),
                    'priority': sr.get('priority', ''),
                    'reportdate': sr.get('reportdate', ''),
                    'reportedby': sr.get('reportedby', ''),
                    'assetnum': sr.get('assetnum', ''),
                    'siteid': sr.get('siteid', ''),
                    'type': 'service_request'
                }
                processed.append(processed_sr)
            except Exception as e:
                logger.warning(f"⚠️ SERVICE REQUESTS: Error processing service request {sr}: {e}")
                continue

        return processed

    def _get_cache_key_hash(self, cache_key: str) -> str:
        """Generate a hash for the cache key to use as filename."""
        return hashlib.md5(cache_key.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get data from memory cache first, then disk cache."""
        # Check memory cache
        if cache_key in self.memory_cache:
            cached_data, timestamp = self.memory_cache[cache_key]
            if datetime.now() - timestamp < self.cache_duration:
                logger.info(f"✅ CACHE: Using memory cache for {cache_key}")
                return cached_data
            else:
                # Remove expired entry
                del self.memory_cache[cache_key]

        # Check disk cache
        try:
            cache_file = os.path.join(self.cache_dir, f"{self._get_cache_key_hash(cache_key)}.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cached_data = json.load(f)

                # Check if disk cache is still valid
                cache_time = datetime.fromisoformat(cached_data.get('cache_timestamp', ''))
                if datetime.now() - cache_time < self.cache_duration:
                    # Load into memory cache
                    self.memory_cache[cache_key] = (cached_data, cache_time)
                    logger.info(f"✅ CACHE: Using disk cache for {cache_key}")
                    return cached_data
                else:
                    # Remove expired disk cache
                    os.remove(cache_file)
        except Exception as e:
            logger.warning(f"⚠️ CACHE: Error reading disk cache for {cache_key}: {e}")

        return None

    def _save_to_cache(self, cache_key: str, data: Dict[str, Any]) -> None:
        """Save data to both memory and disk cache."""
        try:
            timestamp = datetime.now()

            # Save to memory cache
            self.memory_cache[cache_key] = (data, timestamp)

            # Save to disk cache
            cache_data = data.copy()
            cache_data['cache_timestamp'] = timestamp.isoformat()

            cache_file = os.path.join(self.cache_dir, f"{self._get_cache_key_hash(cache_key)}.json")
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.info(f"✅ CACHE: Saved data for {cache_key}")

        except Exception as e:
            logger.warning(f"⚠️ CACHE: Error saving cache for {cache_key}: {e}")

    def clear_cache(self) -> None:
        """Clear both memory and disk cache."""
        try:
            # Clear memory cache
            self.memory_cache.clear()

            # Clear disk cache
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    os.remove(os.path.join(self.cache_dir, filename))

            logger.info("✅ CACHE: Cleared all caches (memory + disk)")

        except Exception as e:
            logger.error(f"❌ CACHE: Error clearing cache: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            memory_count = len(self.memory_cache)

            disk_count = 0
            disk_files = []
            if os.path.exists(self.cache_dir):
                disk_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
                disk_count = len(disk_files)

            return {
                'memory_cache_entries': memory_count,
                'disk_cache_entries': disk_count,
                'cache_duration_minutes': self.cache_duration.total_seconds() / 60,
                'cache_directory': self.cache_dir
            }

        except Exception as e:
            logger.error(f"❌ CACHE: Error getting cache stats: {e}")
            return {'error': str(e)}
