"""
QR Code Generation Service for Inventory Management

This service handles QR code generation for inventory records, encoding essential
inventory data for warehouse operations like cycle counting, adjustments, and transfers.
"""

import qrcode
import json
import base64
import io
import logging
from PIL import Image
from datetime import datetime

class QRCodeService:
    """Service for generating and processing QR codes for inventory management"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def generate_inventory_qr_code(self, inventory_data, balance_record=None):
        """
        Generate QR code for inventory record or specific balance record

        Args:
            inventory_data (dict): Inventory record containing required fields
            balance_record (dict, optional): Specific balance record for balance-level QR codes

        Returns:
            dict: QR code data including base64 image and encoded data
        """
        try:
            # Extract required fields for QR code
            qr_data = self._extract_qr_data(inventory_data, balance_record)

            # Validate required fields
            if not self._validate_qr_data(qr_data):
                raise ValueError("Missing required inventory data for QR code generation")

            # Generate QR code
            qr_code_image = self._create_qr_code(qr_data)

            # Convert to base64 for web display
            base64_image = self._image_to_base64(qr_code_image)

            # Create response
            result = {
                'success': True,
                'qr_data': qr_data,
                'qr_image_base64': base64_image,
                'generated_at': datetime.now().isoformat(),
                'data_encoded': json.dumps(qr_data, separators=(',', ':'))
            }

            # Enhanced logging for different QR types
            qr_type = qr_data.get('qr_type', 'inventory_level')
            item_info = qr_data.get('itemnum', 'UNKNOWN')
            if qr_type == 'balance_specific':
                bin_info = qr_data.get('binnum', 'NO_BIN')
                lot_info = qr_data.get('lotnum', 'NO_LOT')
                balance_info = qr_data.get('curbal', 0)
                self.logger.info(f"✅ QR CODE: Generated balance-specific QR for item {item_info}, bin {bin_info}, lot {lot_info}, balance {balance_info}")
            else:
                self.logger.info(f"✅ QR CODE: Generated inventory-level QR for item {item_info}")

            return result

        except Exception as e:
            self.logger.error(f"❌ QR CODE: Generation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'qr_data': None,
                'qr_image_base64': None
            }
    
    def _extract_qr_data(self, inventory_data, balance_record=None):
        """Extract essential data for QR code encoding

        Args:
            inventory_data: Complete inventory data
            balance_record: Specific balance record for balance-level QR codes (optional)
        """
        # Debug logging to see what data we're receiving
        self.logger.info(f"🔍 QR DATA EXTRACTION: Received inventory data keys: {list(inventory_data.keys())}")
        if balance_record:
            self.logger.info(f"🔍 QR DATA EXTRACTION: Balance-specific QR for balance ID: {balance_record.get('invbalancesid')}")

        # Handle different data structures from comprehensive details API
        self.logger.info(f"🔍 QR DATA EXTRACTION: Data structure analysis:")
        self.logger.info(f"🔍 QR DATA EXTRACTION: Top-level keys: {list(inventory_data.keys())}")
        if 'details' in inventory_data:
            details = inventory_data.get('details', {})
            self.logger.info(f"🔍 QR DATA EXTRACTION: Details keys: {list(details.keys()) if isinstance(details, dict) else 'Not a dict'}")

        if 'basic_info' in inventory_data:
            # Full comprehensive details structure
            basic_info = inventory_data.get('basic_info', {})
            balance_details = inventory_data.get('balance_details', {})
            inventory_section = inventory_data.get('inventory', {})
            current_balances = inventory_section.get('current_balances', {})
            units_ordering = inventory_section.get('units_ordering', {})
            raw_data = inventory_data.get('raw_data', {})
            inventory_raw = raw_data.get('inventory_data', {})

            # Extract required fields from the correct sections
            itemnum = basic_info.get('itemnum', '')
            description = basic_info.get('description', '')
            siteid = basic_info.get('siteid', '')
            location = basic_info.get('location', '') or basic_info.get('store_location', '')

            # Get inventory ID and issue unit
            inventoryid = inventory_raw.get('inventoryid', '')
            issueunit = units_ordering.get('issueunit', '') or inventory_raw.get('issueunit', 'EA')
        else:
            # Simplified structure (from some API calls)
            itemnum = inventory_data.get('itemnum', '')
            description = inventory_data.get('description', '')
            siteid = inventory_data.get('siteid', '')
            location = inventory_data.get('location', '') or inventory_data.get('storeloc', '')
            inventoryid = inventory_data.get('inventoryid', '')
            issueunit = inventory_data.get('issueunit', 'EA')

            # Try to extract from details section if available
            details = inventory_data.get('details', {})
            if details:
                # Extract from nested details structure
                itemnum = itemnum or details.get('itemnum', '')
                description = description or details.get('description', '')
                siteid = siteid or details.get('siteid', '')
                location = location or details.get('location', '') or details.get('storeloc', '')
                inventoryid = inventoryid or details.get('inventoryid', '')
                issueunit = issueunit or details.get('issueunit', 'EA')

                # Try to extract from basic_info within details
                basic_info = details.get('basic_info', {})
                if basic_info:
                    itemnum = itemnum or basic_info.get('itemnum', '')
                    description = description or basic_info.get('description', '')
                    siteid = siteid or basic_info.get('siteid', '')
                    location = location or basic_info.get('location', '') or basic_info.get('store_location', '')

                # Try to extract from raw_data within details
                raw_data = details.get('raw_data', {})
                if raw_data:
                    inventory_raw = raw_data.get('inventory_data', {})
                    if inventory_raw:
                        inventoryid = inventoryid or inventory_raw.get('inventoryid', '')
                        issueunit = issueunit or inventory_raw.get('issueunit', 'EA')

        # Create base QR data structure
        qr_data = {
            'itemnum': itemnum,
            'description': description,
            'storeloc': location,
            'siteid': siteid,
            'inventoryid': inventoryid,
            'issueunit': issueunit,
            'generated_timestamp': datetime.now().isoformat()
        }

        if balance_record:
            # Balance-specific QR code with comprehensive invbalances fields
            # Calculate shelf life and expiration date
            shelf_life_days = self._calculate_shelf_life_days(balance_record)
            expiration_date = self._calculate_expiration_date(balance_record)

            # Get current balance for consistency
            current_balance = balance_record.get('curbal', 0)

            qr_data.update({
                'qr_type': 'balance_specific',
                'invbalancesid': balance_record.get('invbalancesid'),

                # Core balance fields with default sort handling
                'binnum': balance_record.get('binnum', ''),  # Bin Default Sort
                'lotnum': balance_record.get('lotnum', ''),  # Lot Default Sort
                'conditioncode': balance_record.get('conditioncode', ''),  # Condition Code Default Sort
                'curbal': current_balance,  # Current Balance Sort Ascending
                'currentbalance': current_balance,  # For compatibility with QR scanner display
                'stagedcurbal': balance_record.get('stagedcurbal', 0),  # Staged Balance Default Sort
                'stagingbin': balance_record.get('stagingbin', False),  # Staging Bin? Default Sort
                'physcnt': balance_record.get('physcnt', 0),  # Physical Count Default Sort
                'physcntdate': balance_record.get('physcntdate', ''),  # Physical Count Date Default Sort
                'reconciled': balance_record.get('reconciled', False),  # Reconciled? Default Sort

                # Enhanced fields for comprehensive tracking
                'shelf_life_days': shelf_life_days,  # Shelf Life (Days) Default Sort
                'expiration_date': expiration_date,  # Expiration Date Default Sort

                # Additional balance tracking fields
                'expiredqty': balance_record.get('expiredqty', 0),
                'reservedqty': balance_record.get('reservedqty', 0),
                'hardreservedqty': balance_record.get('hardreservedqty', 0),
                'softreservedqty': balance_record.get('softreservedqty', 0),
                'unitcost': balance_record.get('unitcost', 0),
                'location': balance_record.get('location', ''),

                # Additional fields for QR scanner compatibility
                'controlacc': balance_record.get('controlacc', ''),
                'shrinkageacc': balance_record.get('shrinkageacc', ''),

                # Metadata for QR code tracking
                'balance_qr_version': '2.0',  # Version for future compatibility
                'includes_shelf_life': shelf_life_days is not None,
                'includes_expiration': expiration_date is not None
            })

            bin_info = balance_record.get('binnum', 'NO_BIN')
            lot_info = balance_record.get('lotnum', 'NO_LOT')
            balance_info = current_balance  # Use the current_balance variable we set above
            shelf_info = f", shelf life: {shelf_life_days} days" if shelf_life_days else ""
            exp_info = f", expires: {expiration_date}" if expiration_date else ""

            self.logger.info(f"🔍 QR DATA EXTRACTION: Enhanced balance-specific QR for bin '{bin_info}', lot '{lot_info}', balance {balance_info}{shelf_info}{exp_info}")
        else:
            # Inventory-level QR code (original behavior)
            # Get current balance from multiple possible sources
            if 'basic_info' in inventory_data:
                # Full structure
                current_balance = (current_balances.get('curbaltotal') or
                                  current_balances.get('avblbalance') or
                                  balance_details.get('curbal') or
                                  inventory_raw.get('curbaltotal') or 0)

                # Get condition code and bin number
                conditioncode = balance_details.get('conditioncode', '') or basic_info.get('conditioncode', '')
                binnum = balance_details.get('binnum', '') or basic_info.get('binnum', '')
            else:
                # Simplified structure
                current_balance = inventory_data.get('currentbalance', 0)
                conditioncode = inventory_data.get('conditioncode', '')
                binnum = inventory_data.get('binnum', '')

                # Try to extract from details section if available
                details = inventory_data.get('details', {})
                if details:
                    current_balance = current_balance or details.get('currentbalance', 0)
                    conditioncode = conditioncode or details.get('conditioncode', '')
                    binnum = binnum or details.get('binnum', '')

            qr_data.update({
                'qr_type': 'inventory_level',
                'currentbalance': current_balance,
                'conditioncode': conditioncode,
                'binnum': binnum
            })
            self.logger.info(f"🔍 QR DATA EXTRACTION: Inventory-level QR for total balance {current_balance}")

        self.logger.info(f"🔍 QR DATA EXTRACTION: Extracted QR data: {qr_data}")
        return qr_data
    
    def _validate_qr_data(self, qr_data):
        """Validate that required fields are present"""
        required_fields = ['itemnum']  # Only itemnum is truly required
        is_valid = all(qr_data.get(field) for field in required_fields)

        if not is_valid:
            self.logger.error(f"❌ QR VALIDATION: Missing required fields. Data: {qr_data}")
        else:
            self.logger.info(f"✅ QR VALIDATION: Data is valid for item {qr_data.get('itemnum')}")

        return is_valid
    
    def _create_qr_code(self, qr_data):
        """Create QR code image from data"""
        # Convert data to JSON string
        qr_content = json.dumps(qr_data, separators=(',', ':'))
        
        # Create QR code instance
        qr = qrcode.QRCode(
            version=1,  # Controls size (1 is smallest)
            error_correction=qrcode.constants.ERROR_CORRECT_M,  # Medium error correction
            box_size=10,  # Size of each box in pixels
            border=4,  # Border size in boxes
        )
        
        # Add data and make QR code
        qr.add_data(qr_content)
        qr.make(fit=True)
        
        # Create image
        qr_image = qr.make_image(fill_color="black", back_color="white")
        
        return qr_image
    
    def _image_to_base64(self, image):
        """Convert PIL image to base64 string for web display"""
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        buffer.seek(0)
        
        # Encode to base64
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return f"data:image/png;base64,{image_base64}"
    
    def decode_qr_data(self, qr_content):
        """
        Decode QR code content back to inventory data
        
        Args:
            qr_content (str): JSON string from QR code
            
        Returns:
            dict: Decoded inventory data or error
        """
        try:
            # Parse JSON data
            qr_data = json.loads(qr_content)
            
            # Validate decoded data
            if not self._validate_qr_data(qr_data):
                raise ValueError("Invalid QR code data structure")
            
            self.logger.info(f"✅ QR CODE: Decoded for item {qr_data.get('itemnum', 'UNKNOWN')}")
            
            return {
                'success': True,
                'inventory_data': qr_data,
                'decoded_at': datetime.now().isoformat()
            }
            
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ QR CODE: Invalid JSON in QR code: {str(e)}")
            return {
                'success': False,
                'error': 'Invalid QR code format - not valid JSON',
                'inventory_data': None
            }
        except Exception as e:
            self.logger.error(f"❌ QR CODE: Decode failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'inventory_data': None
            }
    
    def generate_printable_qr_label(self, inventory_data, label_size=(400, 300)):
        """
        Generate a printable QR code label with inventory information
        
        Args:
            inventory_data (dict): Inventory record data
            label_size (tuple): Label dimensions in pixels
            
        Returns:
            dict: Label image data and metadata
        """
        try:
            # Generate QR code
            qr_result = self.generate_inventory_qr_code(inventory_data)
            if not qr_result['success']:
                return qr_result
            
            # Create label with QR code and text
            label_image = self._create_printable_label(
                qr_result['qr_data'], 
                qr_result['qr_image_base64'],
                label_size
            )
            
            # Convert label to base64
            label_base64 = self._image_to_base64(label_image)
            
            return {
                'success': True,
                'label_image_base64': label_base64,
                'qr_data': qr_result['qr_data'],
                'label_size': label_size,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ QR LABEL: Generation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'label_image_base64': None
            }
    
    def _create_printable_label(self, qr_data, qr_image_base64, label_size):
        """Create a printable label with QR code and inventory information"""
        from PIL import Image, ImageDraw, ImageFont
        
        # Create label background
        label = Image.new('RGB', label_size, 'white')
        draw = ImageDraw.Draw(label)
        
        # Decode QR code image
        qr_image_data = qr_image_base64.split(',')[1]  # Remove data:image/png;base64, prefix
        qr_image_bytes = base64.b64decode(qr_image_data)
        qr_image = Image.open(io.BytesIO(qr_image_bytes))
        
        # Resize QR code to fit label
        qr_size = min(label_size[0] // 2, label_size[1] - 60)  # Leave space for text
        qr_image = qr_image.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
        
        # Position QR code on left side
        qr_x = 10
        qr_y = (label_size[1] - qr_size) // 2
        label.paste(qr_image, (qr_x, qr_y))
        
        # Add text information on right side
        text_x = qr_x + qr_size + 20
        text_y = 20
        
        try:
            # Try to load a system font
            font_large = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 16)
            font_small = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 12)
        except:
            # Fallback to default font
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Draw inventory information
        # Handle both currentbalance (inventory-level) and curbal (balance-specific) QR codes
        current_balance = qr_data.get('currentbalance') or qr_data.get('curbal', 0)
        qr_type = qr_data.get('qr_type', 'inventory_level')

        lines = [
            f"Item: {qr_data.get('itemnum', 'N/A')}",
            f"Site: {qr_data.get('siteid', 'N/A')}",
            f"Location: {qr_data.get('storeloc', 'N/A')}",
            f"Bin: {qr_data.get('binnum', 'N/A')}",
            f"Balance: {current_balance}",
            f"Condition: {qr_data.get('conditioncode', 'N/A')}",
            f"Type: {'Balance-Specific' if qr_type == 'balance_specific' else 'Inventory-Level'}",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        ]
        
        for i, line in enumerate(lines):
            font = font_large if i == 0 else font_small
            draw.text((text_x, text_y + i * 25), line, fill='black', font=font)
        
        # Add border
        draw.rectangle([(0, 0), (label_size[0]-1, label_size[1]-1)], outline='black', width=2)
        
        return label

    def _calculate_shelf_life_days(self, balance_record):
        """
        Calculate shelf life days for balance record

        Args:
            balance_record (dict): Balance record data

        Returns:
            int or None: Shelf life in days if available
        """
        # Try multiple possible field names for shelf life
        shelf_life_fields = [
            'shelflife', 'shelflifedays', 'shelf_life_days',
            'shelf_life', 'shelfLifeDays', 'shelfLifeInDays'
        ]

        for field in shelf_life_fields:
            shelf_life = balance_record.get(field)
            if shelf_life is not None:
                try:
                    return int(float(shelf_life))
                except (ValueError, TypeError):
                    continue

        # If no direct shelf life field, try to calculate from expiration date and lot date
        expiration_date = balance_record.get('expirationdate') or balance_record.get('expiration_date')
        lot_date = balance_record.get('lotdate') or balance_record.get('lot_date') or balance_record.get('physcntdate')

        if expiration_date and lot_date:
            try:
                from datetime import datetime
                exp_dt = datetime.fromisoformat(expiration_date.replace('Z', '+00:00'))
                lot_dt = datetime.fromisoformat(lot_date.replace('Z', '+00:00'))
                shelf_life_days = (exp_dt - lot_dt).days
                return shelf_life_days if shelf_life_days > 0 else None
            except (ValueError, AttributeError):
                pass

        return None

    def _calculate_expiration_date(self, balance_record):
        """
        Calculate expiration date for balance record

        Args:
            balance_record (dict): Balance record data

        Returns:
            str or None: Expiration date in ISO format if available
        """
        # Try multiple possible field names for expiration date
        expiration_fields = [
            'expirationdate', 'expiration_date', 'expiry_date',
            'expiry', 'expirydate', 'expire_date'
        ]

        for field in expiration_fields:
            exp_date = balance_record.get(field)
            if exp_date:
                # Ensure consistent date format
                try:
                    from datetime import datetime
                    if isinstance(exp_date, str):
                        # Try to parse and reformat to ensure consistency
                        dt = datetime.fromisoformat(exp_date.replace('Z', '+00:00'))
                        return dt.isoformat()
                    return str(exp_date)
                except (ValueError, AttributeError):
                    # Return as-is if parsing fails
                    return str(exp_date)

        # If no direct expiration date, try to calculate from lot date + shelf life
        lot_date = balance_record.get('lotdate') or balance_record.get('lot_date') or balance_record.get('physcntdate')
        shelf_life = self._calculate_shelf_life_days(balance_record)

        if lot_date and shelf_life:
            try:
                from datetime import datetime, timedelta
                lot_dt = datetime.fromisoformat(lot_date.replace('Z', '+00:00'))
                exp_dt = lot_dt + timedelta(days=shelf_life)
                return exp_dt.isoformat()
            except (ValueError, AttributeError):
                pass

        return None
