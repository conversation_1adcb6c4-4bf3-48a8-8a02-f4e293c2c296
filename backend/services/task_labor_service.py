#!/usr/bin/env python3
"""
Task Labor Service for Work Orders
Handles fetching labor records for tasks using MXAPIWODETAIL/labtrans endpoint
Following the exact same pattern as TaskPlannedMaterialsService
"""

import logging
import time
import json
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class TaskLaborService:
    """
    Service for fetching labor records for work order tasks.
    
    This service handles:
    - Fetching labor records using MXAPIWODETAIL/labtrans
    - Site-aware labor filtering
    - Intelligent caching (5-minute timeout)
    - Status-based access control
    """
    
    def __init__(self, token_manager):
        """
        Initialize the service with token manager.
        
        Args:
            token_manager: Authenticated token manager instance
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(f'{__name__}.{self.__class__.__name__}')
        
        # Cache for labor data (5-minute timeout)
        self._labor_cache = {}
        self._cache_timeout = 300  # 5 minutes
        
        # Status-based access control
        self._allowed_statuses = ['APPR', 'ASSIGN', 'WMATL', 'INPRG', 'READY', 'COMP']
        
        self.logger.info("🔧 TASK LABOR SERVICE: Initialized")
    
    def get_task_labor(self, task_wonum: str, site_id: str = None, 
                      task_status: str = None, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get labor records for a specific task.
        
        Args:
            task_wonum: Task work order number
            site_id: Site ID for filtering (optional)
            task_status: Task status for access control
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary with success status, labor records, and metadata
        """
        try:
            # Check if labor should be shown for this status
            if task_status and task_status not in self._allowed_statuses:
                return {
                    'success': True,
                    'show_labor': False,
                    'message': f'Labor records not available for status: {task_status}',
                    'labor': [],
                    'metadata': {'status_restricted': True}
                }
            
            # Check cache first
            cache_key = f"{task_wonum}_{site_id or 'UNKNOWN'}"
            if use_cache and self._is_cache_valid(cache_key):
                self.logger.info(f"🔧 TASK LABOR: Using cached labor for {task_wonum}")
                cached_data = self._labor_cache[cache_key]['data']
                return {
                    'success': True,
                    'show_labor': True,
                    'labor': cached_data,
                    'metadata': {'cached': True, 'count': len(cached_data)}
                }
            
            # Fetch labor records
            labor_records = self._fetch_task_labor_records(task_wonum, site_id)
            
            # Cache the results
            self._labor_cache[cache_key] = {
                'data': labor_records,
                'timestamp': time.time()
            }
            
            self.logger.info(f"🔧 TASK LABOR: Found {len(labor_records)} labor records for {task_wonum}")
            
            return {
                'success': True,
                'show_labor': True,
                'labor': labor_records,
                'metadata': {'cached': False, 'count': len(labor_records)}
            }
            
        except Exception as e:
            self.logger.error(f"🔧 TASK LABOR: Error getting labor for {task_wonum}: {str(e)}")
            return {
                'success': False,
                'show_labor': False,
                'error': str(e),
                'labor': [],
                'metadata': {'error': True}
            }
    
    def get_workorder_site_id(self, task_wonum: str) -> str:
        """
        Get the site ID for a specific work order/task.

        Args:
            task_wonum: Task work order number

        Returns:
            Site ID of the work order, or 'UNKNOWN' if not found
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Query without site restriction to find the work order in any site
            oslc_filter = f'wonum="{task_wonum}"'

            params = {
                "oslc.select": "wonum,siteid",
                "oslc.where": oslc_filter,
                "oslc.pageSize": "1",
                "lean": "1"
            }

            self.logger.info(f"🔧 TASK LABOR: Getting site ID for work order {task_wonum}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"}
            )

            if response.status_code != 200:
                self.logger.error(f"🔧 TASK LABOR: Failed to get work order details: {response.status_code}")
                return 'UNKNOWN'

            data = response.json()
            if not data.get('member'):
                self.logger.warning(f"🔧 TASK LABOR: No work order found for {task_wonum}")
                return 'UNKNOWN'

            work_order = data['member'][0]
            site_id = work_order.get('siteid', 'UNKNOWN')

            self.logger.info(f"🔧 TASK LABOR: Found work order {task_wonum} in site {site_id}")
            return site_id

        except Exception as e:
            self.logger.error(f"🔧 TASK LABOR: Error getting site ID for {task_wonum}: {str(e)}")
            return 'UNKNOWN'

    def _fetch_task_labor_records(self, task_wonum: str, site_id: str = None) -> List[Dict]:
        """
        Fetch labor records for a task using MXAPIWODETAIL/labtrans endpoint.

        Args:
            task_wonum: Task work order number
            site_id: Site ID for filtering

        Returns:
            List of labor record dictionaries
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiwodetail"

        # Query labtrans table using the collection reference approach
        # This method fetches the labtrans_collectionref from the work order and then fetches labor from that collection

        # First, get the work order to find the labtrans_collectionref
        oslc_filter = f'wonum="{task_wonum}"'
        if site_id and site_id != "UNKNOWN":
            oslc_filter += f' and siteid="{site_id}"'

        params = {
            "oslc.select": "wonum,siteid,labtrans_collectionref",  # Get the collection reference
            "oslc.where": oslc_filter,
            "oslc.pageSize": "1",
            "lean": "1"
        }

        self.logger.info(f"🔧 TASK LABOR: Getting work order details for {task_wonum}")
        self.logger.info(f"🔧 TASK LABOR: API URL: {api_url}")
        self.logger.info(f"🔧 TASK LABOR: Filter: {oslc_filter}")

        response = self.token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"}
        )

        if response.status_code != 200:
            self.logger.error(f"🔧 TASK LABOR: Failed to get work order details: {response.status_code}")
            raise Exception(f"Failed to get work order details: {response.status_code}")

        data = response.json()
        if not data.get('member'):
            self.logger.warning(f"🔧 TASK LABOR: No work order found for {task_wonum}")
            return []

        work_order = data['member'][0]
        labtrans_ref = work_order.get('labtrans_collectionref')

        if not labtrans_ref:
            self.logger.info(f"🔧 TASK LABOR: No labtrans collection reference for {task_wonum}")
            return []
        
        # Now fetch the labor records from the collection reference
        self.logger.info(f"🔧 TASK LABOR: Fetching labor from collection: {labtrans_ref}")
        
        # Select comprehensive labor fields including REGULARHRS
        labor_select_fields = [
            "laborcode", "craft", "skilllevel", "laborhrs", "regularhrs", 
            "premiumpayhours", "startdate", "finishdate", "labtransid",
            "taskid", "vendor", "contractnum", "linecost", "rate"
        ]
        
        labor_params = {
            "oslc.select": ",".join(labor_select_fields),
            "oslc.pageSize": "100",  # Get up to 100 labor records
            "lean": "1"
        }
        
        labor_response = self.token_manager.session.get(
            labtrans_ref,
            params=labor_params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"}
        )
        
        if labor_response.status_code != 200:
            self.logger.error(f"🔧 TASK LABOR: Failed to fetch labor records: {labor_response.status_code}")
            raise Exception(f"Failed to fetch labor records: {labor_response.status_code}")
        
        labor_data = labor_response.json()
        labor_records = labor_data.get('member', [])
        
        self.logger.info(f"🔧 TASK LABOR: Retrieved {len(labor_records)} labor records")
        
        # Process and enhance labor records
        processed_records = []
        for record in labor_records:
            processed_record = self._process_labor_record(record)
            processed_records.append(processed_record)
        
        return processed_records
    
    def _process_labor_record(self, record: Dict) -> Dict:
        """
        Process and enhance a labor record.
        
        Args:
            record: Raw labor record from API
            
        Returns:
            Processed labor record
        """
        # Ensure numeric fields are properly formatted
        numeric_fields = ['laborhrs', 'regularhrs', 'premiumpayhours', 'linecost', 'rate', 'taskid']
        for field in numeric_fields:
            if field in record and record[field] is not None:
                try:
                    record[field] = float(record[field])
                except (ValueError, TypeError):
                    record[field] = 0.0
        
        # Format dates
        date_fields = ['startdate', 'finishdate']
        for field in date_fields:
            if field in record and record[field]:
                # Keep the original date format from Maximo
                pass
        
        return record
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self._labor_cache:
            return False
        
        cache_age = time.time() - self._labor_cache[cache_key]['timestamp']
        return cache_age < self._cache_timeout
    
    def clear_cache(self) -> Dict[str, Any]:
        """Clear the labor cache."""
        cache_size = len(self._labor_cache)
        self._labor_cache.clear()
        self.logger.info(f"🔧 TASK LABOR: Cleared cache ({cache_size} entries)")
        
        return {
            'success': True,
            'message': f'Cleared {cache_size} cached labor entries',
            'cleared_count': cache_size
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_entries = len(self._labor_cache)
        valid_entries = sum(1 for key in self._labor_cache.keys() if self._is_cache_valid(key))

        return {
            'success': True,
            'total_entries': total_entries,
            'valid_entries': valid_entries,
            'expired_entries': total_entries - valid_entries,
            'cache_timeout_seconds': self._cache_timeout
        }

    def check_workorder_labor_hours_availability(self, parent_wonum: str, site_id: str) -> Dict[str, Any]:
        """
        Check if a parent work order has any labor hours across all its tasks.

        This queries mxapiwodetail/labtrans using refwo field to find labor records
        for each task under the parent work order.

        Args:
            parent_wonum: Parent work order number
            site_id: Site ID for the work order

        Returns:
            Dict with availability info: {
                'has_labor_hours': bool,
                'total_labor_records': int,
                'total_regular_hours': float,
                'tasks_with_labor': int,
                'cache_hit': bool
            }
        """
        cache_key = f"wo_labor_v2_{parent_wonum}_{site_id}"  # v2 to force cache refresh

        # Check cache first
        if self._is_cache_valid(cache_key):
            self.logger.info(f"👷 WO LABOR: Using cached availability for WO {parent_wonum}")
            cached_data = self._labor_cache[cache_key]['data']
            cached_data['cache_hit'] = True
            return cached_data

        try:
            self.logger.info(f"👷 WO LABOR: Checking labor hours availability for parent WO {parent_wonum}")

            # Step 1: Get all tasks for this parent work order
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Query for all tasks under this parent work order
            oslc_filter = f'parent="{parent_wonum}" and istask=1'
            if site_id and site_id != "UNKNOWN":
                oslc_filter += f' and siteid="{site_id}"'

            params = {
                "oslc.select": "wonum,labtrans_collectionref",  # Only get what we need for performance
                "oslc.where": oslc_filter,
                "oslc.pageSize": "100",  # Get up to 100 tasks
                "lean": "1"
            }

            self.logger.info(f"👷 WO LABOR: Fetching tasks for parent {parent_wonum}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"},
                allow_redirects=True
            )

            if response.status_code != 200:
                self.logger.error(f"👷 WO LABOR: API call failed with status {response.status_code}")
                return {'has_labor_hours': False, 'total_labor_records': 0, 'total_regular_hours': 0.0, 'tasks_with_labor': 0, 'cache_hit': False}

            data = response.json()
            tasks = data.get('member', data.get('rdfs:member', []))

            self.logger.info(f"👷 WO LABOR: Found {len(tasks)} tasks for parent WO {parent_wonum}")

            total_labor_records = 0
            total_regular_hours = 0.0
            tasks_with_labor = 0

            # Check each task for labor (using collection reference count)
            for task in tasks:
                task_wonum = task.get('wonum', '')
                collection_ref = task.get('labtrans_collectionref', '')

                if collection_ref:
                    # Quick check: fetch collection reference to count labor records
                    try:
                        # Fix hostname if needed
                        if 'manage.v2x.maximotest.gov2x.com' in collection_ref:
                            import re
                            hostname_match = re.search(r'https://([^/]+)', base_url)
                            if hostname_match:
                                correct_hostname = hostname_match.group(1)
                                collection_ref = re.sub(r'https://[^/]+', f'https://{correct_hostname}', collection_ref)

                        # Quick count query - get labor records and sum regular hours
                        count_response = self.token_manager.session.get(
                            collection_ref + "?oslc.select=laborcode,regularhrs&lean=1",
                            timeout=(3.0, 10),  # Shorter timeout for count queries
                            headers={"Accept": "application/json"},
                            allow_redirects=True
                        )

                        if count_response.status_code == 200:
                            count_data = count_response.json()
                            labor_records = count_data.get('member', count_data.get('rdfs:member', []))
                            labor_count = len(labor_records)

                            if labor_count > 0:
                                total_labor_records += labor_count
                                tasks_with_labor += 1

                                # Sum regular hours for this task
                                task_regular_hours = 0.0
                                for labor_record in labor_records:
                                    regular_hrs = labor_record.get('regularhrs', 0)
                                    if regular_hrs:
                                        try:
                                            hrs_float = float(regular_hrs)
                                            task_regular_hours += hrs_float
                                        except (ValueError, TypeError):
                                            pass

                                total_regular_hours += task_regular_hours
                                self.logger.info(f"👷 WO LABOR: Task {task_wonum} has {labor_count} labor records, {task_regular_hours:.2f} regular hours")

                    except Exception as e:
                        self.logger.warning(f"👷 WO LABOR: Error checking labor for task {task_wonum}: {str(e)}")
                        continue

            # Prepare result
            result = {
                'has_labor_hours': total_labor_records > 0,
                'total_labor_records': total_labor_records,
                'total_regular_hours': round(total_regular_hours, 2),
                'tasks_with_labor': tasks_with_labor,
                'cache_hit': False
            }

            # Cache the result
            self._labor_cache[cache_key] = {
                'data': result.copy(),  # Store without cache_hit flag
                'timestamp': time.time()
            }

            self.logger.info(f"👷 WO LABOR: Parent WO {parent_wonum} - {total_labor_records} labor records, {total_regular_hours:.2f} regular hours across {tasks_with_labor} tasks")
            return result

        except Exception as e:
            self.logger.error(f"👷 WO LABOR: Error checking availability for WO {parent_wonum}: {str(e)}")
            return {
                'has_labor_hours': False,
                'total_labor_records': 0,
                'total_regular_hours': 0.0,
                'tasks_with_labor': 0,
                'cache_hit': False
            }

    def check_workorder_labor_cost_availability(self, parent_wonum, site_id=None):
        """
        Check labor cost availability for a parent work order by summing PAYRATE * regularhrs from all tasks
        """
        try:
            self.logger.info(f"💰 WO LABOR COST: Checking labor cost availability for parent WO {parent_wonum}")

            # Get site ID if not provided
            if not site_id or site_id == "UNKNOWN":
                site_id = self._get_workorder_site_id(parent_wonum)

            # Use cache key for labor cost
            cache_key = f"wo_labor_cost_v1_{parent_wonum}_{site_id}"

            # Check cache first
            if self._is_cache_valid(cache_key):
                self.logger.info(f"💰 WO LABOR COST: Using cached availability for WO {parent_wonum}")
                cached_data = self._labor_cache[cache_key]['data']
                cached_data['cache_hit'] = True
                return cached_data

            # Step 1: Get all tasks for the parent work order
            base_url = self.token_manager.base_url
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Build filter for tasks
            oslc_filter = f'parent="{parent_wonum}" and istask=1'
            if site_id and site_id != "UNKNOWN":
                oslc_filter += f' and siteid="{site_id}"'

            params = {
                "oslc.select": "wonum,labtrans_collectionref",
                "oslc.where": oslc_filter,
                "oslc.pageSize": "100",
                "lean": "1"
            }

            self.logger.info(f"💰 WO LABOR COST: Fetching tasks for parent {parent_wonum}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"},
                allow_redirects=True
            )

            if response.status_code != 200:
                self.logger.error(f"💰 WO LABOR COST: API call failed with status {response.status_code}")
                return {'has_labor_cost': False, 'total_labor_records': 0, 'total_labor_cost': 0.0, 'tasks_with_labor': 0, 'cache_hit': False}

            data = response.json()
            tasks = data.get('member', data.get('rdfs:member', []))

            self.logger.info(f"💰 WO LABOR COST: Found {len(tasks)} tasks for parent WO {parent_wonum}")

            total_labor_records = 0
            total_labor_cost = 0.0
            tasks_with_labor = 0

            # Check each task for labor cost (using collection reference)
            for task in tasks:
                task_wonum = task.get('wonum', '')
                collection_ref = task.get('labtrans_collectionref', '')

                if collection_ref:
                    try:
                        # Fix hostname if needed
                        if 'manage.v2x.maximotest.gov2x.com' in collection_ref:
                            import re
                            hostname_match = re.search(r'https://([^/]+)', base_url)
                            if hostname_match:
                                correct_hostname = hostname_match.group(1)
                                collection_ref = re.sub(r'https://[^/]+', f'https://{correct_hostname}', collection_ref)

                        # Get labor records with PAYRATE and regularhrs
                        count_response = self.token_manager.session.get(
                            collection_ref + "?oslc.select=laborcode,regularhrs,payrate&lean=1",
                            timeout=(3.0, 10),
                            headers={"Accept": "application/json"},
                            allow_redirects=True
                        )

                        if count_response.status_code == 200:
                            count_data = count_response.json()
                            labor_records = count_data.get('member', count_data.get('rdfs:member', []))
                            labor_count = len(labor_records)

                            if labor_count > 0:
                                total_labor_records += labor_count
                                tasks_with_labor += 1

                                # Calculate labor cost for this task
                                task_labor_cost = 0.0
                                for labor_record in labor_records:
                                    regular_hrs = labor_record.get('regularhrs', 0)
                                    payrate = labor_record.get('payrate', 0)

                                    if regular_hrs and payrate:
                                        try:
                                            hrs_float = float(regular_hrs)
                                            rate_float = float(payrate)
                                            task_labor_cost += hrs_float * rate_float
                                        except (ValueError, TypeError):
                                            pass

                                total_labor_cost += task_labor_cost
                                self.logger.info(f"💰 WO LABOR COST: Task {task_wonum} has {labor_count} labor records, ${task_labor_cost:.2f} labor cost")

                    except Exception as e:
                        self.logger.warning(f"💰 WO LABOR COST: Error checking labor cost for task {task_wonum}: {str(e)}")
                        continue

            # Prepare result
            result = {
                'has_labor_cost': total_labor_cost > 0,
                'total_labor_records': total_labor_records,
                'total_labor_cost': total_labor_cost,
                'tasks_with_labor': tasks_with_labor,
                'cache_hit': False
            }

            self.logger.info(f"💰 WO LABOR COST: Parent WO {parent_wonum} - {total_labor_records} labor records, ${total_labor_cost:.2f} total labor cost across {tasks_with_labor} tasks")

            # Cache the result
            self._labor_cache[cache_key] = {
                'data': result.copy(),
                'timestamp': time.time()
            }

            return result

        except Exception as e:
            self.logger.error(f"💰 WO LABOR COST: Error checking labor cost availability: {str(e)}")
            return {'has_labor_cost': False, 'total_labor_records': 0, 'total_labor_cost': 0.0, 'tasks_with_labor': 0, 'cache_hit': False}

    def get_workorder_task_details(self, parent_wonum: str, site_id: str = None) -> Dict[str, Any]:
        """
        Get comprehensive task details for a parent work order including:
        - Regular hours per task
        - Labor cost per task
        - Material count per task
        - Material cost per task
        """
        try:
            self.logger.info(f"📊 TASK DETAILS: Getting comprehensive task details for parent WO {parent_wonum}")

            # Get site ID if not provided
            if not site_id or site_id == "UNKNOWN":
                site_id = self._get_workorder_site_id(parent_wonum)

            # Use cache key for task details
            cache_key = f"wo_task_details_v1_{parent_wonum}_{site_id}"

            # Check cache first
            if self._is_cache_valid(cache_key):
                self.logger.info(f"📊 TASK DETAILS: Using cached details for WO {parent_wonum}")
                cached_data = self._labor_cache[cache_key]['data']
                cached_data['cache_hit'] = True
                return cached_data

            # Step 1: Get all tasks for the parent work order
            base_url = self.token_manager.base_url
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Build filter for tasks
            oslc_filter = f'parent="{parent_wonum}" and istask=1'
            if site_id and site_id != "UNKNOWN":
                oslc_filter += f' and siteid="{site_id}"'

            params = {
                "oslc.select": "wonum,description,status,labtrans_collectionref,wpmaterial_collectionref",
                "oslc.where": oslc_filter,
                "oslc.pageSize": "100",
                "lean": "1"
            }

            self.logger.info(f"📊 TASK DETAILS: Fetching tasks for parent {parent_wonum}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 30),
                headers={"Accept": "application/json"},
                allow_redirects=True
            )

            if response.status_code != 200:
                self.logger.error(f"📊 TASK DETAILS: API call failed with status {response.status_code}")
                return {'success': False, 'tasks': [], 'total_tasks': 0, 'cache_hit': False}

            data = response.json()
            tasks = data.get('member', data.get('rdfs:member', []))

            self.logger.info(f"📊 TASK DETAILS: Found {len(tasks)} tasks for parent WO {parent_wonum}")

            task_details = []

            # Process each task to get all 4 metrics
            for task in tasks:
                task_wonum = task.get('wonum', '')
                task_description = task.get('description', '')
                task_status = task.get('status', '')
                labtrans_ref = task.get('labtrans_collectionref', '')
                wpmaterial_ref = task.get('wpmaterial_collectionref', '')

                task_detail = {
                    'wonum': task_wonum,
                    'description': task_description,
                    'status': task_status,
                    'regular_hours': 0.0,
                    'labor_cost': 0.0,
                    'material_count': 0,
                    'material_cost': 0.0
                }

                # Get labor details (hours and cost)
                if labtrans_ref:
                    try:
                        # Fix hostname if needed
                        if 'manage.v2x.maximotest.gov2x.com' in labtrans_ref:
                            import re
                            hostname_match = re.search(r'https://([^/]+)', base_url)
                            if hostname_match:
                                correct_hostname = hostname_match.group(1)
                                labtrans_ref = re.sub(r'https://[^/]+', f'https://{correct_hostname}', labtrans_ref)

                        # Get labor records with hours and payrate
                        labor_response = self.token_manager.session.get(
                            labtrans_ref + "?oslc.select=laborcode,regularhrs,payrate&lean=1",
                            timeout=(3.0, 10),
                            headers={"Accept": "application/json"},
                            allow_redirects=True
                        )

                        if labor_response.status_code == 200:
                            labor_data = labor_response.json()
                            labor_records = labor_data.get('member', labor_data.get('rdfs:member', []))

                            for labor_record in labor_records:
                                regular_hrs = labor_record.get('regularhrs', 0)
                                payrate = labor_record.get('payrate', 0)

                                if regular_hrs:
                                    try:
                                        hrs_float = float(regular_hrs)
                                        task_detail['regular_hours'] += hrs_float

                                        if payrate:
                                            rate_float = float(payrate)
                                            task_detail['labor_cost'] += hrs_float * rate_float
                                    except (ValueError, TypeError):
                                        pass

                    except Exception as e:
                        self.logger.warning(f"📊 TASK DETAILS: Error getting labor for task {task_wonum}: {str(e)}")

                # Get material details (count and cost)
                if wpmaterial_ref:
                    try:
                        # Fix hostname if needed
                        if 'manage.v2x.maximotest.gov2x.com' in wpmaterial_ref:
                            import re
                            hostname_match = re.search(r'https://([^/]+)', base_url)
                            if hostname_match:
                                correct_hostname = hostname_match.group(1)
                                wpmaterial_ref = re.sub(r'https://[^/]+', f'https://{correct_hostname}', wpmaterial_ref)

                        # Get material records with linecost
                        material_response = self.token_manager.session.get(
                            wpmaterial_ref + "?oslc.select=itemnum,description,qty,orderunit,linecost&lean=1",
                            timeout=(3.0, 10),
                            headers={"Accept": "application/json"},
                            allow_redirects=True
                        )

                        if material_response.status_code == 200:
                            material_data = material_response.json()
                            materials = material_data.get('member', material_data.get('rdfs:member', []))

                            task_detail['material_count'] = len(materials)

                            for material in materials:
                                linecost = material.get('linecost', 0)

                                if linecost:
                                    try:
                                        cost_float = float(linecost)
                                        task_detail['material_cost'] += cost_float
                                    except (ValueError, TypeError):
                                        pass

                    except Exception as e:
                        self.logger.warning(f"📊 TASK DETAILS: Error getting materials for task {task_wonum}: {str(e)}")

                task_details.append(task_detail)
                self.logger.info(f"📊 TASK DETAILS: Task {task_wonum} - {task_detail['regular_hours']:.2f}h, ${task_detail['labor_cost']:.2f} labor, {task_detail['material_count']} materials, ${task_detail['material_cost']:.2f} material cost")

            # Prepare result
            result = {
                'success': True,
                'tasks': task_details,
                'total_tasks': len(task_details),
                'cache_hit': False
            }

            self.logger.info(f"📊 TASK DETAILS: Parent WO {parent_wonum} - {len(task_details)} tasks processed")

            # Cache the result
            self._labor_cache[cache_key] = {
                'data': result.copy(),
                'timestamp': time.time()
            }

            return result

        except Exception as e:
            self.logger.error(f"📊 TASK DETAILS: Error getting task details: {str(e)}")
            return {'success': False, 'tasks': [], 'total_tasks': 0, 'cache_hit': False}
