"""
Inventory Issue Service

This service handles inventory issue operations using the MXAPIINVENTORY endpoint
with OSLC token session authentication. It follows the same patterns as the
inventory_transfer_service.py for consistency.

Key Features:
- Issue Current Item operations using MXAPIINVENTORY endpoint
- OSLC token session authentication only (no API keys)
- Comprehensive form data population from Maximo APIs
- Lookup services for work orders, assets, GL accounts, etc.
- Error handling and validation
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime


class InventoryIssueService:
    """Service for handling inventory issue operations via MXAPIINVENTORY endpoint."""
    
    def __init__(self, token_manager):
        """
        Initialize the inventory issue service.
        
        Args:
            token_manager: Maximo token manager for authentication
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        
    def submit_issue_current_item(self, issue_data: Dict) -> Dict:
        """
        Submit an issue current item transaction to Maximo.
        
        Args:
            issue_data (Dict): Issue transaction data
            
        Returns:
            Dict: Result of the issue submission
        """
        try:
            self.logger.info(f"🔄 ISSUE CURRENT ITEM: Processing for item {issue_data.get('itemnum')}")
            
            # Build issue payload
            payload = self._build_issue_payload(issue_data)
            
            # Submit to MXAPIINVENTORY endpoint
            result = self._submit_to_maximo(payload)
            
            if result['success']:
                self.logger.info(f"✅ ISSUE CURRENT ITEM: Successfully submitted for item {issue_data.get('itemnum')}")
            else:
                self.logger.error(f"❌ ISSUE CURRENT ITEM: Failed for item {issue_data.get('itemnum')}: {result.get('error')}")
                
            return result
            
        except Exception as e:
            self.logger.error(f"❌ ISSUE CURRENT ITEM: Exception occurred: {str(e)}")
            return {
                'success': False,
                'error': f'Issue submission failed: {str(e)}'
            }
    
    def _build_issue_payload(self, issue_data: Dict) -> Dict:
        """
        Build the payload for issue current item operation.
        
        Args:
            issue_data (Dict): Issue transaction data
            
        Returns:
            Dict: Formatted payload for MXAPIINVENTORY
        """
        try:
            # Extract required fields
            itemnum = issue_data.get('itemnum', '')
            siteid = issue_data.get('siteid', '')
            storeroom = issue_data.get('storeroom', '')
            quantity = float(issue_data.get('quantity', 1.0))
            
            # Build base inventory record
            inventory_record = {
                "itemnum": itemnum,
                "siteid": siteid,
                "location": storeroom
            }
            
            # Build issue transaction record
            issue_transaction = {
                "itemnum": itemnum,
                "siteid": siteid,
                "storeloc": storeroom,
                "quantity": quantity,
                "transtype": issue_data.get('transaction_type', 'ISSUE'),
                "issueunit": issue_data.get('issue_unit', 'EA'),
                "actualdate": issue_data.get('actual_date', datetime.now().isoformat()),
                "enteredby": self.token_manager.username if hasattr(self.token_manager, 'username') else '',
                "memo": issue_data.get('memo', '')
            }
            
            # Add optional fields if provided
            if issue_data.get('bin'):
                issue_transaction['binnum'] = issue_data['bin']
                
            if issue_data.get('lot'):
                issue_transaction['lotnum'] = issue_data['lot']
                
            if issue_data.get('condition_code'):
                issue_transaction['conditioncode'] = issue_data['condition_code']
                
            if issue_data.get('work_order'):
                issue_transaction['refwo'] = issue_data['work_order']
                
            if issue_data.get('wo_task'):
                issue_transaction['taskid'] = issue_data['wo_task']
                
            if issue_data.get('asset'):
                issue_transaction['assetnum'] = issue_data['asset']
                
            if issue_data.get('location'):
                issue_transaction['location'] = issue_data['location']
                
            if issue_data.get('gl_debit_account'):
                issue_transaction['gldebitacct'] = issue_data['gl_debit_account']
                
            if issue_data.get('gl_credit_account'):
                issue_transaction['glcreditacct'] = issue_data['gl_credit_account']
                
            if issue_data.get('issue_to'):
                issue_transaction['issueto'] = issue_data['issue_to']
                
            if issue_data.get('to_site'):
                issue_transaction['tositeid'] = issue_data['to_site']
                
            if issue_data.get('requisition'):
                issue_transaction['mrnum'] = issue_data['requisition']
                
            if issue_data.get('requisition_line'):
                issue_transaction['mrlinenum'] = issue_data['requisition_line']
            
            # Build complete payload with nested MATRECTRANS
            payload = {
                **inventory_record,
                "matrectrans": [issue_transaction]
            }
            
            self.logger.debug(f"🔄 ISSUE: Built payload: {json.dumps(payload, indent=2)}")
            return payload
            
        except Exception as e:
            self.logger.error(f"❌ ISSUE: Error building payload: {str(e)}")
            raise
    
    def _submit_to_maximo(self, payload: Dict) -> Dict:
        """
        Submit the issue payload to MXAPIINVENTORY endpoint.
        
        Args:
            payload (Dict): Issue payload to submit
            
        Returns:
            Dict: Result of the submission
        """
        try:
            # Get the base URL from token manager
            base_url = getattr(self.token_manager, 'base_url', '')
            if not base_url:
                return {
                    'success': False,
                    'error': 'No Maximo base URL configured'
                }

            # Use MXAPIINVENTORY endpoint with MATRECTRANS child object
            api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"

            # Prepare headers for MXAPIINVENTORY with MATRECTRANS
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            }

            # Submit the issue transaction
            self.logger.info(f"🔄 ISSUE: Submitting to {api_url}")
            self.logger.debug(f"🔄 ISSUE: Payload: {json.dumps(payload, indent=2)}")

            response = self.token_manager.session.post(
                api_url,
                json=payload,
                headers=headers,
                timeout=(10, 30)
            )

            self.logger.info(f"🔄 ISSUE: Response status: {response.status_code}")
            
            if response.status_code in [200, 201, 204]:
                try:
                    response_data = response.json() if response.content else {}
                except:
                    response_data = {}
                    
                return {
                    'success': True,
                    'message': 'Issue transaction submitted successfully to Maximo',
                    'response': response_data,
                    'status_code': response.status_code
                }
            else:
                error_message = f"HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if 'Error' in error_data:
                        error_message = error_data['Error'].get('message', error_message)
                    elif 'error' in error_data:
                        error_message = error_data['error'].get('message', error_message)
                except:
                    error_message = response.text[:500] if response.text else error_message
                    
                return {
                    'success': False,
                    'error': error_message,
                    'status_code': response.status_code
                }

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception during submission: {str(e)}")
            return {
                'success': False,
                'error': f'Submission failed: {str(e)}'
            }

    def get_work_orders(self, site_id: str, status_filter: str = "WMATL,PISSUE", search_term: str = "") -> List[Dict]:
        """
        Get work orders in WMATL or PISSUE status for issue transactions.

        Args:
            site_id (str): Site ID to filter work orders
            status_filter (str): Comma-separated status values
            search_term (str): Optional search term for work order number or description

        Returns:
            List[Dict]: List of work orders
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Build status filter
            statuses = [s.strip() for s in status_filter.split(',')]
            status_conditions = ' or '.join([f'status="{status}"' for status in statuses])

            # Build where conditions
            where_conditions = [f'siteid="{site_id}"', f'({status_conditions})']

            # Add search filter if provided
            if search_term:
                search_conditions = [
                    f'wonum~"{search_term}"',
                    f'description~"{search_term}"'
                ]
                where_conditions.append(f'({" or ".join(search_conditions)})')

            params = {
                "oslc.select": "wonum,description,status,siteid,assetnum,location,worktype",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "100",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                work_orders = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(work_orders)} work orders for site {site_id}")
                return work_orders
            else:
                self.logger.error(f"❌ ISSUE: Failed to get work orders: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting work orders: {str(e)}")
            return []

    def get_work_order_tasks(self, wonum: str, site_id: str, status_filter: str = None) -> List[Dict]:
        """
        Get tasks for a specific work order with optional status filtering.

        Args:
            wonum (str): Work order number
            site_id (str): Site ID
            status_filter (str): Comma-separated list of statuses to filter by

        Returns:
            List[Dict]: List of work order tasks
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwotask"

            # Build where clause with optional status filtering
            where_conditions = [f'wonum="{wonum}"', f'siteid="{site_id}"']

            if status_filter:
                statuses = [status.strip() for status in status_filter.split(',')]
                if statuses:
                    status_conditions = [f'status="{status}"' for status in statuses]
                    where_conditions.append(f"({' or '.join(status_conditions)})")

            params = {
                "oslc.select": "taskid,description,status,wonum,siteid",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "50",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                tasks = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(tasks)} tasks for work order {wonum}")
                return tasks
            else:
                self.logger.error(f"❌ ISSUE: Failed to get work order tasks: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting work order tasks: {str(e)}")
            return []

    def get_assets(self, site_id: str, search_term: str = "") -> List[Dict]:
        """
        Get assets for asset lookup.

        Args:
            site_id (str): Site ID to filter assets
            search_term (str): Optional search term for asset number or description

        Returns:
            List[Dict]: List of assets
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiasset"

            # Build filter
            where_conditions = [f'siteid="{site_id}"', 'status!="DECOMMISSIONED"']

            if search_term:
                search_conditions = [
                    f'assetnum~"{search_term}"',
                    f'description~"{search_term}"'
                ]
                where_conditions.append(f'({" or ".join(search_conditions)})')

            params = {
                "oslc.select": "assetnum,description,status,siteid,location,assettype",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "50",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                assets = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(assets)} assets for site {site_id}")
                return assets
            else:
                self.logger.error(f"❌ ISSUE: Failed to get assets: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting assets: {str(e)}")
            return []

    def get_gl_accounts(self, account_type: str = "") -> List[Dict]:
        """
        Get GL accounts for debit/credit account lookup.

        Args:
            account_type (str): Optional filter for account type

        Returns:
            List[Dict]: List of GL accounts
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/glaccount"

            where_conditions = ['status="ACTIVE"']

            if account_type:
                where_conditions.append(f'accounttype="{account_type}"')

            params = {
                "oslc.select": "glaccount,description,accounttype,status",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "100",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                accounts = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(accounts)} GL accounts")
                return accounts
            else:
                self.logger.error(f"❌ ISSUE: Failed to get GL accounts: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting GL accounts: {str(e)}")
            return []

    def get_persons(self, search_term: str = "") -> List[Dict]:
        """
        Get persons for issue to lookup using MXAPIPERSON endpoint with OSLC token session authentication.

        This method fetches person data from the MXAPIPERSON endpoint with status='ACTIVE' filter
        and makes it searchable by displayname for dropdown functionality.

        Args:
            search_term (str): Optional search term for person displayname search

        Returns:
            List[Dict]: List of persons with personid, displayname, locationsite, status fields
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            # Use MXAPIPERSON endpoint with OSLC token session authentication (FIXED)
            api_url = f"{base_url}/oslc/os/mxapiperson"

            where_conditions = []

            # Always filter by status='ACTIVE'
            where_conditions.append('status="ACTIVE"')

            if search_term:
                # Make searchable by personid, displayname, and locationsite for comprehensive dropdown functionality
                search_conditions = [
                    f'personid~"{search_term}"',
                    f'displayname~"{search_term}"',
                    f'locationsite~"{search_term}"'
                ]
                where_conditions.append(f'({" or ".join(search_conditions)})')

            params = {
                # Select the required fields: personid, displayname, locationsite, status
                "oslc.select": "personid,displayname,locationsite,status",
                "oslc.pageSize": "100",  # Increased page size for better user experience
                "lean": "1"  # Use lean mode for better performance
            }

            # Add where conditions (always has status filter)
            params["oslc.where"] = " and ".join(where_conditions)

            self.logger.info(f"🔍 ISSUE: Fetching persons from MXAPIPERSON endpoint with search: '{search_term}' (UPDATED - FORCE RELOAD)")
            self.logger.debug(f"🔍 ISSUE: API URL: {api_url}")
            self.logger.debug(f"🔍 ISSUE: Query params: {params}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            self.logger.info(f"🔍 ISSUE: MXAPIPERSON response status: {response.status_code}")
            self.logger.debug(f"🔍 ISSUE: Response headers: {dict(response.headers)}")
            self.logger.debug(f"🔍 ISSUE: Response text length: {len(response.text)}")

            if response.status_code == 200:
                if response.text.strip():
                    try:
                        data = response.json()
                        persons = data.get('member', [])

                        # Log the structure of the first person for debugging
                        if persons:
                            self.logger.debug(f"🔍 ISSUE: Sample person data structure: {persons[0]}")

                        self.logger.info(f"✅ ISSUE: Found {len(persons)} persons from MXAPIPERSON endpoint")
                        return persons
                    except Exception as json_error:
                        self.logger.error(f"❌ ISSUE: JSON parsing error: {str(json_error)}")
                        self.logger.error(f"❌ ISSUE: Raw response: {response.text[:500]}")
                        return []
                else:
                    self.logger.warning(f"⚠️ ISSUE: MXAPIPERSON returned empty response (no person data available)")
                    return []
            else:
                self.logger.error(f"❌ ISSUE: Failed to get persons from MXAPIPERSON endpoint: {response.status_code}")
                self.logger.error(f"❌ ISSUE: Response text: {response.text[:200]}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting persons from MXAPIPERSON endpoint: {str(e)}")
            return []

    def get_locations(self, site_id: str, search_term: str = "") -> List[Dict]:
        """
        Get locations for location lookup.

        Args:
            site_id (str): Site ID to filter locations
            search_term (str): Optional search term for location

        Returns:
            List[Dict]: List of locations
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapilocations"

            where_conditions = [f'siteid="{site_id}"', 'status="OPERATING"']

            if search_term:
                search_conditions = [
                    f'location~"{search_term}"',
                    f'description~"{search_term}"'
                ]
                where_conditions.append(f'({" or ".join(search_conditions)})')

            params = {
                "oslc.select": "location,description,status,siteid,type",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "50",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                locations = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(locations)} locations for site {site_id}")
                return locations
            else:
                self.logger.error(f"❌ ISSUE: Failed to get locations: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting locations: {str(e)}")
            return []

    def get_requisitions(self, site_id: str, search_term: str = "") -> List[Dict]:
        """
        Get requisitions for requisition lookup.

        Args:
            site_id (str): Site ID to filter requisitions
            search_term (str): Optional search term for requisition number

        Returns:
            List[Dict]: List of requisitions
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapimr"

            where_conditions = [f'siteid="{site_id}"', 'status!="CANCELLED"']

            if search_term:
                where_conditions.append(f'mrnum~"{search_term}"')

            params = {
                "oslc.select": "mrnum,description,status,siteid,requestedby",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "50",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                requisitions = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(requisitions)} requisitions for site {site_id}")
                return requisitions
            else:
                self.logger.error(f"❌ ISSUE: Failed to get requisitions: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting requisitions: {str(e)}")
            return []

    def get_available_sites(self) -> List[Dict]:
        """
        Get available sites for to site lookup.

        Returns:
            List[Dict]: List of sites
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapisite"

            params = {
                "oslc.select": "siteid,description,status,orgid",
                "oslc.where": 'status="ACTIVE"',
                "oslc.pageSize": "100",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                sites = data.get('member', [])
                self.logger.info(f"✅ ISSUE: Found {len(sites)} sites")
                return sites
            else:
                self.logger.error(f"❌ ISSUE: Failed to get sites: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting sites: {str(e)}")
            return []

    def get_inventory_balances(self, itemnum: str, siteid: str, location: str) -> Dict:
        """
        Get inventory balances for bins, lots, and condition codes using MXAPIINVENTORY endpoint.

        Args:
            itemnum (str): Item number
            siteid (str): Site ID
            location (str): Storeroom location

        Returns:
            Dict: Dictionary with bins, lots, and conditions lists
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiinventory"

            # Use MXAPIINVENTORY with invbalances related table for comprehensive data
            params = {
                "oslc.select": "itemnum,siteid,location,status,invbalances",
                "oslc.where": f'itemnum="{itemnum}" and siteid="{siteid}" and location="{location}" and status="ACTIVE"',
                "oslc.pageSize": "100",
                "lean": "1"
            }

            self.logger.info(f"🔍 ISSUE: Fetching inventory balances from MXAPIINVENTORY for {itemnum} at {siteid}/{location}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                inventory_records = data.get('member', [])

                bins = set()
                lots = set()
                conditions = set()

                # Process each inventory record and extract balance data
                for inventory_record in inventory_records:
                    invbalances = inventory_record.get('invbalances', [])

                    # Handle both list and single object cases
                    if not isinstance(invbalances, list):
                        invbalances = [invbalances] if invbalances else []

                    for balance in invbalances:
                        if isinstance(balance, dict):
                            # Only include balances with positive current balance
                            curbal = balance.get('curbal', 0)
                            if curbal and float(curbal) > 0:
                                bin_num = balance.get('binnum', '').strip()
                                lot_num = balance.get('lotnum', '').strip()
                                condition = balance.get('conditioncode', '').strip()

                                if bin_num:
                                    bins.add(bin_num)
                                if lot_num:
                                    lots.add(lot_num)
                                if condition:
                                    conditions.add(condition)

                result = {
                    'bins': sorted(list(bins)),
                    'lots': sorted(list(lots)),
                    'conditions': sorted(list(conditions))
                }

                self.logger.info(f"✅ ISSUE: Found {len(result['bins'])} bins, {len(result['lots'])} lots, {len(result['conditions'])} conditions from MXAPIINVENTORY")

                # Log sample data for debugging
                if result['bins']:
                    self.logger.info(f"🔍 ISSUE: Sample bins: {result['bins'][:5]}")
                if result['lots']:
                    self.logger.info(f"🔍 ISSUE: Sample lots: {result['lots'][:5]}")
                if result['conditions']:
                    self.logger.info(f"🔍 ISSUE: Sample conditions: {result['conditions'][:5]}")

                return result
            else:
                self.logger.error(f"❌ ISSUE: Failed to get inventory balances from MXAPIINVENTORY: {response.status_code}")
                self.logger.error(f"❌ ISSUE: Response: {response.text[:200]}")
                return {'bins': [], 'lots': [], 'conditions': []}

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting inventory balances from MXAPIINVENTORY: {str(e)}")
            return {'bins': [], 'lots': [], 'conditions': []}

    def get_issue_units(self, itemnum: str) -> List[Dict]:
        """
        Get available issue units for an item.

        Args:
            itemnum (str): Item number

        Returns:
            List[Dict]: List of issue units
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiitem"

            params = {
                "oslc.select": "itemnum,issueunit,orderunit",
                "oslc.where": f'itemnum="{itemnum}"',
                "oslc.pageSize": "1",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                if items:
                    item = items[0]
                    units = []

                    # Add issue unit
                    if item.get('issueunit'):
                        units.append({
                            'unit': item['issueunit'],
                            'description': f"Issue Unit ({item['issueunit']})"
                        })

                    # Add order unit if different
                    if item.get('orderunit') and item['orderunit'] != item.get('issueunit'):
                        units.append({
                            'unit': item['orderunit'],
                            'description': f"Order Unit ({item['orderunit']})"
                        })

                    # Add common units if not already included
                    common_units = ['EA', 'RO', 'FT', 'LB', 'GAL']
                    existing_units = [u['unit'] for u in units]

                    for unit in common_units:
                        if unit not in existing_units:
                            units.append({
                                'unit': unit,
                                'description': f"Common Unit ({unit})"
                            })

                    self.logger.info(f"✅ ISSUE: Found {len(units)} issue units for item {itemnum}")
                    return units
                else:
                    # Return default units if item not found
                    return [
                        {'unit': 'EA', 'description': 'Each (EA)'},
                        {'unit': 'RO', 'description': 'Roll (RO)'},
                        {'unit': 'FT', 'description': 'Feet (FT)'},
                        {'unit': 'LB', 'description': 'Pounds (LB)'},
                        {'unit': 'GAL', 'description': 'Gallons (GAL)'}
                    ]
            else:
                self.logger.error(f"❌ ISSUE: Failed to get issue units: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Exception getting issue units: {str(e)}")
            return []

    def get_work_orders_for_issue(self, site_id, configured_statuses, search_term=""):
        """Get work orders for issue transactions using MXAPIWODETAIL with configurable status filtering"""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Build where clause for work orders: siteid, istask=0, historyflag=0, and status filtering
            where_conditions = [
                f'siteid="{site_id}"',
                'istask=0',
                'historyflag=0'
            ]

            if configured_statuses:
                status_conditions = [f'status="{status}"' for status in configured_statuses]
                where_conditions.append(f"({' or '.join(status_conditions)})")

            # Add search term if provided
            if search_term:
                search_conditions = [
                    f'wonum like "%{search_term}%"',
                    f'description like "%{search_term}%"'
                ]
                where_conditions.append(f"({' or '.join(search_conditions)})")

            params = {
                "oslc.select": "wonum,description,status,siteid",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "100",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                work_orders = []

                if 'member' in data:
                    for wo in data['member']:
                        work_orders.append({
                            'wonum': wo.get('wonum', ''),
                            'description': wo.get('description', ''),
                            'status': wo.get('status', ''),
                            'siteid': wo.get('siteid', '')
                        })

                self.logger.info(f"✅ ISSUE: Found {len(work_orders)} work orders for site {site_id}")
                return work_orders
            else:
                self.logger.error(f"❌ ISSUE: Failed to get work orders: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Error getting work orders: {e}")
            return []

    def get_work_order_tasks_for_issue(self, wonum, site_id, configured_statuses):
        """Get tasks for a work order using MXAPIWODETAIL with configurable status filtering"""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"

            # Build where clause for tasks: wonum, siteid, istask=1, historyflag=0, and status filtering
            where_conditions = [
                f'wonum="{wonum}"',
                f'siteid="{site_id}"',
                'istask=1',
                'historyflag=0'
            ]

            if configured_statuses:
                status_conditions = [f'status="{status}"' for status in configured_statuses]
                where_conditions.append(f"({' or '.join(status_conditions)})")

            params = {
                "oslc.select": "taskid,description,status,wonum,siteid,assignedperson,laborcode",
                "oslc.where": " and ".join(where_conditions),
                "oslc.pageSize": "50",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(5.0, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                tasks = []

                if 'member' in data:
                    for task in data['member']:
                        tasks.append({
                            'taskid': task.get('taskid', ''),
                            'description': task.get('description', ''),
                            'status': task.get('status', ''),
                            'wonum': task.get('wonum', ''),
                            'siteid': task.get('siteid', ''),
                            'assignedperson': task.get('assignedperson', ''),
                            'laborcode': task.get('laborcode', '')
                        })

                self.logger.info(f"✅ ISSUE: Found {len(tasks)} tasks for work order {wonum}")
                return tasks
            else:
                self.logger.error(f"❌ ISSUE: Failed to get work order tasks: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ ISSUE: Error getting work order tasks: {e}")
            return []
