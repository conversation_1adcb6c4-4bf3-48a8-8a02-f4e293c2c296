"""
Inventory Management Service

This service provides comprehensive inventory management functionality,
including search, filtering, and data retrieval from Maximo MXAPIINVENTORY endpoint.
Based on the existing inventory_search_service but adapted for standalone inventory management.
"""

import logging
import time
from typing import Dict, List, Tuple, Optional, Any
import requests


class InventoryManagementService:
    """Service for managing inventory operations with dynamic API discovery."""
    
    def __init__(self, token_manager):
        """
        Initialize the Inventory Management Service.
        
        Args:
            token_manager: Token manager for API authentication
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        self._search_cache = {}
        self._cache_timeout = 300  # 5 minutes
        
        # Debug logging for service initialization
        self.logger.info(f"🔧 INIT: InventoryManagementService initialized")
        self.logger.info(f"🔧 INIT: token_manager: {'✅ Available' if token_manager else '❌ None'}")
        
    def search_inventory_items(self, search_term: str, site_id: str, limit: int = 20, page: int = 0, status_filter: str = '') -> <PERSON><PERSON>[List[Dict], Dict]:
        """
        Search inventory items by item number or description with pagination.
        Uses status != 'OBSOLETE' filter instead of status = 'ACTIVE'.

        Args:
            search_term (str): Search term for item number or description
            site_id (str): Site ID to filter inventory
            limit (int): Maximum number of results to return per page
            page (int): Page number for pagination (0-based)
            status_filter (str): Optional status filter (ACTIVE, INACTIVE, PENDING, etc.)

        Returns:
            Tuple[List[Dict], Dict]: (inventory_items, metadata)
        """
        start_time = time.time()
        
        if not search_term or not search_term.strip():
            return [], {'load_time': 0, 'source': 'empty', 'count': 0, 'page': page, 'total_pages': 0}

        search_term = search_term.strip()
        cache_key = f"{search_term}_{site_id}_{status_filter}_{limit}_{page}"

        # Check cache first
        if cache_key in self._search_cache:
            cached_data = self._search_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_timeout:
                self.logger.info(f"🔍 INVENTORY: Cache hit for search: {search_term}")
                cached_data['data'][1]['source'] = 'cache'
                return cached_data['data']

        self.logger.info(f"🔍 INVENTORY: Searching for '{search_term}' in site '{site_id}' with status '{status_filter}' (page {page}, limit {limit})")

        try:
            # Check if token manager is properly initialized
            if not self.token_manager:
                raise Exception("Token manager not available")

            if not hasattr(self.token_manager, 'session') or not self.token_manager.session:
                raise Exception("Session not available - user may not be logged in")

            if not hasattr(self.token_manager, 'base_url') or not self.token_manager.base_url:
                raise Exception("Base URL not available - token manager not properly initialized")

            # Step 1: Search MXAPIITEM for comprehensive item data (primary dataset)
            self.logger.info(f"🔍 INVENTORY: Step 1 - Searching MXAPIITEM for '{search_term}' with status filter '{status_filter}'")
            mxapiitem_results = self._search_mxapiitem_comprehensive(search_term, limit * 3, page, status_filter)

            if not mxapiitem_results:
                self.logger.info(f"🔍 INVENTORY: No items found in MXAPIITEM")
                return [], {
                    'load_time': time.time() - start_time,
                    'source': 'mxapiitem_empty',
                    'count': 0,
                    'page': page,
                    'total_pages': 0
                }

            # Step 2: Filter through MXAPIINVENTORY with site_id and status != 'OBSOLETE'
            self.logger.info(f"🔍 INVENTORY: Step 2 - Filtering {len(mxapiitem_results)} items through MXAPIINVENTORY for site '{site_id}'")
            filtered_items = self._filter_through_mxapiinventory(mxapiitem_results, site_id)

            # Step 3: Sort by relevance and limit results
            filtered_items = self._sort_by_relevance(filtered_items, search_term)
            enhanced_items = filtered_items[:limit]

            # Calculate pagination metadata
            total_items = len(enhanced_items)
            total_pages = max(1, (total_items + limit - 1) // limit) if total_items > 0 else 0
            
            # Cache the results
            result_data = (enhanced_items, {
                'load_time': time.time() - start_time,
                'source': 'mxapiitem_filtered_inventory',
                'count': total_items,
                'page': page,
                'total_pages': total_pages,
                'search_term': search_term,
                'site_id': site_id,
                'mxapiitem_count': len(mxapiitem_results),
                'filtered_count': len(filtered_items)
            })
            
            self._search_cache[cache_key] = {
                'data': result_data,
                'timestamp': time.time()
            }

            self.logger.info(f"✅ INVENTORY: Found {total_items} items in {time.time() - start_time:.2f}s")
            return result_data

        except Exception as e:
            self.logger.error(f"❌ INVENTORY: Search failed: {str(e)}")
            return [], {
                'load_time': time.time() - start_time,
                'source': 'error',
                'count': 0,
                'page': page,
                'total_pages': 0,
                'error': str(e)
            }

    def _search_inventory_primary(self, search_term: str, site_id: str, limit: int, status_filter: str = '') -> List[Dict]:
        """
        Primary search using MXAPIINVENTORY endpoint - COPIED FROM WORKING SERVICE.

        Args:
            search_term (str): Search term
            site_id (str): Site ID for filtering
            limit (int): Maximum results
            status_filter (str): Optional status filter

        Returns:
            List[Dict]: Inventory items from MXAPIINVENTORY
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Build search filter - MXAPIINVENTORY only has siteid and itemnum (NO description field)
        search_term_clean = search_term.replace('"', '\\"')

        # Build status filter
        if status_filter:
            status_condition = f'status="{status_filter}"'
        else:
            # Use status!="OBSOLETE" to exclude obsolete items and bring all other statuses
            status_condition = 'status!="OBSOLETE"'

        oslc_filter = f'itemnum="%{search_term_clean}%" and siteid="{site_id}" and {status_condition}'

        # Select ONLY the fields that actually exist in MXAPIINVENTORY
        # Based on actual API response analysis for item 5975-60-V00-0001
        select_fields = [
            # Core inventory fields (confirmed available)
            "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
            "issueunit", "orderunit", "curbaltotal", "avblbalance",
            "costtype", "conditioncode", "inventoryid", "orgid",
            "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty",
            "opstime", "deliverytime", "admimtime", "statusdate",
            "issue1yrago", "issue2yrago", "issue3yrago", "issueytd",
            "expiredqty", "invreserveqty", "shippedqty"
        ]
        # Note: binnum, abc, storeloc, conditionenabled, physcnt, physcntdate,
        # unitcost, currencycode are NOT available as direct fields

        # Add nested array fields - these will be accessed via array[0].field in processing
        nested_fields = [
            "invcost", "invbalances", "itemcondition", "invvendor"
        ]

        all_fields = select_fields + nested_fields

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": oslc_filter,
            "oslc.pageSize": str(limit),
            "lean": "1"
        }

        self.logger.info(f"🔍 INVENTORY: Searching MXAPIINVENTORY for '{search_term}'")
        self.logger.info(f"🔍 INVENTORY: API URL: {api_url}")
        self.logger.info(f"🔍 INVENTORY: Filter: {oslc_filter}")
        self.logger.info(f"🔍 INVENTORY: OSLC Select: {params['oslc.select']}")

        response = self.token_manager.session.get(
            api_url,
            params=params,
            timeout=(5.0, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        self.logger.info(f"🔍 INVENTORY: Response status: {response.status_code}")

        if response.status_code != 200:
            self.logger.error(f"🔍 INVENTORY: API call failed with status {response.status_code}")
            self.logger.error(f"🔍 INVENTORY: Response text: {response.text}")
            raise Exception(f"API call failed: {response.status_code}")

        try:
            data = response.json()
            items = data.get('member', [])

            self.logger.info(f"🔍 INVENTORY: Found {len(items)} items in MXAPIINVENTORY")

            # Process nested array fields into flat structure
            processed_items = self._process_inventory_data(items)

            # Enhance with ITEM data from MXAPIITEM
            enhanced_items = self._enhance_inventory_with_item_data(processed_items)

            # Return the enhanced items
            self.logger.info(f"✅ INVENTORY: Returning {len(enhanced_items)} enhanced items with ITEM data")
            return enhanced_items

        except Exception as e:
            self.logger.error(f"🔍 INVENTORY: Error parsing API response: {str(e)}")
            self.logger.error(f"🔍 INVENTORY: Raw response: {response.text[:500]}")
            raise Exception(f"Error parsing inventory data: {str(e)}")

    def _enhance_with_item_data(self, inventory_items: List[Dict], site_id: str) -> List[Dict]:
        """
        Enhance inventory items with additional data from MXAPIITEM.
        
        Args:
            inventory_items (List[Dict]): Items from MXAPIINVENTORY
            site_id (str): Site ID for context
            
        Returns:
            List[Dict]: Enhanced inventory items
        """
        if not inventory_items:
            return []

        enhanced_items = []
        
        for inv_item in inventory_items:
            itemnum = inv_item.get('itemnum')
            if not itemnum:
                continue
                
            # Get additional item details from MXAPIITEM
            item_details = self._get_item_details(itemnum)
            
            # Merge inventory and item data - only actual values
            enhanced_item = {}

            # Inventory-specific fields (from MXAPIINVENTORY)
            if inv_item.get('itemnum'):
                enhanced_item['itemnum'] = inv_item['itemnum']
            if inv_item.get('siteid'):
                enhanced_item['siteid'] = inv_item['siteid']
            if inv_item.get('location'):
                enhanced_item['location'] = inv_item['location']
            if inv_item.get('issueunit'):
                enhanced_item['issueunit'] = inv_item['issueunit']
            if inv_item.get('orderunit'):
                enhanced_item['orderunit'] = inv_item['orderunit']
            if inv_item.get('curbaltotal') is not None:
                enhanced_item['curbaltotal'] = inv_item['curbaltotal']
            if inv_item.get('avblbalance') is not None:
                enhanced_item['avblbalance'] = inv_item['avblbalance']
            if inv_item.get('status'):
                enhanced_item['status'] = inv_item['status']
            if inv_item.get('itemtype'):
                enhanced_item['itemtype'] = inv_item['itemtype']
            if inv_item.get('itemsetid'):
                enhanced_item['itemsetid'] = inv_item['itemsetid']

            # Cost information (from MXAPIINVENTORY invcost)
            if inv_item.get('invcost'):
                invcost = inv_item['invcost']
                if invcost.get('avgcost') is not None:
                    enhanced_item['avgcost'] = invcost['avgcost']
                if invcost.get('lastcost') is not None:
                    enhanced_item['lastcost'] = invcost['lastcost']
                if invcost.get('stdcost') is not None:
                    enhanced_item['stdcost'] = invcost['stdcost']

            # Item master fields (from MXAPIITEM) - only if available
            if item_details:
                if item_details.get('description'):
                    enhanced_item['description'] = item_details['description']
                if item_details.get('unitcost') is not None:
                    enhanced_item['unitcost'] = item_details['unitcost']
                if item_details.get('rotating') is not None:
                    enhanced_item['rotating'] = item_details['rotating']
                if item_details.get('conditioncode'):
                    enhanced_item['conditioncode'] = item_details['conditioncode']

            # Source tracking
            enhanced_item['source'] = 'inventory_enhanced'
            
            enhanced_items.append(enhanced_item)
            
        self.logger.info(f"✅ INVENTORY: Enhanced {len(enhanced_items)} inventory items")
        return enhanced_items

    def _get_item_details(self, itemnum: str) -> Optional[Dict]:
        """
        Get item details from MXAPIITEM.
        
        Args:
            itemnum (str): Item number to look up
            
        Returns:
            Optional[Dict]: Item details or None if not found
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiitem"

        params = {
            "oslc.select": "itemnum,description,unitcost,rotating,conditioncode,status",
            "oslc.where": f'itemnum="{itemnum}" and status!="OBSOLETE"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        try:
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])
                if items:
                    return items[0]
                    
        except Exception as e:
            self.logger.warning(f"⚠️ INVENTORY: Failed to get item details for {itemnum}: {str(e)}")
            
        return None

    def _search_inventory_by_description(self, search_term: str, site_id: str, limit: int, page: int) -> List[Dict]:
        """
        Search for items by description in MXAPIITEM, then find matching inventory in MXAPIINVENTORY.

        Args:
            search_term (str): Search term for description
            site_id (str): Site ID for inventory lookup
            limit (int): Maximum number of results to return
            page (int): Page number for pagination

        Returns:
            List[Dict]: Inventory items found by description match
        """
        base_url = getattr(self.token_manager, 'base_url', '')

        # First, search MXAPIITEM for items with matching descriptions
        item_api_url = f"{base_url}/oslc/os/mxapiitem"
        search_term_clean = search_term.replace('"', '\\"')

        # Search for description matches in MXAPIITEM
        item_filter = f'status!="OBSOLETE" and description="%{search_term_clean}%"'

        item_params = {
            "oslc.select": "itemnum,description",
            "oslc.where": item_filter,
            "oslc.pageSize": str(limit * 2),  # Get more items to account for filtering
            "lean": "1"
        }

        try:
            response = self.token_manager.session.get(
                item_api_url,
                params=item_params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                if not items:
                    return []

                # Extract item numbers that match description
                matching_itemnums = [item.get('itemnum') for item in items if item.get('itemnum')]

                if not matching_itemnums:
                    return []

                # Now search MXAPIINVENTORY for these specific item numbers
                inventory_items = []
                for itemnum in matching_itemnums[:limit]:  # Limit to requested number
                    inv_items = self._search_inventory_by_itemnum(itemnum, site_id)
                    inventory_items.extend(inv_items)

                # Enhance with item data
                enhanced_items = self._enhance_with_item_data(inventory_items, site_id)

                self.logger.info(f"✅ INVENTORY: Found {len(enhanced_items)} items by description search")
                return enhanced_items[:limit]  # Apply final limit

            else:
                self.logger.warning(f"⚠️ INVENTORY: MXAPIITEM description search returned status {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ INVENTORY: Description search failed: {str(e)}")
            return []

    def _search_inventory_by_itemnum(self, itemnum: str, site_id: str) -> List[Dict]:
        """
        Search MXAPIINVENTORY for a specific item number.

        Args:
            itemnum (str): Exact item number to search for
            site_id (str): Site ID to filter inventory

        Returns:
            List[Dict]: Inventory items for the specific item number
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Search for exact itemnum match
        oslc_filter = f'siteid="{site_id}" and status!="OBSOLETE" and itemnum="{itemnum}"'

        # Use ONLY fields that actually exist in MXAPIINVENTORY API
        # Based on actual API response analysis for item 5975-60-V00-0001
        select_fields = [
            # Core inventory fields (confirmed available)
            "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
            "issueunit", "orderunit", "curbaltotal", "avblbalance",
            "costtype", "conditioncode", "inventoryid", "orgid",
            "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty",
            "opstime", "deliverytime", "admimtime", "statusdate",
            "issue1yrago", "issue2yrago", "issue3yrago", "issueytd",
            "expiredqty", "invreserveqty", "shippedqty"
        ]
        # REMOVED: binnum, storeloc, abc, conditionenabled, physcnt, physcntdate,
        # unitcost, currencycode - these are NOT available as direct fields

        # Add nested array fields - these will be accessed via array[0].field in processing
        nested_fields = [
            "invcost", "invbalances", "itemcondition", "invvendor"
        ]
        # REMOVED: invcost.*, invbalances.* - these don't work as dot notation
        # The actual data is in nested arrays that need special processing

        all_fields = select_fields + nested_fields

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": oslc_filter,
            "oslc.pageSize": "10",
            "lean": "1"
        }

        try:
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                # Process nested array fields into flat structure
                processed_items = self._process_inventory_data(items)

                # Enhance with ITEM data from MXAPIITEM
                enhanced_items = self._enhance_inventory_with_item_data(processed_items)
                return enhanced_items
            else:
                return []

        except Exception as e:
            self.logger.error(f"❌ INVENTORY: Itemnum search failed for {itemnum}: {str(e)}")
            return []

    def _search_item_master_for_direct_issue(self, search_term: str, site_id: str, limit: int, page: int, status_filter: str = '') -> List[Dict]:
        """
        Search MXAPIITEM for direct issue items when no inventory found.
        Uses status != 'OBSOLETE' filter.

        Args:
            search_term (str): Search term for item number or description
            site_id (str): Site ID for context
            limit (int): Maximum number of results to return
            page (int): Page number for pagination
            status_filter (str): Optional status filter

        Returns:
            List[Dict]: Direct issue items from MXAPIITEM
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiitem"

        # Build search filter for MXAPIITEM
        search_term_clean = search_term.replace('"', '\\"')

        # Build status filter
        if status_filter:
            status_condition = f'status="{status_filter}"'
        else:
            # Use status!="OBSOLETE" to exclude obsolete items and bring all other statuses
            status_condition = 'status!="OBSOLETE"'

        oslc_filter = f'itemnum="%{search_term_clean}%" and {status_condition}'

        # Calculate offset for pagination
        offset = page * limit

        params = {
            "oslc.select": "itemnum,description,unitcost,rotating,conditioncode,status,issueunit,orderunit",
            "oslc.where": oslc_filter,
            "oslc.pageSize": str(limit),
            "oslc.offset": str(offset),
            "lean": "1"
        }

        self.logger.info(f"🔍 INVENTORY: MXAPIITEM query: {oslc_filter}")

        try:
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                # Transform MXAPIITEM data to match inventory structure - only actual values
                direct_issue_items = []
                for item in items:
                    direct_issue_item = {}

                    # Required fields
                    if item.get('itemnum'):
                        direct_issue_item['itemnum'] = item['itemnum']
                    direct_issue_item['siteid'] = site_id  # Use provided site_id

                    # Optional fields - only if they exist
                    if item.get('issueunit'):
                        direct_issue_item['issueunit'] = item['issueunit']
                    if item.get('orderunit'):
                        direct_issue_item['orderunit'] = item['orderunit']
                    if item.get('status'):
                        direct_issue_item['status'] = item['status']
                    if item.get('lottype'):
                        direct_issue_item['lottype'] = item['lottype']
                    if item.get('description'):
                        direct_issue_item['description'] = item['description']
                    if item.get('unitcost') is not None:
                        direct_issue_item['unitcost'] = item['unitcost']
                    if item.get('rotating') is not None:
                        direct_issue_item['rotating'] = item['rotating']
                    if item.get('conditioncode'):
                        direct_issue_item['conditioncode'] = item['conditioncode']

                    # Add source tracking
                    direct_issue_item['source'] = 'direct_issue'
                    direct_issue_items.append(direct_issue_item)

                self.logger.info(f"✅ INVENTORY: MXAPIITEM returned {len(direct_issue_items)} direct issue items")
                return direct_issue_items
            else:
                self.logger.warning(f"⚠️ INVENTORY: MXAPIITEM returned status {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"❌ INVENTORY: MXAPIITEM search failed: {str(e)}")
            return []

    def get_inventory_by_location(self, site_id: str, location: str, limit: int = 20, page: int = 0) -> Tuple[List[Dict], Dict]:
        """
        Get inventory items by location with status != 'OBSOLETE' filter.

        Args:
            site_id (str): Site ID to filter inventory
            location (str): Location to filter by
            limit (int): Maximum number of results to return per page
            page (int): Page number for pagination (0-based)

        Returns:
            Tuple[List[Dict], Dict]: (inventory_items, metadata)
        """
        start_time = time.time()
        cache_key = f"location_{site_id}_{location}_{limit}_{page}"

        # Check cache first
        if cache_key in self._search_cache:
            cached_data = self._search_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_timeout:
                self.logger.info(f"🔍 INVENTORY: Cache hit for location: {location}")
                cached_data['data'][1]['source'] = 'cache'
                return cached_data['data']

        self.logger.info(f"🔍 INVENTORY: Getting inventory for location '{location}' in site '{site_id}' (page {page}, limit {limit})")

        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiinventory"

            # Build filter for location with NOT OBSOLETE status
            oslc_filter = f'siteid="{site_id}" and location="{location}" and status!="OBSOLETE"'

            # Calculate offset for pagination
            offset = page * limit

            # Select ONLY fields that actually exist in MXAPIINVENTORY API
            select_fields = [
                # Core inventory fields (confirmed available)
                "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
                "issueunit", "orderunit", "curbaltotal", "avblbalance",
                "costtype", "conditioncode", "inventoryid", "orgid",
                "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty",
                "opstime", "deliverytime", "admimtime", "statusdate",
                "issue1yrago", "issue2yrago", "issue3yrago", "issueytd",
                "expiredqty", "invreserveqty", "shippedqty"
            ]

            # Add nested array fields - these will be accessed via array[0].field in processing
            nested_fields = [
                "invcost", "invbalances", "itemcondition", "invvendor"
            ]

            all_fields = select_fields + nested_fields

            params = {
                "oslc.select": ",".join(all_fields),
                "oslc.where": oslc_filter,
                "oslc.pageSize": str(limit),
                "oslc.offset": str(offset),
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                # Process nested array fields into flat structure
                processed_items = self._process_inventory_data(items)

                # Enhance with ITEM data from MXAPIITEM
                enhanced_items = self._enhance_inventory_with_item_data(processed_items)

                # Calculate pagination metadata
                total_items = len(enhanced_items)
                total_pages = max(1, (total_items + limit - 1) // limit) if total_items > 0 else 0

                result_data = (enhanced_items, {
                    'load_time': time.time() - start_time,
                    'source': 'api',
                    'count': total_items,
                    'page': page,
                    'total_pages': total_pages,
                    'location': location,
                    'site_id': site_id
                })

                # Cache the results
                self._search_cache[cache_key] = {
                    'data': result_data,
                    'timestamp': time.time()
                }

                self.logger.info(f"✅ INVENTORY: Found {total_items} items for location {location} in {time.time() - start_time:.2f}s")
                return result_data
            else:
                self.logger.warning(f"⚠️ INVENTORY: Location query returned status {response.status_code}")
                return [], {
                    'load_time': time.time() - start_time,
                    'source': 'error',
                    'count': 0,
                    'page': page,
                    'total_pages': 0,
                    'error': f'API returned status {response.status_code}'
                }

        except Exception as e:
            self.logger.error(f"❌ INVENTORY: Location query failed: {str(e)}")
            return [], {
                'load_time': time.time() - start_time,
                'source': 'error',
                'count': 0,
                'page': page,
                'total_pages': 0,
                'error': str(e)
            }

    def _process_inventory_data(self, items: List[Dict]) -> List[Dict]:
        """
        Process inventory data to extract ALL nested array fields into comprehensive flat structure.
        Maps all MXAPIINVENTORY fields and related table data as per user requirements.

        Args:
            items (List[Dict]): Raw inventory items from MXAPIINVENTORY

        Returns:
            List[Dict]: Processed inventory items with ALL flattened nested data
        """
        processed_items = []

        for item in items:
            processed_item = item.copy()

            # Process INVCOST array - extract ALL cost fields
            if 'invcost' in item and isinstance(item['invcost'], list) and item['invcost']:
                cost_data = item['invcost'][0]  # Get first cost record
                # Map all INVCOST fields as per user requirements
                processed_item['invcost_avgcost'] = cost_data.get('avgcost')
                processed_item['invcost_lastcost'] = cost_data.get('lastcost')
                processed_item['invcost_stdcost'] = cost_data.get('stdcost')
                processed_item['invcost_conditioncode'] = cost_data.get('conditioncode')
                processed_item['invcost_orgid'] = cost_data.get('orgid')
                processed_item['invcost_invcostid'] = cost_data.get('invcostid')
                processed_item['invcost_condrate'] = cost_data.get('condrate')

            # Process INVBALANCES array - extract ALL balance records for expandable display
            if 'invbalances' in item and isinstance(item['invbalances'], list) and item['invbalances']:
                # Store all balance records for expandable functionality
                processed_item['invbalances_records'] = []

                for balance_data in item['invbalances']:
                    balance_record = {
                        'curbal': balance_data.get('curbal'),
                        'physcnt': balance_data.get('physcnt'),
                        'physcntdate': balance_data.get('physcntdate'),
                        'conditioncode': balance_data.get('conditioncode'),
                        'reconciled': balance_data.get('reconciled'),
                        'invbalancesid': balance_data.get('invbalancesid'),
                        'stagedcurbal': balance_data.get('stagedcurbal'),
                        'expiredqty': balance_data.get('expiredqty'),
                        'stagingbin': balance_data.get('stagingbin'),
                        'lotnum': balance_data.get('lotnum'),
                        'binnum': balance_data.get('binnum'),
                        'location': balance_data.get('location'),
                        'unitcost': balance_data.get('unitcost'),
                        'reservedqty': balance_data.get('reservedqty'),
                        'hardreservedqty': balance_data.get('hardreservedqty'),
                        'softreservedqty': balance_data.get('softreservedqty'),
                        'storeloc': balance_data.get('storeloc'),
                        'issueunit': balance_data.get('issueunit')
                    }
                    processed_item['invbalances_records'].append(balance_record)

                # Log balance records count for debugging
                self.logger.info(f"🔍 INVENTORY: Item {processed_item.get('itemnum', 'unknown')} has {len(processed_item['invbalances_records'])} balance records")

                # Keep first balance record fields for backward compatibility
                balance_data = item['invbalances'][0]  # Get first balance record
                processed_item['invbalances_curbal'] = balance_data.get('curbal')
                processed_item['invbalances_physcnt'] = balance_data.get('physcnt')
                processed_item['invbalances_physcntdate'] = balance_data.get('physcntdate')
                processed_item['invbalances_conditioncode'] = balance_data.get('conditioncode')
                processed_item['invbalances_reconciled'] = balance_data.get('reconciled')
                processed_item['invbalances_invbalancesid'] = balance_data.get('invbalancesid')
                processed_item['invbalances_stagedcurbal'] = balance_data.get('stagedcurbal')
                processed_item['invbalances_expiredqty'] = balance_data.get('expiredqty')
                processed_item['invbalances_stagingbin'] = balance_data.get('stagingbin')
                processed_item['invbalances_lotnum'] = balance_data.get('lotnum')
                processed_item['invbalances_binnum'] = balance_data.get('binnum')
                processed_item['invbalances_location'] = balance_data.get('location')
                processed_item['invbalances_unitcost'] = balance_data.get('unitcost')
            else:
                # No balance records found
                processed_item['invbalances_records'] = []
                self.logger.info(f"🔍 INVENTORY: Item {processed_item.get('itemnum', 'unknown')} has no balance records")

            # Process INVVENDOR array - extract ALL vendor fields
            if 'invvendor' in item and isinstance(item['invvendor'], list) and item['invvendor']:
                vendor_data = item['invvendor'][0]  # Get first vendor record
                # Map all INVVENDOR fields as per user requirements
                processed_item['invvendor_vendor'] = vendor_data.get('vendor')
                processed_item['invvendor_manufacturer'] = vendor_data.get('manufacturer')
                processed_item['invvendor_modelnum'] = vendor_data.get('modelnum')
                processed_item['invvendor_currencycode'] = vendor_data.get('currencycode')
                processed_item['invvendor_contractnum'] = vendor_data.get('contractnum')
                processed_item['invvendor_invvendorid'] = vendor_data.get('invvendorid')

            # Process ITEMCONDITION array - extract condition details
            if 'itemcondition' in item and isinstance(item['itemcondition'], list) and item['itemcondition']:
                condition_data = item['itemcondition'][0]  # Get first condition record
                processed_item['itemcondition_description'] = condition_data.get('description')
                processed_item['itemcondition_conditioncode'] = condition_data.get('conditioncode')
                processed_item['itemcondition_condrate'] = condition_data.get('condrate')
                processed_item['itemcondition_itemconditionid'] = condition_data.get('itemconditionid')

            processed_items.append(processed_item)

        return processed_items

    def _get_item_data_from_mxapiitem(self, itemnum: str) -> Dict:
        """
        Get ITEM table data from MXAPIITEM endpoint.
        This includes description, rotating, lottype, conditionenabled, etc.

        Args:
            itemnum (str): Item number to look up

        Returns:
            Dict: Item data from MXAPIITEM or empty dict if not found
        """
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiitem"

            # Select ALL available ITEM fields
            item_fields = [
                "itemnum", "description", "status", "itemtype", "itemsetid",
                "issueunit", "orderunit", "rotating", "lottype", "conditionenabled",
                "unitcost", "commoditygroup", "commodity", "glaccount",
                "controlacc", "shrinkageacc", "abctype", "statusdate",
                "changeby", "changedate", "hasld", "langcode"
            ]

            params = {
                "oslc.select": ",".join(item_fields),
                "oslc.where": f'itemnum="{itemnum}" and status!="OBSOLETE"',
                "oslc.pageSize": "1",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])
                if items:
                    item_data = items[0]
                    # Prefix all ITEM fields to distinguish from INVENTORY fields
                    prefixed_item_data = {}
                    for key, value in item_data.items():
                        prefixed_item_data[f'item_{key}'] = value
                    return prefixed_item_data

        except Exception as e:
            self.logger.warning(f"⚠️ INVENTORY: Failed to get item data for {itemnum}: {str(e)}")

        return {}

    def _enhance_inventory_with_item_data(self, inventory_items: List[Dict]) -> List[Dict]:
        """
        Enhance inventory items with ITEM data from MXAPIITEM.

        Args:
            inventory_items (List[Dict]): Processed inventory items from MXAPIINVENTORY

        Returns:
            List[Dict]: Enhanced inventory items with ITEM data included
        """
        enhanced_items = []

        for inv_item in inventory_items:
            enhanced_item = inv_item.copy()

            # Get ITEM data from MXAPIITEM
            itemnum = inv_item.get('itemnum')
            if itemnum:
                item_data = self._get_item_data_from_mxapiitem(itemnum)
                enhanced_item.update(item_data)

            enhanced_items.append(enhanced_item)

        return enhanced_items

    def _search_mxapiitem_comprehensive(self, search_term: str, limit: int, page: int, status_filter: str = '') -> List[Dict]:
        """
        Comprehensive search using MXAPIITEM endpoint with multiple search strategies.
        Supports partial description, partial itemnum, and exact itemnum searches.
        Uses status != 'OBSOLETE' filter as requested.

        Args:
            search_term (str): Search term for item number or description
            limit (int): Maximum number of results to return
            page (int): Page number for pagination
            status_filter (str): Optional status filter

        Returns:
            List[Dict]: Items found from MXAPIITEM
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        if not base_url:
            self.logger.error(f"🔍 INVENTORY: MXAPIITEM search failed: No base_url available")
            return []

        api_url = f"{base_url}/oslc/os/mxapiitem"

        # Clean search term for OSLC query
        search_term_clean = search_term.replace('"', '\\"')

        # Build status filter
        if status_filter:
            status_condition = f'status="{status_filter}"'
        else:
            # Use status!="OBSOLETE" to exclude obsolete items and bring all other statuses
            status_condition = 'status!="OBSOLETE"'

        # Search strategies - ONLY non-OBSOLETE status items
        search_filters = [
            # 1. Exact item number match
            f'itemnum="{search_term_clean}" and {status_condition}',
            # 2. Partial item number match using LIKE pattern
            f'itemnum="%{search_term_clean}%" and {status_condition}',
            # 3. Partial description match using LIKE pattern
            f'description="%{search_term_clean}%" and {status_condition}'
        ]

        # Select fields from MXAPIITEM
        select_fields = [
            "itemnum", "description", "issueunit", "orderunit", "itemsetid",
            "itemtype", "status", "unitcost", "rotating", "conditionenabled",
            "lottype", "commoditygroup", "commodity", "abctype"
        ]

        all_items = []
        found_item_nums = set()  # Track found items to avoid duplicates

        # Calculate offset for pagination
        offset = page * limit

        for i, oslc_filter in enumerate(search_filters):
            try:
                # Check if session is available
                if not hasattr(self.token_manager, 'session') or not self.token_manager.session:
                    self.logger.error(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1} error: No session available")
                    continue

                params = {
                    "oslc.select": ",".join(select_fields),
                    "oslc.where": oslc_filter,
                    "oslc.pageSize": str(limit * 2),  # Get more items to account for filtering
                    "oslc.offset": str(offset) if i == 0 else "0",  # Only apply offset to first search
                    "lean": "1"
                }

                self.logger.info(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1}: {oslc_filter}")

                response = self.token_manager.session.get(
                    api_url,
                    params=params,
                    timeout=(3.05, 10),
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    items = data.get('member', [])

                    for item in items:
                        itemnum = item.get('itemnum', '')
                        if itemnum and itemnum not in found_item_nums:
                            found_item_nums.add(itemnum)

                            # Process the item data
                            processed_item = {
                                'itemnum': itemnum,
                                'description': item.get('description', ''),
                                'status': item.get('status', ''),
                                'itemtype': item.get('itemtype', ''),
                                'issueunit': item.get('issueunit', ''),
                                'orderunit': item.get('orderunit', ''),
                                'unitcost': item.get('unitcost', 0),
                                'rotating': item.get('rotating', False),
                                'conditionenabled': item.get('conditionenabled', False),
                                'lottype': item.get('lottype', ''),
                                'commoditygroup': item.get('commoditygroup', ''),
                                'commodity': item.get('commodity', ''),
                                'abctype': item.get('abctype', ''),
                                'itemsetid': item.get('itemsetid', ''),
                                'source': 'mxapiitem'
                            }
                            all_items.append(processed_item)

                            # Stop if we have enough items
                            if len(all_items) >= limit:
                                break
                else:
                    self.logger.warning(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1} failed: {response.status_code}")

            except Exception as e:
                self.logger.error(f"🔍 INVENTORY: MXAPIITEM search strategy {i+1} error: {str(e)}")
                continue

            # Stop if we have enough items
            if len(all_items) >= limit:
                break

        self.logger.info(f"🔍 INVENTORY: MXAPIITEM comprehensive search found {len(all_items)} items")
        return all_items

    def _sort_by_relevance(self, items: List[Dict], search_term: str) -> List[Dict]:
        """
        Sort items by relevance to search term.
        Exact matches first, then partial matches.

        Args:
            items (List[Dict]): Items to sort
            search_term (str): Original search term

        Returns:
            List[Dict]: Sorted items
        """
        search_term_lower = search_term.lower()

        def relevance_score(item):
            itemnum = item.get('itemnum', '').lower()
            description = item.get('description', '').lower()

            # Exact itemnum match gets highest score
            if itemnum == search_term_lower:
                return 1
            # Exact description match gets second highest
            elif description == search_term_lower:
                return 2
            # Itemnum starts with search term
            elif itemnum.startswith(search_term_lower):
                return 3
            # Description starts with search term
            elif description.startswith(search_term_lower):
                return 4
            # Itemnum contains search term
            elif search_term_lower in itemnum:
                return 5
            # Description contains search term
            elif search_term_lower in description:
                return 6
            # Default score
            else:
                return 7

        return sorted(items, key=relevance_score)

    def _filter_through_mxapiinventory(self, mxapiitem_results: List[Dict], site_id: str) -> List[Dict]:
        """
        Filter MXAPIITEM results through MXAPIINVENTORY to ensure items exist in inventory for the specified site.
        Uses OSLC endpoint with siteid and status != 'OBSOLETE' filters.

        Args:
            mxapiitem_results (List[Dict]): Items from MXAPIITEM search
            site_id (str): Site ID to filter inventory

        Returns:
            List[Dict]: Items that exist in both MXAPIITEM and MXAPIINVENTORY for the site
        """
        if not mxapiitem_results or not site_id:
            return []

        base_url = getattr(self.token_manager, 'base_url', '')
        if not base_url:
            self.logger.error(f"🔍 INVENTORY: MXAPIINVENTORY filter failed: No base_url available")
            return []

        # Check if session is available
        if not hasattr(self.token_manager, 'session') or not self.token_manager.session:
            self.logger.error(f"🔍 INVENTORY: MXAPIINVENTORY filter failed: No session available")
            return []

        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Extract item numbers from MXAPIITEM results
        item_numbers = [item.get('itemnum') for item in mxapiitem_results if item.get('itemnum')]

        if not item_numbers:
            return []

        self.logger.info(f"🔍 INVENTORY: Filtering {len(item_numbers)} items through MXAPIINVENTORY for site '{site_id}'")

        filtered_items = []
        inventory_records = []  # Store all inventory records, not just one per item

        # Process items one by one to avoid OSLC filter complexity issues
        # OSLC doesn't support parentheses grouping, so we need to query each item individually
        for itemnum in item_numbers:
            if not itemnum:
                continue

            # Build simple OSLC filter for single item
            oslc_filter = f'siteid="{site_id}" and status!="OBSOLETE" and itemnum="{itemnum}"'

            # Select comprehensive inventory fields
            select_fields = [
                "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
                "issueunit", "orderunit", "curbaltotal", "avblbalance",
                "costtype", "conditioncode", "inventoryid", "orgid",
                "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty"
            ]

            # Add nested array fields for comprehensive data
            nested_fields = ["invcost", "invbalances", "itemcondition", "invvendor"]
            all_fields = select_fields + nested_fields

            params = {
                "oslc.select": ",".join(all_fields),
                "oslc.where": oslc_filter,
                "oslc.pageSize": "10",
                "lean": "1"
            }

            try:
                self.logger.info(f"🔍 INVENTORY: Checking item '{itemnum}' in MXAPIINVENTORY for site '{site_id}'")

                response = self.token_manager.session.get(
                    api_url,
                    params=params,
                    timeout=(3.05, 15),
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    inventory_items = data.get('member', [])

                    # Process and store ALL inventory records (not just one per item)
                    for inv_item in inventory_items:
                        found_itemnum = inv_item.get('itemnum')
                        if found_itemnum:
                            # Process nested inventory data
                            processed_inv_item = self._process_single_inventory_item(inv_item)
                            inventory_records.append(processed_inv_item)  # Append all records

                    if inventory_items:
                        self.logger.info(f"🔍 INVENTORY: Found item '{itemnum}' in inventory")
                    else:
                        self.logger.info(f"🔍 INVENTORY: Item '{itemnum}' not found in inventory for site '{site_id}'")
                else:
                    self.logger.warning(f"🔍 INVENTORY: Item '{itemnum}' query failed with status {response.status_code}")
                    # Log the response text for debugging
                    try:
                        error_text = response.text[:500]  # First 500 chars
                        self.logger.warning(f"🔍 INVENTORY: Error response: {error_text}")
                    except:
                        pass

            except Exception as e:
                self.logger.error(f"🔍 INVENTORY: Item '{itemnum}' query error: {str(e)}")
                continue

        # Create one result row for EACH inventory record (not one per item)
        # This ensures that if an item has 8 inventory records, we show 8 rows in the table
        for inventory_record in inventory_records:
            itemnum = inventory_record.get('itemnum')

            # Find the corresponding item master data
            item_master_data = None
            for item in mxapiitem_results:
                if item.get('itemnum') == itemnum:
                    item_master_data = item
                    break

            if item_master_data:
                # Merge item master data with this specific inventory record
                merged_item = item_master_data.copy()

                # Add inventory-specific fields from this specific record
                merged_item.update({
                    'siteid': inventory_record.get('siteid'),
                    'location': inventory_record.get('location'),
                    'curbaltotal': inventory_record.get('curbaltotal'),
                    'avblbalance': inventory_record.get('avblbalance'),
                    'inventory_status': inventory_record.get('status'),
                    'inventoryid': inventory_record.get('inventoryid'),
                    'maxlevel': inventory_record.get('maxlevel'),
                    'minlevel': inventory_record.get('minlevel'),
                    'reorder': inventory_record.get('reorder'),
                    'reservedqty': inventory_record.get('reservedqty'),
                    'source': 'mxapiitem_filtered_inventory'
                })

                # Add cost data if available
                if inventory_record.get('invcost_avgcost') is not None:
                    merged_item['avgcost'] = inventory_record.get('invcost_avgcost')
                if inventory_record.get('invcost_lastcost') is not None:
                    merged_item['lastcost'] = inventory_record.get('invcost_lastcost')
                if inventory_record.get('invcost_stdcost') is not None:
                    merged_item['stdcost'] = inventory_record.get('invcost_stdcost')

                # Add balance data if available
                if inventory_record.get('invbalances_physcnt') is not None:
                    merged_item['physcnt'] = inventory_record.get('invbalances_physcnt')
                if inventory_record.get('invbalances_binnum'):
                    merged_item['binnum'] = inventory_record.get('invbalances_binnum')

                # Add ALL balance records for expandable display
                if inventory_record.get('invbalances_records'):
                    merged_item['invbalances_records'] = inventory_record.get('invbalances_records')
                else:
                    merged_item['invbalances_records'] = []

                # Add vendor data if available
                if inventory_record.get('invvendor_vendor'):
                    merged_item['vendor'] = inventory_record.get('invvendor_vendor')
                if inventory_record.get('invvendor_manufacturer'):
                    merged_item['manufacturer'] = inventory_record.get('invvendor_manufacturer')

                filtered_items.append(merged_item)

        self.logger.info(f"🔍 INVENTORY: Filtered result: {len(filtered_items)} items exist in inventory for site '{site_id}'")
        return filtered_items

    def _process_single_inventory_item(self, inv_item: Dict) -> Dict:
        """
        Process a single inventory item to extract nested array data.

        Args:
            inv_item (Dict): Raw inventory item from MXAPIINVENTORY

        Returns:
            Dict: Processed inventory item with flattened nested data
        """
        processed_item = inv_item.copy()

        # Process INVCOST array
        if 'invcost' in inv_item and isinstance(inv_item['invcost'], list) and inv_item['invcost']:
            cost_data = inv_item['invcost'][0]
            processed_item['invcost_avgcost'] = cost_data.get('avgcost')
            processed_item['invcost_lastcost'] = cost_data.get('lastcost')
            processed_item['invcost_stdcost'] = cost_data.get('stdcost')

        # Process INVBALANCES array - extract ALL balance records for expandable display
        if 'invbalances' in inv_item and isinstance(inv_item['invbalances'], list) and inv_item['invbalances']:
            # Store all balance records for expandable functionality
            processed_item['invbalances_records'] = []

            for balance_data in inv_item['invbalances']:
                balance_record = {
                    'curbal': balance_data.get('curbal'),
                    'physcnt': balance_data.get('physcnt'),
                    'physcntdate': balance_data.get('physcntdate'),
                    'conditioncode': balance_data.get('conditioncode'),
                    'reconciled': balance_data.get('reconciled'),
                    'invbalancesid': balance_data.get('invbalancesid'),
                    'stagedcurbal': balance_data.get('stagedcurbal'),
                    'expiredqty': balance_data.get('expiredqty'),
                    'stagingbin': balance_data.get('stagingbin'),
                    'lotnum': balance_data.get('lotnum'),
                    'binnum': balance_data.get('binnum'),
                    'location': balance_data.get('location'),
                    'unitcost': balance_data.get('unitcost'),
                    'reservedqty': balance_data.get('reservedqty'),
                    'hardreservedqty': balance_data.get('hardreservedqty'),
                    'softreservedqty': balance_data.get('softreservedqty'),
                    'storeloc': balance_data.get('storeloc'),
                    'issueunit': balance_data.get('issueunit')
                }
                processed_item['invbalances_records'].append(balance_record)

            # Log balance records count for debugging
            self.logger.info(f"🔍 INVENTORY: Item {processed_item.get('itemnum', 'unknown')} has {len(processed_item['invbalances_records'])} balance records")

            # Keep first balance record fields for backward compatibility
            balance_data = inv_item['invbalances'][0]
            processed_item['invbalances_physcnt'] = balance_data.get('physcnt')
            processed_item['invbalances_binnum'] = balance_data.get('binnum')
            processed_item['invbalances_unitcost'] = balance_data.get('unitcost')
        else:
            # No balance records found
            processed_item['invbalances_records'] = []
            self.logger.info(f"🔍 INVENTORY: Item {processed_item.get('itemnum', 'unknown')} has no balance records")

        # Process INVVENDOR array
        if 'invvendor' in inv_item and isinstance(inv_item['invvendor'], list) and inv_item['invvendor']:
            vendor_data = inv_item['invvendor'][0]
            processed_item['invvendor_vendor'] = vendor_data.get('vendor')
            processed_item['invvendor_manufacturer'] = vendor_data.get('manufacturer')
            processed_item['invvendor_modelnum'] = vendor_data.get('modelnum')

        return processed_item

    def clear_cache(self):
        """Clear the search cache."""
        self._search_cache.clear()
        self.logger.info("🧹 INVENTORY: Search cache cleared")
