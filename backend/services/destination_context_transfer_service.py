"""
Destination Context Transfer Service for Maximo Integration

This service handles inventory transfers using destination site context
in the top-level record, as specified by the user.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List

class DestinationContextTransferService:
    """Service for handling transfers with destination site context"""
    
    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        
    def submit_transfer_with_destination_context(self, transfer_data: Dict) -> Dict:
        """
        Submit transfer using destination site context in top-level record
        
        Args:
            transfer_data (Dict): Transfer information
            
        Returns:
            Dict: Result of the transfer submission
        """
        try:
            # Validate authentication
            if not self.token_manager.is_logged_in():
                return {
                    'success': False,
                    'error': 'Not authenticated with <PERSON><PERSON>'
                }
            
            # Build payload with destination site context
            payload = self._build_destination_context_payload(transfer_data)
            
            # Submit to Maximo
            result = self._submit_to_maximo(payload)
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Exception: {str(e)}")
            return {
                'success': False,
                'error': f'Transfer submission failed: {str(e)}'
            }
    
    def _build_destination_context_payload(self, transfer_data: Dict) -> List[Dict]:
        """
        Build payload with destination site context in top-level record
        
        This uses the exact structure specified by the user:
        - Top-level siteid: destination site (IKWAJ)
        - Top-level location: destination storeroom (KWAJ-1058)
        - Include toissueunit for unit conversion
        """
        # Extract fields
        itemnum = transfer_data.get('itemnum', '')
        quantity = float(transfer_data.get('quantity', 1.0))
        
        from_siteid = transfer_data.get('from_siteid', '')
        to_siteid = transfer_data.get('to_siteid', '')
        from_storeroom = transfer_data.get('from_storeroom', '')
        to_storeroom = transfer_data.get('to_storeroom', '')
        
        from_issue_unit = transfer_data.get('from_issue_unit', 'EA')  # Use EA as universal default
        to_issue_unit = transfer_data.get('to_issue_unit', 'EA')    # Use EA as universal default
        
        from_bin = transfer_data.get('from_bin', 'DEFAULT')
        to_bin = transfer_data.get('to_bin', 'DEFAULT')
        from_lot = transfer_data.get('from_lot', 'DEFAULT')
        to_lot = transfer_data.get('to_lot', 'DEFAULT')
        from_condition = transfer_data.get('from_condition', 'A1')
        to_condition = transfer_data.get('to_condition', 'A1')
        
        # Build payload with DESTINATION site context (user's requested structure)
        payload = [
            {
                "_action": "AddChange",
                "itemnum": itemnum,
                "itemsetid": "ITEMSET",
                "siteid": to_siteid,  # DESTINATION site (IKWAJ)
                "location": to_storeroom,  # DESTINATION location (KWAJ-1058)
                "issueunit": from_issue_unit,
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": itemnum,
                        "issuetype": "TRANSFER",
                        "quantity": quantity,
                        "fromsiteid": from_siteid,
                        "tositeid": to_siteid,
                        "fromstoreloc": from_storeroom,
                        "tostoreloc": to_storeroom,
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": from_issue_unit,
                        "frombinnum": from_bin,
                        "tobinnum": to_bin,
                        "fromlotnum": from_lot,
                        "tolotnum": to_lot,
                        "fromconditioncode": from_condition,
                        "toconditioncode": to_condition,
                        "toissueunit": to_issue_unit  # KEY: Include toissueunit for conversion
                    }
                ]
            }
        ]
        
        self.logger.info(f"🔧 DESTINATION CONTEXT: Built payload with destination site context")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Top-level siteid: {to_siteid}")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Top-level location: {to_storeroom}")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Payload: {json.dumps(payload, indent=2)}")
        
        return payload
    
    def _submit_to_maximo(self, payload: List[Dict]) -> Dict:
        """Submit payload to Maximo API"""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
            
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            }
            
            self.logger.info(f"🔄 DESTINATION CONTEXT: Submitting to {api_url}")
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                headers=headers,
                timeout=(5.0, 30)
            )
            
            self.logger.info(f"📊 DESTINATION CONTEXT: Response status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    self.logger.info(f"📋 DESTINATION CONTEXT: Response: {json.dumps(response_data, indent=2)}")
                    
                    # Check for success (204 status in response)
                    if isinstance(response_data, list) and len(response_data) > 0:
                        first_item = response_data[0]
                        if first_item.get('_responsemeta', {}).get('status') == '204':
                            return {
                                'success': True,
                                'message': 'Destination context transfer submitted successfully to Maximo',
                                'response': response_data,
                                'status_code': response.status_code,
                                'timestamp': datetime.now().isoformat()
                            }
                        else:
                            # Check for errors
                            error_info = None
                            if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                                error = first_item['_responsedata']['Error']
                                error_info = f"{error.get('reasonCode')} - {error.get('message')}"
                            
                            return {
                                'success': False,
                                'message': 'Destination context transfer failed',
                                'response': response_data,
                                'status_code': response.status_code,
                                'error': error_info,
                                'timestamp': datetime.now().isoformat()
                            }
                    
                    return {
                        'success': False,
                        'message': 'Unexpected response format',
                        'response': response_data,
                        'status_code': response.status_code,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Invalid JSON response',
                        'response_text': response.text,
                        'status_code': response.status_code
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP error: {response.status_code}',
                    'response_text': response.text,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Request failed: {str(e)}'
            }
