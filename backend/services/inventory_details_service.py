"""
Inventory Details Service
Handles fetching comprehensive inventory details from MXAPIITEM and MXAPIINVENTORY endpoints
"""

import logging
import time
from typing import Dict, Optional, Tuple, List


class InventoryDetailsService:
    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        self._details_cache = {}
        self.cache_duration = 300  # 5 minutes

    def get_inventory_details(self, itemnum: str, siteid: str, inventoryid: str = None) -> Tuple[Dict, Dict]:
        """
        Get comprehensive inventory details from both MXAPIITEM and MXAPIINVENTORY endpoints.

        Args:
            itemnum (str): Item number
            siteid (str): Site ID
            inventoryid (str, optional): Specific inventory ID to get details for

        Returns:
            Tuple[Dict, Dict]: (inventory_details, metadata)
        """
        start_time = time.time()
        
        if not itemnum or not siteid:
            return {}, {'error': 'Item number and site ID are required', 'load_time': 0}

        cache_key = f"{itemnum}_{siteid}_{inventoryid or 'all'}"
        
        # Check cache first
        if cache_key in self._details_cache:
            cached_result = self._details_cache[cache_key]
            if time.time() - cached_result['timestamp'] < self.cache_duration:
                self.logger.info(f"📦 INVENTORY DETAILS: Cache hit for '{itemnum}' in site '{siteid}'")
                return cached_result['details'], {
                    **cached_result['metadata'],
                    'load_time': time.time() - start_time,
                    'source': 'cache'
                }

        self.logger.info(f"📦 INVENTORY DETAILS: Fetching details for '{itemnum}' in site '{siteid}'")

        try:
            # Step 1: Get item master data from MXAPIITEM
            item_data = self._get_item_master_data(itemnum)
            
            # Step 2: Get inventory data from MXAPIINVENTORY (returns list of records)
            inventory_records = self._get_inventory_data(itemnum, siteid, inventoryid)

            # Step 3: Merge and organize the data
            comprehensive_details = self._merge_and_organize_data(item_data, inventory_records)

            load_time = time.time() - start_time
            metadata = {
                'load_time': load_time,
                'source': 'api',
                'itemnum': itemnum,
                'siteid': siteid,
                'has_item_data': bool(item_data),
                'has_inventory_data': bool(inventory_records),
                'inventory_records_count': len(inventory_records)
            }

            # Cache the result
            self._details_cache[cache_key] = {
                'details': comprehensive_details,
                'metadata': metadata,
                'timestamp': time.time()
            }

            self.logger.info(f"📦 INVENTORY DETAILS: Retrieved details for '{itemnum}' in {load_time:.2f}s")
            return comprehensive_details, metadata

        except Exception as e:
            self.logger.error(f"❌ INVENTORY DETAILS: Failed to get details for '{itemnum}': {str(e)}")
            return {}, {
                'load_time': time.time() - start_time,
                'source': 'error',
                'error': str(e)
            }

    def _get_item_master_data(self, itemnum: str) -> Dict:
        """
        Get item master data from MXAPIITEM endpoint.
        
        Args:
            itemnum (str): Item number
            
        Returns:
            Dict: Item master data
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiitem"

        # Select ITEM fields as per mapping
        select_fields = [
            "itemnum", "description", "status", "itemtype", "itemsetid",
            "rotating", "lottype", "conditionenabled", "issueunit", "orderunit"
        ]

        params = {
            "oslc.select": ",".join(select_fields),
            "oslc.where": f'itemnum="{itemnum}"',
            "oslc.pageSize": "1",
            "lean": "1"
        }

        try:
            self.logger.info(f"📦 INVENTORY DETAILS: Fetching MXAPIITEM data for '{itemnum}'")
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 10),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])
                if items:
                    item_data = items[0]
                    self.logger.info(f"📦 INVENTORY DETAILS: Found MXAPIITEM data for '{itemnum}'")
                    return item_data
                else:
                    self.logger.warning(f"📦 INVENTORY DETAILS: No MXAPIITEM data found for '{itemnum}'")
                    return {}
            else:
                self.logger.warning(f"📦 INVENTORY DETAILS: MXAPIITEM query failed with status {response.status_code}")
                return {}

        except Exception as e:
            self.logger.error(f"📦 INVENTORY DETAILS: MXAPIITEM query error: {str(e)}")
            return {}

    def _get_inventory_data(self, itemnum: str, siteid: str, inventoryid: str = None) -> List[Dict]:
        """
        Get inventory data from MXAPIINVENTORY endpoint.
        Returns specific inventory record if inventoryid provided, otherwise all records.

        Args:
            itemnum (str): Item number
            siteid (str): Site ID
            inventoryid (str, optional): Specific inventory ID to filter by

        Returns:
            List[Dict]: List of inventory records with nested arrays processed
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiinventory"

        # Select comprehensive INVENTORY fields and nested arrays
        select_fields = [
            "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
            "issueunit", "orderunit", "curbaltotal", "avblbalance", "reservedqty",
            "hardreservedqty", "softreservedqty", "minlevel", "maxlevel", "orderqty",
            "deliverytime", "benchstock", "shrinkageacc", "glaccount", "controlacc",
            "abc", "statusdate", "lastissuedate", "inventoryid", "orgid"
        ]

        # Add nested array fields for comprehensive data
        nested_fields = ["invcost", "invbalances", "invvendor"]
        all_fields = select_fields + nested_fields

        # Build OSLC where clause (don't filter by inventoryid in OSLC to avoid server errors)
        where_clause = f'itemnum="{itemnum}" and siteid="{siteid}" and status!="OBSOLETE"'

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": where_clause,
            "oslc.pageSize": "50",  # Get up to 50 inventory records for the item
            "lean": "1"
        }

        try:
            self.logger.info(f"📦 INVENTORY DETAILS: Fetching MXAPIINVENTORY data for '{itemnum}' in site '{siteid}'")
            
            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])
                if items:
                    # Process ALL inventory records, not just the first one
                    processed_records = []
                    for inventory_data in items:
                        processed_data = self._process_nested_inventory_data(inventory_data)
                        processed_records.append(processed_data)

                    # Filter by inventoryid if provided (after getting results to avoid OSLC server errors)
                    if inventoryid:
                        filtered_records = [record for record in processed_records if str(record.get('inventoryid')) == str(inventoryid)]
                        if filtered_records:
                            self.logger.info(f"📦 INVENTORY DETAILS: Found specific inventory record {inventoryid} for '{itemnum}' in site '{siteid}'")
                            return filtered_records
                        else:
                            self.logger.warning(f"📦 INVENTORY DETAILS: Inventory record {inventoryid} not found for '{itemnum}' in site '{siteid}'")
                            return []
                    else:
                        self.logger.info(f"📦 INVENTORY DETAILS: Found {len(processed_records)} MXAPIINVENTORY records for '{itemnum}' in site '{siteid}'")
                        return processed_records
                else:
                    self.logger.warning(f"📦 INVENTORY DETAILS: No MXAPIINVENTORY data found for '{itemnum}' in site '{siteid}'")
                    return []
            else:
                self.logger.warning(f"📦 INVENTORY DETAILS: MXAPIINVENTORY query failed with status {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"📦 INVENTORY DETAILS: MXAPIINVENTORY query error: {str(e)}")
            return []

    def _process_nested_inventory_data(self, inventory_data: Dict) -> Dict:
        """
        Process nested array data from MXAPIINVENTORY response.
        
        Args:
            inventory_data (Dict): Raw inventory data
            
        Returns:
            Dict: Processed inventory data with flattened nested arrays
        """
        processed_data = inventory_data.copy()

        # Process INVCOST array
        if 'invcost' in inventory_data and isinstance(inventory_data['invcost'], list):
            for cost_item in inventory_data['invcost']:
                processed_data['invcost_avgcost'] = cost_item.get('avgcost')
                processed_data['invcost_lastcost'] = cost_item.get('lastcost')
                processed_data['invcost_stdcost'] = cost_item.get('stdcost')
                processed_data['invcost_conditioncode'] = cost_item.get('conditioncode')
                break  # Take first cost record

        # Process INVBALANCES array
        if 'invbalances' in inventory_data and isinstance(inventory_data['invbalances'], list):
            for balance_item in inventory_data['invbalances']:
                processed_data['invbalances_location'] = balance_item.get('location')
                processed_data['invbalances_binnum'] = balance_item.get('binnum')
                processed_data['invbalances_curbal'] = balance_item.get('curbal')
                processed_data['invbalances_conditioncode'] = balance_item.get('conditioncode')
                processed_data['invbalances_lotnum'] = balance_item.get('lotnum')
                processed_data['invbalances_physcnt'] = balance_item.get('physcnt')
                processed_data['invbalances_physcntdate'] = balance_item.get('physcntdate')
                processed_data['invbalances_unitcost'] = balance_item.get('unitcost')
                processed_data['invbalances_stagingbin'] = balance_item.get('stagingbin')
                processed_data['invbalances_stagedcurbal'] = balance_item.get('stagedcurbal')
                break  # Take first balance record

        # Process INVVENDOR array
        if 'invvendor' in inventory_data and isinstance(inventory_data['invvendor'], list):
            for vendor_item in inventory_data['invvendor']:
                processed_data['invvendor_vendor'] = vendor_item.get('vendor')
                processed_data['invvendor_manufacturer'] = vendor_item.get('manufacturer')
                processed_data['invvendor_modelnum'] = vendor_item.get('modelnum')
                processed_data['invvendor_currencycode'] = vendor_item.get('currencycode')
                processed_data['invvendor_contractnum'] = vendor_item.get('contractnum')
                break  # Take first vendor record

        return processed_data

    def _merge_and_organize_data(self, item_data: Dict, inventory_records: List[Dict]) -> Dict:
        """
        Merge and organize data from both endpoints according to field mapping.
        When we have a specific inventory record (inventoryid provided), format it for details display.
        When we have multiple records, use the first one for summary.

        Args:
            item_data (Dict): Data from MXAPIITEM
            inventory_records (List[Dict]): List of inventory records from MXAPIINVENTORY

        Returns:
            Dict: Organized comprehensive inventory details
        """
        # Use first inventory record for details, or empty dict if no records
        inventory_data = inventory_records[0] if inventory_records else {}

        # Basic Info Section (using item data + inventory record)
        basic_info = {
            'itemnum': item_data.get('itemnum') or inventory_data.get('itemnum'),
            'description': item_data.get('description'),
            'status': item_data.get('status'),
            'itemtype': item_data.get('itemtype'),
            'itemsetid': item_data.get('itemsetid'),
            'abctype': inventory_data.get('abc'),
            'rotating': item_data.get('rotating'),
            'lottype': item_data.get('lottype'),
            'siteid': inventory_data.get('siteid'),
            'location': inventory_data.get('location'),
            'store_location': inventory_data.get('location'),
            'binnum': inventory_data.get('invbalances_binnum'),
            'conditionenabled': item_data.get('conditionenabled'),
            'conditioncode': inventory_data.get('invcost_conditioncode'),
            'lotnum': inventory_data.get('invbalances_lotnum')
        }

        # Inventory Section - Current Balances
        current_balances = {
            'curbaltotal': inventory_data.get('curbaltotal'),
            'avblbalance': inventory_data.get('avblbalance'),
            'reservedqty': inventory_data.get('reservedqty'),
            'hardreservedqty': inventory_data.get('hardreservedqty'),
            'softreservedqty': inventory_data.get('softreservedqty'),
            'physcnt': inventory_data.get('invbalances_physcnt'),
            'physcntdate': inventory_data.get('invbalances_physcntdate')
        }

        # Units & Ordering
        units_ordering = {
            'issueunit': inventory_data.get('issueunit'),
            'orderunit': inventory_data.get('orderunit'),
            'minlevel': inventory_data.get('minlevel'),
            'orderqty': inventory_data.get('orderqty'),
            'maxlevel': inventory_data.get('maxlevel'),
            'reorderqty': inventory_data.get('orderqty'),
            'deliverytime': inventory_data.get('deliverytime')
        }

        # Cost Information
        cost_information = {
            'avgcost': inventory_data.get('invcost_avgcost'),
            'lastcost': inventory_data.get('invcost_lastcost'),
            'stdcost': inventory_data.get('invcost_stdcost'),
            'unitcost': inventory_data.get('invbalances_unitcost'),
            'currencycode': inventory_data.get('invvendor_currencycode'),
            'vendor': inventory_data.get('invvendor_vendor'),
            'manufacturer': inventory_data.get('invvendor_manufacturer'),
            'modelnum': inventory_data.get('invvendor_modelnum'),
            'contractnum': inventory_data.get('invvendor_contractnum')
        }

        # Balance Details
        balance_details = {
            'store_location': inventory_data.get('invbalances_location'),
            'binnum': inventory_data.get('invbalances_binnum'),
            'curbal': inventory_data.get('invbalances_curbal'),
            'conditioncode': inventory_data.get('invbalances_conditioncode'),
            'lotnum': inventory_data.get('invbalances_lotnum'),
            'physcnt': inventory_data.get('invbalances_physcnt'),
            'physcntdate': inventory_data.get('invbalances_physcntdate'),
            'stagingbin': inventory_data.get('invbalances_stagingbin'),
            'stagedcurbal': inventory_data.get('invbalances_stagedcurbal')
        }

        # Technical Details
        technical_details = {
            'benchstock': inventory_data.get('benchstock'),
            'shrinkageacc': inventory_data.get('shrinkageacc'),
            'glaccount': inventory_data.get('glaccount'),
            'controlacc': inventory_data.get('controlacc'),
            'manufacturer': inventory_data.get('invvendor_manufacturer'),
            'modelnum': inventory_data.get('invvendor_modelnum'),
            'vendor': inventory_data.get('invvendor_vendor')
        }

        # System Information
        system_information = {
            'itemsetid': inventory_data.get('itemsetid'),
            'currencycode': inventory_data.get('invvendor_currencycode'),
            'statusdate': inventory_data.get('statusdate'),
            'lastissuedate': inventory_data.get('lastissuedate')
        }

        # Additional Attributes
        additional_attributes = {
            'rotating': item_data.get('rotating'),
            'conditionenabled': item_data.get('conditionenabled'),
            'abc': inventory_data.get('abc')
        }

        # Organize into comprehensive structure
        comprehensive_details = {
            'basic_info': basic_info,
            'inventory': {
                'current_balances': current_balances,
                'units_ordering': units_ordering
            },
            'cost_information': cost_information,
            'balance_details': balance_details,
            'technical_details': technical_details,
            'system_information': system_information,
            'additional_attributes': additional_attributes,
            'raw_data': {
                'item_data': item_data,
                'inventory_data': inventory_data  # Single inventory record for this specific details view
            }
        }

        return comprehensive_details

    def clear_cache(self):
        """Clear the details cache."""
        self._details_cache.clear()
        self.logger.info("🧹 INVENTORY DETAILS: Cache cleared")
