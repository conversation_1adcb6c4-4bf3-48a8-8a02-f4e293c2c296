"""
Inventory Transfer Service for Maximo Integration

This service handles inventory transfers using the TransferCurrentItem action
via the MXAPIINVENTORY endpoint with OSLC token authentication.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any

class InventoryTransferService:
    """Service for handling inventory transfers via MXAPIINVENTORY endpoint"""
    
    def __init__(self, token_manager):
        """
        Initialize the inventory transfer service
        
        Args:
            token_manager: Authenticated token manager instance
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        
    def submit_transfer_current_item(self, transfer_data: Dict) -> Dict:
        """
        Submit transfer current item with automatic same-site/cross-site detection

        Args:
            transfer_data (Dict): Complete transfer information including item data and transfer details

        Returns:
            Dict: Result of the transfer submission
        """
        try:
            # Validate authentication
            if not self.token_manager.is_logged_in():
                return {
                    'success': False,
                    'error': 'Not authenticated with <PERSON><PERSON>'
                }

            # Validate transfer data
            validation_result = self.validate_transfer_data(transfer_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"Validation failed: {', '.join(validation_result['errors'])}"
                }

            # Determine transfer type and route accordingly
            from_siteid = transfer_data.get('from_siteid', '')
            to_siteid = transfer_data.get('to_siteid', '')
            transfer_type = transfer_data.get('transfer_type', 'auto')

            if transfer_type == 'same_site' or (transfer_type == 'auto' and from_siteid == to_siteid):
                return self.submit_same_site_transfer(transfer_data)
            elif transfer_type == 'cross_site' or (transfer_type == 'auto' and from_siteid != to_siteid):
                return self.submit_cross_site_transfer(transfer_data)
            else:
                return {
                    'success': False,
                    'error': f'Unknown transfer type: {transfer_type}'
                }

        except Exception as e:
            self.logger.error(f"❌ INVENTORY TRANSFER: Exception occurred: {str(e)}")
            return {
                'success': False,
                'error': f'Transfer submission failed: {str(e)}'
            }

    def submit_same_site_transfer(self, transfer_data: Dict) -> Dict:
        """
        Submit same-site transfer using source site context with conversion factor

        Args:
            transfer_data (Dict): Transfer data

        Returns:
            Dict: Result of the transfer submission
        """
        try:
            self.logger.info(f"🔄 SAME SITE TRANSFER: Processing for item {transfer_data.get('itemnum')}")

            # Build same-site payload with source context
            payload = self._build_same_site_payload(transfer_data)

            # Submit to MXAPIINVENTORY endpoint
            result = self._submit_to_maximo(payload)

            if result['success']:
                self.logger.info(f"✅ SAME SITE TRANSFER: Successfully submitted for item {transfer_data.get('itemnum')}")
            else:
                self.logger.error(f"❌ SAME SITE TRANSFER: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")

            return result

        except Exception as e:
            self.logger.error(f"❌ SAME SITE TRANSFER: Exception occurred: {str(e)}")
            return {
                'success': False,
                'error': f'Same-site transfer failed: {str(e)}'
            }

    def submit_cross_site_transfer(self, transfer_data: Dict) -> Dict:
        """
        Submit cross-site transfer using destination site context with toissueunit

        Args:
            transfer_data (Dict): Transfer data

        Returns:
            Dict: Result of the transfer submission
        """
        try:
            self.logger.info(f"🔄 CROSS SITE TRANSFER: Processing for item {transfer_data.get('itemnum')}")

            # Build cross-site payload with destination context
            payload = self._build_cross_site_payload(transfer_data)

            # Submit to MXAPIINVENTORY endpoint
            result = self._submit_to_maximo(payload)

            if result['success']:
                self.logger.info(f"✅ CROSS SITE TRANSFER: Successfully submitted for item {transfer_data.get('itemnum')}")
            else:
                self.logger.error(f"❌ CROSS SITE TRANSFER: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")

            return result

        except Exception as e:
            self.logger.error(f"❌ CROSS SITE TRANSFER: Exception occurred: {str(e)}")
            return {
                'success': False,
                'error': f'Cross-site transfer failed: {str(e)}'
            }
    
    def _build_same_site_payload(self, transfer_data: Dict) -> List[Dict]:
        """
        Build same-site transfer payload using source site context with conversion factor

        Args:
            transfer_data (Dict): Complete transfer information

        Returns:
            List[Dict]: Same-site transfer payload with source context
        """
        # Extract required fields from transfer data
        itemnum = transfer_data.get('itemnum', '')
        quantity = float(transfer_data.get('quantity', 1.0))

        # From location details
        from_siteid = transfer_data.get('from_siteid', '')
        from_storeroom = transfer_data.get('from_storeroom', '')
        from_bin = transfer_data.get('from_bin', '') or None  # Send null instead of "DEFAULT"
        from_lot = transfer_data.get('from_lot', '') or None  # Send null instead of "DEFAULT"
        from_condition = transfer_data.get('from_condition', 'A1')
        from_issue_unit = transfer_data.get('from_issue_unit', 'EA')  # Use EA as universal default

        # To location details
        to_siteid = transfer_data.get('to_siteid', '')
        to_storeroom = transfer_data.get('to_storeroom', '')
        to_bin = transfer_data.get('to_bin', '') or None  # Send null instead of "DEFAULT"
        to_lot = transfer_data.get('to_lot', '') or None  # Send null instead of "DEFAULT"
        to_condition = transfer_data.get('to_condition', 'A1')

        # Conversion factor for unit differences
        conversion_factor = float(transfer_data.get('conversion_factor', 1.0))

        # Build same-site payload with SOURCE site context
        payload = [
            {
                "_action": "AddChange",
                "itemnum": itemnum,
                "itemsetid": "ITEMSET",
                "siteid": from_siteid,  # SOURCE site context
                "location": from_storeroom,  # SOURCE location
                "issueunit": from_issue_unit,
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": itemnum,
                        "issuetype": "TRANSFER",
                        "quantity": quantity,
                        "fromsiteid": from_siteid,
                        "tositeid": to_siteid,
                        "fromstoreloc": from_storeroom,
                        "tostoreloc": to_storeroom,
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": from_issue_unit,
                        "fromconditioncode": from_condition,
                        "toconditioncode": to_condition
                    }
                ]
            }
        ]

        # Add conversion factor if units are different
        if conversion_factor != 1.0:
            payload[0]["matrectrans"][0]["conversionfactor"] = conversion_factor

        # Add bin/lot fields only if they have actual values (not null/empty)
        matrectrans = payload[0]["matrectrans"][0]
        if from_bin:
            matrectrans["frombinnum"] = from_bin
        if to_bin:
            matrectrans["tobinnum"] = to_bin
        if from_lot:
            matrectrans["fromlotnum"] = from_lot
        if to_lot:
            matrectrans["tolotnum"] = to_lot

        self.logger.info(f"🔧 SAME SITE TRANSFER: Built payload with source context for {itemnum}")
        self.logger.info(f"🔧 SAME SITE TRANSFER: Source site: {from_siteid}, Conversion: {conversion_factor}")

        return payload

    def _build_cross_site_payload(self, transfer_data: Dict) -> List[Dict]:
        """
        Build cross-site transfer payload using destination site context with toissueunit

        Args:
            transfer_data (Dict): Complete transfer information

        Returns:
            List[Dict]: Cross-site transfer payload with destination context
        """
        # Extract required fields from transfer data
        itemnum = transfer_data.get('itemnum', '')
        quantity = float(transfer_data.get('quantity', 1.0))

        # From location details
        from_siteid = transfer_data.get('from_siteid', '')
        from_storeroom = transfer_data.get('from_storeroom', '')
        from_bin = transfer_data.get('from_bin', '') or None  # Send null instead of "DEFAULT"
        from_lot = transfer_data.get('from_lot', '') or None  # Send null instead of "DEFAULT"
        from_condition = transfer_data.get('from_condition', 'A1')
        from_issue_unit = transfer_data.get('from_issue_unit', 'EA')  # Use EA as universal default

        # To location details
        to_siteid = transfer_data.get('to_siteid', '')
        to_storeroom = transfer_data.get('to_storeroom', '')
        to_bin = transfer_data.get('to_bin', '') or None  # Send null instead of "DEFAULT"
        to_lot = transfer_data.get('to_lot', '') or None  # Send null instead of "DEFAULT"
        to_condition = transfer_data.get('to_condition', 'A1')
        to_issue_unit = transfer_data.get('to_issue_unit', 'EA')

        # Build cross-site payload with DESTINATION site context and toissueunit
        # This works for ALL cross-site transfers, not just specific site combinations
        payload = [
            {
                "_action": "AddChange",
                "itemnum": itemnum,
                "itemsetid": "ITEMSET",
                "siteid": to_siteid,  # DESTINATION site context
                "location": to_storeroom,  # DESTINATION location
                "issueunit": from_issue_unit,
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": itemnum,
                        "issuetype": "TRANSFER",
                        "quantity": quantity,
                        "fromsiteid": from_siteid,
                        "tositeid": to_siteid,
                        "fromstoreloc": from_storeroom,
                        "tostoreloc": to_storeroom,
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": from_issue_unit,
                        "fromconditioncode": from_condition,
                        "toconditioncode": to_condition,
                        "toissueunit": to_issue_unit  # KEY: toissueunit for cross-site conversion
                    }
                ]
            }
        ]

        # Add bin/lot fields only if they have actual values (not null/empty)
        matrectrans = payload[0]["matrectrans"][0]
        if from_bin:
            matrectrans["frombinnum"] = from_bin
        if to_bin:
            matrectrans["tobinnum"] = to_bin
        if from_lot:
            matrectrans["fromlotnum"] = from_lot
        if to_lot:
            matrectrans["tolotnum"] = to_lot

        self.logger.info(f"🔧 CROSS SITE TRANSFER: Built payload with destination context for {itemnum}")
        self.logger.info(f"🔧 CROSS SITE TRANSFER: Destination site: {to_siteid}, toissueunit: {to_issue_unit}")

        return payload
    
    def _submit_to_maximo(self, payload: List[Dict]) -> Dict:
        """
        Submit the payload to MXAPIINVENTORY endpoint with MATRECTRANS child object

        Args:
            payload (List[Dict]): The formatted payload array with MATRECTRANS child

        Returns:
            Dict: Result of the submission
        """
        try:
            # Get the base URL from token manager
            base_url = getattr(self.token_manager, 'base_url', '')
            if not base_url:
                return {
                    'success': False,
                    'error': 'No Maximo base URL configured'
                }

            # Use MXAPIINVENTORY endpoint with MATRECTRANS child object (correct wsmethod approach)
            api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"

            # Prepare headers for MXAPIINVENTORY with MATRECTRANS
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            }

            # LOG THE EXACT PAYLOAD BEING SENT TO MAXIMO
            self.logger.info("=" * 80)
            self.logger.info("📋 EXACT PAYLOAD BEING SENT TO MAXIMO:")
            self.logger.info("=" * 80)
            self.logger.info(f"🔗 URL: {api_url}")
            self.logger.info(f"📋 Headers: {headers}")
            self.logger.info("📋 Payload:")
            self.logger.info(json.dumps(payload, indent=2))
            self.logger.info("=" * 80)

            # Make the API call using session authentication (like successful cost adjustments)
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                headers=headers,
                timeout=(5.0, 30)
            )

            # LOG THE EXACT RESPONSE FROM MAXIMO
            self.logger.info("=" * 80)
            self.logger.info("📋 EXACT RESPONSE FROM MAXIMO:")
            self.logger.info("=" * 80)
            self.logger.info(f"📊 Status Code: {response.status_code}")
            if response.text:
                try:
                    response_json = response.json()
                    self.logger.info("📋 Response JSON:")
                    self.logger.info(json.dumps(response_json, indent=2))
                except:
                    self.logger.info(f"📋 Response Text: {response.text}")
            else:
                self.logger.info("📋 Response: (Empty)")
            self.logger.info("=" * 80)

            # Check response status - TransferCurrentItem action expects different patterns
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    self.logger.info(f"✅ INVENTORY TRANSFER: Response data: {json.dumps(response_data, indent=2)}")

                    # First check for errors in the response
                    if isinstance(response_data, list) and len(response_data) > 0:
                        first_item = response_data[0]

                        # Check for error responses
                        if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                            error = first_item['_responsedata']['Error']
                            detailed_error = {
                                'reasonCode': error.get('reasonCode'),
                                'message': error.get('message'),
                                'statusCode': error.get('statusCode'),
                                'extendedError': error.get('extendedError', {})
                            }

                            self.logger.error(f"❌ INVENTORY TRANSFER: Error response: {error.get('reasonCode')} - {error.get('message')}")

                            return {
                                'success': False,
                                'message': 'Transfer failed - Please review the error details and correct the issue',
                                'error': f"{error.get('reasonCode')} - {error.get('message')}",
                                'detailed_error': detailed_error,
                                'response': response_data,
                                'status_code': response.status_code,
                                'timestamp': datetime.now().isoformat(),
                                'user_action_required': True,
                                'error_guidance': self._get_error_guidance(detailed_error)
                            }

                        # Check for success (204 status)
                        elif first_item.get('_responsemeta', {}).get('status') == '204':
                            self.logger.info(f"✅ INVENTORY TRANSFER: Success response received (204 status)")
                            return {
                                'success': True,
                                'message': 'Inventory transfer submitted successfully to Maximo',
                                'timestamp': datetime.now().isoformat(),
                                'status_code': 200,
                                'response': response_data
                            }

                    # Check for other success patterns
                    success_indicators = [
                        # Direct success response with transfer data
                        (isinstance(response_data, dict) and
                         ('inventoryid' in response_data or 'itemnum' in response_data)),
                        # Array with transfer result
                        (isinstance(response_data, list) and len(response_data) > 0 and
                         ('inventoryid' in response_data[0] or 'itemnum' in response_data[0]))
                    ]

                    if any(success_indicators):
                        self.logger.info(f"✅ INVENTORY TRANSFER: Success response received (HTTP 200)")
                        return {
                            'success': True,
                            'message': 'Inventory transfer submitted successfully to Maximo',
                            'timestamp': datetime.now().isoformat(),
                            'status_code': 200,
                            'response': response_data
                        }
                    else:
                        self.logger.warning(f"⚠️ INVENTORY TRANSFER: Unexpected response format")
                        return {
                            'success': False,
                            'message': 'Unexpected response format from Maximo',
                            'response': response_data,
                            'timestamp': datetime.now().isoformat(),
                            'status_code': 200,
                            'user_action_required': True
                        }
                except json.JSONDecodeError:
                    self.logger.info(f"✅ INVENTORY TRANSFER: Success response received (HTTP 200, non-JSON)")
                    return {
                        'success': True,
                        'message': 'Inventory transfer submitted successfully to Maximo',
                        'timestamp': datetime.now().isoformat(),
                        'status_code': 200
                    }
            elif response.status_code == 204:
                self.logger.info(f"✅ INVENTORY TRANSFER: Success response received (HTTP 204)")
                return {
                    'success': True,
                    'message': 'Inventory transfer submitted successfully to Maximo',
                    'timestamp': datetime.now().isoformat(),
                    'status_code': 204
                }
            else:
                # Handle error response
                try:
                    error_data = response.json()
                    error_message = self._extract_error_message(error_data)
                except json.JSONDecodeError:
                    error_message = f"HTTP {response.status_code}: {response.text[:200]}"
                
                self.logger.error(f"❌ INVENTORY TRANSFER: API error: {error_message}")
                return {
                    'success': False,
                    'error': error_message,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            self.logger.error(f"❌ INVENTORY TRANSFER: Request failed: {str(e)}")
            return {
                'success': False,
                'error': f'Network error: {str(e)}'
            }
    
    def _extract_error_message(self, error_data: Dict) -> str:
        """
        Extract meaningful error message from Maximo error response
        
        Args:
            error_data (Dict): Error response from Maximo
            
        Returns:
            str: Formatted error message
        """
        # Handle OSLC error format
        if 'oslc:Error' in error_data:
            oslc_error = error_data['oslc:Error']
            message = oslc_error.get('oslc:message', '')
            reason_code = oslc_error.get('spi:reasonCode', '')
            status_code = oslc_error.get('oslc:statusCode', '')
            
            if reason_code and message:
                return f"{reason_code}: {message}"
            elif message:
                return message
            elif status_code:
                return f"Error {status_code}"
        
        # Handle other error formats
        if 'error' in error_data:
            return str(error_data['error'])
        
        if 'message' in error_data:
            return str(error_data['message'])
        
        # Fallback to string representation
        return str(error_data)

    def validate_transfer_data(self, transfer_data: Dict) -> Dict:
        """
        Validate transfer data before submission

        Args:
            transfer_data (Dict): Transfer data to validate

        Returns:
            Dict: Validation result with success flag and errors
        """
        errors = []

        # Check required fields
        required_fields = ['itemnum', 'quantity', 'from_siteid', 'to_siteid', 'from_storeroom', 'to_storeroom']
        for field in required_fields:
            if field not in transfer_data or not transfer_data[field]:
                errors.append(f"Missing required field: {field}")

        # Validate quantity
        if 'quantity' in transfer_data:
            try:
                quantity = float(transfer_data['quantity'])
                if quantity <= 0:
                    errors.append("Quantity must be greater than 0")
            except (ValueError, TypeError):
                errors.append("Quantity must be a valid number")

        # Validate conversion factor
        if 'conversion_factor' in transfer_data:
            try:
                conversion_factor = float(transfer_data['conversion_factor'])
                if conversion_factor <= 0:
                    errors.append("Conversion factor must be greater than 0")
            except (ValueError, TypeError):
                errors.append("Conversion factor must be a valid number")

        # NOTE: Removed hardcoded duplicate validation - let Maximo handle BMXAA1861E errors
        # This allows the actual Maximo response to come through for user to see and fix

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    def get_available_storerooms(self, siteid: str) -> List[Dict]:
        """
        Get available storerooms for a specific site using MXAPIINVENTORY endpoint

        Args:
            siteid (str): Site ID to get storerooms for

        Returns:
            List[Dict]: List of available storerooms
        """
        try:
            if not self.token_manager.is_logged_in():
                return []

            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiinventory"

            # Query for unique locations in the specified site
            params = {
                "oslc.select": "location",
                "oslc.where": f'siteid="{siteid}" and status="ACTIVE"',
                "oslc.pageSize": "1000",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                # Extract unique storerooms
                storerooms = set()
                for item in items:
                    location = item.get('location', '').strip()
                    if location:
                        storerooms.add(location)

                # Convert to list of dictionaries
                return [{'location': loc, 'description': f'Storeroom {loc}'} for loc in sorted(storerooms)]

            return []

        except Exception as e:
            self.logger.error(f"❌ INVENTORY TRANSFER: Error getting storerooms: {str(e)}")
            return []

    def get_available_bins_lots_conditions(self, siteid: str, location: str) -> Dict:
        """
        Get available bins, lots, and condition codes for a specific site and location using MXAPIINVENTORY

        Args:
            siteid (str): Site ID
            location (str): Storeroom location

        Returns:
            Dict: Dictionary containing bins, lots, and condition codes
        """
        try:
            if not self.token_manager.is_logged_in():
                return {'bins': [], 'lots': [], 'conditions': []}

            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiinventory"

            # Query for inventory records with invbalances data using MXAPIINVENTORY endpoint
            params = {
                "oslc.select": "itemnum,siteid,location,status,invbalances",
                "oslc.where": f'siteid="{siteid}" and location="{location}" and status="ACTIVE"',
                "oslc.pageSize": "1000",
                "lean": "1"
            }

            self.logger.info(f"🔍 TRANSFER: Fetching bins/lots/conditions from MXAPIINVENTORY for {siteid}/{location}")

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                bins = set()
                lots = set()
                conditions = set()

                # Extract unique values from invbalances related table
                for item in items:
                    invbalances = item.get('invbalances', [])

                    # Handle both list and single object cases
                    if not isinstance(invbalances, list):
                        invbalances = [invbalances] if invbalances else []

                    for balance in invbalances:
                        if isinstance(balance, dict):
                            # Only include balances with positive current balance
                            curbal = balance.get('curbal', 0)
                            if curbal and float(curbal) > 0:
                                bin_num = balance.get('binnum', '').strip()
                                lot_num = balance.get('lotnum', '').strip()
                                condition = balance.get('conditioncode', '').strip()

                                if bin_num:
                                    bins.add(bin_num)
                                if lot_num:
                                    lots.add(lot_num)
                                if condition:
                                    conditions.add(condition)

                result = {
                    'bins': sorted(list(bins)),
                    'lots': sorted(list(lots)),
                    'conditions': sorted(list(conditions))
                }

                self.logger.info(f"✅ TRANSFER: Found {len(result['bins'])} bins, {len(result['lots'])} lots, {len(result['conditions'])} conditions from MXAPIINVENTORY")
                return result

            self.logger.error(f"❌ TRANSFER: Failed to get bins/lots/conditions from MXAPIINVENTORY: {response.status_code}")
            return {'bins': [], 'lots': [], 'conditions': []}

        except Exception as e:
            self.logger.error(f"❌ INVENTORY TRANSFER: Error getting bins/lots/conditions from MXAPIINVENTORY: {str(e)}")
            return {'bins': [], 'lots': [], 'conditions': []}

    def get_available_issue_units(self, itemnum: str) -> List[str]:
        """
        Get available issue units for an item using MXAPIINVENTORY endpoint

        Args:
            itemnum (str): Item number

        Returns:
            List[str]: List of available issue units
        """
        try:
            if not self.token_manager.is_logged_in():
                return ['EA']

            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiinventory"

            # Query for issue units for the item
            params = {
                "oslc.select": "issueunit,orderunit",
                "oslc.where": f'itemnum="{itemnum}" and status="ACTIVE"',
                "oslc.pageSize": "100",
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                items = data.get('member', [])

                units = set(['EA'])  # Always include EA as default
                for item in items:
                    issue_unit = item.get('issueunit', '').strip()
                    order_unit = item.get('orderunit', '').strip()

                    if issue_unit:
                        units.add(issue_unit)
                    if order_unit:
                        units.add(order_unit)

                return sorted(list(units))

            return ['EA']

        except Exception as e:
            self.logger.error(f"❌ INVENTORY TRANSFER: Error getting issue units: {str(e)}")
            return ['EA']

    def _get_error_guidance(self, detailed_error):
        """
        Provide user-friendly guidance based on error codes

        Args:
            detailed_error (Dict): Detailed error information

        Returns:
            str: User-friendly guidance message
        """
        if not detailed_error:
            return "Please check your input and try again."

        reason_code = detailed_error.get('reasonCode', '')

        guidance_map = {
            'BMXAA1861E': {
                'title': 'Duplicate Location/Item/Bin/Lot Combination',
                'guidance': 'This combination already exists in the system. Please try one of the following:',
                'actions': [
                    'Change the destination bin to a different value',
                    'Change the destination lot to a different value',
                    'Use a different destination storeroom',
                    'Check if this transfer was already completed'
                ]
            },
            'BMXAA1785E': {
                'title': 'Unit of Measure Conversion Error',
                'guidance': 'No conversion exists between the specified units. Please:',
                'actions': [
                    'Use the same unit for both source and destination',
                    'Contact your system administrator to set up the conversion',
                    'Manually specify a conversion factor if known'
                ]
            },
            'BMXAA2694E': {
                'title': 'Location Does Not Exist',
                'guidance': 'The specified location does not exist in the target site. Please:',
                'actions': [
                    'Verify the destination storeroom exists in the destination site',
                    'Use a different destination storeroom',
                    'Contact your administrator to create the location'
                ]
            }
        }

        if reason_code in guidance_map:
            error_info = guidance_map[reason_code]
            guidance = f"{error_info['title']}: {error_info['guidance']}\n"
            for i, action in enumerate(error_info['actions'], 1):
                guidance += f"{i}. {action}\n"
            return guidance.strip()
        else:
            return f"Error {reason_code}: Please review the error message and contact support if needed."
