"""
Asset Management Service

This service provides comprehensive asset management functionality,
including search, filtering, and data retrieval from Maximo MXAPIASSET endpoint.
Follows the same patterns as InventoryManagementService and EnhancedWorkOrderService.
"""

import logging
import time
import threading
from typing import Dict, List, Tuple, Optional, Any
import requests


class AssetManagementService:
    """Service for managing asset operations with OSLC integration and caching."""
    
    def __init__(self, token_manager):
        """
        Initialize the Asset Management Service.
        
        Args:
            token_manager: Token manager for OSLC authentication
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        self._search_cache = {}
        self._cache_timeout = 300  # 5 minutes
        self._lock = threading.RLock()  # Thread-safe operations
        
        # Performance monitoring
        self._performance_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'api_calls': 0,
            'average_response_time': 0.0,
            'total_assets_fetched': 0,
            'last_reset': time.time()
        }
        
        # Debug logging for service initialization
        self.logger.info(f"🔧 INIT: AssetManagementService initialized")
        self.logger.info(f"🔧 INIT: token_manager: {'✅ Available' if token_manager else '❌ None'}")
        
    def search_assets(self, search_term: str = '', site_id: str = '', limit: int = 20, page: int = 0, status_filter: str = '') -> Tuple[List[Dict], Dict]:
        """
        Search assets by asset number, description, or other fields with pagination.
        Uses status != 'DECOMMISSIONED' filter as specified in requirements.

        Args:
            search_term (str): Search term for asset number, description, etc.
            site_id (str): Site ID to filter assets
            limit (int): Maximum number of results to return per page
            page (int): Page number for pagination (0-based)
            status_filter (str): Optional status filter (OPERATING, ACTIVE, etc.)

        Returns:
            Tuple[List[Dict], Dict]: (asset_items, metadata)
        """
        start_time = time.time()
        
        # Build cache key
        cache_key = f"{search_term}_{site_id}_{status_filter}_{limit}_{page}"

        # Check cache first
        with self._lock:
            if cache_key in self._search_cache:
                cached_data = self._search_cache[cache_key]
                if time.time() - cached_data['timestamp'] < self._cache_timeout:
                    self.logger.info(f"🔍 ASSET: Cache hit for search: {search_term}")
                    self._update_performance_stats(time.time() - start_time, cache_hit=True, api_call=False)
                    cached_data['data'][1]['source'] = 'cache'
                    return cached_data['data']

        self.logger.info(f"🔍 ASSET: Searching for '{search_term}' in site '{site_id}' with status '{status_filter}' (page {page}, limit {limit})")

        try:
            # Validate token manager and session
            if not self.token_manager:
                raise Exception("Token manager not available")

            if not hasattr(self.token_manager, 'session') or not self.token_manager.session:
                raise Exception("Session not available - user may not be logged in")

            if not hasattr(self.token_manager, 'base_url') or not self.token_manager.base_url:
                raise Exception("Base URL not available - token manager not properly initialized")

            # Check if session is still valid
            if not self.is_session_valid():
                raise Exception("Session expired - please login again")

            # Search MXAPIASSET endpoint with enhanced multi-field search
            self.logger.info(f"🔍 ASSET: Searching MXAPIASSET for '{search_term}' with status filter '{status_filter}'")
            asset_results = self._search_mxapiasset_enhanced(search_term, site_id, limit, page, status_filter)

            if not asset_results:
                self.logger.info(f"🔍 ASSET: No assets found")
                return [], {
                    'load_time': time.time() - start_time,
                    'source': 'mxapiasset_empty',
                    'count': 0,
                    'page': page,
                    'total_pages': 0
                }

            # Enhance asset data with additional processing
            enhanced_assets = self._enhance_asset_data(asset_results)

            # Calculate pagination metadata
            total_items = len(enhanced_assets)
            total_pages = max(1, (total_items + limit - 1) // limit) if total_items > 0 else 0
            
            # Cache the results
            result_data = (enhanced_assets, {
                'load_time': time.time() - start_time,
                'source': 'mxapiasset',
                'count': total_items,
                'page': page,
                'total_pages': total_pages,
                'search_term': search_term,
                'site_id': site_id
            })
            
            with self._lock:
                self._search_cache[cache_key] = {
                    'data': result_data,
                    'timestamp': time.time()
                }

            self._update_performance_stats(time.time() - start_time, cache_hit=False, api_call=True, asset_count=total_items)
            self.logger.info(f"✅ ASSET: Found {total_items} assets in {time.time() - start_time:.2f}s")
            return result_data

        except Exception as e:
            self.logger.error(f"❌ ASSET: Search failed: {str(e)}")
            self._update_performance_stats(time.time() - start_time, cache_hit=False, api_call=False)
            return [], {
                'load_time': time.time() - start_time,
                'source': 'error',
                'count': 0,
                'page': page,
                'total_pages': 0,
                'error': str(e)
            }

    def _search_mxapiasset_enhanced(self, search_term: str, site_id: str, limit: int, page: int, status_filter: str = '') -> List[Dict]:
        """
        Enhanced search using multiple separate queries to avoid OSLC parsing issues.

        Args:
            search_term (str): Search term
            site_id (str): Site ID for filtering
            limit (int): Maximum results
            page (int): Page number
            status_filter (str): Optional status filter

        Returns:
            List[Dict]: Combined asset items from multiple searches
        """
        if not search_term:
            # If no search term, use the simple search
            return self._search_mxapiasset(search_term, site_id, limit, page, status_filter)

        # Search different fields separately to avoid OSLC OR parsing issues
        search_fields = [
            ('assetnum', 'Asset Number'),
            ('description', 'Description'),
            ('assettag', 'Asset Tag'),
            ('serialnum', 'Serial Number'),
            ('model', 'Model')
        ]

        all_assets = []
        seen_assets = set()  # Track unique assets by assetnum+siteid

        for field, field_name in search_fields:
            try:
                self.logger.info(f"🔍 ASSET: Searching {field_name} field for '{search_term}'")
                field_results = self._search_mxapiasset_single_field(search_term, site_id, limit, page, status_filter, field)

                # Add unique assets to results
                for asset in field_results:
                    asset_key = f"{asset.get('assetnum', '')}_{asset.get('siteid', '')}"
                    if asset_key not in seen_assets:
                        seen_assets.add(asset_key)
                        all_assets.append(asset)

                        # Stop if we have enough results
                        if len(all_assets) >= limit:
                            break

            except Exception as e:
                self.logger.warning(f"⚠️ ASSET: Search failed for {field_name}: {str(e)}")
                continue

            # Stop if we have enough results
            if len(all_assets) >= limit:
                break

        self.logger.info(f"✅ ASSET: Enhanced search found {len(all_assets)} unique assets")
        return all_assets[:limit]  # Ensure we don't exceed limit

    def _search_mxapiasset_single_field(self, search_term: str, site_id: str, limit: int, page: int, status_filter: str, field: str) -> List[Dict]:
        """
        Search a single field to avoid OSLC parsing issues.

        Args:
            search_term (str): Search term
            site_id (str): Site ID for filtering
            limit (int): Maximum results
            page (int): Page number
            status_filter (str): Optional status filter
            field (str): Field name to search

        Returns:
            List[Dict]: Asset items from single field search
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiasset"

        # Build search filter
        search_term_clean = search_term.replace('"', '\\"') if search_term else ''

        # Build status filter - exclude DECOMMISSIONED as per requirements
        if status_filter:
            status_condition = f'status="{status_filter}"'
        else:
            status_condition = 'status!="DECOMMISSIONED"'

        # Build OSLC filter - simple AND conditions only
        filter_conditions = [status_condition]

        if site_id:
            filter_conditions.append(f'siteid="{site_id}"')

        if search_term:
            # Search single field only - no OR conditions to avoid parsing issues
            search_filter = f'{field}="%{search_term_clean}%"'
            filter_conditions.append(search_filter)

        oslc_filter = ' and '.join(filter_conditions)

        # Use the same field selection as the original method
        select_fields = [
            "assetnum", "siteid", "description", "status", "location",
            "assettag", "serialnum", "model", "type", "assettype",
            "orgid", "changedate", "changeby", "statusdate", "priority"
        ]

        nested_fields = [
            "assetmeter", "assetspec", "locations", "assetancestor"
        ]

        all_fields = select_fields + nested_fields

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": oslc_filter,
            "oslc.pageSize": str(min(limit, 20)),  # Smaller page size for individual searches
            "oslc.page": str(page),
            "lean": "1"
        }

        self.logger.info(f"🔍 ASSET: Single field search - {field}: {oslc_filter}")

        response = self.token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        # Handle session expiration with retry logic (same as before)
        if 'login' in response.url.lower():
            self.logger.warning(f"Session expired during {field} search")
            if hasattr(self.token_manager, 'force_session_refresh'):
                self.logger.info(f"🔄 ASSET: Attempting session refresh for {field} search...")
                if self.token_manager.force_session_refresh():
                    self.logger.info(f"✅ ASSET: Session refreshed, retrying {field} search...")
                    try:
                        response = self.token_manager.session.get(
                            api_url,
                            params=params,
                            timeout=(3.05, 30),
                            headers={"Accept": "application/json"},
                            allow_redirects=True
                        )
                        if response.status_code == 200 and 'login' not in response.url.lower():
                            self.logger.info(f"✅ ASSET: Retry {field} search after session refresh successful")
                        else:
                            self.logger.warning(f"❌ ASSET: Retry {field} search after session refresh failed")
                            return []
                    except Exception as retry_e:
                        self.logger.error(f"Error during {field} search retry after session refresh: {retry_e}")
                        return []
                else:
                    self.logger.warning(f"❌ ASSET: Session refresh failed for {field} search")
                    return []
            else:
                return []

        if response.status_code != 200:
            self.logger.error(f"❌ ASSET: {field} search failed with status {response.status_code}")
            self.logger.error(f"❌ ASSET: Response text: {response.text[:500]}")
            return []

        try:
            data = response.json()
            assets = data.get('member', [])
            self.logger.info(f"✅ ASSET: {field} search retrieved {len(assets)} assets")
            return assets
        except ValueError as e:
            self.logger.error(f"❌ ASSET: Failed to parse JSON response for {field} search: {str(e)}")
            self.logger.error(f"❌ ASSET: Response text: {response.text[:500]}")
            return []

    def _search_mxapiasset(self, search_term: str, site_id: str, limit: int, page: int, status_filter: str = '') -> List[Dict]:
        """
        Search using MXAPIASSET endpoint with OSLC protocol.

        Args:
            search_term (str): Search term
            site_id (str): Site ID for filtering
            limit (int): Maximum results
            page (int): Page number
            status_filter (str): Optional status filter

        Returns:
            List[Dict]: Asset items from MXAPIASSET
        """
        base_url = getattr(self.token_manager, 'base_url', '')
        api_url = f"{base_url}/oslc/os/mxapiasset"

        # Build search filter
        search_term_clean = search_term.replace('"', '\\"') if search_term else ''

        # Build status filter - exclude DECOMMISSIONED as per requirements
        if status_filter:
            status_condition = f'status="{status_filter}"'
        else:
            # Use status!="DECOMMISSIONED" to exclude decommissioned assets
            status_condition = 'status!="DECOMMISSIONED"'

        # Build OSLC filter
        filter_conditions = [status_condition]
        
        if site_id:
            filter_conditions.append(f'siteid="{site_id}"')
            
        if search_term:
            # Start with the most important field - asset number using LIKE pattern
            # This follows the working pattern from inventory service
            search_filter = f'assetnum="%{search_term_clean}%"'
            filter_conditions.append(search_filter)

        oslc_filter = ' and '.join(filter_conditions)

        # Select fields for list view as specified in requirements
        # Required fields: location, location description, siteid, status, description, 
        # assetnum, asset tag, hand receipt #, model, serial #, type
        select_fields = [
            "assetnum", "siteid", "description", "status", "location", 
            "assettag", "serialnum", "model", "type", "assettype",
            "orgid", "changedate", "changeby", "statusdate", "priority",
            "purchaseprice", "replacecost", "totalcost", "vendor", "manufacturer",
            "installdate", "parent", "isrunning", "moved", "disabled",
            "_rowstamp", "assetid"
        ]

        # Add nested fields for additional data
        nested_fields = [
            "assetmeter", "assetspec", "locations"
        ]

        all_fields = select_fields + nested_fields

        params = {
            "oslc.select": ",".join(all_fields),
            "oslc.where": oslc_filter,
            "oslc.pageSize": str(limit),
            "oslc.page": str(page),
            "lean": "1"
        }

        self.logger.info(f"🔍 ASSET: Searching MXAPIASSET")
        self.logger.info(f"🔍 ASSET: API URL: {api_url}")
        self.logger.info(f"🔍 ASSET: Filter: {oslc_filter}")

        response = self.token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 30),
            headers={"Accept": "application/json"},
            allow_redirects=True
        )

        self.logger.info(f"🔍 ASSET: Response status: {response.status_code}")

        # Handle session expiration with retry logic
        if 'login' in response.url.lower():
            self.logger.warning("Session expired during asset search")
            # Try to refresh session once
            if hasattr(self.token_manager, 'force_session_refresh'):
                self.logger.info("🔄 ASSET: Attempting session refresh...")
                if self.token_manager.force_session_refresh():
                    self.logger.info("✅ ASSET: Session refreshed, retrying search...")
                    # Retry the search once with refreshed session
                    try:
                        response = self.token_manager.session.get(
                            api_url,
                            params=params,
                            timeout=(3.05, 30),
                            headers={"Accept": "application/json"},
                            allow_redirects=True
                        )
                        if response.status_code == 200 and 'login' not in response.url.lower():
                            self.logger.info("✅ ASSET: Retry after session refresh successful")
                            # Continue with processing the response
                        else:
                            self.logger.warning("❌ ASSET: Retry after session refresh failed")
                            return []
                    except Exception as retry_e:
                        self.logger.error(f"Error during retry after session refresh: {retry_e}")
                        return []
                else:
                    self.logger.warning("❌ ASSET: Session refresh failed")
                    return []
            else:
                return []

        if response.status_code != 200:
            self.logger.error(f"❌ ASSET: API call failed with status {response.status_code}")
            self.logger.error(f"❌ ASSET: Response text: {response.text[:500]}")
            return []

        try:
            data = response.json()
            assets = data.get('member', [])
            self.logger.info(f"✅ ASSET: Retrieved {len(assets)} assets from MXAPIASSET")
            return assets
        except ValueError as e:
            self.logger.error(f"❌ ASSET: Failed to parse JSON response: {str(e)}")
            self.logger.error(f"❌ ASSET: Response text: {response.text[:500]}")
            return []

    def _enhance_asset_data(self, assets: List[Dict]) -> List[Dict]:
        """
        Enhance asset data with additional processing and formatting.

        Args:
            assets (List[Dict]): Raw asset data from API

        Returns:
            List[Dict]: Enhanced asset data
        """
        enhanced_assets = []
        
        for asset in assets:
            enhanced_asset = asset.copy()
            
            # Add location description if available from nested data
            if 'locations' in asset and isinstance(asset['locations'], list) and asset['locations']:
                location_data = asset['locations'][0]
                enhanced_asset['location_description'] = location_data.get('description', '')
            else:
                enhanced_asset['location_description'] = ''
            
            # Format dates for display
            for date_field in ['changedate', 'statusdate', 'installdate']:
                if enhanced_asset.get(date_field):
                    try:
                        # Format date for display (assuming ISO format from API)
                        date_str = enhanced_asset[date_field]
                        if 'T' in date_str:
                            enhanced_asset[f'{date_field}_formatted'] = date_str.split('T')[0]
                        else:
                            enhanced_asset[f'{date_field}_formatted'] = date_str
                    except:
                        enhanced_asset[f'{date_field}_formatted'] = enhanced_asset[date_field]
            
            # Add display flags
            enhanced_asset['has_meters'] = bool(asset.get('assetmeter', []))
            enhanced_asset['has_specifications'] = bool(asset.get('assetspec', []))
            
            enhanced_assets.append(enhanced_asset)
        
        return enhanced_assets

    def _update_performance_stats(self, response_time: float, cache_hit: bool, api_call: bool, asset_count: int = 0):
        """Update performance monitoring statistics."""
        with self._lock:
            stats = self._performance_stats
            stats['total_requests'] += 1

            if cache_hit:
                stats['cache_hits'] += 1
            if api_call:
                stats['api_calls'] += 1
            if asset_count > 0:
                stats['total_assets_fetched'] += asset_count

            # Update average response time using exponential moving average
            alpha = 0.1  # Smoothing factor
            if stats['average_response_time'] == 0:
                stats['average_response_time'] = response_time
            else:
                stats['average_response_time'] = (alpha * response_time) + ((1 - alpha) * stats['average_response_time'])

    def clear_cache(self):
        """Clear the search cache."""
        with self._lock:
            self._search_cache.clear()
            self.logger.info("🧹 ASSET: Cache cleared")

    def get_performance_stats(self) -> Dict:
        """Get performance statistics."""
        with self._lock:
            stats = self._performance_stats.copy()
            stats['cache_hit_ratio'] = (stats['cache_hits'] / max(stats['total_requests'], 1)) * 100
            return stats

    def get_cache_stats(self) -> Dict:
        """Get cache statistics."""
        with self._lock:
            return {
                'cache_entries': len(self._search_cache),
                'cache_timeout_seconds': self._cache_timeout,
                'cache_hit_ratio': (self._performance_stats['cache_hits'] / max(self._performance_stats['total_requests'], 1)) * 100,
                'total_requests': self._performance_stats['total_requests'],
                'api_calls': self._performance_stats['api_calls']
            }

    def is_session_valid(self) -> bool:
        """Check if the current session is valid."""
        try:
            return (self.token_manager and
                    hasattr(self.token_manager, 'is_logged_in') and
                    self.token_manager.is_logged_in())
        except Exception as e:
            self.logger.error(f"❌ ASSET: Session validation failed: {str(e)}")
            return False

    def get_asset_details(self, assetnum: str, siteid: str) -> Dict:
        """
        Get detailed information for a specific asset using streamlined approach.

        Args:
            assetnum (str): Asset number
            siteid (str): Site ID

        Returns:
            Dict: Detailed asset information
        """
        # Clean and validate inputs
        assetnum = assetnum.strip() if assetnum else ''
        siteid = siteid.strip() if siteid else ''

        if not assetnum or not siteid:
            return {}

        cache_key = f"detail_{assetnum}_{siteid}"

        # Check cache first
        with self._lock:
            if cache_key in self._search_cache:
                cached_data = self._search_cache[cache_key]
                if time.time() - cached_data['timestamp'] < self._cache_timeout:
                    return cached_data['data']

        try:
            # Use streamlined query method
            asset_detail = self._get_asset_details_streamlined(assetnum, siteid)

            if asset_detail:
                # Cache the result
                with self._lock:
                    self._search_cache[cache_key] = {
                        'data': asset_detail,
                        'timestamp': time.time()
                    }
                return asset_detail
            else:
                return {}

        except Exception as e:
            return {}

    def _get_asset_details_streamlined(self, assetnum: str, siteid: str) -> Dict:
        """Streamlined asset details retrieval using only the working method."""
        try:
            if not self.token_manager or not self.token_manager.session:
                return {}

            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiasset"

            # Use minimal fields first to ensure query works
            simple_fields = ["assetnum", "siteid", "description", "status", "location"]
            oslc_filter = f'assetnum="{assetnum}" and siteid="{siteid}"'

            params = {
                "oslc.select": ",".join(simple_fields),
                "oslc.where": oslc_filter,
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"},
                allow_redirects=True
            )

            if response.status_code == 200:
                try:
                    data = response.json()
                    assets = data.get('member', [])

                    if assets:
                        basic_asset = assets[0]
                        # Try to enhance with more fields
                        enhanced_asset = self._enhance_streamlined_asset(basic_asset, api_url)
                        return enhanced_asset if enhanced_asset else basic_asset
                    else:
                        return {}
                except ValueError:
                    return {}
            else:
                return {}

        except Exception:
            return {}

    def _enhance_streamlined_asset(self, basic_asset: Dict, api_url: str) -> Dict:
        """Enhance basic asset with additional fields."""
        try:
            assetnum = basic_asset.get('assetnum')
            siteid = basic_asset.get('siteid')

            # All fields for complete asset details
            all_fields = [
                "assetnum", "siteid", "description", "status", "location",
                "assettag", "serialnum", "model", "type", "assettype",
                "orgid", "changedate", "changeby", "statusdate", "priority",
                "purchaseprice", "replacecost", "totalcost", "vendor", "manufacturer",
                "installdate", "parent", "isrunning", "moved", "disabled",
                "_rowstamp", "assetid", "calnum", "failurecode", "problemcode",
                "warrantyexpdate", "tloamstartdate", "tloamenddate", "returndate",
                "leasecost", "ytdcost", "budgetcost", "unchargedcost", "acqcost"
            ]

            oslc_filter = f'assetnum="{assetnum}" and siteid="{siteid}"'

            params = {
                "oslc.select": ",".join(all_fields),
                "oslc.where": oslc_filter,
                "lean": "1"
            }

            response = self.token_manager.session.get(
                api_url,
                params=params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"},
                allow_redirects=True
            )

            if response.status_code == 200:
                try:
                    data = response.json()
                    assets = data.get('member', [])

                    if assets:
                        enhanced_asset = assets[0]
                        return self._enhance_asset_detail(enhanced_asset)
                    else:
                        return None
                except ValueError:
                    return None
            else:
                return None

        except Exception:
            return None



    def debug_search_results(self, search_term: str, site_id: str) -> Dict:
        """Debug method to check what assets are actually returned by search."""
        try:
            self.logger.info(f"🔍 DEBUG: Searching for assets with term '{search_term}' in site '{site_id}'")

            # Use the enhanced search to get results
            assets = self._search_mxapiasset_enhanced(search_term, site_id, 10, 0, '')

            debug_info = {
                'search_term': search_term,
                'site_id': site_id,
                'total_found': len(assets),
                'assets': []
            }

            for asset in assets:
                asset_info = {
                    'assetnum': asset.get('assetnum', 'N/A'),
                    'siteid': asset.get('siteid', 'N/A'),
                    'description': asset.get('description', 'N/A')[:50] + '...' if asset.get('description') else 'N/A',
                    'status': asset.get('status', 'N/A'),
                    'location': asset.get('location', 'N/A')
                }
                debug_info['assets'].append(asset_info)
                self.logger.info(f"🔍 DEBUG: Found asset - {asset_info['assetnum']} in {asset_info['siteid']}")

            return debug_info

        except Exception as e:
            self.logger.error(f"❌ DEBUG: Search debug failed: {str(e)}")
            return {'error': str(e)}

    def _enhance_asset_detail(self, asset: Dict) -> Dict:
        """
        Enhance asset detail data with additional processing and categorization.

        Args:
            asset (Dict): Raw asset data from API

        Returns:
            Dict: Enhanced asset detail data with categorized information
        """
        enhanced_asset = asset.copy()

        # Categorize asset information for detail view
        enhanced_asset['categories'] = {
            'basic_information': {
                'title': 'Basic Information',
                'icon': 'fas fa-info-circle',
                'fields': {
                    'assetnum': {'label': 'Asset Number', 'value': asset.get('assetnum', ''), 'icon': 'fas fa-hashtag'},
                    'description': {'label': 'Description', 'value': asset.get('description', ''), 'icon': 'fas fa-file-text'},
                    'assettag': {'label': 'Asset Tag', 'value': asset.get('assettag', ''), 'icon': 'fas fa-tag'},
                    'status': {'label': 'Status', 'value': asset.get('status', ''), 'icon': 'fas fa-circle'},
                    'type': {'label': 'Type', 'value': asset.get('type', ''), 'icon': 'fas fa-cogs'},
                    'assettype': {'label': 'Asset Type', 'value': asset.get('assettype', ''), 'icon': 'fas fa-layer-group'},
                    'priority': {'label': 'Priority', 'value': asset.get('priority', ''), 'icon': 'fas fa-exclamation-triangle'}
                }
            },
            'location_information': {
                'title': 'Location Information',
                'icon': 'fas fa-map-marker-alt',
                'fields': {
                    'siteid': {'label': 'Site ID', 'value': asset.get('siteid', ''), 'icon': 'fas fa-building'},
                    'location': {'label': 'Location', 'value': asset.get('location', ''), 'icon': 'fas fa-map-pin'},
                    'orgid': {'label': 'Organization', 'value': asset.get('orgid', ''), 'icon': 'fas fa-sitemap'},
                    'parent': {'label': 'Parent Asset', 'value': asset.get('parent', ''), 'icon': 'fas fa-level-up-alt'}
                }
            },
            'technical_information': {
                'title': 'Technical Information',
                'icon': 'fas fa-wrench',
                'fields': {
                    'serialnum': {'label': 'Serial Number', 'value': asset.get('serialnum', ''), 'icon': 'fas fa-barcode'},
                    'model': {'label': 'Model', 'value': asset.get('model', ''), 'icon': 'fas fa-cube'},
                    'manufacturer': {'label': 'Manufacturer', 'value': asset.get('manufacturer', ''), 'icon': 'fas fa-industry'},
                    'vendor': {'label': 'Vendor', 'value': asset.get('vendor', ''), 'icon': 'fas fa-handshake'},
                    'calnum': {'label': 'Calibration Number', 'value': asset.get('calnum', ''), 'icon': 'fas fa-ruler'}
                }
            },
            'financial_information': {
                'title': 'Financial Information',
                'icon': 'fas fa-dollar-sign',
                'fields': {
                    'purchaseprice': {'label': 'Purchase Price', 'value': asset.get('purchaseprice', ''), 'icon': 'fas fa-money-bill'},
                    'replacecost': {'label': 'Replacement Cost', 'value': asset.get('replacecost', ''), 'icon': 'fas fa-exchange-alt'},
                    'totalcost': {'label': 'Total Cost', 'value': asset.get('totalcost', ''), 'icon': 'fas fa-calculator'},
                    'ytdcost': {'label': 'YTD Cost', 'value': asset.get('ytdcost', ''), 'icon': 'fas fa-chart-line'},
                    'budgetcost': {'label': 'Budget Cost', 'value': asset.get('budgetcost', ''), 'icon': 'fas fa-piggy-bank'}
                }
            },
            'dates_and_status': {
                'title': 'Dates & Status',
                'icon': 'fas fa-calendar',
                'fields': {
                    'installdate': {'label': 'Install Date', 'value': self._format_date(asset.get('installdate', '')), 'icon': 'fas fa-calendar-plus'},
                    'statusdate': {'label': 'Status Date', 'value': self._format_date(asset.get('statusdate', '')), 'icon': 'fas fa-calendar-check'},
                    'changedate': {'label': 'Last Changed', 'value': self._format_date(asset.get('changedate', '')), 'icon': 'fas fa-calendar-edit'},
                    'changeby': {'label': 'Changed By', 'value': asset.get('changeby', ''), 'icon': 'fas fa-user-edit'},
                    'warrantyexpdate': {'label': 'Warranty Expiry', 'value': self._format_date(asset.get('warrantyexpdate', '')), 'icon': 'fas fa-shield-alt'}
                }
            }
        }

        # Add location description if available
        if 'locations' in asset and isinstance(asset['locations'], list) and asset['locations']:
            location_data = asset['locations'][0]
            enhanced_asset['categories']['location_information']['fields']['location_description'] = {
                'label': 'Location Description',
                'value': location_data.get('description', ''),
                'icon': 'fas fa-info'
            }

        # Add meter information
        if 'assetmeter' in asset and isinstance(asset['assetmeter'], list):
            enhanced_asset['meters'] = asset['assetmeter']
            enhanced_asset['has_meters'] = len(asset['assetmeter']) > 0
        else:
            enhanced_asset['meters'] = []
            enhanced_asset['has_meters'] = False

        # Add specification information
        if 'assetspec' in asset and isinstance(asset['assetspec'], list):
            enhanced_asset['specifications'] = asset['assetspec']
            enhanced_asset['has_specifications'] = len(asset['assetspec']) > 0
        else:
            enhanced_asset['specifications'] = []
            enhanced_asset['has_specifications'] = False

        # Add work order and ticket counts
        enhanced_asset['workorder_count'] = len(asset.get('workorder', []))
        enhanced_asset['ticket_count'] = len(asset.get('ticket', []))

        return enhanced_asset

    def _format_date(self, date_str: str) -> str:
        """Format date string for display."""
        if not date_str:
            return ''
        try:
            if 'T' in date_str:
                return date_str.split('T')[0]
            return date_str
        except:
            return date_str

    def get_asset_actions(self, assetnum: str, siteid: str) -> List[Dict]:
        """
        Get available actions for an asset based on requirements.
        Priority actions: View Tickets, View Work Orders, Report Downtime, Asset Details, Add/Upload Image

        Args:
            assetnum (str): Asset number
            siteid (str): Site ID

        Returns:
            List[Dict]: Available actions for the asset
        """
        actions = [
            {
                'id': 'view_related_records',
                'title': 'View Related Records',
                'icon': 'fas fa-list-alt',
                'description': 'View work orders and service requests for this asset',
                'priority': 1,
                'enabled': True
            }
        ]

        return sorted(actions, key=lambda x: x['priority'])
