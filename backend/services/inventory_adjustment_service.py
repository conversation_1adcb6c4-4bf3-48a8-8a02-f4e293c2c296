"""
Inventory Adjustment Service for Maximo Integration

This service handles inventory adjustments by submitting to the mxapiinventory endpoint
with proper payload structure and authentication.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any

class InventoryAdjustmentService:
    """Service for handling inventory adjustments via mxapiinventory endpoint"""
    
    def __init__(self, token_manager):
        """
        Initialize the inventory adjustment service
        
        Args:
            token_manager: Authenticated token manager instance
        """
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        
    def submit_inventory_adjustment(self, inventory_data: Dict, adjustment_data: Dict) -> Dict:
        """
        Submit inventory adjustment to mxapiinventory endpoint
        
        Args:
            inventory_data (Dict): Current inventory information from QR scan
            adjustment_data (Dict): Adjustment details including type, quantity, reason
            
        Returns:
            Dict: Result of the adjustment submission
        """
        try:
            # Validate authentication
            if not self.token_manager.is_logged_in():
                return {
                    'success': False,
                    'error': 'Not authenticated with <PERSON><PERSON>'
                }
            
            # Build the mxapiinventory payload
            payload = self._build_adjustment_payload(inventory_data, adjustment_data)
            
            # Submit to mxapiinventory endpoint
            result = self._submit_to_maximo(payload)
            
            if result['success']:
                self.logger.info(f"✅ INVENTORY ADJUSTMENT: Successfully submitted for item {inventory_data.get('itemnum')}")
            else:
                self.logger.error(f"❌ INVENTORY ADJUSTMENT: Failed for item {inventory_data.get('itemnum')}: {result.get('error')}")
                
            return result
            
        except Exception as e:
            self.logger.error(f"❌ INVENTORY ADJUSTMENT: Exception occurred: {str(e)}")
            return {
                'success': False,
                'error': f'Adjustment submission failed: {str(e)}'
            }
    
    def _build_adjustment_payload(self, inventory_data: Dict, adjustment_data: Dict) -> List[Dict]:
        """
        Build the mxapiinventory payload structure
        
        Args:
            inventory_data (Dict): Current inventory information
            adjustment_data (Dict): Adjustment details
            
        Returns:
            List[Dict]: Properly formatted payload for mxapiinventory
        """
        # Extract required fields from inventory data
        itemnum = inventory_data.get('itemnum', '')
        siteid = inventory_data.get('siteid', '')
        location = inventory_data.get('storeloc', '')
        binnum = inventory_data.get('binnum', '')  # Optional - can be empty
        conditioncode = inventory_data.get('conditioncode', 'A1')
        issueunit = inventory_data.get('issueunit', 'EA')

        # Get current balance
        current_balance = float(inventory_data.get('currentbalance', 0))

        # Determine adjustment method and calculate values
        adjustment_method = adjustment_data.get('adjustment_method', 'CURRENT_BALANCE')

        if adjustment_method == 'PHYSICAL_COUNT':
            # Physical Count Adjustment - use provided physical count, curbal stays same
            physical_count = float(adjustment_data.get('physical_count', current_balance))
            adjusted_curbal = current_balance  # Keep original current balance
        else:
            # Current Balance Adjustment - adjust curbal, physcnt stays same as curbal
            adjustment_type = adjustment_data.get('adjustment_type', 'POSITIVE')
            quantity = float(adjustment_data.get('quantity', 0))

            if adjustment_type == 'POSITIVE':
                adjusted_curbal = current_balance + quantity
            else:  # NEGATIVE
                adjusted_curbal = current_balance - quantity

            # Ensure current balance is not negative
            adjusted_curbal = max(0, adjusted_curbal)
            physical_count = adjusted_curbal  # Physical count matches new current balance
        
        # Get fields from form and QR scan data
        reason_code = adjustment_data.get('reason_code', '')
        notes = adjustment_data.get('notes', '')
        lotnum = inventory_data.get('lotnum', '')
        lottype = inventory_data.get('lottype', '')
        controlacc = inventory_data.get('controlacc', '')
        shrinkageacc = inventory_data.get('shrinkageacc', '')
        reconciled = adjustment_data.get('reconciled', True)

        # Build memo field from reason code and notes (Reason Code maps to MEMO field)
        memo = reason_code
        if notes:
            memo = f"{reason_code}: {notes}".strip(': ') if reason_code else notes

        # Use current timestamp for physcntdate
        from datetime import datetime
        physcntdate = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")

        # Build the EXACT payload structure as specified
        payload = [
            {
                "_action": "AddChange",
                "itemnum": itemnum,
                "itemsetid": "ITEMSET",
                "siteid": siteid,
                "location": location,
                "issueunit": issueunit,
                "minlevel": 0,
                "orderqty": 1,
                "invbalances": [
                    {
                        "binnum": binnum,
                        "curbal": adjusted_curbal,
                        "physcnt": physical_count,
                        "physcntdate": physcntdate,
                        "conditioncode": conditioncode,
                        "lotnum": lotnum,
                        "reconciled": reconciled,
                        "memo": memo,
                        "controlacc": controlacc,
                        "shrinkageacc": shrinkageacc
                    }
                ]
            }
        ]
        
        self.logger.info(f"🔧 INVENTORY ADJUSTMENT: Built payload for {itemnum}")
        self.logger.debug(f"🔧 INVENTORY ADJUSTMENT: Payload: {json.dumps(payload, indent=2)}")
        
        return payload
    
    def _submit_to_maximo(self, payload: List[Dict]) -> Dict:
        """
        Submit the payload to mxapiinventory endpoint using EXACT same method as workorder task status update

        Args:
            payload (List[Dict]): The formatted payload

        Returns:
            Dict: Result of the submission
        """
        try:
            # Get the base URL from token manager
            base_url = getattr(self.token_manager, 'base_url', '')
            if not base_url:
                return {
                    'success': False,
                    'error': 'No Maximo base URL configured'
                }

            # Build the API URL using exact MxLoader pattern with session authentication
            api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"

            # Prepare headers using exact MxLoader pattern
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            }

            self.logger.info(f"🔄 INVENTORY ADJUSTMENT: Submitting to {api_url}")
            self.logger.debug(f"🔄 INVENTORY ADJUSTMENT: Headers: {headers}")
            self.logger.debug(f"🔄 INVENTORY ADJUSTMENT: Payload: {payload}")

            # Make the API call using session authentication (like successful test)
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                headers=headers,
                timeout=(5.0, 30)
            )
            
            self.logger.info(f"🔄 INVENTORY ADJUSTMENT: Response status: {response.status_code}")
            
            # Check response status - MxLoader pattern: HTTP 200 with _responsemeta.status=204
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    # Check for MxLoader pattern: [{"_responsemeta":{"status":"204"}}]
                    if (isinstance(response_data, list) and len(response_data) > 0 and
                        '_responsemeta' in response_data[0] and
                        response_data[0]['_responsemeta'].get('status') == '204'):

                        self.logger.info(f"✅ INVENTORY ADJUSTMENT: Success response received (HTTP 200 with _responsemeta.status=204)")
                        return {
                            'success': True,
                            'message': 'Inventory adjustment submitted successfully to Maximo',
                            'timestamp': datetime.now().isoformat(),
                            'status_code': 204,
                            'response': response_data
                        }
                    else:
                        self.logger.info(f"✅ INVENTORY ADJUSTMENT: Success response received (HTTP 200)")
                        return {
                            'success': True,
                            'response': response_data,
                            'message': 'Inventory adjustment submitted successfully to Maximo',
                            'timestamp': datetime.now().isoformat(),
                            'status_code': 200
                        }
                except json.JSONDecodeError:
                    self.logger.info(f"✅ INVENTORY ADJUSTMENT: Success response received (HTTP 200, non-JSON)")
                    return {
                        'success': True,
                        'message': 'Inventory adjustment submitted successfully to Maximo',
                        'timestamp': datetime.now().isoformat(),
                        'status_code': 200
                    }
            else:
                # Handle error response
                try:
                    error_data = response.json()
                    error_message = self._extract_error_message(error_data)
                except json.JSONDecodeError:
                    error_message = f"HTTP {response.status_code}: {response.text[:200]}"
                
                self.logger.error(f"❌ INVENTORY ADJUSTMENT: API error: {error_message}")
                return {
                    'success': False,
                    'error': error_message,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            self.logger.error(f"❌ INVENTORY ADJUSTMENT: Request failed: {str(e)}")
            return {
                'success': False,
                'error': f'Network error: {str(e)}'
            }
    
    def _extract_error_message(self, error_data: Dict) -> str:
        """
        Extract meaningful error message from Maximo error response
        
        Args:
            error_data (Dict): Error response from Maximo
            
        Returns:
            str: Formatted error message
        """
        # Handle OSLC error format
        if 'oslc:Error' in error_data:
            oslc_error = error_data['oslc:Error']
            message = oslc_error.get('oslc:message', '')
            reason_code = oslc_error.get('spi:reasonCode', '')
            status_code = oslc_error.get('oslc:statusCode', '')
            
            if reason_code and message:
                return f"{reason_code}: {message}"
            elif message:
                return message
            elif status_code:
                return f"Error {status_code}"
        
        # Handle other error formats
        if 'error' in error_data:
            return str(error_data['error'])
        
        if 'message' in error_data:
            return str(error_data['message'])
        
        # Fallback to string representation
        return str(error_data)
    
    def validate_adjustment_data(self, adjustment_data: Dict) -> Dict:
        """
        Validate adjustment data before submission
        
        Args:
            adjustment_data (Dict): Adjustment data to validate
            
        Returns:
            Dict: Validation result with success flag and errors
        """
        errors = []
        
        # Check required fields
        required_fields = ['adjustment_type']
        for field in required_fields:
            if field not in adjustment_data or not adjustment_data[field]:
                errors.append(f"Missing required field: {field}")

        # Check for quantity or physical_count
        if adjustment_data.get('adjustment_type') == 'PHYSICAL_COUNT':
            if 'physical_count' not in adjustment_data or adjustment_data['physical_count'] is None:
                errors.append("Missing required field: physical_count")
        else:
            if 'quantity' not in adjustment_data or not adjustment_data['quantity']:
                errors.append("Missing required field: quantity")
        
        # Validate adjustment type
        if 'adjustment_type' in adjustment_data:
            valid_types = ['POSITIVE', 'NEGATIVE', 'PHYSICAL_COUNT']
            if adjustment_data['adjustment_type'] not in valid_types:
                errors.append(f"Invalid adjustment type. Must be one of: {valid_types}")
        
        # Validate quantity or physical_count
        if adjustment_data.get('adjustment_type') == 'PHYSICAL_COUNT':
            if 'physical_count' in adjustment_data:
                try:
                    physical_count = float(adjustment_data['physical_count'])
                    if physical_count < 0:
                        errors.append("Physical count must be 0 or greater")
                except (ValueError, TypeError):
                    errors.append("Physical count must be a valid number")
        else:
            if 'quantity' in adjustment_data:
                try:
                    quantity = float(adjustment_data['quantity'])
                    if quantity <= 0:
                        errors.append("Quantity must be greater than 0")
                except (ValueError, TypeError):
                    errors.append("Quantity must be a valid number")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
