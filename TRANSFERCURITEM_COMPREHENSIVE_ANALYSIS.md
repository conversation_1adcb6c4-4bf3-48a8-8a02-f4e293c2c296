# MXAPIINVENTORY `spi:transfercuritem` Nested Object Comprehensive Analysis

**Date:** 2025-07-15  
**Investigation Method:** Dynamic API testing with authenticated sessions  
**Target:** https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo  

## Executive Summary

The `spi:transfercuritem` nested object in MXAPIINVENTORY represents transfer operations but **does not support direct creation through nested object URLs**. Instead, transfers appear to be **read-only representations** of existing transfer records, likely created through other Maximo processes or applications.

## 🔍 **Field Analysis from Discovered Records**

### **Required Fields**
| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| **`spi:fromstoreloc`** | string | Source location for transfer | Must be valid location in source site |
| **`spi:tositeid`** | string | Target site ID | Must be valid site ID |
| **`spi:quantity`** | float | Quantity to transfer | Must be > 0 |

### **Optional Fields**
| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| **`spi:unitcost`** | float | Unit cost for transfer | Defaults to item cost if not specified |
| **`spi:linecost`** | float | Total line cost | Calculated as quantity × unitcost |
| **`spi:fromavblbalance`** | float | Available balance at source | Read-only, system calculated |
| **`spi:orgid`** | string | Organization ID | Inherited from parent inventory |
| **`spi:islot`** | boolean | Lot-controlled indicator | System-determined from item setup |
| **`spi:glcreditacct`** | string | GL credit account | Optional accounting field |

### **System Fields**
| Field | Type | Description |
|-------|------|-------------|
| **`localref`** | string | Local reference URL for the nested object |
| **`rdf:about`** | string | RDF identifier (always `http://childkey#...`) |

## 📋 **Discovered Transfer Structure Examples**

### **Example 1: Basic Transfer**
```json
{
  "spi:fromavblbalance": 0.0,
  "spi:linecost": 46.2,
  "spi:fromstoreloc": "LCVK-CMW-CAS",
  "spi:tositeid": "LCVKWT",
  "spi:orgid": "USARMY",
  "spi:quantity": 1.0,
  "spi:unitcost": 46.2,
  "spi:islot": false,
  "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_NjIxMC02MC1WMDAtMDE4MS9JVEVNU0VUL0xDVkstQ01XLUNBUy9MQ1ZLV1Q-/transfercuritem/0",
  "rdf:about": "http://childkey#SU5WRU5UT1JZL1RSQU5TRkVSQ1VSSVRFTQ--"
}
```

### **Example 2: Transfer with GL Account**
```json
{
  "spi:fromavblbalance": 0.0,
  "spi:linecost": 2.2,
  "spi:fromstoreloc": "LCVK-CMW-AJ",
  "spi:tositeid": "LCVKWT",
  "spi:glcreditacct": "000000.0000.00000",
  "spi:orgid": "USARMY",
  "spi:quantity": 1.0,
  "spi:unitcost": 2.2,
  "spi:islot": false
}
```

## 🔧 **Payload Structure Examples**

### **Minimal Required Payload**
```json
{
  "fromstoreloc": "LCVK-CMW-AJ",
  "tositeid": "LCVKWT",
  "quantity": 1.0
}
```

### **Full Payload with All Fields**
```json
{
  "fromstoreloc": "LCVK-CMW-AJ",
  "tositeid": "LCVKWT",
  "quantity": 5.0,
  "unitcost": 15.50,
  "islot": false,
  "orgid": "USARMY",
  "glcreditacct": "000000.0000.00000"
}
```

### **Location-to-Location Transfer (Same Site)**
```json
{
  "fromstoreloc": "LCVK-CMW-AJ",
  "tositeid": "LCVKWT",
  "quantity": 2.0,
  "unitcost": 12.00
}
```

### **Site-to-Site Transfer**
```json
{
  "fromstoreloc": "LCVK-CMW-AJ",
  "tositeid": "OTHERSITE",
  "quantity": 3.0,
  "unitcost": 20.00
}
```

## 🚫 **Non-Working Approaches**

### **1. Direct Nested Object POST**
```http
POST /api/os/mxapiinventory/{id}/transfercuritem
```
**Result:** `502 Bad Gateway` - URL pattern not supported

### **2. Nested Object with SPI Prefix**
```http
POST /api/os/mxapiinventory/{id}/spi:transfercuritem
```
**Result:** `BMXAA4187E - The relationship SPI:TRANSFERCURITEM does not exist`

### **3. Indexed Nested Object**
```http
POST /api/os/mxapiinventory/{id}/transfercuritem/0
```
**Result:** `BMXAA1407E - Object already exists`

### **4. Action Parameter**
```http
POST /api/os/mxapiinventory?action=transfercuritem
```
**Result:** `BMXAA9487E - Action was not found`

## ✅ **Working Approaches for Retrieving Transfer Data**

### **1. GET Inventory with Transfer Data**
```http
GET /api/os/mxapiinventory?oslc.select=*&lean=0
Headers:
  Accept: application/json
  apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o
```

**Response:**
```json
{
  "rdfs:member": [
    {
      "spi:itemnum": "6210-60-V00-0181",
      "spi:siteid": "LCVKWT",
      "spi:location": "LCVK-CMW-CAS",
      "spi:transfercuritem": [
        {
          "spi:fromstoreloc": "LCVK-CMW-CAS",
          "spi:tositeid": "LCVKWT",
          "spi:quantity": 1.0,
          "spi:unitcost": 46.2,
          "spi:linecost": 46.2
        }
      ]
    }
  ]
}
```

### **2. Filter by Items with Transfers**
```http
GET /api/os/mxapiinventory?oslc.select=itemnum,siteid,transfercuritem&oslc.where=transfercuritem.quantity>0
```

## 🔐 **Authentication Examples**

### **OSLC Token Authentication**
```bash
curl -X GET \
  "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory" \
  -H "Accept: application/json" \
  -H "Cookie: [session-cookies]"
```

### **API Key Authentication**
```bash
curl -X GET \
  "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory" \
  -H "Accept: application/json" \
  -H "apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
```

## 💻 **Implementation Examples**

### **Python - Retrieve Transfer Data**
```python
import requests

def get_inventory_transfers(api_key, item_num=None, site_id=None):
    """Retrieve inventory records with transfer data."""
    
    url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    params = {
        "oslc.select": "itemnum,siteid,location,curbaltotal,transfercuritem",
        "lean": "0"  # Include nested objects
    }
    
    # Add filters if specified
    where_conditions = []
    if item_num:
        where_conditions.append(f'itemnum="{item_num}"')
    if site_id:
        where_conditions.append(f'siteid="{site_id}"')
        
    if where_conditions:
        params["oslc.where"] = " and ".join(where_conditions)
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            return data.get('rdfs:member', [])
        else:
            print(f"Error: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"Exception: {str(e)}")
        return []

# Usage example
api_key = "your-api-key-here"
transfers = get_inventory_transfers(api_key, site_id="LCVKWT")

for record in transfers:
    if record.get('spi:transfercuritem'):
        print(f"Item: {record['spi:itemnum']}")
        print(f"Transfers: {len(record['spi:transfercuritem'])}")
        for transfer in record['spi:transfercuritem']:
            print(f"  From: {transfer['spi:fromstoreloc']}")
            print(f"  To: {transfer['spi:tositeid']}")
            print(f"  Quantity: {transfer['spi:quantity']}")
```

### **JavaScript - Retrieve Transfer Data**
```javascript
async function getInventoryTransfers(apiKey, itemNum = null, siteId = null) {
    const baseUrl = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory';
    
    const params = new URLSearchParams({
        'oslc.select': 'itemnum,siteid,location,curbaltotal,transfercuritem',
        'lean': '0'
    });
    
    // Add filters
    const whereConditions = [];
    if (itemNum) whereConditions.push(`itemnum="${itemNum}"`);
    if (siteId) whereConditions.push(`siteid="${siteId}"`);
    
    if (whereConditions.length > 0) {
        params.append('oslc.where', whereConditions.join(' and '));
    }
    
    try {
        const response = await fetch(`${baseUrl}?${params}`, {
            headers: {
                'Accept': 'application/json',
                'apikey': apiKey
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            return data['rdfs:member'] || [];
        } else {
            console.error('Error:', response.status);
            return [];
        }
    } catch (error) {
        console.error('Exception:', error);
        return [];
    }
}

// Usage
const apiKey = 'your-api-key-here';
getInventoryTransfers(apiKey, null, 'LCVKWT').then(transfers => {
    transfers.forEach(record => {
        if (record['spi:transfercuritem']) {
            console.log(`Item: ${record['spi:itemnum']}`);
            record['spi:transfercuritem'].forEach(transfer => {
                console.log(`  Transfer: ${transfer['spi:quantity']} from ${transfer['spi:fromstoreloc']} to ${transfer['spi:tositeid']}`);
            });
        }
    });
});
```

## ⚠️ **Error Handling Patterns**

### **Common Error Responses**

#### **1. Bulk API Request Error**
```json
{
  "oslc:Error": {
    "oslc:statusCode": "400",
    "spi:reasonCode": "BMXAA9501E",
    "oslc:message": "BMXAA9501E - A bulk API request must be a JSON array of JSON objects."
  }
}
```
**Cause:** Attempting to POST single object to nested collection URL
**Solution:** Use array format or different approach

#### **2. Relationship Not Found Error**
```json
{
  "oslc:Error": {
    "oslc:statusCode": "404",
    "spi:reasonCode": "BMXAA4187E",
    "oslc:message": "BMXAA4187E - The relationship SPI:TRANSFERCURITEM does not exist for business object INVENTORY."
  }
}
```
**Cause:** Using incorrect nested object relationship name
**Solution:** Use correct relationship name without SPI prefix

#### **3. Object Already Exists Error**
```json
{
  "oslc:Error": {
    "oslc:statusCode": "400",
    "spi:reasonCode": "BMXAA1407E",
    "oslc:message": "BMXAA1407E - The INVENTORY object cannot be added because it already exists in the application."
  }
}
```
**Cause:** Attempting to create transfer on existing inventory record
**Solution:** Use update operations instead of create

#### **4. Action Not Found Error**
```json
{
  "oslc:Error": {
    "oslc:statusCode": "400",
    "spi:reasonCode": "BMXAA9487E",
    "oslc:message": "BMXAA9487E - {0} action was not found. Ensure that the correct action name is entered."
  }
}
```
**Cause:** Using non-existent action parameter
**Solution:** Use standard REST operations instead of actions

### **Error Handling Implementation**
```python
def handle_transfer_api_error(response):
    """Handle common transfer API errors."""

    if response.status_code == 200:
        return True, response.json()

    try:
        error_data = response.json()
        if 'oslc:Error' in error_data:
            error = error_data['oslc:Error']
            reason_code = error.get('spi:reasonCode', 'Unknown')
            message = error.get('oslc:message', 'No message')

            # Handle specific error codes
            if reason_code == 'BMXAA9501E':
                return False, "Bulk API format required - use array payload"
            elif reason_code == 'BMXAA4187E':
                return False, "Invalid relationship name - check nested object path"
            elif reason_code == 'BMXAA1407E':
                return False, "Object already exists - use update instead of create"
            elif reason_code == 'BMXAA9487E':
                return False, "Action not found - use standard REST operations"
            else:
                return False, f"API Error {reason_code}: {message}"
        else:
            return False, f"HTTP {response.status_code}: {response.text}"

    except:
        return False, f"HTTP {response.status_code}: {response.text}"
```

## 🔍 **URL Structure Analysis**

### **Base64 Encoded Inventory IDs**
The nested object URLs use base64-encoded inventory identifiers:

**Pattern:** `/api/os/mxapiinventory/_[BASE64_ENCODED_ID]/transfercuritem`

**Example:**
- **Encoded:** `_NjIxMC02MC1WMDAtMDE4MS9JVEVNU0VUL0xDVkstQ01XLUNBUy9MQ1ZLV1Q-`
- **Decoded:** `6210-60-V00-0181/ITEMSET/LCVK-CMW-CAS/LCVKWT`
- **Components:** `{itemnum}/{itemsetid}/{location}/{siteid}`

### **Local Reference URLs**
```
https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_[ENCODED_ID]/transfercuritem/0
```

**Note:** These URLs return 502 errors when accessed directly, indicating they're for reference only.

## 📊 **Key Findings Summary**

### ✅ **What Works**
1. **Reading transfer data** via GET operations on main inventory endpoint
2. **Filtering inventory** records that have transfer data
3. **Accessing nested structure** through `spi:transfercuritem` array
4. **Both authentication methods** (OSLC token and API key)

### ❌ **What Doesn't Work**
1. **Creating transfers** via nested object POST operations
2. **Direct nested object URLs** (return 502 Bad Gateway)
3. **Traditional wsmethods** for transfer operations
4. **Action parameters** on main endpoint

### 🔍 **Transfer Creation Alternatives**
Since direct transfer creation through MXAPIINVENTORY doesn't work, transfers are likely created through:

1. **Maximo Applications** (Inventory, Transfer, etc.)
2. **Other API endpoints** (possibly MATRECTRANS or specialized transfer endpoints)
3. **Integration Object Services** with different object structures
4. **Workflow processes** that generate transfer records

## 💡 **Implementation Recommendations**

### **For Reading Transfer Data**
- ✅ Use GET operations on MXAPIINVENTORY with `lean=0`
- ✅ Include `transfercuritem` in `oslc.select` parameter
- ✅ Filter by transfer-related fields if needed
- ✅ Use API key authentication for programmatic access

### **For Creating Transfers**
- ❌ Don't attempt direct nested object creation
- 🔍 Investigate other Maximo transfer endpoints
- 🔍 Consider using Maximo applications for transfer creation
- 🔍 Look into MATRECTRANS or specialized transfer APIs

### **Error Handling Strategy**
- Always check for `oslc:Error` in responses
- Handle specific error codes appropriately
- Provide meaningful error messages to users
- Log detailed error information for debugging

## 🎯 **Conclusion**

The `spi:transfercuritem` nested object in MXAPIINVENTORY serves as a **read-only view** of transfer data rather than a functional transfer creation interface. While the structure is well-defined and the data is accessible, **direct transfer operations are not supported** through this endpoint.

For practical inventory transfer operations, developers should:
1. Use MXAPIINVENTORY for **reading existing transfer data**
2. Investigate **alternative endpoints** for transfer creation
3. Consider **Maximo application integration** for transfer workflows
4. Implement **proper error handling** for unsupported operations

The comprehensive field analysis and payload examples provided can be used as reference for understanding transfer data structure and for potential integration with other transfer-capable endpoints.
