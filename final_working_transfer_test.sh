#!/bin/bash

# Final Working Transfer Test
# ===========================

echo "🎯 FINAL WORKING TRANSFER TEST"
echo "============================="

echo "🔍 Key Insights:"
echo "  ✅ API calls are working perfectly"
echo "  ✅ Authentication is successful"
echo "  ✅ Transfer mechanism is functional"
echo "  ❌ Issue: Invalid destination locations"
echo ""
echo "💡 Solution: Use a location that actually exists in IKWAJ site"
echo "   From your app logs: Found 16 storerooms for site IKWAJ"
echo "   Let's try with the source location as destination (should exist)"
echo ""

# Test 1: Use RIP001 as destination (we know it exists)
echo "📋 TEST 1: Transfer to RIP001 (known to exist)"
echo "============================================"

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "28-800-0004",
    "to_bin": "TEMP-BIN"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 This should work if RIP001 exists in IKWAJ site"
echo ""

# Test 2: Try with a completely different approach - no bins
echo "📋 TEST 2: Minimal transfer to RIP001"
echo "==================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 0.1,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 Minimal transfer attempt"
echo ""

# Test 3: Create a successful transfer by using a different approach
echo "📋 TEST 3: Check what locations are actually available"
echo "=================================================="

echo "Let's check what storerooms are available in IKWAJ:"
curl -X GET "http://127.0.0.1:5010/api/inventory/transfer-current-item/storerooms?siteid=IKWAJ" \
  -H "Accept: application/json" \
  -s | head -20

echo ""
echo "🔍 Above shows actual available storerooms in IKWAJ"
echo ""

# Test 4: If we can get the storerooms, let's try with the first one
echo "📋 TEST 4: Manual test with known good location"
echo "============================================="

echo "Based on your app's successful storeroom lookup, let's try a manual approach:"
echo "1. Your app found 16 storerooms for IKWAJ"
echo "2. One of them should be valid for transfers"
echo "3. Let's try with a common pattern"

# Try with a pattern that might work
curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 Trying with KWAJ (site prefix)"
echo ""

echo "🎉 SUCCESS CRITERIA"
echo "=================="
echo "Look for a response with:"
echo "  ✅ '_responsemeta': { 'status': '204' }"
echo "  ✅ '_responsedata': {} (empty, no Error object)"
echo "  ✅ HTTP_STATUS: 200"
echo ""
echo "If we get this, the transfer is successful!"
echo ""
echo "📋 NEXT STEPS IF NO SUCCESS:"
echo "=========================="
echo "1. Check Maximo UI for valid locations in IKWAJ site"
echo "2. Create the KWAJ-1058 location in Maximo if needed"
echo "3. Update your application to validate locations before transfer"
echo "4. Use the working API pattern we've confirmed"
