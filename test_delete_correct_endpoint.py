#!/usr/bin/env python3
"""
Test script to test delete functionality with the correct endpoint
"""

import requests
import json

def test_delete_with_correct_endpoint():
    """Test delete functionality with the correct endpoint URL"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2219753'
    
    print(f"🔍 Testing delete with correct endpoint")
    print("=" * 60)
    
    # Get existing files
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        attachments_response = requests.get(attachments_url, timeout=30)
        print(f"   📤 Attachments URL: {attachments_url}")
        print(f"   🔄 Status: {attachments_response.status_code}")
        
        if attachments_response.status_code == 200:
            attachments_data = attachments_response.json()
            if attachments_data.get('success') and attachments_data.get('attachments'):
                attachments = attachments_data['attachments']
                
                print(f"   📋 Found {len(attachments)} attachments:")
                
                # Find files we can delete (uploaded by SOFG118757)
                deletable_files = []
                for i, attachment in enumerate(attachments):
                    filename = attachment.get('filename', 'Unknown')
                    docinfoid = attachment.get('docinfoid')
                    changeby = attachment.get('changeby', 'Unknown')
                    size = attachment.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
                    
                    print(f"      {i+1}. {filename} (ID: {docinfoid}) - {size} bytes - By: {changeby}")
                    
                    if changeby == 'SOFG118757':
                        deletable_files.append(attachment)
                
                if deletable_files:
                    # Test delete with the first deletable file
                    test_file = deletable_files[0]
                    docinfoid = test_file.get('docinfoid')
                    filename = test_file.get('filename')
                    
                    print(f"\n🗑️ Testing delete with: {filename} (ID: {docinfoid})")
                    
                    # CORRECT DELETE ENDPOINT - no /delete at the end
                    delete_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}'
                    
                    try:
                        delete_response = requests.delete(delete_url, timeout=60)
                        print(f"   📤 Delete URL: {delete_url}")
                        print(f"   🔄 Status: {delete_response.status_code}")
                        print(f"   📊 Content Length: {len(delete_response.content)} bytes")
                        print(f"   📋 Content Type: {delete_response.headers.get('content-type', 'Unknown')}")
                        
                        if delete_response.status_code == 200:
                            try:
                                delete_data = delete_response.json()
                                print(f"   ✅ Delete response: {delete_data}")
                                
                                if delete_data.get('success'):
                                    print(f"   🎉 DELETE SUCCESS!")
                                    
                                    # Verify deletion
                                    print(f"\n✅ Verifying deletion...")
                                    import time
                                    time.sleep(2)
                                    
                                    verify_response = requests.get(attachments_url, timeout=30)
                                    if verify_response.status_code == 200:
                                        verify_data = verify_response.json()
                                        if verify_data.get('success'):
                                            remaining_attachments = verify_data.get('attachments', [])
                                            
                                            # Check if file is gone
                                            file_still_exists = any(
                                                str(att.get('docinfoid')) == str(docinfoid) 
                                                for att in remaining_attachments
                                            )
                                            
                                            if file_still_exists:
                                                print(f"   ❌ File still exists after deletion!")
                                                return False
                                            else:
                                                print(f"   ✅ File successfully deleted!")
                                                print(f"   📊 Attachments before: {len(attachments)}")
                                                print(f"   📊 Attachments after: {len(remaining_attachments)}")
                                                return True
                                else:
                                    print(f"   ❌ Delete failed: {delete_data.get('error', 'Unknown error')}")
                                    return False
                            except json.JSONDecodeError:
                                print(f"   ⚠️  Delete returned non-JSON: {delete_response.text[:200]}")
                                return False
                        else:
                            print(f"   ❌ Delete failed with status: {delete_response.status_code}")
                            try:
                                error_data = delete_response.json()
                                print(f"   📝 Error: {error_data}")
                                
                                # Analyze specific errors
                                error_msg = error_data.get('error', '').lower()
                                if 'not found' in error_msg:
                                    print(f"   💡 File may not exist or already deleted")
                                elif 'permission' in error_msg or 'unauthorized' in error_msg:
                                    print(f"   💡 User may not have permission to delete this file")
                                elif 'method not allowed' in error_msg:
                                    print(f"   💡 DELETE method may not be supported")
                                else:
                                    print(f"   💡 Check server logs for detailed error information")
                                    
                            except:
                                print(f"   📝 Response: {delete_response.text[:200]}")
                            return False
                    
                    except Exception as e:
                        print(f"   ❌ Delete exception: {e}")
                        return False
                
                else:
                    print(f"\n⚠️  No deletable files found (no files by SOFG118757)")
                    
                    # Let's try to upload a test file first
                    print(f"\n📤 Uploading a test file to delete...")
                    return upload_and_delete_test_file(base_url, wonum)
            else:
                print(f"   ❌ Failed to get attachments: {attachments_data}")
                return False
        else:
            print(f"   ❌ Attachments request failed: {attachments_response.status_code}")
            return False
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def upload_and_delete_test_file(base_url, wonum):
    """Upload a test file and then delete it"""
    
    print(f"🔄 Upload and delete test workflow")
    print("=" * 40)
    
    # Create a simple test file
    test_content = "This is a test file for delete functionality.\nCreated for testing purposes."
    test_filename = "delete_test.txt"
    
    with open(test_filename, 'w') as f:
        f.write(test_content)
    
    print(f"   📄 Created test file: {test_filename}")
    
    # Upload the file
    upload_url = f'{base_url}/api/workorder/{wonum}/attachments/upload'
    
    try:
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/plain')}
            data = {
                'description': 'Test file for delete functionality',
                'category': 'Attachments'
            }
            
            print(f"   📤 Uploading test file...")
            upload_response = requests.post(upload_url, files=files, data=data, timeout=60)
            
            print(f"   🔄 Upload status: {upload_response.status_code}")
            
            if upload_response.status_code == 200:
                upload_data = upload_response.json()
                if upload_data.get('success'):
                    print(f"   ✅ Upload successful!")
                    
                    # Wait for upload to be processed
                    import time
                    time.sleep(3)
                    
                    # Find the uploaded file
                    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
                    attachments_response = requests.get(attachments_url, timeout=30)
                    
                    if attachments_response.status_code == 200:
                        attachments_data = attachments_response.json()
                        if attachments_data.get('success'):
                            attachments = attachments_data.get('attachments', [])
                            
                            # Find our uploaded file
                            uploaded_file = None
                            for attachment in attachments:
                                if (attachment.get('filename', '').lower() == test_filename.lower() and 
                                    attachment.get('changeby') == 'SOFG118757'):
                                    uploaded_file = attachment
                                    break
                            
                            if uploaded_file:
                                docinfoid = uploaded_file.get('docinfoid')
                                print(f"   📋 Found uploaded file with ID: {docinfoid}")
                                
                                # Now test delete with CORRECT endpoint
                                delete_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}'
                                delete_response = requests.delete(delete_url, timeout=60)
                                
                                print(f"   🗑️ Delete URL: {delete_url}")
                                print(f"   🗑️ Delete status: {delete_response.status_code}")
                                
                                if delete_response.status_code == 200:
                                    try:
                                        delete_data = delete_response.json()
                                        if delete_data.get('success'):
                                            print(f"   ✅ DELETE SUCCESS!")
                                            return True
                                        else:
                                            print(f"   ❌ Delete failed: {delete_data.get('error')}")
                                            return False
                                    except:
                                        print(f"   ⚠️  Delete response: {delete_response.text[:100]}")
                                        return False
                                else:
                                    print(f"   ❌ Delete failed: {delete_response.status_code}")
                                    try:
                                        error_data = delete_response.json()
                                        print(f"   📝 Error: {error_data}")
                                    except:
                                        print(f"   📝 Response: {delete_response.text[:200]}")
                                    return False
                            else:
                                print(f"   ❌ Could not find uploaded file")
                                return False
                        else:
                            print(f"   ❌ Failed to get attachments after upload")
                            return False
                    else:
                        print(f"   ❌ Failed to get attachments: {attachments_response.status_code}")
                        return False
                else:
                    print(f"   ❌ Upload failed: {upload_data}")
                    return False
            else:
                print(f"   ❌ Upload failed: {upload_response.status_code}")
                if upload_response.status_code == 405:
                    print(f"   💡 Session may have expired - try logging in again")
                return False
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False
    
    finally:
        # Clean up test file
        import os
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"   🧹 Cleaned up test file")

if __name__ == "__main__":
    print("🧪 Testing Delete with Correct Endpoint")
    print("=" * 60)
    
    success = test_delete_with_correct_endpoint()
    
    print("\n" + "=" * 60)
    print(f"🎯 Delete Test Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if success:
        print("🎉 Delete functionality is working with correct endpoint!")
    else:
        print("⚠️  Delete functionality still needs investigation")
        print("\n📋 Possible issues:")
        print("1. Session expired - need to login again")
        print("2. User permissions for deletion")
        print("3. Backend delete implementation issues")
        print("4. OSLC API restrictions")
