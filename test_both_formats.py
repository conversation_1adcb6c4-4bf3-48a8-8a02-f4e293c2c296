#!/usr/bin/env python3
"""
Test both array and object formats to see which gives HTTP 204.
"""
import sys
import os
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def test_both_formats():
    """Test both array and object formats."""
    
    print("🔧 Testing both array and object formats")
    print("=" * 60)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Check if logged in
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo.")
        return False
    
    print(f"✅ Logged in as: {getattr(token_manager, 'username', 'Unknown')}")
    
    # Test data
    base_payload = {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "itemsetid": "ITEMSET",
        "siteid": "LCVKNT",
        "location": "RIP001",
        "issueunit": "RO",
        "minlevel": 0,
        "orderqty": 1,
        "invbalances": [
            {
                "binnum": "28-800-0004",
                "curbal": 30,
                "physcnt": 0,
                "physcntdate": "2021-09-24T09:16:12",
                "conditioncode": "A1",
                "lotnum": "",
                "reconciled": True,
                "memo": "",
                "controlacc": "",
                "shrinkageacc": ""
            }
        ]
    }
    
    # Test 1: Array format (as you specified)
    array_payload = [base_payload]
    
    # Test 2: Object format (what Maximo seems to expect)
    object_payload = base_payload
    
    url = f"{BASE_URL}/oslc/os/mxapiinventory"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test array format
    print(f"\n🔄 Test 1: Array format")
    print(f"📋 Payload: [object]")
    
    try:
        response = token_manager.session.post(
            url,
            json=array_payload,
            headers=headers,
            timeout=(5.0, 30)
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 204:
            print(f"✅ SUCCESS! HTTP 204 - Array format works")
            return True
        else:
            print(f"❌ Array format failed with {response.status_code}")
            if response.text:
                try:
                    error_data = response.json()
                    print(f"📋 Error: {error_data}")
                except:
                    print(f"📋 Response: {response.text[:200]}")
        
    except Exception as e:
        print(f"❌ Array test failed: {str(e)}")
    
    # Test object format
    print(f"\n🔄 Test 2: Object format")
    print(f"📋 Payload: object")
    
    try:
        response = token_manager.session.post(
            url,
            json=object_payload,
            headers=headers,
            timeout=(5.0, 30)
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 204:
            print(f"✅ SUCCESS! HTTP 204 - Object format works")
            return True
        else:
            print(f"❌ Object format failed with {response.status_code}")
            if response.text:
                try:
                    error_data = response.json()
                    print(f"📋 Error: {error_data}")
                except:
                    print(f"📋 Response: {response.text[:200]}")
        
    except Exception as e:
        print(f"❌ Object test failed: {str(e)}")
    
    return False

def main():
    """Main function."""
    print("🚀 Testing both formats to find HTTP 204")
    print("=" * 50)
    
    success = test_both_formats()
    
    if success:
        print("\n🎉 FOUND THE CORRECT FORMAT THAT RETURNS HTTP 204!")
    else:
        print("\n❌ Neither format returned HTTP 204")
    
    return success

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
