#!/usr/bin/env python3
"""
Maximo Integration Explorer™ - Comprehensive Inventory Service Discovery
Using Working Authentication Patterns

This script uses the known working API key and authentication patterns to systematically
discover and document ALL inventory service methods available in IBM Maximo.

Author: Maximo Integration Explorer™
Date: 2025-07-16
"""

import sys
import os
import json
import time
import requests
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin
import base64

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"  # Known working API key

class MaximoInventoryServiceComprehensiveDiscovery:
    """Comprehensive Maximo Inventory Service Discovery using working patterns"""
    
    def __init__(self):
        """Initialize the discovery tool."""
        self.base_url = BASE_URL
        self.api_key = API_KEY
        self.discovery_results = {
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'api_key_preview': f"{self.api_key[:10]}...{self.api_key[-10:]}",
            'soap_wsdl': {},
            'rest_oslc': {},
            'working_patterns': {},
            'consolidated_methods': []
        }
        
    def discover_soap_wsdl_services(self):
        """Discover SOAP/WSDL services using multiple endpoint patterns."""
        print("\n🌐 SOAP/WSDL Service Discovery")
        print("=" * 80)
        
        # Try multiple WSDL endpoint patterns
        wsdl_endpoints = [
            f"{self.base_url}/services/InventoryService?wsdl",
            f"{self.base_url}/wsdl/InventoryService.wsdl",
            f"{self.base_url}/services/MXAPIINVENTORY?wsdl",
            f"{self.base_url}/services/inventory?wsdl"
        ]
        
        headers = {
            "Accept": "text/xml, application/xml",
            "apikey": self.api_key
        }
        
        for wsdl_url in wsdl_endpoints:
            print(f"📋 Testing WSDL endpoint: {wsdl_url}")
            
            try:
                response = requests.get(
                    wsdl_url,
                    headers=headers,
                    timeout=(3.05, 30)
                )
                
                print(f"  Status: {response.status_code}")
                
                if response.status_code == 200:
                    print("  ✅ WSDL found!")
                    self._parse_wsdl_content(response.text, wsdl_url)
                    break
                elif response.status_code == 404:
                    print("  ❌ Not found")
                else:
                    print(f"  ❌ Error: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Exception: {str(e)}")
                
        if not self.discovery_results['soap_wsdl']:
            print("❌ No WSDL services discovered")
            self.discovery_results['soap_wsdl']['status'] = 'No WSDL endpoints found'
            
    def _parse_wsdl_content(self, wsdl_content: str, wsdl_url: str):
        """Parse WSDL XML content to extract operations."""
        try:
            root = ET.fromstring(wsdl_content)
            
            # Define namespaces
            namespaces = {
                'wsdl': 'http://schemas.xmlsoap.org/wsdl/',
                'soap': 'http://schemas.xmlsoap.org/wsdl/soap/',
                'tns': 'http://www.ibm.com/maximo'
            }
            
            operations = []
            
            # Find all operations in portType
            for porttype in root.findall('.//wsdl:portType', namespaces):
                porttype_name = porttype.get('name', 'Unknown')
                print(f"  📦 Found PortType: {porttype_name}")
                
                for operation in porttype.findall('wsdl:operation', namespaces):
                    op_name = operation.get('name')
                    
                    # Get input/output messages
                    input_elem = operation.find('wsdl:input', namespaces)
                    output_elem = operation.find('wsdl:output', namespaces)
                    
                    op_info = {
                        'name': op_name,
                        'porttype': porttype_name,
                        'input_message': input_elem.get('message') if input_elem is not None else None,
                        'output_message': output_elem.get('message') if output_elem is not None else None
                    }
                    
                    operations.append(op_info)
                    print(f"    🔧 Operation: {op_name}")
                    
            self.discovery_results['soap_wsdl'] = {
                'wsdl_url': wsdl_url,
                'operations': operations,
                'total_operations': len(operations)
            }
            
        except ET.ParseError as e:
            print(f"  ❌ WSDL parsing error: {str(e)}")
            self.discovery_results['soap_wsdl']['parse_error'] = str(e)
            
    def discover_rest_oslc_capabilities(self):
        """Discover REST/OSLC capabilities using working patterns."""
        print("\n🔗 REST/OSLC Capabilities Discovery")
        print("=" * 80)
        
        # Test both API and OSLC endpoints
        endpoints = [
            f"{self.base_url}/api/os/mxapiinventory",
            f"{self.base_url}/oslc/os/mxapiinventory"
        ]
        
        for endpoint in endpoints:
            endpoint_type = "API" if "/api/" in endpoint else "OSLC"
            print(f"\n📋 Testing {endpoint_type} endpoint: {endpoint}")
            
            # Test basic GET capabilities
            self._test_basic_get(endpoint, endpoint_type)
            
            # Test OPTIONS method
            self._test_options_method(endpoint, endpoint_type)
            
            # Test schema discovery
            self._test_schema_discovery(endpoint, endpoint_type)
            
    def _test_basic_get(self, endpoint: str, endpoint_type: str):
        """Test basic GET capabilities."""
        print(f"  📋 Testing basic GET for {endpoint_type}")
        
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        try:
            response = requests.get(
                endpoint,
                params={
                    "oslc.select": "itemnum,siteid,location,curbaltotal",
                    "oslc.pageSize": "1",
                    "lean": "1"
                },
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"    ✅ Success - Structure: {list(data.keys())}")
                    
                    # Store successful pattern
                    if endpoint_type not in self.discovery_results['rest_oslc']:
                        self.discovery_results['rest_oslc'][endpoint_type] = {}
                        
                    self.discovery_results['rest_oslc'][endpoint_type]['basic_get'] = {
                        'status': 'success',
                        'endpoint': endpoint,
                        'response_structure': list(data.keys()),
                        'sample_count': len(data.get('member', data.get('rdfs:member', [])))
                    }
                    
                except Exception as parse_error:
                    print(f"    ❌ JSON parsing failed: {str(parse_error)}")
            else:
                print(f"    ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            
    def _test_options_method(self, endpoint: str, endpoint_type: str):
        """Test OPTIONS method."""
        print(f"  📋 Testing OPTIONS for {endpoint_type}")
        
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        try:
            response = requests.options(
                endpoint,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"    Status: {response.status_code}")
            
            if 'Allow' in response.headers:
                allowed_methods = response.headers['Allow']
                print(f"    ✅ Allowed Methods: {allowed_methods}")
                
                if endpoint_type not in self.discovery_results['rest_oslc']:
                    self.discovery_results['rest_oslc'][endpoint_type] = {}
                    
                self.discovery_results['rest_oslc'][endpoint_type]['allowed_methods'] = allowed_methods.split(', ')
            else:
                print("    ❌ No Allow header")
                
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            
    def _test_schema_discovery(self, endpoint: str, endpoint_type: str):
        """Test schema discovery."""
        print(f"  📋 Testing schema discovery for {endpoint_type}")
        
        schema_url = f"{endpoint}?_schema"
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        try:
            response = requests.get(
                schema_url,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    schema_data = response.json()
                    print(f"    ✅ Schema found - Keys: {list(schema_data.keys())}")
                    
                    if endpoint_type not in self.discovery_results['rest_oslc']:
                        self.discovery_results['rest_oslc'][endpoint_type] = {}
                        
                    self.discovery_results['rest_oslc'][endpoint_type]['schema'] = {
                        'available': True,
                        'keys': list(schema_data.keys()),
                        'url': schema_url
                    }
                    
                except Exception as parse_error:
                    print(f"    ❌ Schema parsing failed: {str(parse_error)}")
            else:
                print(f"    ❌ Schema not available: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            
    def discover_wsmethods(self):
        """Discover web service methods using working patterns."""
        print("\n🔧 WSMethod Discovery")
        print("=" * 80)
        
        # Known inventory-related wsmethods from your codebase
        inventory_wsmethods = [
            'ADDCHANGE', 'ADJUSTCURRENTBALANCE', 'ADJUSTPHYSICALCOUNT',
            'TRANSFERCURITEM', 'ISSUECURITEM', 'RECEIVECURITEM', 'RETURNCURITEM',
            'CREATE', 'UPDATE', 'DELETE', 'SAVE', 'VALIDATE',
            'GETINVENTORY', 'GETBALANCE', 'GETAVAILABILITY'
        ]
        
        endpoint_url = f"{self.base_url}/api/os/mxapiinventory"
        discovered_methods = {}
        
        for wsmethod in inventory_wsmethods:
            print(f"  🔍 Testing wsmethod: {wsmethod}")
            
            test_url = f"{endpoint_url}?action=wsmethod:{wsmethod}"
            
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "apikey": self.api_key
            }
            
            try:
                # Test with minimal payload
                response = requests.post(
                    test_url,
                    json={"test": "discovery"},
                    headers=headers,
                    timeout=(3.05, 15)
                )
                
                method_info = {
                    'status_code': response.status_code,
                    'exists': self._analyze_wsmethod_response(response),
                    'response_preview': self._get_response_preview(response)
                }
                
                if method_info['exists']:
                    print(f"    ✅ Method available - Status: {response.status_code}")
                else:
                    print(f"    ❌ Method not available - Status: {response.status_code}")
                    
                discovered_methods[wsmethod] = method_info
                
            except Exception as e:
                discovered_methods[wsmethod] = {
                    'status_code': None,
                    'exists': False,
                    'error': str(e)
                }
                print(f"    ❌ Error: {str(e)}")
                
        self.discovery_results['rest_oslc']['wsmethods'] = discovered_methods
        
        # Count valid methods
        valid_methods = [m for m, info in discovered_methods.items() if info.get('exists', False)]
        print(f"\n📊 Discovered {len(valid_methods)} available wsmethods")
        
    def _analyze_wsmethod_response(self, response) -> bool:
        """Analyze response to determine if wsmethod is available."""
        # Method exists if we get anything other than 404 or method-not-found errors
        if response.status_code == 404:
            return False
            
        try:
            response_text = response.text.lower()
            not_found_indicators = [
                'method not found',
                'unknown method',
                'invalid action',
                'wsmethod not found',
                'action not supported'
            ]
            
            for indicator in not_found_indicators:
                if indicator in response_text:
                    return False
                    
            # If we get here, method likely exists (even if it returns validation errors)
            return True
            
        except:
            return response.status_code not in [404, 501]
            
    def _get_response_preview(self, response) -> str:
        """Get a preview of the response content."""
        try:
            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()
                preview = json.dumps(data, indent=2)
                return preview[:300] + "..." if len(preview) > 300 else preview
            else:
                return response.text[:300] + "..." if len(response.text) > 300 else response.text
        except:
            return f"Status: {response.status_code}, Headers: {dict(response.headers)}"

    def discover_nested_object_operations(self):
        """Discover nested object operations like transfercuritem, invbalances."""
        print("\n🔗 Nested Object Operations Discovery")
        print("=" * 80)

        # Get a sample inventory record first
        sample_inventory = self._get_sample_inventory_record()

        if not sample_inventory:
            print("❌ Could not get sample inventory record for nested object testing")
            return

        inventory_id = sample_inventory.get('inventoryid')
        if not inventory_id:
            print("❌ Sample inventory record missing inventoryid")
            return

        print(f"📦 Using sample inventory ID: {inventory_id}")

        # Test nested object endpoints
        base_url = f"{self.base_url}/api/os/mxapiinventory"
        encoded_id = base64.b64encode(str(inventory_id).encode()).decode()

        nested_endpoints = [
            f"{base_url}/_{encoded_id}/transfercuritem",
            f"{base_url}/_{encoded_id}/invbalances",
            f"{base_url}/_{encoded_id}/invcost",
            f"{base_url}/_{encoded_id}/invvendor"
        ]

        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }

        nested_results = {}

        for endpoint in nested_endpoints:
            endpoint_name = endpoint.split('/')[-1]
            print(f"  🔍 Testing nested endpoint: {endpoint_name}")

            try:
                # Test GET
                response = requests.get(
                    endpoint,
                    headers=headers,
                    timeout=(3.05, 15)
                )

                print(f"    GET Status: {response.status_code}")

                nested_results[endpoint_name] = {
                    'endpoint': endpoint,
                    'get_status': response.status_code,
                    'get_success': response.status_code == 200
                }

                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"    ✅ GET Success - Structure: {list(data.keys())}")
                        nested_results[endpoint_name]['get_structure'] = list(data.keys())
                    except:
                        print(f"    ❌ GET Response not JSON")

            except Exception as e:
                print(f"    ❌ GET Error: {str(e)}")
                nested_results[endpoint_name] = {'error': str(e)}

        self.discovery_results['rest_oslc']['nested_objects'] = nested_results

    def _get_sample_inventory_record(self):
        """Get a sample inventory record for testing."""
        endpoint_url = f"{self.base_url}/api/os/mxapiinventory"

        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }

        try:
            response = requests.get(
                endpoint_url,
                params={
                    "oslc.select": "inventoryid,itemnum,siteid,location",
                    "oslc.pageSize": "1",
                    "lean": "1"
                },
                headers=headers,
                timeout=(3.05, 15)
            )

            if response.status_code == 200:
                data = response.json()
                members = data.get('member', data.get('rdfs:member', []))
                if members:
                    return members[0]

        except Exception as e:
            print(f"Error getting sample inventory: {str(e)}")

        return None

    def consolidate_all_methods(self):
        """Consolidate all discovered methods into a unified structure."""
        print("\n📊 Consolidating All Discovery Results")
        print("=" * 80)

        consolidated = []

        # Add SOAP/WSDL operations
        soap_operations = self.discovery_results.get('soap_wsdl', {}).get('operations', [])
        for op in soap_operations:
            consolidated.append({
                'name': op['name'],
                'type': 'SOAP/WSDL',
                'endpoint': self.discovery_results['soap_wsdl'].get('wsdl_url', 'Unknown'),
                'method': 'POST',
                'authentication': ['API Key', 'Session'],
                'input_message': op.get('input_message'),
                'output_message': op.get('output_message'),
                'description': f"SOAP operation from InventoryService WSDL"
            })

        # Add REST/OSLC basic operations
        for endpoint_type, endpoint_data in self.discovery_results.get('rest_oslc', {}).items():
            if endpoint_type in ['API', 'OSLC'] and isinstance(endpoint_data, dict):

                # Add basic CRUD operations
                if 'allowed_methods' in endpoint_data:
                    for method in endpoint_data['allowed_methods']:
                        consolidated.append({
                            'name': f"REST_{method}",
                            'type': f'REST/{endpoint_type}',
                            'endpoint': endpoint_data.get('basic_get', {}).get('endpoint', 'Unknown'),
                            'method': method,
                            'authentication': ['API Key', 'Session'],
                            'description': f"Standard REST {method} operation via {endpoint_type}"
                        })

        # Add discovered wsmethods
        wsmethods = self.discovery_results.get('rest_oslc', {}).get('wsmethods', {})
        for wsmethod, info in wsmethods.items():
            if info.get('exists', False):
                consolidated.append({
                    'name': wsmethod,
                    'type': 'WSMethod',
                    'endpoint': f"{self.base_url}/api/os/mxapiinventory?action=wsmethod:{wsmethod}",
                    'method': 'POST',
                    'authentication': ['API Key', 'Session'],
                    'status_code': info.get('status_code'),
                    'description': f"Web service method for inventory operations"
                })

        # Add nested object operations
        nested_objects = self.discovery_results.get('rest_oslc', {}).get('nested_objects', {})
        for obj_name, obj_info in nested_objects.items():
            if obj_info.get('get_success', False):
                consolidated.append({
                    'name': f"GET_{obj_name}",
                    'type': 'Nested Object',
                    'endpoint': obj_info.get('endpoint', 'Unknown'),
                    'method': 'GET',
                    'authentication': ['API Key', 'Session'],
                    'description': f"Access {obj_name} nested object data"
                })

        self.discovery_results['consolidated_methods'] = consolidated

        print(f"✅ Consolidated {len(consolidated)} total methods:")
        for method in consolidated:
            print(f"  🔧 {method['name']} ({method['type']})")

    def generate_comprehensive_report(self):
        """Generate comprehensive integration report."""
        print("\n📋 Generating Comprehensive Integration Report")
        print("=" * 80)

        # Save JSON report
        json_filename = f"maximo_inventory_comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_filename, 'w') as f:
            json.dump(self.discovery_results, f, indent=2, default=str)
        print(f"✅ JSON report saved: {json_filename}")

        # Generate markdown report
        markdown_filename = f"maximo_inventory_integration_guide_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self._generate_integration_markdown(markdown_filename)
        print(f"✅ Integration guide saved: {markdown_filename}")

        return json_filename, markdown_filename

    def _generate_integration_markdown(self, filename: str):
        """Generate comprehensive integration markdown guide."""
        with open(filename, 'w') as f:
            f.write("# Maximo Inventory Service Integration Guide\n\n")
            f.write(f"**Generated:** {self.discovery_results['timestamp']}\n")
            f.write(f"**Base URL:** {self.discovery_results['base_url']}\n")
            f.write(f"**API Key:** {self.discovery_results['api_key_preview']}\n\n")

            # Authentication section
            f.write("## Authentication\n\n")
            f.write("### API Key Authentication (Recommended)\n")
            f.write("```python\n")
            f.write("headers = {\n")
            f.write("    'Accept': 'application/json',\n")
            f.write("    'apikey': 'your-api-key-here'\n")
            f.write("}\n")
            f.write("```\n\n")

            # Available methods summary
            consolidated = self.discovery_results.get('consolidated_methods', [])
            f.write(f"## Available Methods ({len(consolidated)} total)\n\n")

            # Group by type
            method_types = {}
            for method in consolidated:
                method_type = method['type']
                if method_type not in method_types:
                    method_types[method_type] = []
                method_types[method_type].append(method)

            for method_type, methods in method_types.items():
                f.write(f"### {method_type} ({len(methods)} methods)\n\n")
                f.write("| Name | Method | Endpoint | Description |\n")
                f.write("|------|--------|----------|-------------|\n")
                for method in methods:
                    endpoint = method['endpoint'].replace(self.discovery_results['base_url'], '')
                    f.write(f"| {method['name']} | {method['method']} | {endpoint} | {method['description']} |\n")
                f.write("\n")

            # Integration examples
            f.write("## Integration Examples\n\n")

            # Basic GET example
            f.write("### Basic Inventory Query\n")
            f.write("```python\n")
            f.write("import requests\n\n")
            f.write(f"url = '{self.base_url}/api/os/mxapiinventory'\n")
            f.write("headers = {'Accept': 'application/json', 'apikey': 'your-api-key'}\n")
            f.write("params = {\n")
            f.write("    'oslc.select': 'itemnum,siteid,location,curbaltotal',\n")
            f.write("    'oslc.where': 'siteid=\"YOURSITE\"',\n")
            f.write("    'oslc.pageSize': '10'\n")
            f.write("}\n")
            f.write("response = requests.get(url, headers=headers, params=params)\n")
            f.write("```\n\n")

            # WSMethod example
            wsmethods = [m for m in consolidated if m['type'] == 'WSMethod']
            if wsmethods:
                sample_wsmethod = wsmethods[0]
                f.write(f"### {sample_wsmethod['name']} WSMethod\n")
                f.write("```python\n")
                f.write(f"url = '{sample_wsmethod['endpoint']}'\n")
                f.write("headers = {\n")
                f.write("    'Accept': 'application/json',\n")
                f.write("    'Content-Type': 'application/json',\n")
                f.write("    'apikey': 'your-api-key'\n")
                f.write("}\n")
                f.write("payload = {'your': 'data'}\n")
                f.write("response = requests.post(url, headers=headers, json=payload)\n")
                f.write("```\n\n")


def main():
    """Main execution function."""
    print("🚀 Maximo Integration Explorer™ - Comprehensive Discovery")
    print("=" * 80)
    print("Using Working Authentication Patterns")
    print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # Initialize discovery
    discovery = MaximoInventoryServiceComprehensiveDiscovery()

    # Step 1: Discover SOAP/WSDL services
    discovery.discover_soap_wsdl_services()

    # Step 2: Discover REST/OSLC capabilities
    discovery.discover_rest_oslc_capabilities()

    # Step 3: Discover web service methods
    discovery.discover_wsmethods()

    # Step 4: Discover nested object operations
    discovery.discover_nested_object_operations()

    # Step 5: Consolidate all methods
    discovery.consolidate_all_methods()

    # Step 6: Generate comprehensive report
    json_file, markdown_file = discovery.generate_comprehensive_report()

    # Final summary
    print("\n🎉 Comprehensive Discovery Complete!")
    print("=" * 80)
    print(f"📄 JSON Report: {json_file}")
    print(f"📄 Integration Guide: {markdown_file}")
    print("\n💡 Ready for Python Integration Module!")


if __name__ == "__main__":
    main()
