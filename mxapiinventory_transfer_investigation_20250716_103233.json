{"investigation_date": "2025-07-16T10:30:52.024856", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "oslc_endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory", "authentication": "OSLC Token Session", "discovered_methods": [], "failed_methods": [{"method": "transfercurrentitem", "timestamp": "2025-07-16T10:30:56.800962", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfercurrentitem"}, {"method": "transfercuritem", "timestamp": "2025-07-16T10:31:01.264757", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfercuritem"}, {"method": "issuecurrentitem", "timestamp": "2025-07-16T10:31:06.595758", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=issuecurrentitem"}, {"method": "receivecurrentitem", "timestamp": "2025-07-16T10:31:11.240923", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=receivecurrentitem"}, {"method": "transferitem", "timestamp": "2025-07-16T10:31:16.196357", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferitem"}, {"method": "transferinventory", "timestamp": "2025-07-16T10:31:21.544222", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferinventory"}, {"method": "transferstock", "timestamp": "2025-07-16T10:31:26.293934", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferstock"}, {"method": "transfermaterial", "timestamp": "2025-07-16T10:31:30.703595", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfermaterial"}, {"method": "transferasset", "timestamp": "2025-07-16T10:31:35.468498", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferasset"}, {"method": "transferbalance", "timestamp": "2025-07-16T10:31:41.282244", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferbalance"}, {"method": "transferquantity", "timestamp": "2025-07-16T10:31:45.912616", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferquantity"}, {"method": "movetransfer", "timestamp": "2025-07-16T10:31:50.744211", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=movetransfer"}, {"method": "createtransfer", "timestamp": "2025-07-16T10:31:55.856016", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=createtransfer"}, {"method": "processtransfer", "timestamp": "2025-07-16T10:32:01.929144", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=processtransfer"}, {"method": "completetransfer", "timestamp": "2025-07-16T10:32:21.170803", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=completetransfer"}, {"method": "validatetransfer", "timestamp": "2025-07-16T10:32:28.881503", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=validatetransfer"}], "endpoint_info": {"api_accessible": false}, "test_results": [{"method": "transfercurrentitem", "timestamp": "2025-07-16T10:30:56.800962", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfercurrentitem"}, {"method": "transfercuritem", "timestamp": "2025-07-16T10:31:01.264757", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfercuritem"}, {"method": "issuecurrentitem", "timestamp": "2025-07-16T10:31:06.595758", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=issuecurrentitem"}, {"method": "receivecurrentitem", "timestamp": "2025-07-16T10:31:11.240923", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=receivecurrentitem"}, {"method": "transferitem", "timestamp": "2025-07-16T10:31:16.196357", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferitem"}, {"method": "transferinventory", "timestamp": "2025-07-16T10:31:21.544222", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferinventory"}, {"method": "transferstock", "timestamp": "2025-07-16T10:31:26.293934", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferstock"}, {"method": "transfermaterial", "timestamp": "2025-07-16T10:31:30.703595", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfermaterial"}, {"method": "transferasset", "timestamp": "2025-07-16T10:31:35.468498", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferasset"}, {"method": "transferbalance", "timestamp": "2025-07-16T10:31:41.282244", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferbalance"}, {"method": "transferquantity", "timestamp": "2025-07-16T10:31:45.912616", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transferquantity"}, {"method": "movetransfer", "timestamp": "2025-07-16T10:31:50.744211", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=movetransfer"}, {"method": "createtransfer", "timestamp": "2025-07-16T10:31:55.856016", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=createtransfer"}, {"method": "processtransfer", "timestamp": "2025-07-16T10:32:01.929144", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=processtransfer"}, {"method": "completetransfer", "timestamp": "2025-07-16T10:32:21.170803", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=completetransfer"}, {"method": "validatetransfer", "timestamp": "2025-07-16T10:32:28.881503", "status": "not_found", "http_status": 200, "response_data": null, "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=validatetransfer"}]}