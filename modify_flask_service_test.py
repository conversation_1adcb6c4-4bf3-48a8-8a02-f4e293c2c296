#!/usr/bin/env python3
"""
Modify Flask Service Test
=========================

Create a modified version of the inventory transfer service that can test
different payload structures for cross-site transfers, then test via Flask app.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import requests
import time
from datetime import datetime

def create_test_endpoint():
    """Create a test endpoint payload for cross-site testing."""
    print("🔧 CREATING TEST ENDPOINT FOR CROSS-SITE PAYLOAD TESTING")
    print("=" * 60)
    
    # Create a simple test script that we can call via Flask
    test_script = '''
import sys
import os
import json
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def test_cross_site_payload(payload_type="destination_site"):
    """Test cross-site transfer with different payload structures."""
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        return {"error": "Not authenticated", "success": False}
    
    api_endpoint = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    # Different payload structures based on type
    if payload_type == "destination_site":
        # Test Case 1: Destination Site as Top-Level
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "itemsetid": "ITEMSET",
                "siteid": "IKWAJ",
                "location": "KWAJ-1058",
                "issueunit": "RO",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058",
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": "RO",
                        "frombinnum": "DEFAULT",
                        "tobinnum": "DEFAULT",
                        "fromlotnum": "DEFAULT",
                        "tolotnum": "DEFAULT",
                        "fromconditioncode": "A1",
                        "toconditioncode": "A1"
                    }
                ]
            }
        ]
    elif payload_type == "source_site":
        # Test Case 2: Source Site as Top-Level
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "itemsetid": "ITEMSET",
                "siteid": "LCVKWT",
                "location": "RIP001",
                "issueunit": "RO",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058",
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": "RO",
                        "frombinnum": "DEFAULT",
                        "tobinnum": "DEFAULT",
                        "fromlotnum": "DEFAULT",
                        "tolotnum": "DEFAULT",
                        "fromconditioncode": "A1",
                        "toconditioncode": "A1"
                    }
                ]
            }
        ]
    elif payload_type == "minimal_destination":
        # Test Case 3: Minimal Destination Context
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "siteid": "IKWAJ",
                "location": "KWAJ-1058",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058"
                    }
                ]
            }
        ]
    elif payload_type == "dual_record":
        # Test Case 4: Dual Record Approach
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "siteid": "LCVKWT",
                "location": "RIP001",
                "issueunit": "RO",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058",
                        "issueunit": "RO"
                    }
                ]
            },
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "siteid": "IKWAJ",
                "location": "KWAJ-1058",
                "issueunit": "RO"
            }
        ]
    else:
        return {"error": f"Unknown payload type: {payload_type}", "success": False}
    
    try:
        # Submit to Maximo API
        response = token_manager.session.post(
            api_endpoint,
            json=payload,
            headers={
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            },
            timeout=(5.0, 30)
        )
        
        result = {
            "payload_type": payload_type,
            "http_status": response.status_code,
            "success": False,
            "response_data": None,
            "error": None
        }
        
        if response.text:
            try:
                data = response.json()
                result["response_data"] = data
                
                # Check for success
                if response.status_code == 200:
                    if isinstance(data, list) and len(data) > 0:
                        first_item = data[0]
                        if first_item.get('_responsemeta', {}).get('status') == '204':
                            result["success"] = True
                            return result
                        elif '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                            error = first_item['_responsedata']['Error']
                            result["error"] = f"{error.get('reasonCode')} - {error.get('message')}"
                
            except json.JSONDecodeError:
                result["error"] = "Non-JSON response"
                result["response_data"] = response.text
        
        return result
        
    except Exception as e:
        return {
            "payload_type": payload_type,
            "success": False,
            "error": str(e),
            "http_status": None,
            "response_data": None
        }

if __name__ == "__main__":
    import sys
    payload_type = sys.argv[1] if len(sys.argv) > 1 else "destination_site"
    result = test_cross_site_payload(payload_type)
    print(json.dumps(result, indent=2))
'''
    
    # Save the test script
    with open('test_cross_site_payload.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Test script created: test_cross_site_payload.py")
    return True

def run_comprehensive_cross_site_test():
    """Run comprehensive cross-site transfer tests."""
    print("🚀 COMPREHENSIVE CROSS-SITE TRANSFER TEST")
    print("=" * 50)
    print("🎯 Objective: Test different payload structures via authenticated session")
    print("📋 Strategy: Use working Flask authentication with modified payloads")
    print("")
    
    # Create test script
    if not create_test_endpoint():
        print("❌ Failed to create test script")
        return
    
    # Test cases to run
    test_cases = [
        {
            "name": "Destination Site as Top-Level",
            "type": "destination_site",
            "description": "Top-level inventory record in destination site (IKWAJ)"
        },
        {
            "name": "Source Site as Top-Level", 
            "type": "source_site",
            "description": "Top-level inventory record in source site (LCVKWT)"
        },
        {
            "name": "Minimal Destination Context",
            "type": "minimal_destination", 
            "description": "Minimal payload with destination site context"
        },
        {
            "name": "Dual Record Approach",
            "type": "dual_record",
            "description": "Create inventory records in both sites"
        }
    ]
    
    successful_tests = []
    
    # Run each test case
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 TEST {i}: {test_case['name']}")
        print(f"📝 {test_case['description']}")
        print("=" * 70)
        
        try:
            # Run the test script
            import subprocess
            result = subprocess.run(
                ['python3', 'test_cross_site_payload.py', test_case['type']], 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            if result.returncode == 0:
                try:
                    data = json.loads(result.stdout)
                    print(f"📊 HTTP Status: {data.get('http_status')}")
                    print(f"✅ Success: {data.get('success')}")
                    
                    if data.get('success'):
                        print("🎉 SUCCESS! Cross-site transfer worked!")
                        successful_tests.append({
                            'test_case': test_case,
                            'result': data
                        })
                        
                        # Save successful result
                        filename = f"successful_cross_site_result_{i}.json"
                        with open(filename, 'w') as f:
                            json.dump(data, f, indent=2)
                        print(f"💾 Successful result saved: {filename}")
                    else:
                        error_msg = data.get('error', 'Unknown error')
                        print(f"❌ Failed: {error_msg}")
                    
                    if data.get('response_data'):
                        print(f"📋 Response: {json.dumps(data['response_data'], indent=2)[:500]}...")
                    
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON response: {result.stdout}")
            else:
                print(f"❌ Script failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏱️  Test timeout")
        except Exception as e:
            print(f"❌ Test error: {str(e)}")
        
        # Brief pause between tests
        time.sleep(2)
    
    # Generate summary
    print(f"\n📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 40)
    print(f"🔬 Total tests: {len(test_cases)}")
    print(f"✅ Successful: {len(successful_tests)}")
    print(f"❌ Failed: {len(test_cases) - len(successful_tests)}")
    
    if successful_tests:
        print(f"\n🎉 SUCCESSFUL CROSS-SITE PATTERNS FOUND!")
        print("=" * 50)
        
        for i, success in enumerate(successful_tests, 1):
            test_case = success['test_case']
            result = success['result']
            print(f"\n✅ Success {i}: {test_case['name']}")
            print(f"   Type: {test_case['type']}")
            print(f"   Description: {test_case['description']}")
            print(f"   HTTP Status: {result.get('http_status')}")
        
        # Save all successful patterns
        with open('all_successful_cross_site_results.json', 'w') as f:
            json.dump(successful_tests, f, indent=2)
        print(f"\n💾 All successful results saved: all_successful_cross_site_results.json")
        
        # Store successful pattern for memory
        best_pattern = successful_tests[0]  # Use first successful pattern
        memory_data = {
            "successful_cross_site_transfer_pattern": {
                "payload_type": best_pattern['test_case']['type'],
                "description": best_pattern['test_case']['description'],
                "validation_approach": "Uses proper site context in top-level inventory record",
                "implementation_notes": "Modify inventory transfer service to use this payload structure for cross-site transfers"
            }
        }
        
        with open('cross_site_transfer_memory.json', 'w') as f:
            json.dump(memory_data, f, indent=2)
        print(f"💾 Memory data saved: cross_site_transfer_memory.json")
        
    else:
        print(f"\n❌ NO SUCCESSFUL CROSS-SITE PATTERNS FOUND")
        print("=" * 50)
        print("📋 All payload structure variations failed")
        print("🔍 This indicates the validation is deeply embedded in Maximo")
        print("💡 Alternative approaches may be needed:")
        print("   • Use different Maximo endpoints")
        print("   • Implement via Maximo integration services")
        print("   • Create inventory records in destination site first")
        print("   • Use Maximo UI automation")

def main():
    """Main function."""
    run_comprehensive_cross_site_test()

if __name__ == "__main__":
    main()
