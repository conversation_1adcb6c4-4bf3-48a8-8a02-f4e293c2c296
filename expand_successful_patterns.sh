#!/bin/bash

# Expand Successful Transfer Patterns
# ===================================

echo "🎉 EXPANDING SUCCESSFUL TRANSFER PATTERNS"
echo "========================================"

echo "✅ SUCCESSFUL PATTERN FOUND:"
echo "   Same-site transfer (IKWAJ to IKWAJ)"
echo "   With complete field set including bins, lots, conditions"
echo ""
echo "🔍 OBJECTIVE: Find more working patterns based on success"
echo "📋 STRATEGY: Vary the successful pattern systematically"
echo ""

# Counter for tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for success
test_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..70})"
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "Response:"
    echo "$response"
    
    # Check for success patterns
    if echo "$response" | grep -q '"status": "204"'; then
        echo "🎉 SUCCESS! Transfer completed!"
        echo "💾 Saving successful payload..."
        echo "$payload" > "successful_pattern_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Failed: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING VARIATIONS OF SUCCESSFUL PATTERN"
echo "==========================================="

# Test 1: Successful pattern - baseline (should work again)
test_payload "Baseline Success Pattern" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: Reverse the successful transfer
test_payload "Reverse Successful Transfer" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1115",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: Different storeroom combination in IKWAJ
test_payload "Different IKWAJ Storerooms" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1500",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: Successful pattern with smaller quantity
test_payload "Smaller Quantity" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 0.5,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Without bins (minimal successful pattern)
test_payload "Without Bins" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 6: Without lots
test_payload "Without Lots" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 7: Without conditions
test_payload "Without Conditions" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT"
}'

# Test 8: Minimal successful fields only
test_payload "Minimal Successful Fields" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 9: Cross-site using successful pattern structure
test_payload "Cross-Site with Successful Structure" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 10: Different quantities
test_payload "Different Quantity" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 2.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 EXPANDED PATTERN TEST SUMMARY"
echo "==============================="
echo "✅ Successful transfers: $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 MULTIPLE WORKING PATTERNS FOUND!"
    echo "=================================="
    echo "✅ Total working patterns: $SUCCESS_COUNT"
    echo "💾 Check successful_pattern_*.json files"
    echo ""
    echo "📋 CURL COMMANDS FOR SUCCESSFUL PATTERNS:"
    echo "========================================"
    
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "successful_pattern_$i.json" ]; then
            echo ""
            echo "# Pattern $i - Working Transfer"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat successful_pattern_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -w \"\\nHTTP_STATUS:%{http_code}\\nTIME:%{time_total}\\n\" \\"
            echo "  -s"
        fi
    done
    
    echo ""
    echo "🔧 IMPLEMENTATION RECOMMENDATIONS:"
    echo "================================="
    echo "1. Use same-site transfers when possible (IKWAJ to IKWAJ works)"
    echo "2. Include complete field set: bins, lots, conditions"
    echo "3. Use 'DEFAULT' values for bins and lots"
    echo "4. Use 'A1' condition code"
    echo "5. Ensure inventory exists in source location"
    echo "6. Validate storeroom combinations before transfer"
    
else
    echo ""
    echo "🔍 ANALYSIS NEEDED"
    echo "================="
    echo "Only the original pattern worked. This suggests:"
    echo "• Specific inventory balance requirements"
    echo "• Exact field combination needed"
    echo "• Limited available inventory for transfers"
fi
