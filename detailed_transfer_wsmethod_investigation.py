#!/usr/bin/env python3
"""
Detailed MXAPIINVENTORY Transfer WSMethod Investigation
======================================================

Deep investigation of transfer-related web service methods with actual POST payloads.
Based on initial findings that methods return HTTP 200 but non-JSON responses.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

class DetailedTransferWSMethodInvestigator:
    """Deep investigation of transfer wsmethods with POST payloads."""
    
    def __init__(self, base_url):
        """Initialize the investigator."""
        self.base_url = base_url
        self.token_manager = None
        self.session = None
        self.oslc_endpoint = f"{base_url}/oslc/os/mxapiinventory"
        
        # Priority transfer methods that showed HTTP 200 responses
        self.priority_methods = [
            'transfercurrentitem',
            'transfercuritem', 
            'issuecurrentitem',
            'receivecurrentitem'
        ]
        
        # Test payload for transfer operations
        self.test_payload = {
            "itemnum": "5975-60-V00-0529",
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromlocation": "RIP001",
            "tolocation": "KWAJ-1058",
            "quantity": 1.0,
            "fromissueunit": "RO",
            "toissueunit": "RO",
            "frombinnum": "28-800-0004",
            "fromlotnum": "TEST",
            "tolotnum": "TEST",
            "fromconditioncode": "A1",
            "toconditioncode": "A1"
        }
        
        self.results = {
            'investigation_date': datetime.now().isoformat(),
            'endpoint': self.oslc_endpoint,
            'authentication': 'OSLC Token Session',
            'test_payload': self.test_payload,
            'method_results': [],
            'working_methods': [],
            'error_methods': []
        }
    
    def initialize_authentication(self):
        """Initialize authentication."""
        print("🔐 Initializing OSLC Token Session Authentication")
        print("=" * 60)
        
        try:
            self.token_manager = MaximoTokenManager(self.base_url)
            
            if self.token_manager.is_logged_in():
                self.session = self.token_manager.session
                print("✅ OSLC token session authenticated successfully")
                return True
            else:
                print("❌ OSLC token session not available")
                return False
                
        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            return False
    
    def test_wsmethod_post(self, method_name):
        """Test a wsmethod with POST request and payload."""
        print(f"\n🧪 Testing WSMethod: {method_name}")
        print("-" * 50)
        
        result = {
            'method': method_name,
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'http_status': None,
            'response_data': None,
            'response_text': None,
            'error': None,
            'url_tested': None,
            'payload_used': self.test_payload.copy()
        }
        
        # Test different URL patterns
        url_patterns = [
            f"{self.oslc_endpoint}/{method_name}",
            f"{self.oslc_endpoint}?wsmethod={method_name}",
            f"{self.oslc_endpoint}?action={method_name}"
        ]
        
        for url in url_patterns:
            try:
                result['url_tested'] = url
                print(f"  🔗 Testing POST: {url}")
                
                # Test POST with JSON payload
                response = self.session.post(
                    url,
                    json=self.test_payload,
                    timeout=(3.05, 30),
                    headers={
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    }
                )
                
                result['http_status'] = response.status_code
                result['response_text'] = response.text[:500] if response.text else None
                
                print(f"  📊 POST Status: {response.status_code}")
                print(f"  📄 Response Length: {len(response.text) if response.text else 0} chars")
                
                if response.status_code in [200, 201, 204]:
                    try:
                        if response.text:
                            data = response.json()
                            result['response_data'] = data
                            result['status'] = 'working_json'
                            print(f"  ✅ Method working - JSON response")
                            print(f"  📋 Response: {json.dumps(data, indent=4)[:200]}...")
                        else:
                            result['status'] = 'working_no_content'
                            print(f"  ✅ Method working - No content response")
                        
                        self.results['working_methods'].append(result.copy())
                        return result
                        
                    except json.JSONDecodeError:
                        result['status'] = 'working_non_json'
                        print(f"  ✅ Method working - Non-JSON response")
                        print(f"  📄 Response text: {response.text[:200]}...")
                        self.results['working_methods'].append(result.copy())
                        return result
                        
                elif response.status_code == 400:
                    print(f"  ⚠️  Bad Request - Method exists but payload invalid")
                    try:
                        error_data = response.json()
                        result['response_data'] = error_data
                        result['status'] = 'exists_bad_payload'
                        print(f"  📋 Error: {json.dumps(error_data, indent=4)[:200]}...")
                    except json.JSONDecodeError:
                        result['status'] = 'exists_bad_payload_non_json'
                        print(f"  📄 Error text: {response.text[:200]}...")
                    
                    self.results['working_methods'].append(result.copy())
                    return result
                    
                elif response.status_code == 404:
                    print(f"  ❌ Method not found")
                    continue
                    
                elif response.status_code == 405:
                    print(f"  ❌ Method not allowed")
                    continue
                    
                else:
                    print(f"  ⚠️  Unexpected status: {response.status_code}")
                    print(f"  📄 Response: {response.text[:200]}...")
                    
            except requests.exceptions.Timeout:
                print(f"  ⏱️  Timeout")
                result['error'] = 'timeout'
            except Exception as e:
                print(f"  ❌ Error: {str(e)}")
                result['error'] = str(e)
        
        # If we get here, method was not found or failed
        result['status'] = 'not_working'
        self.results['error_methods'].append(result)
        return result
    
    def test_alternative_payloads(self, method_name):
        """Test alternative payload formats for a method."""
        print(f"\n🔄 Testing Alternative Payloads for: {method_name}")
        print("-" * 50)
        
        # Alternative payload formats
        payloads = [
            # Minimal payload
            {
                "itemnum": "5975-60-V00-0529",
                "fromsiteid": "LCVKWT",
                "tositeid": "IKWAJ",
                "quantity": 1.0
            },
            # With action parameter
            {
                "action": method_name,
                "itemnum": "5975-60-V00-0529",
                "fromsiteid": "LCVKWT",
                "tositeid": "IKWAJ",
                "quantity": 1.0
            },
            # Array format
            [{
                "itemnum": "5975-60-V00-0529",
                "fromsiteid": "LCVKWT",
                "tositeid": "IKWAJ",
                "quantity": 1.0
            }]
        ]
        
        url = f"{self.oslc_endpoint}/{method_name}"
        
        for i, payload in enumerate(payloads, 1):
            try:
                print(f"  🧪 Testing payload {i}: {json.dumps(payload, indent=2)[:100]}...")
                
                response = self.session.post(
                    url,
                    json=payload,
                    timeout=(3.05, 30),
                    headers={
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    }
                )
                
                print(f"  📊 Status: {response.status_code}")
                
                if response.status_code in [200, 201, 204]:
                    print(f"  ✅ Payload {i} successful!")
                    if response.text:
                        try:
                            data = response.json()
                            print(f"  📋 Response: {json.dumps(data, indent=4)[:200]}...")
                        except json.JSONDecodeError:
                            print(f"  📄 Response: {response.text[:200]}...")
                    return payload
                elif response.status_code == 400:
                    print(f"  ⚠️  Payload {i} - Bad Request")
                    if response.text:
                        print(f"  📄 Error: {response.text[:200]}...")
                else:
                    print(f"  ❌ Payload {i} - Status: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Payload {i} - Error: {str(e)}")
        
        return None
    
    def investigate_priority_methods(self):
        """Investigate priority transfer methods."""
        print("\n🔬 Investigating Priority Transfer Methods")
        print("=" * 60)
        
        for i, method in enumerate(self.priority_methods, 1):
            print(f"\n[{i}/{len(self.priority_methods)}] Investigating: {method}")
            
            # Test basic POST
            result = self.test_wsmethod_post(method)
            self.results['method_results'].append(result)
            
            # If method exists but payload is bad, try alternatives
            if result['status'] in ['exists_bad_payload', 'exists_bad_payload_non_json']:
                print(f"  🔄 Method exists but payload needs adjustment...")
                working_payload = self.test_alternative_payloads(method)
                if working_payload:
                    result['working_payload'] = working_payload
            
            # Brief pause between tests
            time.sleep(1)
    
    def generate_detailed_report(self):
        """Generate detailed investigation report."""
        print("\n📋 DETAILED INVESTIGATION REPORT")
        print("=" * 60)
        
        print(f"🕐 Investigation Date: {self.results['investigation_date']}")
        print(f"🔗 Endpoint: {self.results['endpoint']}")
        print(f"🔐 Authentication: {self.results['authentication']}")
        
        working = self.results['working_methods']
        errors = self.results['error_methods']
        
        print(f"\n📊 SUMMARY:")
        print(f"  ✅ Working/Existing Methods: {len(working)}")
        print(f"  ❌ Non-Working Methods: {len(errors)}")
        print(f"  📝 Total Tested: {len(self.priority_methods)}")
        
        if working:
            print(f"\n🎯 DISCOVERED TRANSFER METHODS:")
            for method in working:
                print(f"\n  ✅ {method['method']}")
                print(f"     Status: {method['status']}")
                print(f"     HTTP: {method['http_status']}")
                print(f"     URL: {method['url_tested']}")
                
                if method.get('response_data'):
                    print(f"     Response Data: {json.dumps(method['response_data'], indent=6)[:300]}...")
                elif method.get('response_text'):
                    print(f"     Response Text: {method['response_text'][:200]}...")
                
                if method.get('working_payload'):
                    print(f"     Working Payload: {json.dumps(method['working_payload'], indent=6)}")
        
        # Save detailed report
        report_filename = f"detailed_transfer_wsmethod_investigation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Detailed report saved: {report_filename}")
        
        return self.results

def main():
    """Main investigation function."""
    print("🚀 DETAILED MXAPIINVENTORY TRANSFER WSMETHOD INVESTIGATION")
    print("=" * 70)
    print("Objective: Deep investigation of transfer wsmethods with POST payloads")
    print("Authentication: OSLC Token Session (MaximoTokenManager)")
    print("Scope: Priority transfer methods in MXAPIINVENTORY endpoint")
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    investigator = DetailedTransferWSMethodInvestigator(base_url)
    
    # Initialize authentication
    if not investigator.initialize_authentication():
        print("❌ Authentication failed. Cannot proceed.")
        return
    
    # Investigate priority methods
    investigator.investigate_priority_methods()
    
    # Generate detailed report
    results = investigator.generate_detailed_report()
    
    print("\n🎯 DETAILED INVESTIGATION COMPLETE")
    print("=" * 70)
    
    if results['working_methods']:
        print("✅ Transfer methods discovered! Check the detailed report for implementation details.")
    else:
        print("❌ No working transfer methods found with current payloads.")

if __name__ == "__main__":
    main()
