#!/usr/bin/env python3
"""
Debug the exact Flask PO logic to understand why it's not working
This replicates the fetch_purchase_orders function logic step by step
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add backend path for imports
sys.path.append('backend/auth')

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError as e:
    print(f"❌ Cannot import MaximoTokenManager: {e}")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
TARGET_ITEM = "5975-60-V00-0001"
TARGET_SITE = "LCVKWT"

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def debug_flask_po_logic():
    """Debug the exact Flask PO logic step by step."""
    print_section("DEBUGGING FLASK PO LOGIC")
    
    # Step 1: Initialize token manager (same as Flask)
    print("Step 1: Initialize token manager")
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Step 2: Check authentication (same as Flask)
    print("Step 2: Check authentication")
    if not hasattr(token_manager, 'username') or not token_manager.username:
        print("❌ User not authenticated, skipping PO query")
        return []
    
    print(f"✅ User authenticated: {getattr(token_manager, 'username', 'Unknown')}")
    
    # Step 3: Set up session authentication (same as Flask)
    print("Step 3: Set up session authentication")
    base_url = getattr(token_manager, 'base_url', '')
    api_url = f"{base_url}/oslc/os/mxapipo"
    auth_method = "Session Token"
    headers = {"Accept": "application/json"}
    
    print(f"Base URL: {base_url}")
    print(f"API URL: {api_url}")
    print(f"Auth Method: {auth_method}")
    
    # Step 4: Build query parameters (same as Flask)
    print("Step 4: Build query parameters")
    po_fields = [
        "ponum", "status", "status_description", "currencycode",
        "totalcost", "pretaxtotal", "siteid", "vendor", "description",
        "orderdate", "requireddate", "receipts", "internal"
    ]
    nested_fields = ["poline"]
    all_fields = po_fields + nested_fields
    
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}" and status!="CAN" and status!="CLOSE"'
    
    params = {
        "oslc.select": ",".join(all_fields),
        "oslc.where": where_clause,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    print(f"Where clause: {where_clause}")
    print(f"Parameters: {params}")
    
    # Step 5: Try session authentication (same as Flask)
    print("Step 5: Try session authentication")
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 15),
            headers=headers
        )
        
        print(f"Session Response Status: {response.status_code}")
        print(f"Session Response Headers: {dict(response.headers)}")
        print(f"Session Response Content Type: {response.headers.get('content-type', 'unknown')}")
        print(f"Session Response Preview: {response.text[:200]}...")
        
        # Step 6: Check if session auth failed (same as Flask)
        print("Step 6: Check if session auth failed")
        if response.status_code == 200 and response.text.strip().startswith('<!doctype html>'):
            print("⚠️ Session auth returned login page, falling back to API key")
            
            # Step 7: API Key fallback (same as Flask)
            print("Step 7: API Key fallback")
            api_key = os.environ.get('MAXIMO_API_KEY')
            print(f"API Key available: {'Yes' if api_key else 'No'}")
            
            if api_key:
                api_url = f"{base_url}/api/os/mxapipo"
                auth_method = "API Key (Fallback)"
                headers = {
                    "Accept": "application/json",
                    "apikey": api_key
                }
                
                print(f"Fallback API URL: {api_url}")
                print(f"Fallback Auth Method: {auth_method}")
                
                response = requests.get(
                    api_url,
                    params=params,
                    timeout=(3.05, 15),
                    headers=headers
                )
                
                print(f"Fallback Response Status: {response.status_code}")
                print(f"Fallback Response Headers: {dict(response.headers)}")
                print(f"Fallback Response Content Type: {response.headers.get('content-type', 'unknown')}")
                print(f"Fallback Response Preview: {response.text[:200]}...")
            else:
                print("❌ No API key available for fallback")
                return []
        
        # Step 8: Process response (same as Flask)
        print("Step 8: Process response")
        if response.status_code == 200:
            try:
                data = response.json()
                po_records = data.get('member', [])
                print(f"✅ JSON parsed successfully")
                print(f"PO records found: {len(po_records)}")
                
                # Step 9: Process POLINE data (same as Flask)
                print("Step 9: Process POLINE data")
                processed_pos = []
                for i, po in enumerate(po_records):
                    print(f"Processing PO {i+1}: {po.get('ponum', 'Unknown')}")
                    if 'poline' in po and isinstance(po['poline'], list):
                        print(f"  POLINE records: {len(po['poline'])}")
                        for j, line in enumerate(po['poline']):
                            print(f"    Line {j+1}: item={line.get('itemnum')}, site={line.get('siteid')}")
                            if line.get('itemnum') == TARGET_ITEM and line.get('siteid') == TARGET_SITE:
                                processed_pos.append({
                                    'ponum': po.get('ponum'),
                                    'storeroom': line.get('storeloc'),
                                    'internal': po.get('internal', False),
                                    'status': po.get('status'),
                                    'status_description': po.get('status_description'),
                                    'currency': po.get('currencycode'),
                                    'unit_cost': line.get('unitcost'),
                                    'quantity_ordered': line.get('orderqty'),
                                    'quantity_received': line.get('receivedqty', 0),
                                    'vendor': po.get('vendor'),
                                    'order_date': po.get('orderdate'),
                                    'required_date': po.get('requireddate'),
                                    'line_number': line.get('polinenum'),
                                    'line_cost': line.get('linecost')
                                })
                                print(f"      ✅ Matched! Added to processed_pos")
                            else:
                                print(f"      ❌ No match")
                    else:
                        print(f"  No POLINE data or not a list")
                
                print(f"Final processed POs: {len(processed_pos)}")
                return processed_pos
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                return []
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return []

if __name__ == "__main__":
    print(f"🚀 Debugging Flask PO Logic")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    result = debug_flask_po_logic()
    
    print(f"\n{'='*80}")
    print("🏁 Debug Complete")
    print(f"{'='*80}")
    print(f"Final Result: {len(result)} purchase orders processed")
    
    if result:
        print("\nProcessed Purchase Orders:")
        for i, po in enumerate(result, 1):
            print(f"{i}. PO #{po['ponum']} - Status: {po['status']} - Qty: {po['quantity_ordered']}")
    else:
        print("❌ No purchase orders processed - check debug output above for issues")
