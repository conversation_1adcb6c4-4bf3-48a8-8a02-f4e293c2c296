#!/usr/bin/env python3
"""
Analyze MXAPIINVENTORY response structure
"""

import json

def analyze_response():
    """Analyze the MXAPIINVENTORY response structure."""
    
    # Load the JSON data
    with open('mxapiinventory_sample.json', 'r') as f:
        data = json.load(f)

    print('🔍 MXAPIINVENTORY Field Analysis')
    print('=' * 60)

    # Get the first record
    members = data.get('rdfs:member', [])
    if members:
        first_record = members[0]
        print(f'Total Fields in Record: {len(first_record.keys())}')
        print()
        
        # Categorize fields
        categories = {
            'identifiers': [],
            'quantities': [],
            'dates': [],
            'locations': [],
            'financial': [],
            'nested_arrays': [],
            'references': [],
            'other': []
        }
        
        for field_name, value in first_record.items():
            field_clean = field_name.replace('spi:', '')
            
            if 'ref' in field_name.lower() or 'href' in field_name.lower():
                categories['references'].append(field_name)
            elif any(keyword in field_clean.lower() for keyword in ['id', 'num', 'code']):
                categories['identifiers'].append(field_name)
            elif any(keyword in field_clean.lower() for keyword in ['qty', 'bal', 'count', 'level']):
                categories['quantities'].append(field_name)
            elif any(keyword in field_clean.lower() for keyword in ['date', 'time']):
                categories['dates'].append(field_name)
            elif any(keyword in field_clean.lower() for keyword in ['location', 'site', 'bin']):
                categories['locations'].append(field_name)
            elif any(keyword in field_clean.lower() for keyword in ['cost', 'price', 'acc']):
                categories['financial'].append(field_name)
            elif isinstance(value, list):
                categories['nested_arrays'].append(field_name)
            else:
                categories['other'].append(field_name)
        
        print('📊 Field Categories:')
        for category, fields in categories.items():
            if fields:
                print(f'  {category.replace("_", " ").title()}: {len(fields)} fields')
                for field in fields[:3]:
                    clean_field = field.replace('spi:', '')
                    print(f'    - {clean_field}')
                if len(fields) > 3:
                    print(f'    ... and {len(fields) - 3} more')
                print()
        
        # Analyze nested arrays
        print('🔗 Nested Array Analysis:')
        for field_name, value in first_record.items():
            if isinstance(value, list) and value:
                print(f'  {field_name.replace("spi:", "")}: Array with {len(value)} items')
                if isinstance(value[0], dict):
                    sample_keys = list(value[0].keys())[:5]
                    print(f'    Sample object keys: {[k.replace("spi:", "") for k in sample_keys]}')
                else:
                    print(f'    Item type: {type(value[0]).__name__}')
                print()
        
        # Show some key fields with sample values
        print('📝 Key Fields with Sample Values:')
        key_fields = ['spi:itemnum', 'spi:siteid', 'spi:location', 'spi:curbal', 'spi:unitcost', 'spi:status']
        for field in key_fields:
            if field in first_record:
                print(f'  {field.replace("spi:", "")}: {first_record[field]}')
        
        # Show all field names for reference
        print('\n📋 All Available Fields:')
        all_fields = sorted([f.replace('spi:', '') for f in first_record.keys() if not f.startswith('rdf:') and not f.startswith('localref')])
        for i, field in enumerate(all_fields):
            if i % 4 == 0:
                print()
            print(f'  {field:<25}', end='')
        print('\n')
        
    else:
        print('No records found in response')

if __name__ == "__main__":
    analyze_response()
