#!/usr/bin/env python3
"""
Test script to verify the automatic refresh fix is working correctly
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:5010"

def test_javascript_syntax():
    """Test that the JavaScript file has no syntax errors"""
    print("🔍 Testing JavaScript syntax...")
    
    try:
        # Try to load the inventory management page
        response = requests.get(f"{BASE_URL}/inventory-management")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for key refresh functionality
            checks = [
                ("refreshInventoryAfterAdjustment function", "refreshInventoryAfterAdjustment" in content),
                ("currentSiteId usage", "this.currentSiteId" in content),
                ("Physical Count refresh call", "this.refreshInventoryAfterAdjustment('Physical Count')" in content),
                ("Current Balance refresh call", "this.refreshInventoryAfterAdjustment('Current Balance')" in content),
                ("showRefreshLoadingIndicator", "showRefreshLoadingIndicator" in content),
                ("hideRefreshLoadingIndicator", "hideRefreshLoadingIndicator" in content),
            ]
            
            all_passed = True
            for check_name, passed in checks:
                if passed:
                    print(f"✅ {check_name} found")
                else:
                    print(f"❌ {check_name} missing")
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ Page load failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_api_endpoints():
    """Test that the adjustment API endpoints are working"""
    print("\n🔍 Testing API endpoints...")
    
    # Test physical count endpoint
    physical_payload = [
        {
            "_action": "AddChange",
            "itemnum": "TEST-ITEM",
            "itemsetid": "ITEMSET",
            "siteid": "TESTSITE",
            "location": "TESTLOC",
            "invbalances": [
                {
                    "binnum": "TEST-BIN",
                    "physcnt": 25,
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "memo": "TEST"
                }
            ]
        }
    ]
    
    # Test current balance endpoint
    balance_payload = [
        {
            "_action": "AddChange",
            "itemnum": "TEST-ITEM",
            "itemsetid": "ITEMSET",
            "siteid": "TESTSITE",
            "location": "TESTLOC",
            "invbalances": [
                {
                    "binnum": "TEST-BIN",
                    "curbal": 30,
                    "conditioncode": "A1",
                    "memo": "TEST"
                }
            ]
        }
    ]
    
    endpoints = [
        ("Physical Count", "/api/inventory/physical-count-adjustment", physical_payload),
        ("Current Balance", "/api/inventory/current-balance-adjustment", balance_payload)
    ]
    
    results = []
    for name, endpoint, payload in endpoints:
        try:
            response = requests.post(f"{BASE_URL}{endpoint}", json=payload)
            
            if response.status_code == 401:
                print(f"✅ {name} endpoint: Authentication required (expected)")
                results.append(True)
            elif response.status_code == 200:
                print(f"✅ {name} endpoint: Working correctly")
                results.append(True)
            else:
                print(f"❌ {name} endpoint: Unexpected status {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {name} endpoint: Error - {str(e)}")
            results.append(False)
    
    return all(results)

def test_cache_busting():
    """Test that cache busting is working"""
    print("\n🔍 Testing cache busting...")
    
    try:
        response = requests.get(f"{BASE_URL}/inventory-management")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for cache busting parameter
            if "inventory_management.js?v=" in content:
                print("✅ Cache busting parameter found")
                return True
            else:
                print("❌ Cache busting parameter not found")
                return False
        else:
            print(f"❌ Page load failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def check_refresh_logic():
    """Check the refresh logic implementation"""
    print("\n🔍 Checking refresh logic implementation...")
    
    try:
        # Read the JavaScript file directly
        with open('frontend/static/js/inventory_management.js', 'r') as f:
            js_content = f.read()
        
        checks = [
            ("Uses currentSiteId instead of currentSiteFilter", "this.currentSiteId" in js_content and "this.currentSiteFilter" not in js_content.replace("// Check if we have search parameters to refresh", "")),
            ("Has proper refresh condition", "if (!this.currentSearchTerm && !this.currentSiteId)" in js_content),
            ("Calls performSearch with current page", "await this.performSearch(this.currentPage)" in js_content),
            ("Has debugging logs", "console.log(`🔄 REFRESH:" in js_content),
            ("Updates success message", "refreshing inventory data" in js_content),
        ]
        
        all_passed = True
        for check_name, passed in checks:
            if passed:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error reading JavaScript file: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Automatic Refresh Fix...\n")
    
    tests = [
        ("JavaScript Syntax", test_javascript_syntax),
        ("API Endpoints", test_api_endpoints),
        ("Cache Busting", test_cache_busting),
        ("Refresh Logic", check_refresh_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("AUTOMATIC REFRESH FIX TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The automatic refresh fix should be working correctly.")
        print("\n📋 Next Steps:")
        print("1. Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)")
        print("2. Log into the inventory management system")
        print("3. Search for an inventory item")
        print("4. Try submitting a Physical Count or Current Balance adjustment")
        print("5. Watch for automatic refresh with updated data")
    else:
        print("⚠️ Some tests failed - check the implementation")

if __name__ == "__main__":
    main()
