#!/usr/bin/env python3
"""
Test script to configure and test signature system for COMP status
"""
import requests
import json

BASE_URL = "http://localhost:5010"

def test_signature_setup():
    """Configure signature system for COMP status and test it"""
    
    print("🧪 Testing Signature System Setup for COMP Status")
    print("=" * 60)
    
    # Step 1: Check current configuration
    print("\n1️⃣ Checking current signature configuration...")
    try:
        response = requests.get(f"{BASE_URL}/api/admin/signature-config")
        if response.status_code == 200:
            current_config = response.json()
            print(f"✅ Current config: {json.dumps(current_config, indent=2)}")
        else:
            print(f"❌ Failed to get config: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting config: {e}")
        return False
    
    # Step 2: Configure signature for COMP status
    print("\n2️⃣ Configuring signature requirement for COMP status...")
    config_data = {
        "statuses": ["COMP"],
        "scope": ["parent", "task"],
        "enabled": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/admin/signature-config",
            json=config_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Configuration saved: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Failed to save config: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error saving config: {e}")
        return False
    
    # Step 3: Test signature requirement check
    print("\n3️⃣ Testing signature requirement for COMP status...")
    test_cases = [
        {"status": "COMP", "wo_type": "task", "expected": True},
        {"status": "COMP", "wo_type": "parent", "expected": True},
        {"status": "INPRG", "wo_type": "task", "expected": False},
    ]
    
    for case in test_cases:
        try:
            response = requests.post(
                f"{BASE_URL}/api/admin/signature-required",
                json=case,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                required = result.get('signature_required', False)
                status_icon = "✅" if required == case['expected'] else "❌"
                print(f"{status_icon} {case['status']} ({case['wo_type']}): Required={required}, Expected={case['expected']}")
            else:
                print(f"❌ Failed to check {case}: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Error checking {case}: {e}")
    
    # Step 4: Verify final configuration
    print("\n4️⃣ Verifying final configuration...")
    try:
        response = requests.get(f"{BASE_URL}/api/admin/signature-config")
        if response.status_code == 200:
            final_config = response.json()
            print(f"✅ Final config: {json.dumps(final_config, indent=2)}")
            
            config = final_config.get('config', {})
            if (config.get('enabled') and 
                'COMP' in config.get('statuses', []) and 
                'task' in config.get('scope', [])):
                print("✅ Signature system is properly configured for COMP status!")
                return True
            else:
                print("❌ Configuration is not correct")
                return False
        else:
            print(f"❌ Failed to verify config: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error verifying config: {e}")
        return False

if __name__ == "__main__":
    success = test_signature_setup()
    if success:
        print("\n🎉 Signature system is ready! Try changing a task status to COMP.")
    else:
        print("\n💥 Setup failed. Check the errors above.")
