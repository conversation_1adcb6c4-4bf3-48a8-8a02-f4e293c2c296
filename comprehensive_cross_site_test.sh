#!/bin/bash

# Comprehensive Cross-Site Test - Find 204 Responses
# ==================================================

echo "🚀 COMPREHENSIVE CROSS-SITE TEST - FIND 204 RESPONSES"
echo "====================================================="

echo "🎯 OBJECTIVE: Test multiple storeroom and unit combinations until we get 204"
echo "📋 STRATEGY: Systematic testing of different combinations"
echo "🔧 FIXES APPLIED:"
echo "   • Removed hardcoded LCVKWT→IKWAJ site restriction"
echo "   • Removed hardcoded RO→EA unit restriction"
echo "   • Universal EA default units"
echo ""

# Authenticate first
echo "🔐 AUTHENTICATION CHECK:"
response=$(curl -s http://127.0.0.1:5010/api/enhanced-workorders/available-sites)
if echo "$response" | grep -q "401"; then
    echo "❌ Not authenticated - please login at http://127.0.0.1:5010 first"
    exit 1
else
    echo "✅ Authentication OK"
fi

# Counter for tracking results
SUCCESS_204_COUNT=0
SUCCESS_OTHER_COUNT=0
ERROR_COUNT=0
TEST_COUNT=0

# Function to test cross-site combination
test_cross_site() {
    local test_name="$1"
    local from_site="$2"
    local to_site="$3"
    local from_store="$4"
    local to_store="$5"
    local from_unit="$6"
    local to_unit="$7"
    local quantity="$8"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo ""
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..70})"
    echo "🔄 $from_site/$from_store → $to_site/$to_store ($from_unit→$to_unit, qty:$quantity)"
    
    payload='{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "'$from_site'",
        "to_siteid": "'$to_site'",
        "from_storeroom": "'$from_store'",
        "to_storeroom": "'$to_store'",
        "quantity": '$quantity',
        "from_issue_unit": "'$from_unit'",
        "to_issue_unit": "'$to_unit'",
        "from_condition": "A1",
        "to_condition": "A1"
    }'
    
    echo "🔄 Submitting..."
    
    response=$(curl -X POST "http://127.0.0.1:5010/api/inventory/transfer-cross-site" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -s)
    
    echo "📊 Response: $response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉🎉🎉 204 SUCCESS FOUND! 🎉🎉🎉"
        SUCCESS_204_COUNT=$((SUCCESS_204_COUNT + 1))
        echo "$payload" > "SUCCESS_204_pattern_$TEST_COUNT.json"
        echo "💾 Saved to: SUCCESS_204_pattern_$TEST_COUNT.json"
        return 0
    elif echo "$response" | grep -q '"success": true'; then
        echo "✅ SUCCESS (non-204)"
        SUCCESS_OTHER_COUNT=$((SUCCESS_OTHER_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"success": false'; then
        echo "❌ FAILED"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        
        if echo "$response" | grep -q 'BMXAA1861E'; then
            echo "   📋 BMXAA1861E: Duplicate combination"
        elif echo "$response" | grep -q 'BMXAA2694E'; then
            echo "   📋 BMXAA2694E: Location does not exist"
        elif echo "$response" | grep -q 'BMXAA1785E'; then
            echo "   📋 BMXAA1785E: Unit conversion error"
        else
            echo "   📋 Other error"
        fi
        return 1
    else
        echo "⚠️  Unexpected response"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        return 1
    fi
}

echo ""
echo "🚀 TESTING CROSS-SITE COMBINATIONS"
echo "=================================="

# Test 1: Original working pattern (LCVKWT → IKWAJ)
test_cross_site "Original Pattern: CMW-AJ → KWAJ-1058" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1058" "EA" "EA" "1.0"

# Test 2: Different units
test_cross_site "Different Units: CMW-AJ → KWAJ-1058 (RO→EA)" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1058" "RO" "EA" "1.0"

# Test 3: Different destination storeroom
test_cross_site "Different Dest: CMW-AJ → KWAJ-1115" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1115" "EA" "EA" "1.0"

# Test 4: Different source storeroom
test_cross_site "Different Source: CMW-AJH → KWAJ-1058" \
    "LCVKWT" "IKWAJ" "CMW-AJH" "KWAJ-1058" "EA" "EA" "1.0"

# Test 5: Both LCVKWT storerooms
test_cross_site "Both LCVKWT: CMW-AJ → CMW-BU" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "CMW-BU" "EA" "EA" "1.0"

# Test 6: Reverse direction
test_cross_site "Reverse: IKWAJ → LCVKWT" \
    "IKWAJ" "LCVKWT" "KWAJ-1058" "CMW-AJ" "EA" "EA" "1.0"

# Test 7: Same site (should work)
test_cross_site "Same Site: IKWAJ → IKWAJ" \
    "IKWAJ" "IKWAJ" "KWAJ-1058" "KWAJ-1115" "EA" "EA" "1.0"

# Test 8: Small quantity
test_cross_site "Small Quantity: CMW-AJ → KWAJ-1058" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1058" "EA" "EA" "0.1"

# Test 9: Different item quantity
test_cross_site "Different Qty: CMW-AJ → KWAJ-1058" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1058" "EA" "EA" "2.0"

# Test 10: RO to RO (same units)
test_cross_site "Same Units RO: CMW-AJ → KWAJ-1058" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1058" "RO" "RO" "1.0"

echo ""
echo "📊 COMPREHENSIVE TEST SUMMARY"
echo "============================="
echo "🎉 204 Success responses: $SUCCESS_204_COUNT"
echo "✅ Other success responses: $SUCCESS_OTHER_COUNT"
echo "❌ Failed responses: $ERROR_COUNT"
echo "📝 Total tests: $TEST_COUNT"

if [ $SUCCESS_204_COUNT -gt 0 ]; then
    echo ""
    echo "🎉🎉🎉 FOUND WORKING 204 PATTERNS! 🎉🎉🎉"
    echo "========================================"
    echo "💾 Check SUCCESS_204_pattern_*.json files for working combinations"
    echo ""
    echo "📋 Working 204 patterns:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "SUCCESS_204_pattern_$i.json" ]; then
            echo "✅ Test $i: 204 SUCCESS - $(cat SUCCESS_204_pattern_$i.json | grep -o '"from_storeroom": "[^"]*"' | cut -d'"' -f4) → $(cat SUCCESS_204_pattern_$i.json | grep -o '"to_storeroom": "[^"]*"' | cut -d'"' -f4)"
        fi
    done
elif [ $SUCCESS_OTHER_COUNT -gt 0 ]; then
    echo ""
    echo "✅ FOUND WORKING PATTERNS (non-204)"
    echo "==================================="
    echo "📋 Some transfers are working - check Flask logs for actual Maximo status"
else
    echo ""
    echo "❌ NO WORKING PATTERNS FOUND"
    echo "============================"
    echo "🔍 All combinations failed - check error patterns above"
fi

echo ""
echo "🎯 NEXT STEPS:"
if [ $SUCCESS_204_COUNT -gt 0 ]; then
    echo "✅ Use the working 204 patterns for implementation"
    echo "✅ Update UI to use successful combinations"
    echo "✅ Document working storeroom/unit combinations"
else
    echo "🔍 Analyze error patterns to understand restrictions"
    echo "🔧 Check Flask logs for detailed Maximo responses"
    echo "💡 Try different storeroom combinations"
    echo "🔐 Verify authentication and permissions"
fi

echo ""
echo "📋 FLASK TERMINAL ANALYSIS:"
echo "=========================="
echo "Look for these patterns in Flask logs:"
echo "• 204 status in Maximo responses = SUCCESS"
echo "• BMXAA1861E = Duplicate combination (try different bins/lots)"
echo "• BMXAA2694E = Location doesn't exist (try different storerooms)"
echo "• BMXAA1785E = Unit conversion error (try same units)"
