#!/usr/bin/env python3
"""
Test script for the Signature System
Tests signature configuration, enforcement, and PDF generation
"""

import requests
import json
import base64
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_WONUM = "15643629"  # Replace with actual work order number
TEST_TASK_WONUM = "15643630"  # Replace with actual task work order number

def create_test_signature():
    """Create a simple test signature as base64 PNG"""
    # This creates a minimal PNG image for testing
    # In real usage, this would come from the canvas
    test_signature_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    return f"data:image/png;base64,{test_signature_b64}"

def test_signature_config_api():
    """Test signature configuration API endpoints"""
    print("🧪 Testing Signature Configuration API...")
    
    # Test GET configuration (should return default empty config)
    response = requests.get(f"{BASE_URL}/api/admin/signature-config")
    print(f"GET config status: {response.status_code}")
    if response.status_code == 200:
        config = response.json()
        print(f"Current config: {config}")
    
    # Test POST configuration
    test_config = {
        "statuses": ["COMP", "APPR", "INPRG"],
        "scope": ["parent", "task"],
        "enabled": True
    }
    
    response = requests.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=test_config,
        headers={'Content-Type': 'application/json'}
    )
    print(f"POST config status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Config saved: {result.get('success')}")
    
    return response.status_code == 200

def test_signature_requirement_check():
    """Test signature requirement checking"""
    print("\n🧪 Testing Signature Requirement Check...")
    
    test_cases = [
        {"status": "COMP", "wo_type": "parent", "expected": True},
        {"status": "COMP", "wo_type": "task", "expected": True},
        {"status": "ASSIGN", "wo_type": "parent", "expected": False},
        {"status": "APPR", "wo_type": "parent", "expected": True},
    ]
    
    for case in test_cases:
        response = requests.post(
            f"{BASE_URL}/api/admin/signature-required",
            json=case,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            required = result.get('signature_required', False)
            status = "✅" if required == case['expected'] else "❌"
            print(f"{status} {case['status']} ({case['wo_type']}): Required={required}, Expected={case['expected']}")
        else:
            print(f"❌ Failed to check requirement for {case}")

def test_signature_submission():
    """Test signature submission and PDF generation"""
    print("\n🧪 Testing Signature Submission...")
    
    signature_data = {
        "wonum": TEST_WONUM,
        "status": "COMP",
        "wo_type": "parent",
        "signature_data": create_test_signature(),
        "customer_name": "Test Customer",
        "comments": "Test signature submission",
        "date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    response = requests.post(
        f"{BASE_URL}/api/signature/submit",
        json=signature_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Signature submission status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Submission success: {result.get('success')}")
        print(f"PDF attached: {result.get('pdf_attached')}")
        if result.get('attachment_info'):
            print(f"Attachment info: {result['attachment_info']}")
    else:
        print(f"Submission failed: {response.text}")
    
    return response.status_code == 200

def test_ai_insights():
    """Test AI insights generation"""
    print("\n🧪 Testing AI Insights...")
    
    response = requests.get(f"{BASE_URL}/api/admin/signature-analytics")
    print(f"AI insights status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            analytics = result['analytics']
            print(f"Configuration summary: {analytics.get('configuration_summary')}")
            print(f"Number of insights: {len(analytics.get('insights', []))}")
            
            # Print first few insights
            for i, insight in enumerate(analytics.get('insights', [])[:3]):
                print(f"Insight {i+1}: {insight.get('title')} - {insight.get('type')}")
        else:
            print(f"AI insights failed: {result.get('error')}")
    
    return response.status_code == 200

def test_status_change_enforcement():
    """Test status change enforcement (requires actual work order)"""
    print("\n🧪 Testing Status Change Enforcement...")
    
    # Test parent work order status change
    response = requests.post(
        f"{BASE_URL}/api/workorder/{TEST_WONUM}/change-status",
        json={"status": "COMP", "wo_type": "parent"},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Parent WO status change: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        if result.get('signature_required'):
            print("✅ Signature requirement correctly enforced for parent WO")
        else:
            print("❌ Signature requirement not enforced for parent WO")
    
    # Test task work order status change
    response = requests.post(
        f"{BASE_URL}/api/task/{TEST_TASK_WONUM}/status",
        json={"status": "COMP"},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Task WO status change: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        if result.get('signature_required'):
            print("✅ Signature requirement correctly enforced for task WO")
        else:
            print("❌ Signature requirement not enforced for task WO")

def test_admin_page_access():
    """Test admin page accessibility"""
    print("\n🧪 Testing Admin Page Access...")
    
    response = requests.get(f"{BASE_URL}/admin")
    print(f"Admin page status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Admin page accessible")
        # Check if signature config content is present
        if "Signature Config" in response.text:
            print("✅ Signature config tab found")
        else:
            print("❌ Signature config tab not found")
    else:
        print("❌ Admin page not accessible")

def run_all_tests():
    """Run all signature system tests"""
    print("🚀 Starting Signature System Tests")
    print("=" * 50)
    
    tests = [
        ("Admin Page Access", test_admin_page_access),
        ("Signature Configuration API", test_signature_config_api),
        ("Signature Requirement Check", test_signature_requirement_check),
        ("AI Insights", test_ai_insights),
        ("Signature Submission", test_signature_submission),
        ("Status Change Enforcement", test_status_change_enforcement),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "="*50)
    print("🏁 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Signature system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    print("Signature System Test Suite")
    print("Make sure the Flask app is running on localhost:5000")
    print("Update TEST_WONUM and TEST_TASK_WONUM with actual work order numbers")
    
    input("Press Enter to start tests...")
    run_all_tests()
