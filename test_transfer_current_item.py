#!/usr/bin/env python3
"""
Test script for Transfer Current Item functionality
Tests the new transfer service and API endpoints
"""

import requests
import json
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_SITE = "LCVKWT"  # Default test site
TEST_ITEM = "5975-60-V00-0529"  # Example item from requirements

def test_transfer_endpoints():
    """Test the transfer API endpoints"""
    print("🔧 Testing Transfer Current Item Endpoints")
    print("=" * 50)
    
    # Test 1: Get storerooms for a site
    print("\n📋 Test 1: Get Storerooms for Site")
    try:
        response = requests.get(f"{BASE_URL}/api/inventory/transfer-current-item/storerooms", 
                              params={'siteid': TEST_SITE})
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Storerooms found: {len(data.get('storerooms', []))}")
            if data.get('storerooms'):
                print(f"Sample storerooms: {data['storerooms'][:3]}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test 2: Get bins/lots/conditions for a location
    print("\n📦 Test 2: Get Bins/Lots/Conditions")
    try:
        response = requests.get(f"{BASE_URL}/api/inventory/transfer-current-item/bins-lots-conditions", 
                              params={'siteid': TEST_SITE, 'location': 'RIP001'})
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            location_data = data.get('data', {})
            print(f"Bins found: {len(location_data.get('bins', []))}")
            print(f"Lots found: {len(location_data.get('lots', []))}")
            print(f"Conditions found: {len(location_data.get('conditions', []))}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test 3: Get issue units for an item
    print("\n🔧 Test 3: Get Issue Units")
    try:
        response = requests.get(f"{BASE_URL}/api/inventory/transfer-current-item/issue-units", 
                              params={'itemnum': TEST_ITEM})
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Issue units found: {data.get('units', [])}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")

def test_transfer_validation():
    """Test transfer data validation"""
    print("\n🔍 Testing Transfer Data Validation")
    print("=" * 40)
    
    # Test with missing required fields
    print("\n❌ Test: Missing Required Fields")
    transfer_data = {
        "itemnum": TEST_ITEM,
        # Missing required fields
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/inventory/transfer-current-item", 
                               json=transfer_data)
        print(f"Status: {response.status_code}")
        data = response.json()
        print(f"Success: {data.get('success')}")
        print(f"Error: {data.get('error')}")
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test with valid data structure (but may fail due to business rules)
    print("\n✅ Test: Valid Data Structure")
    transfer_data = {
        "itemnum": TEST_ITEM,
        "quantity": 1.0,
        "from_siteid": TEST_SITE,
        "to_siteid": TEST_SITE,
        "from_storeroom": "RIP001",
        "to_storeroom": "LCVK-CMW-BU",
        "from_issue_unit": "EA",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/inventory/transfer-current-item", 
                               json=transfer_data)
        print(f"Status: {response.status_code}")
        data = response.json()
        print(f"Success: {data.get('success')}")
        if data.get('success'):
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {data.get('error')}")
    except Exception as e:
        print(f"Exception: {e}")

def test_inventory_search():
    """Test that we can search for inventory items to transfer"""
    print("\n🔍 Testing Inventory Search for Transfer")
    print("=" * 40)
    
    url = f"{BASE_URL}/api/inventory/management/search"
    params = {
        'search_term': TEST_ITEM,
        'site_id': TEST_SITE,
        'limit': 5,
        'page': 0
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            items = data.get('items', [])
            print(f"Items found: {len(items)}")
            
            if items:
                item = items[0]
                print(f"Sample item: {item.get('itemnum')} - {item.get('description', 'No description')}")
                print(f"Has balance records: {len(item.get('invbalances_records', []))}")
                print(f"Current balance: {item.get('curbaltotal', 'N/A')}")
                print(f"Location: {item.get('location', 'N/A')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    print("🚀 Transfer Current Item Test Suite")
    print("=" * 60)
    
    # Test inventory search first
    test_inventory_search()
    
    # Test transfer endpoints
    test_transfer_endpoints()
    
    # Test transfer validation
    test_transfer_validation()
    
    print("\n✅ Test suite completed!")
    print("\nNext steps:")
    print("1. Open http://127.0.0.1:5010/inventory-management in browser")
    print("2. Search for an inventory item")
    print("3. Click the 'Transfer' button on an item with balance records")
    print("4. Test the transfer form functionality")
