# Digital Signature System - Setup Guide

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Application
```bash
python app.py
```

### 3. Access Admin Configuration
1. Navigate to `http://localhost:5000`
2. Login to Maximo
3. Click "Admin" button on welcome page
4. Go to "Signature Config" tab

### 4. Configure Signature Requirements
1. **Select Critical Statuses**: Check COMP, APPR, INPRG
2. **Set Scope**: Enable both "Parent Work Orders" and "Task Work Orders"
3. **Review AI Insights**: Check recommendations
4. **Save Configuration**: Click "Save Configuration"

### 5. Test the System
1. Navigate to Enhanced Work Orders
2. Try to change a work order status to COMP
3. Signature modal should appear
4. Complete signature and verify PDF attachment

## Detailed Configuration

### Recommended Initial Setup

#### Critical Statuses (Minimum)
- ✅ **COMP** (Complete) - Work completion requires signature
- ✅ **APPR** (Approved) - Approval requires signature  
- ✅ **INPRG** (In Progress) - Starting work requires signature

#### Optional Statuses (Based on Business Needs)
- **CLOSE** - Final closure signature
- **ASSIGN** - Assignment acknowledgment
- **READY** - Ready to start confirmation
- **WAPPR** - Approval request signature

#### Scope Configuration
- ✅ **Parent Work Orders** - Main work order signatures
- ✅ **Task Work Orders** - Task-level signatures
- **Recommendation**: Enable both for complete audit coverage

### AI Insights Interpretation

#### Coverage Analysis
- **0-15%**: Low coverage, minimal disruption
- **15-25%**: Optimal coverage (recommended)
- **25-50%**: High coverage, good security
- **50%+**: Very high coverage, potential user fatigue

#### Insight Types
- 🔴 **High Priority**: Immediate attention needed
- 🟡 **Medium Priority**: Should be addressed
- 🔵 **Low Priority**: Informational

## Testing Checklist

### ✅ Configuration Testing
- [ ] Admin page accessible
- [ ] Signature config saves successfully
- [ ] AI insights load and display
- [ ] Configuration persists after restart

### ✅ Signature Enforcement Testing
- [ ] Parent work order status changes trigger signature when configured
- [ ] Task work order status changes trigger signature when configured
- [ ] Non-configured statuses bypass signature requirement
- [ ] Signature modal appears with correct information

### ✅ Signature Capture Testing
- [ ] Signature pad works with mouse
- [ ] Signature pad works with touch (mobile)
- [ ] Customer name validation works
- [ ] Form validation prevents submission without signature
- [ ] Clear signature function works

### ✅ PDF Generation Testing
- [ ] PDF generates successfully
- [ ] PDF contains signature image
- [ ] PDF includes all metadata (customer, date, comments)
- [ ] PDF attaches to Maximo work order
- [ ] Attachment naming follows convention

### ✅ Integration Testing
- [ ] Status change completes after signature
- [ ] Work order updates in Maximo
- [ ] Attachment appears in Maximo
- [ ] Error handling works for failed operations

## Troubleshooting

### Common Issues

#### 1. PDF Generation Fails
**Error**: `PDF generation libraries not available`
**Solution**: 
```bash
pip install reportlab Pillow
```

#### 2. Signature Modal Doesn't Appear
**Possible Causes**:
- Configuration not saved
- Status not in configured list
- Scope doesn't match work order type
**Solution**: Check admin configuration and save again

#### 3. Attachment Fails
**Error**: `Attachment failed: [error message]`
**Possible Causes**:
- Maximo connectivity issues
- Insufficient permissions
- Work order not found
**Solution**: Check Maximo connection and work order access

#### 4. JavaScript Errors
**Common Issues**:
- Bootstrap modal not initialized
- Canvas not supported
- Network connectivity
**Solution**: Check browser console and network tab

### Debug Steps

#### 1. Check Configuration
```javascript
// In browser console
fetch('/api/admin/signature-config')
  .then(r => r.json())
  .then(console.log);
```

#### 2. Test Signature Requirement
```javascript
// In browser console
fetch('/api/admin/signature-required', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({status: 'COMP', wo_type: 'parent'})
}).then(r => r.json()).then(console.log);
```

#### 3. Check Flask Logs
Look for log messages starting with:
- `📝 SIGNATURE:`
- `📄 SIGNATURE PDF:`
- `📎 SIGNATURE:`
- `🔄 SIGNATURE STATUS:`

## Production Deployment

### Security Considerations
1. **HTTPS Required**: Use SSL/TLS in production
2. **Session Security**: Configure secure session cookies
3. **Access Control**: Restrict admin access appropriately
4. **Audit Logging**: Enable comprehensive logging

### Performance Optimization
1. **PDF Caching**: Consider caching for repeated operations
2. **Database Storage**: Move configuration to database
3. **CDN**: Use CDN for static assets
4. **Compression**: Enable gzip compression

### Monitoring
1. **Signature Volume**: Monitor daily signature counts
2. **Error Rates**: Track PDF generation failures
3. **Performance**: Monitor signature submission times
4. **Storage**: Monitor attachment storage usage

## Advanced Configuration

### Custom Status Mapping
For organizations with custom statuses, update the status list in:
- `app.py` - Backend validation
- `admin.html` - Frontend configuration
- `workorder_detail.html` - Status change integration

### Integration with External Systems
The signature system can be extended to integrate with:
- External signature services (DocuSign, Adobe Sign)
- Document management systems
- Compliance reporting tools
- Audit trail systems

### Bulk Operations
For bulk signature requirements:
1. Extend API to support multiple work orders
2. Add batch signature capture interface
3. Implement bulk PDF generation
4. Add progress tracking for large operations

## Support

### Documentation
- `SIGNATURE_SYSTEM_DOCUMENTATION.md` - Complete technical documentation
- `WORKORDER_ATTACHMENTS_DOCUMENTATION.md` - Attachment system details
- `WO_Task_Status_Change_Documentation.md` - Status change implementation

### Testing
- `test_signature_system.py` - Automated test suite
- Manual testing checklist above
- Browser developer tools for debugging

### Logs and Monitoring
- Flask application logs
- Browser console logs
- Network request monitoring
- Maximo API response logs

## Success Criteria

### ✅ System is Working When:
1. **Configuration**: Admin can configure signature requirements
2. **Enforcement**: Status changes trigger signatures when required
3. **Capture**: Users can provide digital signatures
4. **Generation**: PDFs generate with complete information
5. **Attachment**: PDFs attach to Maximo work orders automatically
6. **Insights**: AI provides relevant configuration recommendations

### 📊 Key Metrics:
- **Signature Compliance**: 100% of configured status changes require signatures
- **PDF Generation Success**: >99% success rate
- **Attachment Success**: >95% success rate (some Maximo limitations expected)
- **User Experience**: <5 seconds total time for signature process

## Next Steps

After successful setup:
1. **User Training**: Train users on signature process
2. **Policy Development**: Develop signature requirement policies
3. **Regular Review**: Review AI insights quarterly
4. **Optimization**: Adjust configuration based on usage patterns
5. **Expansion**: Consider additional signature scenarios

The Digital Signature System is now ready for production use with comprehensive audit trails and compliance features!
