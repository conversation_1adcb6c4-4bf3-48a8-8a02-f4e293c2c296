# Troubleshooting COMP Status Network Error

## Issue Description
When COMP status is selected in the signature config and you try to change a task status to COMP, you get a "Network error occurred while updating task status" message, while other statuses work fine.

## Root Cause Analysis
The issue was likely caused by a circular dependency in the `process_status_change_with_signature` function that was making HTTP requests to itself, causing network timeouts or failures.

## Fixes Applied

### 1. **Fixed Circular Dependency**
- **Problem**: `process_status_change_with_signature` was making HTTP requests back to the same Flask app
- **Solution**: Changed to call MXAPI service directly instead of HTTP requests
- **File**: `app.py` lines 3988-4033

### 2. **Enhanced Error Handling**
- **Added**: Comprehensive error logging and debugging
- **Added**: Better error messages in frontend
- **Added**: Detailed logging for signature submission process

### 3. **Added Debugging**
- **Added**: Debug logging to `is_signature_required` function
- **Added**: Console logging in frontend JavaScript
- **Added**: Detailed error reporting in signature submission

## Troubleshooting Steps

### Step 1: Check Flask Logs
1. Start your Flask app with debug logging
2. Try to change a task status to COMP
3. Look for these log messages:
   ```
   📝 SIGNATURE CHECK: Checking COMP for task
   📝 SIGNATURE: Processing signature for WO [wonum] -> COMP (task)
   🔄 SIGNATURE STATUS: Processing status change for task WO [wonum] -> COMP
   ```

### Step 2: Check Browser Console
1. Open browser developer tools (F12)
2. Go to Console tab
3. Try the status change
4. Look for these messages:
   ```
   📝 Submitting signature payload: {...}
   📝 Signature submission response status: 200
   📝 Signature submission result: {...}
   ```

### Step 3: Test Configuration
Run the debug script:
```bash
python debug_comp_status.py
```

### Step 4: Manual Testing
1. **Check Admin Config**:
   - Go to `/admin`
   - Verify COMP is checked in Signature Config
   - Verify "Task Work Orders" scope is enabled
   - Save configuration

2. **Test Status Change**:
   - Go to a task work order
   - Try changing status to COMP
   - Signature modal should appear

3. **Complete Signature**:
   - Fill in customer name
   - Draw signature
   - Submit
   - Check for success message

## Common Issues & Solutions

### Issue 1: "Network error occurred while updating task status"
**Cause**: Circular HTTP request or timeout
**Solution**: Fixed in `process_status_change_with_signature` function

### Issue 2: Signature modal doesn't appear
**Possible Causes**:
- Configuration not saved properly
- COMP not in configured statuses
- Task scope not enabled

**Debug Steps**:
```javascript
// In browser console
fetch('/api/admin/signature-config')
  .then(r => r.json())
  .then(console.log);
```

### Issue 3: Signature submits but status doesn't change
**Possible Causes**:
- MXAPI service failure
- Work order locked
- Insufficient permissions

**Debug Steps**:
Check Flask logs for:
```
❌ SIGNATURE STATUS: Failed to change status: [error]
```

### Issue 4: PDF generation fails
**Possible Causes**:
- Missing reportlab/Pillow libraries
- Invalid signature data

**Solution**:
```bash
pip install reportlab Pillow
```

## Testing Checklist

### ✅ Configuration Test
- [ ] Admin page loads
- [ ] COMP status checkbox works
- [ ] Task scope checkbox works
- [ ] Configuration saves successfully

### ✅ Signature Requirement Test
- [ ] COMP status change triggers signature modal
- [ ] Other statuses work normally
- [ ] Both parent and task scopes work

### ✅ Signature Submission Test
- [ ] Customer name validation works
- [ ] Signature pad works
- [ ] PDF generates successfully
- [ ] Status changes after signature
- [ ] PDF attaches to Maximo

## Debug Commands

### Check Current Configuration
```bash
curl -X GET http://localhost:5000/api/admin/signature-config
```

### Test Signature Requirement
```bash
curl -X POST http://localhost:5000/api/admin/signature-required \
  -H "Content-Type: application/json" \
  -d '{"status": "COMP", "wo_type": "task"}'
```

### Check Flask Logs
Look for these patterns:
- `📝 SIGNATURE:` - Signature processing
- `🔍 SIGNATURE CHECK:` - Requirement checking
- `🔄 SIGNATURE STATUS:` - Status change processing
- `❌` - Error indicators
- `✅` - Success indicators

## Expected Behavior

### Normal Flow (COMP Status)
1. User clicks status change to COMP
2. System checks signature requirement
3. Signature modal appears
4. User provides signature and customer name
5. PDF generates
6. PDF attaches to Maximo
7. Status changes to COMP
8. Success message appears

### Error Indicators
- **Red ❌ logs**: Errors that need attention
- **Yellow ⚠️ logs**: Warnings (may continue)
- **Blue 🔍 logs**: Debug information
- **Green ✅ logs**: Success confirmations

## If Issues Persist

### 1. Check Dependencies
```bash
pip list | grep -E "(reportlab|Pillow|requests|flask)"
```

### 2. Restart Flask App
```bash
python app.py
```

### 3. Clear Browser Cache
- Hard refresh (Ctrl+F5)
- Clear browser cache
- Try incognito/private mode

### 4. Test with Different Work Order
- Try different task work order number
- Verify work order exists in Maximo
- Check work order permissions

### 5. Check Maximo Connectivity
- Verify you're logged into Maximo
- Test other work order operations
- Check network connectivity

## Success Indicators

When working correctly, you should see:
1. **Flask Logs**: Successful signature processing messages
2. **Browser Console**: No JavaScript errors
3. **UI**: Signature modal appears and submits successfully
4. **Maximo**: Status changes and PDF attachment appears
5. **No Network Errors**: Status change completes without errors

The fixes applied should resolve the network error issue with COMP status changes. The enhanced logging will help identify any remaining issues.
