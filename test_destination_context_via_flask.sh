#!/bin/bash

# Test Destination Site Context via Flask
# ========================================

echo "🚀 TESTING DESTINATION SITE CONTEXT VIA FLASK APPLICATION"
echo "========================================================="

echo "🎯 OBJECTIVE: Get 204 success using destination site context structure"
echo "📋 STRATEGY: Test with toissueunit: EA and destination site approach"
echo "🔍 KEY: Include toissueunit field to handle unit conversion"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_destination_context_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting destination context transfer..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Destination context transfer worked with 204 status!"
        echo "$payload" > "destination_context_success_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: destination_context_success_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING DESTINATION SITE CONTEXT VARIATIONS"
echo "=============================================="

# Test 1: Basic destination context with toissueunit
test_destination_context_payload "Destination Context - KWAJ-1058 with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: Different source storeroom with toissueunit
test_destination_context_payload "RIP001 → KWAJ-1058 with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: Different destination with toissueunit
test_destination_context_payload "CMW-AJ → KWAJ-1115 with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: Small quantity with toissueunit
test_destination_context_payload "Small Quantity with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 0.1,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Without bins/lots but with toissueunit
test_destination_context_payload "Minimal with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA"
}'

# Test 6: Same unit for both (EA to EA)
test_destination_context_payload "EA to EA Units" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "EA",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 7: RO to RO Units (no conversion)
test_destination_context_payload "RO to RO Units" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 8: Different bins with toissueunit
test_destination_context_payload "Different Bins with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "28-800-0004",
    "to_bin": "58-A-A01-1",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 9: Different lots with toissueunit
test_destination_context_payload "Different Lots with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "LOT123",
    "to_lot": "TEST",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 10: CMW-BU source with toissueunit
test_destination_context_payload "CMW-BU → KWAJ-1058 with toissueunit" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-BU",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 DESTINATION CONTEXT TEST SUMMARY"
echo "=================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found working destination context patterns!"
    echo "====================================================="
    echo "💾 Check destination_context_success_*.json files for working patterns"
    echo ""
    echo "📋 Working curl commands:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "destination_context_success_$i.json" ]; then
            echo ""
            echo "# Working Destination Context Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat destination_context_success_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
    
    echo ""
    echo "🎯 KEY SUCCESS FACTORS:"
    echo "• Include to_issue_unit: EA for unit conversion"
    echo "• Use proper destination site context"
    echo "• Complete field validation with DEFAULT values"
    echo "• A1 condition codes work properly"
    
else
    echo ""
    echo "❌ No destination context success found yet"
    echo "🔄 The Flask app may need modification to support destination site context"
    echo ""
    echo "💡 NEXT STEPS:"
    echo "• Check if Flask app supports to_issue_unit parameter"
    echo "• Verify if destination site context is implemented"
    echo "• Consider modifying the inventory transfer service"
fi

echo ""
echo "🎯 IMPLEMENTATION NOTE:"
echo "If successful, this confirms destination site context approach works"
echo "and should be implemented in the Flask application service layer."
