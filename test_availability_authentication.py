#!/usr/bin/env python3
"""
Test script to verify availability API authentication and error handling.
"""

import requests
import json
import sys

def test_unauthenticated_request():
    """Test availability API without authentication."""
    print("🔐 Testing Unauthenticated Availability Request")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:5010/api/inventory/availability/5975-60-V00-0001"
    params = {"siteid": "LCVKWT"}
    
    try:
        # Make request without authentication cookies
        response = requests.get(api_url, params=params, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if data.get('success'):
                print("✅ API returned success (unexpected for unauthenticated request)")
                return True
            else:
                error_msg = data.get('error', 'Unknown error')
                print(f"❌ API returned error: {error_msg}")
                
                # Check if it's the expected authentication error
                if 'Not logged in' in error_msg or 'Authentication' in error_msg:
                    print("✅ Correct authentication error returned")
                    return True
                else:
                    print("⚠️ Unexpected error message")
                    return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response Text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_inventory_page_redirect():
    """Test if inventory management page redirects to login when not authenticated."""
    print("\n🔄 Testing Inventory Page Authentication Redirect")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:5010/inventory-management", 
                              allow_redirects=False, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f"Redirect Location: {location}")
            
            if 'login' in location.lower():
                print("✅ Correctly redirects to login page")
                return True
            else:
                print("⚠️ Redirects to unexpected location")
                return False
        elif response.status_code == 200:
            print("⚠️ Page loads without authentication (unexpected)")
            return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_error_handling_improvements():
    """Test if the JavaScript error handling improvements are in place."""
    print("\n🔧 Testing JavaScript Error Handling Improvements")
    print("=" * 50)
    
    js_file_path = "frontend/static/js/inventory_management.js"
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        # Check for improved error handling
        error_handling_checks = [
            "credentials: 'include'",  # Proper authentication headers
            "Authentication required",  # User-friendly auth error
            "Please log in to view availability data",  # Specific login message
            "response.status === 401",  # HTTP 401 handling
            "response.status === 403",  # HTTP 403 handling
            "response.status === 404",  # HTTP 404 handling
            "alert-link",  # Login link in error message
            "Unable to connect to the server",  # Network error message
            "Request timed out",  # Timeout error message
        ]
        
        found_improvements = 0
        for check in error_handling_checks:
            if check in js_content:
                print(f"✅ Found improvement: {check}")
                found_improvements += 1
            else:
                print(f"❌ Missing improvement: {check}")
        
        if found_improvements >= len(error_handling_checks) * 0.8:  # 80% threshold
            print(f"✅ Error handling improvements implemented ({found_improvements}/{len(error_handling_checks)})")
            return True
        else:
            print(f"❌ Insufficient error handling improvements ({found_improvements}/{len(error_handling_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Error checking JavaScript improvements: {str(e)}")
        return False

def provide_user_guidance():
    """Provide guidance for testing the availability feature."""
    print("\n📋 User Guidance for Testing Availability Feature")
    print("=" * 50)
    
    print("To test the enhanced availability modal:")
    print()
    print("1. 🔐 AUTHENTICATION REQUIRED:")
    print("   • Go to: http://127.0.0.1:5010/login/login")
    print("   • Log in with your Maximo credentials")
    print("   • This is required for the availability API to work")
    print()
    print("2. 📋 ACCESS INVENTORY MANAGEMENT:")
    print("   • After login, go to: http://127.0.0.1:5010/inventory-management")
    print("   • You should now see the inventory search interface")
    print()
    print("3. 🔍 TEST AVAILABILITY MODAL:")
    print("   • Search for test items: '5975-60-V00-0001' or '8010-60-V00-0113'")
    print("   • Click the green 'View Availability' button")
    print("   • Verify the new diary-style vertical sidebar navigation")
    print("   • Test the Summary tab (should be active by default)")
    print("   • Switch between different tabs to test smooth transitions")
    print()
    print("4. 📱 MOBILE TESTING:")
    print("   • Use browser developer tools to simulate mobile device")
    print("   • Verify that summary cards are hidden on mobile")
    print("   • Confirm that all navigation tabs are accessible")
    print("   • Test that no vertical scrolling is required to access tabs")
    print()
    print("5. 🐛 ERROR SCENARIOS:")
    print("   • If you see 'Authentication required' - you need to log in first")
    print("   • If you see 'Network error' - check server is running")
    print("   • If you see 'Site ID required' - ensure you've selected a site")

def main():
    """Run authentication and error handling tests."""
    print("🧪 Testing Availability Feature Authentication & Error Handling")
    print("=" * 70)
    
    tests = [
        test_unauthenticated_request,
        test_inventory_page_redirect,
        test_error_handling_improvements
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed >= total * 0.8:  # 80% pass rate
        print(f"✅ {passed}/{total} tests passed! Error handling is working correctly.")
        print("\n📋 Key improvements:")
        print("   • Enhanced authentication error messages")
        print("   • Proper HTTP status code handling")
        print("   • User-friendly error descriptions")
        print("   • Login redirect links in error messages")
        print("   • Network error differentiation")
    else:
        print(f"❌ {passed}/{total} tests passed. Please review error handling.")
    
    # Always provide user guidance
    provide_user_guidance()
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
