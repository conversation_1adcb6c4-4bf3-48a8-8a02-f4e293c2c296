#!/usr/bin/env python3
"""
Test mxapiinventory endpoint directly with session authentication using exact payload structure.
"""
import sys
import os
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def test_mxapiinventory_direct():
    """Test mxapiinventory endpoint directly using session authentication."""
    
    print("🔧 Testing mxapiinventory directly with session authentication")
    print("=" * 60)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Check if logged in
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo. Please login first through the web app.")
        return False
    
    print(f"✅ Logged in to Maximo as: {getattr(token_manager, 'username', 'Unknown')}")
    
    # The exact payload structure from your example
    payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 35,  # Adding 5 units
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "lotnum": "",
                    "reconciled": True,
                    "memo": "QR_SCANNER_TEST: Adding 5 units via QR scanner",
                    "controlacc": "",
                    "shrinkageacc": ""
                }
            ]
        }
    ]
    
    print(f"📋 Payload:")
    print(json.dumps(payload, indent=2))
    
    # Try different URL patterns
    test_urls = [
        f"{BASE_URL}/oslc/os/mxapiinventory",
        f"{BASE_URL}/api/os/mxapiinventory",
        f"{BASE_URL}/oslc/os/mxapiinventory?action=wsmethod:addchange",
    ]
    
    # Try different header combinations
    header_sets = [
        {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-method-override": "PATCH"
        },
        {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-method-override": "BULK"
        }
    ]
    
    for i, url in enumerate(test_urls):
        for j, headers in enumerate(header_sets):
            print(f"\n🔄 Test {i+1}.{j+1}: {url}")
            print(f"📋 Headers: {headers}")
            
            try:
                response = token_manager.session.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=(5.0, 30)
                )
                
                print(f"📊 Status Code: {response.status_code}")
                
                if response.status_code in [200, 201]:
                    try:
                        response_data = response.json()
                        print(f"✅ SUCCESS! Response:")
                        print(json.dumps(response_data, indent=2))
                        
                        # Check for success indicators
                        if isinstance(response_data, list) and len(response_data) > 0:
                            first_item = response_data[0]
                            if 'inventoryid' in first_item or 'itemnum' in first_item:
                                print(f"\n🎉 INVENTORY ADJUSTMENT SUCCESSFUL!")
                                print(f"📋 Item: {first_item.get('itemnum', 'Unknown')}")
                                if 'inventoryid' in first_item:
                                    print(f"📋 Inventory ID: {first_item['inventoryid']}")
                                return True
                        
                    except json.JSONDecodeError:
                        print(f"✅ SUCCESS! (Non-JSON response)")
                        print(f"📋 Response: {response.text[:200]}")
                        return True
                        
                elif response.status_code in [400, 403, 404, 500]:
                    try:
                        error_data = response.json()
                        print(f"❌ Error Response:")
                        print(json.dumps(error_data, indent=2))
                        
                        if 'oslc:Error' in error_data:
                            error_info = error_data['oslc:Error']
                            print(f"📋 Maximo Error: {error_info.get('oslc:message', 'Unknown')}")
                        
                    except json.JSONDecodeError:
                        print(f"❌ Error (Non-JSON): {response.text[:200]}")
                        
                else:
                    print(f"⚠️ Unexpected status: {response.status_code}")
                    print(f"📋 Response: {response.text[:200]}")
                
            except Exception as e:
                print(f"❌ Request failed: {str(e)}")
    
    print(f"\n❌ All tests failed")
    return False

def main():
    """Main function."""
    print("🚀 Starting direct mxapiinventory test")
    print("=" * 50)
    
    success = test_mxapiinventory_direct()
    
    if success:
        print("\n🎉 DIRECT TEST COMPLETED SUCCESSFULLY!")
        print("✅ The mxapiinventory endpoint is working")
        print("✅ Your payload structure is correct")
        print("✅ Session authentication works")
    else:
        print("\n❌ DIRECT TEST FAILED")
        print("🔍 Check the error messages above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
