#!/usr/bin/env python3
"""
Test script to verify the download fix works
"""

import requests
import json

def test_download_fixed():
    """Test that download now works with the fixed logic"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing FIXED download functionality for {wonum}")
    print("=" * 60)
    
    # Test downloading a recent file that should work
    # We know docinfoid 1926176 is our test_upload.txt file (115 bytes)
    docinfoid = '1926176'
    
    print(f"📥 Testing download of test_upload.txt (ID: {docinfoid})")
    
    download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
    
    try:
        response = requests.get(download_url, timeout=60)
        print(f"   📤 Download URL: {download_url}")
        print(f"   🔄 Status: {response.status_code}")
        print(f"   📊 Content Length: {len(response.content)} bytes")
        print(f"   📋 Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            content = response.content
            
            # Check if it's actually file content
            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                print(f"   ❌ Got HTML response: {content[:100].decode('utf-8', errors='ignore')}")
            else:
                print(f"   ✅ SUCCESS! Got file content: {content.decode('utf-8', errors='ignore')}")
                
                # Save the file
                test_filename = f"downloaded_test_upload.txt"
                with open(test_filename, 'wb') as f:
                    f.write(content)
                print(f"   💾 Saved to: {test_filename}")
                
                # Verify content matches what we expect
                expected_content = "This is a test file for attachment upload debugging.\nIt contains some sample text to test the upload functionality."
                actual_content = content.decode('utf-8')
                
                if actual_content.strip() == expected_content.strip():
                    print(f"   🎉 PERFECT! Content matches exactly what we uploaded!")
                    return True
                else:
                    print(f"   ⚠️  Content differs from expected")
                    print(f"   Expected: {expected_content}")
                    print(f"   Actual: {actual_content}")
        else:
            print(f"   ❌ Download failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   📝 Error: {error_data.get('error')}")
            except:
                print(f"   📝 Response: {response.text[:200]}")
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return False

def test_view_functionality():
    """Test the view functionality as well"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    docinfoid = '1926176'
    
    print(f"\n👁️ Testing VIEW functionality for test_upload.txt (ID: {docinfoid})")
    
    view_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/view'
    
    try:
        response = requests.get(view_url, timeout=60)
        print(f"   📤 View URL: {view_url}")
        print(f"   🔄 Status: {response.status_code}")
        print(f"   📊 Content Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            content = response.content
            
            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                print(f"   ❌ Got HTML response: {content[:100].decode('utf-8', errors='ignore')}")
            else:
                print(f"   ✅ SUCCESS! Got file content for viewing: {content.decode('utf-8', errors='ignore')}")
                return True
        else:
            print(f"   ❌ View failed with status {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return False

if __name__ == "__main__":
    print("🧪 Testing Fixed Download & View Functionality")
    print("=" * 60)
    
    download_success = test_download_fixed()
    view_success = test_view_functionality()
    
    print("\n" + "=" * 60)
    print("🎯 RESULTS:")
    print(f"   📥 Download: {'✅ SUCCESS' if download_success else '❌ FAILED'}")
    print(f"   👁️ View: {'✅ SUCCESS' if view_success else '❌ FAILED'}")
    
    if download_success and view_success:
        print("\n🎉 BOTH DOWNLOAD AND VIEW ARE WORKING PERFECTLY!")
        print("🚀 The attachment functionality is now complete!")
    else:
        print("\n⚠️  Some functionality still needs work")
