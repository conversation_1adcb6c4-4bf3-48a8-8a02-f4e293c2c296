<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixed Download Logic</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Fixed QR Download Logic Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Balance QR with qrType='balance'</h3>
        <button onclick="testBalanceQRTypeBalance()">Test Balance QR (qrType='balance')</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Balance QR with qr_data.qr_type='balance_specific'</h3>
        <button onclick="testBalanceQRTypeSpecific()">Test Balance QR (qr_data.qr_type='balance_specific')</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Real API Response Simulation</h3>
        <button onclick="testRealAPIResponse()">Test Real API Response</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        // Fixed download logic
        function generateFilename(qrResult, balanceRecord, qrType) {
            const qrData = qrResult.qr_data || {};
            const itemnum = qrData.itemnum || 'UNKNOWN_ITEM';
            
            // Check if this is a balance-specific QR (either from parameter or QR data)
            const isBalanceQR = qrType === 'balance' || qrData.qr_type === 'balance_specific';
            
            let filename;
            if (isBalanceQR) {
                // Balance-specific QR: itemnum_binnum.png
                let binnum = qrData.binnum || (balanceRecord && balanceRecord.binnum);
                
                if (!binnum) {
                    // Generate random binnum if not available
                    const randomBin = 'BIN' + Math.random().toString(36).substr(2, 6).toUpperCase();
                    binnum = randomBin;
                }
                filename = `${itemnum}_${binnum}.png`;
            } else {
                // Inventory-level QR: itemnum.png
                filename = `${itemnum}.png`;
            }

            // Clean filename to remove invalid characters
            filename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
            return { filename, isBalanceQR };
        }

        function testBalanceQRTypeBalance() {
            const qrResult = {
                qr_data: {
                    itemnum: 'TEST-ITEM-123',
                    binnum: 'A-01-01',
                    curbal: 100,
                    qr_type: 'balance_specific'
                }
            };
            const balanceRecord = { binnum: 'A-01-01', curbal: 100 };
            const result = generateFilename(qrResult, balanceRecord, 'balance');
            
            const expected = 'TEST-ITEM-123_A-01-01.png';
            const pass = result.filename === expected && result.isBalanceQR === true;
            
            document.getElementById('result1').innerHTML = `
                <strong>Expected:</strong> ${expected}<br>
                <strong>Generated:</strong> ${result.filename}<br>
                <strong>Is Balance QR:</strong> ${result.isBalanceQR}<br>
                <strong>Status:</strong> <span class="${pass ? 'pass' : 'fail'}">${pass ? '✅ PASS' : '❌ FAIL'}</span>
            `;
            document.getElementById('result1').className = `result ${pass ? 'pass' : 'fail'}`;
        }

        function testBalanceQRTypeSpecific() {
            const qrResult = {
                qr_data: {
                    itemnum: 'TEST-ITEM-456',
                    binnum: 'B-02-03',
                    curbal: 250,
                    qr_type: 'balance_specific'
                }
            };
            const balanceRecord = { binnum: 'B-02-03', curbal: 250 };
            const result = generateFilename(qrResult, balanceRecord, 'inventory'); // Wrong qrType but should still work
            
            const expected = 'TEST-ITEM-456_B-02-03.png';
            const pass = result.filename === expected && result.isBalanceQR === true;
            
            document.getElementById('result2').innerHTML = `
                <strong>Expected:</strong> ${expected}<br>
                <strong>Generated:</strong> ${result.filename}<br>
                <strong>Is Balance QR:</strong> ${result.isBalanceQR}<br>
                <strong>qrType param:</strong> 'inventory' (should be overridden by qr_data.qr_type)<br>
                <strong>Status:</strong> <span class="${pass ? 'pass' : 'fail'}">${pass ? '✅ PASS' : '❌ FAIL'}</span>
            `;
            document.getElementById('result2').className = `result ${pass ? 'pass' : 'fail'}`;
        }

        function testRealAPIResponse() {
            // Real API response from the curl test
            const qrResult = {
                qr_data: {
                    itemnum: "TEST-ITEM-RAW",
                    binnum: "A-01-01",
                    curbal: 100,
                    qr_type: "balance_specific",
                    conditioncode: "A1",
                    lotnum: "LOT123"
                }
            };
            const balanceRecord = {
                binnum: "A-01-01",
                curbal: 100,
                conditioncode: "A1"
            };
            const result = generateFilename(qrResult, balanceRecord, 'balance');
            
            const expected = 'TEST-ITEM-RAW_A-01-01.png';
            const pass = result.filename === expected && result.isBalanceQR === true;
            
            document.getElementById('result3').innerHTML = `
                <strong>Expected:</strong> ${expected}<br>
                <strong>Generated:</strong> ${result.filename}<br>
                <strong>Is Balance QR:</strong> ${result.isBalanceQR}<br>
                <strong>Current Balance:</strong> ${qrResult.qr_data.curbal}<br>
                <strong>Bin Number:</strong> ${qrResult.qr_data.binnum}<br>
                <strong>Status:</strong> <span class="${pass ? 'pass' : 'fail'}">${pass ? '✅ PASS' : '❌ FAIL'}</span>
            `;
            document.getElementById('result3').className = `result ${pass ? 'pass' : 'fail'}`;
        }
    </script>
</body>
</html>
