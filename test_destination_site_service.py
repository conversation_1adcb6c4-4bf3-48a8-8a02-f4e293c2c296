#!/usr/bin/env python3
"""
Test Destination Site Context Service
=====================================

Create a modified inventory transfer service that uses destination site context
in the top-level record, exactly as specified by the user.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

class DestinationSiteTransferService:
    """Modified inventory transfer service using destination site context"""
    
    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        
    def submit_transfer_with_destination_context(self, transfer_data: Dict) -> Dict:
        """
        Submit transfer using destination site context in top-level record
        
        Args:
            transfer_data (Dict): Transfer information
            
        Returns:
            Dict: Result of the transfer submission
        """
        try:
            # Validate authentication
            if not self.token_manager.is_logged_in():
                return {
                    'success': False,
                    'error': 'Not authenticated with <PERSON><PERSON>'
                }
            
            # Build payload with destination site context
            payload = self._build_destination_context_payload(transfer_data)
            
            # Submit to Maximo
            result = self._submit_to_maximo(payload)
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Exception: {str(e)}")
            return {
                'success': False,
                'error': f'Transfer submission failed: {str(e)}'
            }
    
    def _build_destination_context_payload(self, transfer_data: Dict) -> List[Dict]:
        """
        Build payload with destination site context in top-level record
        
        This uses the exact structure specified by the user:
        - Top-level siteid: destination site (IKWAJ)
        - Top-level location: destination storeroom (KWAJ-1058)
        - Include toissueunit for unit conversion
        """
        # Extract fields
        itemnum = transfer_data.get('itemnum', '')
        quantity = float(transfer_data.get('quantity', 1.0))
        
        from_siteid = transfer_data.get('from_siteid', '')
        to_siteid = transfer_data.get('to_siteid', '')
        from_storeroom = transfer_data.get('from_storeroom', '')
        to_storeroom = transfer_data.get('to_storeroom', '')
        
        from_issue_unit = transfer_data.get('from_issue_unit', 'RO')
        to_issue_unit = transfer_data.get('to_issue_unit', 'EA')
        
        from_bin = transfer_data.get('from_bin', 'DEFAULT')
        to_bin = transfer_data.get('to_bin', 'DEFAULT')
        from_lot = transfer_data.get('from_lot', 'DEFAULT')
        to_lot = transfer_data.get('to_lot', 'DEFAULT')
        from_condition = transfer_data.get('from_condition', 'A1')
        to_condition = transfer_data.get('to_condition', 'A1')
        
        # Build payload with DESTINATION site context (user's requested structure)
        payload = [
            {
                "_action": "AddChange",
                "itemnum": itemnum,
                "itemsetid": "ITEMSET",
                "siteid": to_siteid,  # DESTINATION site (IKWAJ)
                "location": to_storeroom,  # DESTINATION location (KWAJ-1058)
                "issueunit": from_issue_unit,
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": itemnum,
                        "issuetype": "TRANSFER",
                        "quantity": quantity,
                        "fromsiteid": from_siteid,
                        "tositeid": to_siteid,
                        "fromstoreloc": from_storeroom,
                        "tostoreloc": to_storeroom,
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": from_issue_unit,
                        "frombinnum": from_bin,
                        "tobinnum": to_bin,
                        "fromlotnum": from_lot,
                        "tolotnum": to_lot,
                        "fromconditioncode": from_condition,
                        "toconditioncode": to_condition,
                        "toissueunit": to_issue_unit  # KEY: Include toissueunit for conversion
                    }
                ]
            }
        ]
        
        self.logger.info(f"🔧 DESTINATION CONTEXT: Built payload with destination site context")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Top-level siteid: {to_siteid}")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Top-level location: {to_storeroom}")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Payload: {json.dumps(payload, indent=2)}")
        
        return payload
    
    def _submit_to_maximo(self, payload: List[Dict]) -> Dict:
        """Submit payload to Maximo API"""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
            
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            }
            
            self.logger.info(f"🔄 DESTINATION CONTEXT: Submitting to {api_url}")
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                headers=headers,
                timeout=(5.0, 30)
            )
            
            self.logger.info(f"📊 DESTINATION CONTEXT: Response status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    self.logger.info(f"📋 DESTINATION CONTEXT: Response: {json.dumps(response_data, indent=2)}")
                    
                    # Check for success (204 status in response)
                    if isinstance(response_data, list) and len(response_data) > 0:
                        first_item = response_data[0]
                        if first_item.get('_responsemeta', {}).get('status') == '204':
                            return {
                                'success': True,
                                'message': 'Destination context transfer submitted successfully to Maximo',
                                'response': response_data,
                                'status_code': response.status_code,
                                'timestamp': datetime.now().isoformat()
                            }
                        else:
                            # Check for errors
                            error_info = None
                            if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                                error = first_item['_responsedata']['Error']
                                error_info = f"{error.get('reasonCode')} - {error.get('message')}"
                            
                            return {
                                'success': False,
                                'message': 'Destination context transfer failed',
                                'response': response_data,
                                'status_code': response.status_code,
                                'error': error_info,
                                'timestamp': datetime.now().isoformat()
                            }
                    
                    return {
                        'success': False,
                        'message': 'Unexpected response format',
                        'response': response_data,
                        'status_code': response.status_code,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Invalid JSON response',
                        'response_text': response.text,
                        'status_code': response.status_code
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP error: {response.status_code}',
                    'response_text': response.text,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Request failed: {str(e)}'
            }

def test_destination_context_service():
    """Test the destination context service"""
    print("🚀 TESTING DESTINATION SITE CONTEXT SERVICE")
    print("=" * 50)
    
    # Initialize authentication
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Authentication failed. Cannot proceed.")
        return
    
    print("✅ Authenticated with Maximo")
    
    # Initialize destination context service
    service = DestinationSiteTransferService(token_manager)
    
    # Test cases
    test_cases = [
        {
            "name": "Destination Context - KWAJ-1058",
            "data": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "CMW-AJ",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "to_issue_unit": "EA",
                "from_bin": "DEFAULT",
                "to_bin": "DEFAULT",
                "from_lot": "DEFAULT",
                "to_lot": "DEFAULT",
                "from_condition": "A1",
                "to_condition": "A1"
            }
        },
        {
            "name": "Destination Context - KWAJ-1115",
            "data": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "CMW-AJ",
                "to_storeroom": "KWAJ-1115",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "to_issue_unit": "EA",
                "from_bin": "DEFAULT",
                "to_bin": "DEFAULT",
                "from_lot": "DEFAULT",
                "to_lot": "DEFAULT",
                "from_condition": "A1",
                "to_condition": "A1"
            }
        },
        {
            "name": "Destination Context - RIP001 Source",
            "data": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "RIP001",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "to_issue_unit": "EA",
                "from_bin": "DEFAULT",
                "to_bin": "DEFAULT",
                "from_lot": "DEFAULT",
                "to_lot": "DEFAULT",
                "from_condition": "A1",
                "to_condition": "A1"
            }
        }
    ]
    
    successful_tests = []
    
    # Run tests
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 TEST {i}: {test_case['name']}")
        print("=" * 60)
        
        result = service.submit_transfer_with_destination_context(test_case['data'])
        
        print(f"📊 Result: {json.dumps(result, indent=2)}")
        
        if result.get('success'):
            print("🎉 SUCCESS! Destination context transfer worked!")
            successful_tests.append({
                'test_case': test_case['name'],
                'data': test_case['data'],
                'result': result
            })
            
            # Save successful test
            filename = f"successful_destination_context_service_{i}.json"
            with open(filename, 'w') as f:
                json.dump({
                    'test_case': test_case['name'],
                    'transfer_data': test_case['data'],
                    'result': result
                }, f, indent=2)
            print(f"💾 Successful test saved: {filename}")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")
    
    # Summary
    print(f"\n📊 DESTINATION CONTEXT SERVICE TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Successful: {len(successful_tests)}")
    print(f"❌ Failed: {len(test_cases) - len(successful_tests)}")
    
    if successful_tests:
        print(f"\n🎉 DESTINATION CONTEXT APPROACH WORKS!")
        print("=" * 40)
        for success in successful_tests:
            print(f"✅ {success['test_case']}")
        
        # Save all successful tests
        with open('all_successful_destination_context_tests.json', 'w') as f:
            json.dump(successful_tests, f, indent=2)
        print(f"\n💾 All successful tests saved: all_successful_destination_context_tests.json")
    else:
        print(f"\n❌ NO SUCCESS WITH DESTINATION CONTEXT SERVICE")

if __name__ == "__main__":
    test_destination_context_service()
