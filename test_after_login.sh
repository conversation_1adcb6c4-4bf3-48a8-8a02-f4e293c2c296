#!/bin/bash

# Test After Login - Working Combinations
# =======================================

echo "🚀 TEST AFTER LOGIN - WORKING COMBINATIONS"
echo "=========================================="

echo "🎯 OBJECTIVE: Test the exact working combinations after authentication"
echo "📋 PREREQUISITE: You must login to http://127.0.0.1:5010 first!"
echo "🔍 WORKING PATTERN: CMW-AJ → CMW-BU (Confirmed 204 success)"
echo ""

echo "⚠️  IMPORTANT: Before running this test:"
echo "1. Go to http://127.0.0.1:5010"
echo "2. Login with your Maximo credentials"
echo "3. Then run this script"
echo ""

read -p "Have you logged in to the Flask app? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please login first at http://127.0.0.1:5010"
    exit 1
fi

echo "🔄 Testing working combinations..."
echo ""

# Test 1: CONFIRMED WORKING PATTERN - CMW-AJ → CMW-BU
echo "📋 TEST 1: CONFIRMED WORKING PATTERN (CMW-AJ → CMW-BU)"
echo "$(printf '=%.0s' {1..60})"

response1=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-BU",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 Response:"
echo "$response1"
echo ""

if echo "$response1" | grep -q '"status": "204"'; then
    echo "🎉 SUCCESS! Pattern 1 worked with 204 status!"
elif echo "$response1" | grep -q '"success": true'; then
    echo "✅ SUCCESS! Pattern 1 accepted by system"
elif echo "$response1" | grep -q "401"; then
    echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
else
    echo "❌ Pattern 1 failed - check response above"
fi

echo ""

# Test 2: NEW SAME SITE TRANSFER BUTTON
echo "📋 TEST 2: NEW SAME SITE TRANSFER BUTTON"
echo "$(printf '=%.0s' {1..60})"

response2=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-same-site \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 Response:"
echo "$response2"
echo ""

if echo "$response2" | grep -q '"status": "204"'; then
    echo "🎉 SUCCESS! Same Site Transfer button worked with 204 status!"
elif echo "$response2" | grep -q '"success": true'; then
    echo "✅ SUCCESS! Same Site Transfer button accepted by system"
elif echo "$response2" | grep -q "401"; then
    echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
else
    echo "❌ Same Site Transfer failed - check response above"
fi

echo ""

# Test 3: NEW CROSS SITE TRANSFER BUTTON
echo "📋 TEST 3: NEW CROSS SITE TRANSFER BUTTON"
echo "$(printf '=%.0s' {1..60})"

response3=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-cross-site \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 Response:"
echo "$response3"
echo ""

if echo "$response3" | grep -q '"status": "204"'; then
    echo "🎉 SUCCESS! Cross Site Transfer button worked with 204 status!"
elif echo "$response3" | grep -q '"success": true'; then
    echo "✅ SUCCESS! Cross Site Transfer button accepted by system"
elif echo "$response3" | grep -q "401"; then
    echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
else
    echo "❌ Cross Site Transfer failed - check response above"
fi

echo ""

# Test 4: ERROR HANDLING TEST
echo "📋 TEST 4: ERROR HANDLING (Intentional Duplicate)"
echo "$(printf '=%.0s' {1..60})"

response4=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-same-site \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 Response:"
echo "$response4"
echo ""

if echo "$response4" | grep -q '"success": false'; then
    echo "✅ ERROR HANDLING WORKING! Detailed error response provided"
    if echo "$response4" | grep -q '"detailed_error"'; then
        echo "✅ DETAILED ERROR: Error details included"
    fi
    if echo "$response4" | grep -q '"error_guidance"'; then
        echo "✅ ERROR GUIDANCE: User guidance provided"
    fi
elif echo "$response4" | grep -q "401"; then
    echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
else
    echo "⚠️  Error handling test had unexpected result"
fi

echo ""
echo "📊 IMPLEMENTATION TEST SUMMARY"
echo "============================="

# Count successes
success_count=0
if echo "$response1" | grep -q '"success": true' && ! echo "$response1" | grep -q "401"; then
    success_count=$((success_count + 1))
fi
if echo "$response2" | grep -q '"success": true' && ! echo "$response2" | grep -q "401"; then
    success_count=$((success_count + 1))
fi
if echo "$response3" | grep -q '"success": true' && ! echo "$response3" | grep -q "401"; then
    success_count=$((success_count + 1))
fi

echo "✅ Successful transfers: $success_count/3"
echo "📝 Error handling test completed"

if [ $success_count -eq 3 ]; then
    echo ""
    echo "🎉 IMPLEMENTATION SUCCESS!"
    echo "========================"
    echo "✅ All transfer patterns working"
    echo "✅ Two button system implemented"
    echo "✅ Error handling functional"
    echo ""
    echo "🎯 READY FOR PRODUCTION USE!"
    echo "• Same Site Transfer button: Working"
    echo "• Cross Site Transfer button: Working"
    echo "• Error display: Working"
    echo "• Unit conversion: Working"
elif [ $success_count -gt 0 ]; then
    echo ""
    echo "🔄 PARTIAL SUCCESS"
    echo "=================="
    echo "✅ Some patterns working"
    echo "🔍 Check responses above for details"
    echo "💡 May need authentication or minor adjustments"
else
    echo ""
    echo "❌ AUTHENTICATION REQUIRED"
    echo "========================="
    echo "🔐 Please login at http://127.0.0.1:5010 first"
    echo "🔄 Then run this test again"
fi

echo ""
echo "🎯 NEXT STEPS:"
echo "• Test the UI at http://127.0.0.1:5010/inventory-management"
echo "• Verify the two transfer buttons appear"
echo "• Test transfers through the web interface"
echo "• Check error messages display properly"
