#!/usr/bin/env python3
"""
Simple test to verify the signature flow works end-to-end
Tests ONLY the signature functionality without authentication complexity
"""

import requests
import json
import base64
from datetime import datetime

BASE_URL = "http://localhost:5011"
session = requests.Session()

def create_test_signature():
    """Create a simple test signature as base64 PNG"""
    test_signature_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    return f"data:image/png;base64,{test_signature_b64}"

def test_complete_signature_flow():
    """Test the complete signature flow end-to-end"""
    print("🧪 TESTING COMPLETE SIGNATURE FLOW")
    print("=" * 50)
    
    # Step 1: Configure signature requirement
    print("📝 Step 1: Configure COMP status for signature requirement...")
    config_data = {
        "statuses": ["COMP"],
        "scope": ["parent", "task"],
        "enabled": True
    }
    
    response = session.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=config_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print(f"❌ Config failed: {response.status_code} - {response.text}")
        return False
    
    print("✅ Configuration saved successfully")
    
    # Step 2: Test task status change (should require signature)
    print("\n📝 Step 2: Test task status change to COMP...")
    task_wonum = "TEST123"  # Use a test work order number
    
    response = session.post(
        f"{BASE_URL}/api/task/{task_wonum}/status",
        json={"status": "COMP"},
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print(f"❌ Status change failed: {response.status_code} - {response.text}")
        return False
    
    data = response.json()
    print(f"Status change response: {json.dumps(data, indent=2)}")
    
    if not data.get('signature_required'):
        print("❌ Signature requirement not detected")
        return False
    
    print("✅ Signature requirement detected correctly")
    
    # Step 3: Submit signature
    print("\n📝 Step 3: Submit signature...")
    signature_data = {
        "wonum": task_wonum,
        "status": "COMP",
        "wo_type": "task",
        "signature_data": create_test_signature(),
        "customer_name": "Test Customer",
        "comments": "Test signature submission",
        "date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    response = session.post(
        f"{BASE_URL}/api/signature/submit",
        json=signature_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print(f"❌ Signature submission failed: {response.status_code} - {response.text}")
        return False
    
    data = response.json()
    print(f"Signature submission response: {json.dumps(data, indent=2)}")
    
    if data.get('success'):
        print("✅ Signature submission successful!")
        return True
    else:
        print(f"❌ Signature submission failed: {data.get('error')}")
        return False

def test_without_signature():
    """Test status change without signature requirement"""
    print("\n🧪 TESTING STATUS CHANGE WITHOUT SIGNATURE")
    print("=" * 50)
    
    # Configure no signature requirements
    print("📝 Disabling signature requirements...")
    config_data = {
        "statuses": [],
        "scope": ["parent", "task"],
        "enabled": False
    }
    
    response = session.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=config_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print(f"❌ Config failed: {response.status_code}")
        return False
    
    print("✅ Signature requirements disabled")
    
    # Test status change
    print("\n📝 Testing status change to INPRG (no signature)...")
    task_wonum = "TEST123"
    
    response = session.post(
        f"{BASE_URL}/api/task/{task_wonum}/status",
        json={"status": "INPRG"},
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print(f"❌ Status change failed: {response.status_code} - {response.text}")
        return False
    
    data = response.json()
    print(f"Status change response: {json.dumps(data, indent=2)}")
    
    # For test work order, we expect it to fail with "work order not found"
    # but NOT require signature
    if data.get('signature_required'):
        print("❌ Unexpected signature requirement")
        return False
    
    print("✅ No signature requirement (as expected)")
    return True

if __name__ == "__main__":
    print("🚀 SIGNATURE FLOW TEST")
    print("Testing the complete signature system end-to-end")
    print("=" * 60)
    
    try:
        # Test 1: Complete signature flow
        test1_success = test_complete_signature_flow()
        
        # Test 2: Status change without signature
        test2_success = test_without_signature()
        
        print("\n" + "=" * 60)
        print("🏁 TEST RESULTS")
        print("=" * 60)
        
        print(f"✅ Signature Flow Test: {'PASS' if test1_success else 'FAIL'}")
        print(f"✅ No Signature Test: {'PASS' if test2_success else 'FAIL'}")
        
        if test1_success and test2_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Signature system is working correctly")
            print("✅ Configuration system is working")
            print("✅ Status change detection is working")
            print("✅ Signature submission is working")
            print("\n💡 The signature system is FULLY FUNCTIONAL!")
            print("If you're still seeing 'network errors' in the browser,")
            print("it's a frontend JavaScript issue, not a backend problem.")
        else:
            print("\n❌ SOME TESTS FAILED")
            print("Check the Flask logs for detailed error information.")
            
    except Exception as e:
        print(f"\n❌ TEST ERROR: {e}")
        print("Make sure the Flask app is running on localhost:5010")
    
    print("\n" + "=" * 60)
