#!/bin/bash

# Test Working Transfer - Use Valid Locations
# ===========================================

echo "🚀 TESTING WITH VALID LOCATIONS"
echo "==============================="

echo "🔍 Analysis from previous tests:"
echo "  ❌ KWAJ-1058 doesn't exist in Maximo system"
echo "  ❌ CENTRAL is not a valid location"
echo "  ✅ RIP001 exists (source location works)"
echo ""
echo "💡 Strategy: Use locations that we know exist from your app's data"
echo ""

# Test 1: Transfer within same site to a different valid location
echo "📋 TEST 1: Transfer to another location in LCVKWT"
echo "=============================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "LCVKWT",
    "from_storeroom": "RIP001",
    "to_storeroom": "WAREHOUSE",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🏢 Testing transfer within same site"
echo ""

# Test 2: Try with a common storeroom name
echo "📋 TEST 2: Transfer to STOREROOM"
echo "=============================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "STOREROOM",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "📦 Testing with common storeroom name"
echo ""

# Test 3: Try with MAIN storeroom
echo "📋 TEST 3: Transfer to MAIN"
echo "=========================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "MAIN",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🏭 Testing with MAIN storeroom"
echo ""

# Test 4: Try with IKWAJ as destination (site code as location)
echo "📋 TEST 4: Transfer to site code as location"
echo "=========================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "IKWAJ",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🏢 Testing with site code as location"
echo ""

# Test 5: Try with DEFAULT location
echo "📋 TEST 5: Transfer to DEFAULT"
echo "============================"

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "DEFAULT",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "📍 Testing with DEFAULT location"
echo ""

# Test 6: Try with smaller quantity and no bins
echo "📋 TEST 6: Minimal successful transfer attempt"
echo "==========================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "WAREHOUSE",
    "quantity": 0.1,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 Testing minimal transfer with small quantity"
echo ""

echo "🔍 LOOKING FOR SUCCESS PATTERNS"
echo "==============================="
echo "Watch for responses with:"
echo "  ✅ '_responsemeta': { 'status': '204' }"
echo "  ✅ No 'Error' object in '_responsedata'"
echo "  ✅ HTTP_STATUS:200 with successful response"
echo ""
echo "If we find a working pattern, we can:"
echo "  1. Use that location in your application"
echo "  2. Create the KWAJ-1058 location in Maximo"
echo "  3. Update your application's location validation"
