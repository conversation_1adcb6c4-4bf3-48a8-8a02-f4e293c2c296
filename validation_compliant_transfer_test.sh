#!/bin/bash

# Validation Compliant Transfer Testing
# =====================================

echo "🎯 VALIDATION COMPLIANT TRANSFER TESTING"
echo "========================================"

echo "🔍 OBJECTIVE: Pass Maximo validation by using correct data structure"
echo "✅ STRATEGY: Work WITH validation, not around it"
echo "📋 APPROACH: Use valid locations, proper payload structure, correct business logic"
echo ""

# Counter for tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for success
test_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..70})"
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "Response:"
    echo "$response"
    
    # Check for success patterns
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"statusCode": "204"'; then
        echo "🎉 SUCCESS! Transfer completed with validation passed!"
        echo "💾 Saving successful payload..."
        echo "$payload" > "working_transfer_payload_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Validation failed: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING WITH VALIDATION COMPLIANCE"
echo "===================================="

# Test 1: Use locations that exist in the same site (IKWAJ to IKWAJ)
echo "Strategy 1: Same-site transfers (should pass validation)"
test_payload "IKWAJ to IKWAJ - Same Site Transfer" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 2: Different locations within IKWAJ
test_payload "IKWAJ Internal Transfer - Different Locations" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1115",
    "to_storeroom": "KWAJ-1500",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 3: Use space-named location within same site
test_payload "IKWAJ Internal - Space Named Location" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "8051 FLOOR-X",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 4: Check if we can find valid locations in LCVKWT
echo ""
echo "🔍 Checking available locations in LCVKWT..."
lcvkwt_locations=$(curl -X GET "http://127.0.0.1:5010/api/inventory/transfer-current-item/storerooms?siteid=LCVKWT" \
    -H "Accept: application/json" -s)
echo "LCVKWT Locations:"
echo "$lcvkwt_locations" | head -10
echo ""

# Test 5: LCVKWT internal transfer (if we have multiple locations)
test_payload "LCVKWT Internal Transfer" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "LCVKWT",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP002",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 6: Try with different item that might exist in both sites
test_payload "Different Item - Cross Site" '{
    "itemnum": "COMMON-ITEM",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "EA"
}'

# Test 7: Smaller quantity to avoid inventory shortage
test_payload "Small Quantity Transfer" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 0.01,
    "from_issue_unit": "RO"
}'

# Test 8: With proper bins that might exist
test_payload "With Valid Bins" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT"
}'

# Test 9: With lot numbers
test_payload "With Lot Numbers" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT"
}'

# Test 10: Complete transfer with all fields
test_payload "Complete Transfer - All Fields" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 VALIDATION COMPLIANCE TEST SUMMARY"
echo "===================================="
echo "✅ Successful transfers: $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 FOUND WORKING TRANSFER PATTERNS!"
    echo "=================================="
    echo "✅ Validation passed for $SUCCESS_COUNT test(s)"
    echo "💾 Check working_transfer_payload_*.json files for successful patterns"
    echo "🔧 Use these patterns in your application"
    echo ""
    echo "📋 NEXT STEPS:"
    echo "1. Review successful payload structures"
    echo "2. Update your application to use working patterns"
    echo "3. Implement proper validation in your UI"
    echo "4. Test with real inventory data"
else
    echo ""
    echo "🔍 NO SUCCESS YET - ANALYZING PATTERNS"
    echo "====================================="
    echo "📋 Common validation issues found:"
    echo "• Location validation against wrong site"
    echo "• Inventory availability issues"
    echo "• Item existence in target locations"
    echo ""
    echo "🔄 CONTINUING WITH ADVANCED VALIDATION STRATEGIES..."
fi
