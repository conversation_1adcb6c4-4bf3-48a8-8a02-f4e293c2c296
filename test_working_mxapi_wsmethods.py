#!/usr/bin/env python3
"""
Test Working MXAPI Endpoints for WSMethods

This script tests the working MXAPI endpoints (like MXAPIITEM, MXAPIINVENTORY)
for transfer-related wsmethods and itemavailability functionality.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

class WorkingMXAPITester:
    """Tests working MXAPI endpoints for wsmethods."""
    
    def __init__(self):
        """Initialize the tester."""
        self.base_url = BASE_URL
        self.api_key = API_KEY
        self.working_endpoints = [
            "mxapiinventory",
            "mxapiitem", 
            "mxapiworkorder",
            "mxapilabor",
            "mxapiasset"
        ]
        
    def test_all_working_endpoints(self):
        """Test all working MXAPI endpoints for wsmethods."""
        print("🔍 Testing Working MXAPI Endpoints for WSMethods")
        print("=" * 80)
        
        results = {}
        
        for endpoint_name in self.working_endpoints:
            print(f"\n📋 Testing Endpoint: {endpoint_name}")
            print("-" * 50)
            
            endpoint_url = f"{self.base_url}/api/os/{endpoint_name}"
            result = self._test_endpoint_comprehensive(endpoint_url, endpoint_name)
            results[endpoint_name] = result
            
        return results
        
    def _test_endpoint_comprehensive(self, endpoint_url: str, endpoint_name: str) -> Dict:
        """Comprehensive testing of an endpoint."""
        
        # First verify the endpoint is accessible
        if not self._verify_endpoint_access(endpoint_url):
            return {"accessible": False, "wsmethods": {}}
            
        # Test inventory/item related wsmethods
        wsmethods_to_test = [
            "itemavailability",
            "transfercurrentitem",
            "issuecurrentitem", 
            "receivecurrentitem",
            "addchange",
            "adjustcurrentbalance",
            "adjustphysicalcount"
        ]
        
        results = {
            "accessible": True,
            "endpoint": endpoint_url,
            "wsmethods": {}
        }
        
        for wsmethod in wsmethods_to_test:
            print(f"   🧪 Testing wsmethod: {wsmethod}")
            method_result = self._test_wsmethod_detailed(endpoint_url, wsmethod, endpoint_name)
            results["wsmethods"][wsmethod] = method_result
            
        return results
        
    def _verify_endpoint_access(self, endpoint_url: str) -> bool:
        """Verify that an endpoint is accessible."""
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        try:
            response = requests.get(
                endpoint_url,
                params={"oslc.pageSize": "1"},
                headers=headers,
                timeout=(3.05, 10)
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'rdfs:member' in data or 'oslc:responseInfo' in data:
                        print(f"   ✅ Endpoint accessible")
                        return True
                except:
                    pass
                    
            print(f"   ❌ Endpoint not accessible: {response.status_code}")
            return False
            
        except Exception as e:
            print(f"   ❌ Endpoint access error: {str(e)}")
            return False
            
    def _test_wsmethod_detailed(self, endpoint_url: str, wsmethod: str, endpoint_name: str) -> Dict:
        """Test a specific wsmethod with detailed analysis."""
        
        test_url = f"{endpoint_url}?action=wsmethod:{wsmethod}"
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        # Create appropriate test payload based on endpoint
        test_payload = self._get_test_payload(endpoint_name, wsmethod)
        
        result = {
            "exists": False,
            "status_code": None,
            "response": None,
            "error_code": None,
            "error_message": None,
            "test_payload": test_payload
        }
        
        try:
            response = requests.post(
                test_url,
                json=test_payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            result["status_code"] = response.status_code
            
            if response.content:
                try:
                    data = response.json()
                    result["response"] = data
                    
                    # Analyze the response
                    if response.status_code == 200:
                        result["exists"] = True
                        print(f"      ✅ SUCCESS - {wsmethod} works!")
                        print(f"         Response: {json.dumps(data, indent=8)}")
                    elif 'oslc:Error' in data:
                        error = data['oslc:Error']
                        reason_code = error.get('spi:reasonCode', 'Unknown')
                        message = error.get('oslc:message', 'No message')
                        
                        result["error_code"] = reason_code
                        result["error_message"] = message
                        
                        # Check if method exists but has parameter issues
                        if reason_code not in ['BMXAA9372E', 'BMXAA9487E']:  # Method not found errors
                            result["exists"] = True
                            print(f"      ✅ EXISTS - {wsmethod} (parameter error)")
                            print(f"         Error: {reason_code} - {message[:100]}...")
                        else:
                            print(f"      ❌ NOT FOUND - {wsmethod}")
                    else:
                        print(f"      ⚠️ UNEXPECTED - {wsmethod} (status {response.status_code})")
                        
                except Exception as e:
                    result["response"] = response.text
                    print(f"      ⚠️ JSON ERROR - {wsmethod}: {str(e)}")
            else:
                print(f"      ❌ NO CONTENT - {wsmethod}")
                
        except Exception as e:
            result["response"] = str(e)
            print(f"      ❌ REQUEST ERROR - {wsmethod}: {str(e)}")
            
        return result
        
    def _get_test_payload(self, endpoint_name: str, wsmethod: str) -> Dict:
        """Get appropriate test payload for endpoint and wsmethod."""
        
        base_payload = {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT"
        }
        
        if endpoint_name == "mxapiinventory":
            base_payload.update({
                "location": "LCVK-CMW-AJ"
            })
        elif endpoint_name == "mxapiworkorder":
            base_payload = {
                "wonum": "15643629",
                "siteid": "LCVKWT"
            }
        elif endpoint_name == "mxapiasset":
            base_payload = {
                "assetnum": "TEST-ASSET-001",
                "siteid": "LCVKWT"
            }
        elif endpoint_name == "mxapilabor":
            base_payload = {
                "laborcode": "ADMIN",
                "siteid": "LCVKWT"
            }
            
        # Add wsmethod-specific fields
        if wsmethod in ["transfercurrentitem"]:
            base_payload.update({
                "fromlocation": "LCVK-CMW-AJ",
                "tolocation": "LCVKWT-STORE",
                "quantity": 1.0
            })
        elif wsmethod in ["issuecurrentitem", "receivecurrentitem"]:
            base_payload.update({
                "quantity": 1.0
            })
        elif wsmethod in ["addchange", "adjustcurrentbalance"]:
            base_payload.update({
                "curbal": 100.0
            })
        elif wsmethod == "adjustphysicalcount":
            base_payload.update({
                "physcnt": 100.0
            })
            
        return base_payload
        
    def test_successful_wsmethods_with_session(self, results: Dict):
        """Test successful wsmethods with session authentication."""
        print(f"\n🔑 Testing Successful WSMethods with Session Authentication")
        print("=" * 80)
        
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
            from backend.auth.token_manager import MaximoTokenManager
            
            token_manager = MaximoTokenManager(self.base_url)
            
            if not token_manager.is_logged_in():
                print("❌ Session authentication not available")
                return
                
            print("✅ Session authentication available")
            
            # Find successful wsmethods
            successful_methods = []
            for endpoint_name, endpoint_result in results.items():
                if endpoint_result.get("accessible"):
                    for wsmethod, method_result in endpoint_result["wsmethods"].items():
                        if method_result.get("exists") and method_result.get("status_code") == 200:
                            successful_methods.append({
                                "endpoint": endpoint_name,
                                "wsmethod": wsmethod,
                                "payload": method_result["test_payload"]
                            })
                            
            if successful_methods:
                print(f"📋 Testing {len(successful_methods)} successful wsmethods with session auth:")
                
                for method_info in successful_methods:
                    self._test_wsmethod_with_session(token_manager, method_info)
            else:
                print("❌ No successful wsmethods found to test with session auth")
                
        except ImportError:
            print("❌ Token manager not available for session testing")
            
    def _test_wsmethod_with_session(self, token_manager, method_info: Dict):
        """Test a specific wsmethod with session authentication."""
        endpoint_name = method_info["endpoint"]
        wsmethod = method_info["wsmethod"]
        payload = method_info["payload"]
        
        print(f"\n   🧪 Testing {endpoint_name}.{wsmethod} with session:")
        
        # Use OSLC endpoint for session auth
        endpoint_url = f"{self.base_url}/oslc/os/{endpoint_name}"
        test_url = f"{endpoint_url}?action=wsmethod:{wsmethod}"
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        try:
            response = token_manager.session.post(
                test_url,
                json=payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"      Status: {response.status_code}")
            print(f"      Payload: {json.dumps(payload, indent=6)}")
            
            if response.content:
                try:
                    data = response.json()
                    print(f"      Response: {json.dumps(data, indent=6)}")
                except:
                    print(f"      Response (text): {response.text[:200]}...")
            else:
                print(f"      No response content")
                
        except Exception as e:
            print(f"      ❌ Session test error: {str(e)}")
            
    def generate_wsmethod_documentation(self, results: Dict):
        """Generate comprehensive wsmethod documentation."""
        print(f"\n📚 WSMethod Documentation")
        print("=" * 80)
        
        # Find all existing wsmethods
        existing_methods = {}
        
        for endpoint_name, endpoint_result in results.items():
            if endpoint_result.get("accessible"):
                for wsmethod, method_result in endpoint_result["wsmethods"].items():
                    if method_result.get("exists"):
                        if wsmethod not in existing_methods:
                            existing_methods[wsmethod] = []
                        existing_methods[wsmethod].append({
                            "endpoint": endpoint_name,
                            "result": method_result
                        })
                        
        if existing_methods:
            for wsmethod, endpoints in existing_methods.items():
                print(f"\n🔸 WSMethod: {wsmethod}")
                print(f"   Available on {len(endpoints)} endpoint(s):")
                
                for endpoint_info in endpoints:
                    endpoint_name = endpoint_info["endpoint"]
                    result = endpoint_info["result"]
                    
                    print(f"\n   📋 Endpoint: {endpoint_name}")
                    print(f"      URL: {self.base_url}/api/os/{endpoint_name}?action=wsmethod:{wsmethod}")
                    print(f"      Status: {result['status_code']}")
                    print(f"      Test Payload: {json.dumps(result['test_payload'], indent=6)}")
                    
                    if result.get("error_code"):
                        print(f"      Error: {result['error_code']} - {result['error_message'][:100]}...")
                    elif result.get("response") and isinstance(result["response"], dict):
                        print(f"      Response: {json.dumps(result['response'], indent=6)}")
        else:
            print("❌ No working wsmethods found")

def main():
    """Main execution function."""
    print("🔍 Working MXAPI Endpoints WSMethod Testing")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Initialize tester
    tester = WorkingMXAPITester()
    
    # Test all working endpoints
    results = tester.test_all_working_endpoints()
    
    # Test successful methods with session auth
    tester.test_successful_wsmethods_with_session(results)
    
    # Generate comprehensive documentation
    tester.generate_wsmethod_documentation(results)
    
    print(f"\n📊 Testing Summary")
    print("=" * 80)
    print("✅ All working MXAPI endpoints tested")
    print("✅ Transfer-related wsmethods tested")
    print("✅ Session authentication tested for successful methods")
    print("✅ Comprehensive documentation generated")
    
    print(f"\n✅ Working MXAPI testing completed")

if __name__ == "__main__":
    main()
