#!/usr/bin/env python3
"""
Direct API Cross-Site Transfer Test
===================================

Test cross-site transfers directly against the Maximo API using the exact
payload structures specified in the test cases, bypassing the Flask application
to test the hypothesis about top-level site context.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import time
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def test_direct_api_cross_site():
    """Test cross-site transfers directly via Maximo API."""
    print("🚀 DIRECT API CROSS-SITE TRANSFER TEST")
    print("=" * 50)
    print("🎯 Objective: Test payload structures directly against Maximo API")
    print("📋 Strategy: Bypass Flask app, test exact payload structures")
    print("")
    
    # Initialize authentication
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Authentication failed. Cannot proceed with direct API tests.")
        return
    
    print("✅ Authenticated with Maximo API")
    
    api_endpoint = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    # Test cases with exact payload structures
    test_cases = [
        {
            "name": "Test Case 1: Destination Site as Top-Level (IKWAJ)",
            "description": "Top-level inventory record in destination site",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test Case 2: Source Site as Top-Level (LCVKWT)",
            "description": "Top-level inventory record in source site",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test Case 3: Minimal Destination Site Context",
            "description": "Minimal payload with destination site context",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test Case 4: Dual Record Approach",
            "description": "Create records in both sites",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "issueunit": "RO"
                        }
                    ]
                },
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO"
                }
            ]
        },
        {
            "name": "Test Case 5: Alternative Field Names",
            "description": "Using alternative field naming convention",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO"
                        }
                    ]
                }
            ]
        }
    ]
    
    successful_tests = []
    
    # Run tests
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 {test_case['name']}")
        print(f"📝 {test_case['description']}")
        print("=" * 80)
        
        try:
            print(f"🔄 Submitting payload to Maximo API...")
            print(f"📋 Payload: {json.dumps(test_case['payload'], indent=2)}")
            
            response = token_manager.session.post(
                api_endpoint,
                json=test_case['payload'],
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "x-method-override": "BULK"
                },
                timeout=(5.0, 30)
            )
            
            print(f"📊 HTTP Status: {response.status_code}")
            
            if response.text:
                try:
                    data = response.json()
                    print(f"📋 Response: {json.dumps(data, indent=2)}")
                    
                    # Check for success
                    if response.status_code == 200:
                        if isinstance(data, list) and len(data) > 0:
                            first_item = data[0]
                            if first_item.get('_responsemeta', {}).get('status') == '204':
                                print("🎉 SUCCESS! Cross-site transfer worked!")
                                successful_tests.append({
                                    'test_case': test_case['name'],
                                    'payload': test_case['payload'],
                                    'response': data
                                })
                                
                                # Save successful payload
                                filename = f"successful_cross_site_direct_api_{i}.json"
                                with open(filename, 'w') as f:
                                    json.dump(test_case['payload'], f, indent=2)
                                print(f"💾 Successful payload saved: {filename}")
                            else:
                                # Check for errors
                                if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                                    error = first_item['_responsedata']['Error']
                                    print(f"❌ Business Logic Error: {error.get('reasonCode')} - {error.get('message')}")
                                else:
                                    print(f"⚠️  Unexpected response structure")
                        else:
                            print(f"⚠️  Empty or unexpected response format")
                    else:
                        print(f"❌ HTTP Error: {response.status_code}")
                        
                except json.JSONDecodeError:
                    print(f"📄 Non-JSON Response: {response.text}")
                    
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Brief pause between tests
        time.sleep(2)
    
    # Summary
    print(f"\n📊 DIRECT API TEST SUMMARY")
    print("=" * 40)
    print(f"🔬 Total tests: {len(test_cases)}")
    print(f"✅ Successful: {len(successful_tests)}")
    print(f"❌ Failed: {len(test_cases) - len(successful_tests)}")
    
    if successful_tests:
        print(f"\n🎉 SUCCESSFUL CROSS-SITE PATTERNS FOUND!")
        print("=" * 50)
        
        for i, success in enumerate(successful_tests, 1):
            print(f"\n✅ Success {i}: {success['test_case']}")
            print(f"📋 Payload: {json.dumps(success['payload'], indent=2)[:300]}...")
        
        # Save all successful patterns
        with open('successful_direct_api_cross_site_patterns.json', 'w') as f:
            json.dump(successful_tests, f, indent=2)
        print(f"\n💾 All successful patterns saved: successful_direct_api_cross_site_patterns.json")
        
        # Generate curl commands for direct API
        print(f"\n🌐 CURL COMMANDS FOR DIRECT API SUCCESS:")
        print("=" * 50)
        
        for i, success in enumerate(successful_tests, 1):
            print(f"\n# Success Pattern {i}: {success['test_case']}")
            curl_payload = json.dumps(success['payload'], separators=(',', ':'))
            print(f"curl -X POST '{api_endpoint}' \\")
            print(f"  -H 'Accept: application/json' \\")
            print(f"  -H 'Content-Type: application/json' \\")
            print(f"  -H 'x-method-override: BULK' \\")
            print(f"  --cookie 'cookies.txt' \\")
            print(f"  -d '{curl_payload}' \\")
            print(f"  -w '\\nHTTP_STATUS:%{{http_code}}\\nTIME:%{{time_total}}\\n' \\")
            print(f"  -s")
    else:
        print(f"\n❌ NO SUCCESSFUL CROSS-SITE PATTERNS FOUND")
        print("=" * 50)
        print("📋 All test cases failed with the same validation error")
        print("🔍 This suggests the validation logic is deeply embedded")
        print("💡 Alternative approaches may be needed:")
        print("   • Use different transfer endpoints")
        print("   • Create inventory records in destination site first")
        print("   • Use Maximo integration services")
        print("   • Implement via Maximo UI automation")

def main():
    """Main function."""
    test_direct_api_cross_site()

if __name__ == "__main__":
    main()
