#!/usr/bin/env python3
"""
Add Destination Context Endpoint to Flask App
==============================================

This script adds a new endpoint to the Flask application that uses
destination site context for cross-site transfers.

Author: Maximo Architect
Date: 2025-07-16
"""

import os

def add_destination_context_endpoint():
    """Add destination context endpoint to app.py"""
    
    # Read the current app.py file
    app_file = 'app.py'
    if not os.path.exists(app_file):
        print("❌ app.py not found")
        return False
    
    with open(app_file, 'r') as f:
        content = f.read()
    
    # Find the location to insert the new endpoint (after the existing transfer endpoint)
    insert_marker = "@app.route('/api/inventory/transfer-current-item', methods=['POST'])"
    insert_position = content.find(insert_marker)
    
    if insert_position == -1:
        print("❌ Could not find insertion point in app.py")
        return False
    
    # Find the end of the existing endpoint function
    lines = content.split('\n')
    insert_line = -1
    
    for i, line in enumerate(lines):
        if insert_marker in line:
            # Find the end of this function (next @app.route or end of file)
            for j in range(i + 1, len(lines)):
                if lines[j].startswith('@app.route') or j == len(lines) - 1:
                    insert_line = j
                    break
            break
    
    if insert_line == -1:
        print("❌ Could not find end of existing endpoint")
        return False
    
    # New endpoint code
    new_endpoint_code = '''
@app.route('/api/inventory/transfer-current-item-destination-context', methods=['POST'])
def submit_transfer_current_item_destination_context():
    """Submit transfer current item using DESTINATION SITE CONTEXT approach"""
    try:
        # Check if user is logged in
        if not hasattr(token_manager, 'username') or not token_manager.username:
            return jsonify({'success': False, 'error': 'Not authenticated'}), 401

        # Get transfer data from request
        transfer_data = request.get_json()
        if not transfer_data:
            return jsonify({'success': False, 'error': 'No transfer data provided'}), 400

        logger.info(f"🔄 DESTINATION CONTEXT TRANSFER: Received request for item {transfer_data.get('itemnum', 'UNKNOWN')}")

        # Initialize the destination context transfer service
        from backend.services.destination_context_transfer_service import DestinationContextTransferService
        transfer_service = DestinationContextTransferService(token_manager)

        # Submit the transfer using destination context
        result = transfer_service.submit_transfer_with_destination_context(transfer_data)

        if result['success']:
            logger.info(f"✅ DESTINATION CONTEXT TRANSFER: Success for item {transfer_data.get('itemnum')}")
        else:
            logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Failed for item {transfer_data.get('itemnum')}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Exception occurred: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
'''
    
    # Insert the new endpoint
    lines.insert(insert_line, new_endpoint_code)
    
    # Write back to file
    with open(app_file, 'w') as f:
        f.write('\n'.join(lines))
    
    print("✅ Added destination context endpoint to app.py")
    return True

def create_destination_context_service():
    """Create the destination context transfer service"""
    
    service_dir = 'backend/services'
    if not os.path.exists(service_dir):
        os.makedirs(service_dir)
    
    service_file = os.path.join(service_dir, 'destination_context_transfer_service.py')
    
    service_code = '''"""
Destination Context Transfer Service for Maximo Integration

This service handles inventory transfers using destination site context
in the top-level record, as specified by the user.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List

class DestinationContextTransferService:
    """Service for handling transfers with destination site context"""
    
    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.logger = logging.getLogger(__name__)
        
    def submit_transfer_with_destination_context(self, transfer_data: Dict) -> Dict:
        """
        Submit transfer using destination site context in top-level record
        
        Args:
            transfer_data (Dict): Transfer information
            
        Returns:
            Dict: Result of the transfer submission
        """
        try:
            # Validate authentication
            if not self.token_manager.is_logged_in():
                return {
                    'success': False,
                    'error': 'Not authenticated with Maximo'
                }
            
            # Build payload with destination site context
            payload = self._build_destination_context_payload(transfer_data)
            
            # Submit to Maximo
            result = self._submit_to_maximo(payload)
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ DESTINATION CONTEXT TRANSFER: Exception: {str(e)}")
            return {
                'success': False,
                'error': f'Transfer submission failed: {str(e)}'
            }
    
    def _build_destination_context_payload(self, transfer_data: Dict) -> List[Dict]:
        """
        Build payload with destination site context in top-level record
        
        This uses the exact structure specified by the user:
        - Top-level siteid: destination site (IKWAJ)
        - Top-level location: destination storeroom (KWAJ-1058)
        - Include toissueunit for unit conversion
        """
        # Extract fields
        itemnum = transfer_data.get('itemnum', '')
        quantity = float(transfer_data.get('quantity', 1.0))
        
        from_siteid = transfer_data.get('from_siteid', '')
        to_siteid = transfer_data.get('to_siteid', '')
        from_storeroom = transfer_data.get('from_storeroom', '')
        to_storeroom = transfer_data.get('to_storeroom', '')
        
        from_issue_unit = transfer_data.get('from_issue_unit', 'RO')
        to_issue_unit = transfer_data.get('to_issue_unit', 'EA')
        
        from_bin = transfer_data.get('from_bin', 'DEFAULT')
        to_bin = transfer_data.get('to_bin', 'DEFAULT')
        from_lot = transfer_data.get('from_lot', 'DEFAULT')
        to_lot = transfer_data.get('to_lot', 'DEFAULT')
        from_condition = transfer_data.get('from_condition', 'A1')
        to_condition = transfer_data.get('to_condition', 'A1')
        
        # Build payload with DESTINATION site context (user's requested structure)
        payload = [
            {
                "_action": "AddChange",
                "itemnum": itemnum,
                "itemsetid": "ITEMSET",
                "siteid": to_siteid,  # DESTINATION site (IKWAJ)
                "location": to_storeroom,  # DESTINATION location (KWAJ-1058)
                "issueunit": from_issue_unit,
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": itemnum,
                        "issuetype": "TRANSFER",
                        "quantity": quantity,
                        "fromsiteid": from_siteid,
                        "tositeid": to_siteid,
                        "fromstoreloc": from_storeroom,
                        "tostoreloc": to_storeroom,
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": from_issue_unit,
                        "frombinnum": from_bin,
                        "tobinnum": to_bin,
                        "fromlotnum": from_lot,
                        "tolotnum": to_lot,
                        "fromconditioncode": from_condition,
                        "toconditioncode": to_condition,
                        "toissueunit": to_issue_unit  # KEY: Include toissueunit for conversion
                    }
                ]
            }
        ]
        
        self.logger.info(f"🔧 DESTINATION CONTEXT: Built payload with destination site context")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Top-level siteid: {to_siteid}")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Top-level location: {to_storeroom}")
        self.logger.info(f"🔧 DESTINATION CONTEXT: Payload: {json.dumps(payload, indent=2)}")
        
        return payload
    
    def _submit_to_maximo(self, payload: List[Dict]) -> Dict:
        """Submit payload to Maximo API"""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
            
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            }
            
            self.logger.info(f"🔄 DESTINATION CONTEXT: Submitting to {api_url}")
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                headers=headers,
                timeout=(5.0, 30)
            )
            
            self.logger.info(f"📊 DESTINATION CONTEXT: Response status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    self.logger.info(f"📋 DESTINATION CONTEXT: Response: {json.dumps(response_data, indent=2)}")
                    
                    # Check for success (204 status in response)
                    if isinstance(response_data, list) and len(response_data) > 0:
                        first_item = response_data[0]
                        if first_item.get('_responsemeta', {}).get('status') == '204':
                            return {
                                'success': True,
                                'message': 'Destination context transfer submitted successfully to Maximo',
                                'response': response_data,
                                'status_code': response.status_code,
                                'timestamp': datetime.now().isoformat()
                            }
                        else:
                            # Check for errors
                            error_info = None
                            if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                                error = first_item['_responsedata']['Error']
                                error_info = f"{error.get('reasonCode')} - {error.get('message')}"
                            
                            return {
                                'success': False,
                                'message': 'Destination context transfer failed',
                                'response': response_data,
                                'status_code': response.status_code,
                                'error': error_info,
                                'timestamp': datetime.now().isoformat()
                            }
                    
                    return {
                        'success': False,
                        'message': 'Unexpected response format',
                        'response': response_data,
                        'status_code': response.status_code,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Invalid JSON response',
                        'response_text': response.text,
                        'status_code': response.status_code
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP error: {response.status_code}',
                    'response_text': response.text,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Request failed: {str(e)}'
            }
'''
    
    with open(service_file, 'w') as f:
        f.write(service_code)
    
    print(f"✅ Created destination context service: {service_file}")
    return True

def main():
    """Main function"""
    print("🔧 SETTING UP DESTINATION CONTEXT ENDPOINT")
    print("=" * 50)
    
    # Create the service
    if create_destination_context_service():
        print("✅ Service created successfully")
    else:
        print("❌ Failed to create service")
        return
    
    # Add the endpoint
    if add_destination_context_endpoint():
        print("✅ Endpoint added successfully")
        print("")
        print("🎯 NEW ENDPOINT AVAILABLE:")
        print("POST /api/inventory/transfer-current-item-destination-context")
        print("")
        print("📋 This endpoint uses destination site context:")
        print("• Top-level siteid: destination site (IKWAJ)")
        print("• Top-level location: destination storeroom (KWAJ-1058)")
        print("• Includes toissueunit for unit conversion")
        print("")
        print("🔄 Restart the Flask application to use the new endpoint")
    else:
        print("❌ Failed to add endpoint")

if __name__ == "__main__":
    main()
