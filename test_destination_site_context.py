#!/usr/bin/env python3
"""
Test Destination Site Context for Cross-Site Transfers
======================================================

Test cross-site transfers using destination site context in the top-level record
with the exact structure specified by the user.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import time
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def test_destination_site_context():
    """Test cross-site transfers with destination site context."""
    print("🚀 TESTING DESTINATION SITE CONTEXT FOR CROSS-SITE TRANSFERS")
    print("=" * 65)
    print("🎯 Objective: Get 204 success using destination site in top-level record")
    print("📋 Strategy: Use IKWAJ as top-level siteid with KWAJ-1058 location")
    print("🔍 Key: Include toissueunit: EA for unit conversion")
    print("")
    
    # Initialize authentication
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Authentication failed. Cannot proceed with direct API tests.")
        return
    
    print("✅ Authenticated with Maximo API")
    
    api_endpoint = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    successful_tests = []
    test_count = 0
    
    # Test cases with destination site context
    test_cases = [
        {
            "name": "Destination Site Context - KWAJ-1058",
            "description": "Top-level IKWAJ site with KWAJ-1058 location",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "CMW-AJ",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "toissueunit": "EA"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Destination Site Context - KWAJ-1115",
            "description": "Top-level IKWAJ site with KWAJ-1115 location",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1115",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "CMW-AJ",
                            "tostoreloc": "KWAJ-1115",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "toissueunit": "EA"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Destination Site Context - Different Source",
            "description": "Use CMW-BU as source with destination context",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "CMW-BU",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "toissueunit": "EA"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Destination Site Context - RIP001 Source",
            "description": "Use RIP001 as source with destination context",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "toissueunit": "EA"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Destination Site Context - Small Quantity",
            "description": "Small quantity with destination context",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 0.1,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "CMW-AJ",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "DEFAULT",
                            "tobinnum": "DEFAULT",
                            "fromlotnum": "DEFAULT",
                            "tolotnum": "DEFAULT",
                            "toissueunit": "EA"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Destination Site Context - No Bins/Lots",
            "description": "Minimal fields with destination context",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "IKWAJ",
                    "location": "KWAJ-1058",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "CMW-AJ",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "toissueunit": "EA"
                        }
                    ]
                }
            ]
        }
    ]
    
    # Run tests
    for i, test_case in enumerate(test_cases, 1):
        test_count += 1
        print(f"\n🧪 TEST {i}: {test_case['name']}")
        print(f"📝 {test_case['description']}")
        print("=" * 80)
        
        try:
            print(f"🔄 Submitting payload to Maximo API...")
            print(f"📋 Payload: {json.dumps(test_case['payload'], indent=2)}")
            
            response = token_manager.session.post(
                api_endpoint,
                json=test_case['payload'],
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "x-method-override": "BULK"
                },
                timeout=(5.0, 30)
            )
            
            print(f"📊 HTTP Status: {response.status_code}")
            
            if response.text:
                try:
                    data = response.json()
                    print(f"📋 Response: {json.dumps(data, indent=2)}")
                    
                    # Check for success
                    if response.status_code == 200:
                        if isinstance(data, list) and len(data) > 0:
                            first_item = data[0]
                            if first_item.get('_responsemeta', {}).get('status') == '204':
                                print("🎉 SUCCESS! Cross-site transfer worked with destination site context!")
                                successful_tests.append({
                                    'test_case': test_case['name'],
                                    'payload': test_case['payload'],
                                    'response': data
                                })
                                
                                # Save successful payload
                                filename = f"successful_destination_context_{i}.json"
                                with open(filename, 'w') as f:
                                    json.dump(test_case['payload'], f, indent=2)
                                print(f"💾 Successful payload saved: {filename}")
                            else:
                                # Check for errors
                                if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                                    error = first_item['_responsedata']['Error']
                                    print(f"❌ Business Logic Error: {error.get('reasonCode')} - {error.get('message')}")
                                else:
                                    print(f"⚠️  Unexpected response structure")
                        else:
                            print(f"⚠️  Empty or unexpected response format")
                    else:
                        print(f"❌ HTTP Error: {response.status_code}")
                        
                except json.JSONDecodeError:
                    print(f"📄 Non-JSON Response: {response.text}")
                    
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Brief pause between tests
        time.sleep(2)
    
    # Summary
    print(f"\n📊 DESTINATION SITE CONTEXT TEST SUMMARY")
    print("=" * 50)
    print(f"🔬 Total tests: {test_count}")
    print(f"✅ Successful: {len(successful_tests)}")
    print(f"❌ Failed: {test_count - len(successful_tests)}")
    
    if successful_tests:
        print(f"\n🎉 SUCCESS! DESTINATION SITE CONTEXT WORKS!")
        print("=" * 50)
        
        for i, success in enumerate(successful_tests, 1):
            print(f"\n✅ Success {i}: {success['test_case']}")
            print(f"📋 Payload: {json.dumps(success['payload'], indent=2)[:300]}...")
        
        # Save all successful patterns
        with open('successful_destination_site_context_patterns.json', 'w') as f:
            json.dump(successful_tests, f, indent=2)
        print(f"\n💾 All successful patterns saved: successful_destination_site_context_patterns.json")
        
        # Generate curl commands for Flask app testing
        print(f"\n🌐 EQUIVALENT FLASK APP TESTS:")
        print("=" * 40)
        
        for i, success in enumerate(successful_tests, 1):
            # Extract key fields for Flask app format
            matrectrans = success['payload'][0]['matrectrans'][0]
            flask_payload = {
                "itemnum": success['payload'][0]['itemnum'],
                "from_siteid": matrectrans['fromsiteid'],
                "to_siteid": matrectrans['tositeid'],
                "from_storeroom": matrectrans['fromstoreloc'],
                "to_storeroom": matrectrans['tostoreloc'],
                "quantity": matrectrans['quantity'],
                "from_issue_unit": matrectrans['issueunit'],
                "to_issue_unit": matrectrans.get('toissueunit', 'EA'),
                "from_bin": matrectrans.get('frombinnum', 'DEFAULT'),
                "to_bin": matrectrans.get('tobinnum', 'DEFAULT'),
                "from_lot": matrectrans.get('fromlotnum', 'DEFAULT'),
                "to_lot": matrectrans.get('tolotnum', 'DEFAULT'),
                "from_condition": "A1",
                "to_condition": "A1"
            }
            
            print(f"\n# Flask Test {i}: {success['test_case']}")
            print(f"curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\")
            print(f"  -H \"Content-Type: application/json\" \\")
            print(f"  -H \"Accept: application/json\" \\")
            print(f"  -d '{json.dumps(flask_payload, separators=(',', ':'))}' \\")
            print(f"  -s")
    else:
        print(f"\n❌ NO SUCCESS WITH DESTINATION SITE CONTEXT")
        print("=" * 50)
        print("📋 All destination site context tests failed")
        print("🔍 The validation logic may still be tied to source site context")
        print("💡 Consider hybrid approaches or Maximo configuration changes")

def main():
    """Main function."""
    test_destination_site_context()

if __name__ == "__main__":
    main()
