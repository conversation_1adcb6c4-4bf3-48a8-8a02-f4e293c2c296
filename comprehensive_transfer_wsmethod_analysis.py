#!/usr/bin/env python3
"""
Comprehensive MXAPIINVENTORY Transfer WSMethod Analysis
======================================================

Final comprehensive analysis of transfer wsmethods with proper API headers,
content types, and payload formats to determine actual API functionality.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

class ComprehensiveTransferWSMethodAnalyzer:
    """Comprehensive analysis of transfer wsmethods."""
    
    def __init__(self, base_url):
        """Initialize the analyzer."""
        self.base_url = base_url
        self.token_manager = None
        self.session = None
        self.api_endpoint = f"{base_url}/api/os/mxapiinventory"
        self.oslc_endpoint = f"{base_url}/oslc/os/mxapiinventory"
        
        # Confirmed working methods from previous investigation
        self.confirmed_methods = [
            'transfercurrentitem',
            'transfercuritem', 
            'issuecurrentitem',
            'receivecurrentitem'
        ]
        
        self.results = {
            'investigation_date': datetime.now().isoformat(),
            'endpoints_tested': [self.api_endpoint, self.oslc_endpoint],
            'authentication': 'OSLC Token Session',
            'method_analysis': [],
            'working_patterns': [],
            'api_documentation': {}
        }
    
    def initialize_authentication(self):
        """Initialize authentication."""
        print("🔐 Initializing OSLC Token Session Authentication")
        print("=" * 60)
        
        try:
            self.token_manager = MaximoTokenManager(self.base_url)
            
            if self.token_manager.is_logged_in():
                self.session = self.token_manager.session
                print("✅ OSLC token session authenticated successfully")
                return True
            else:
                print("❌ OSLC token session not available")
                return False
                
        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            return False
    
    def test_method_comprehensive(self, method_name):
        """Comprehensive test of a method with multiple approaches."""
        print(f"\n🔬 Comprehensive Analysis: {method_name}")
        print("=" * 60)
        
        analysis = {
            'method': method_name,
            'timestamp': datetime.now().isoformat(),
            'test_results': [],
            'working_patterns': [],
            'documentation': {}
        }
        
        # Test configurations
        test_configs = [
            {
                'name': 'API Endpoint - JSON',
                'url': f"{self.api_endpoint}/{method_name}",
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                'payload_type': 'json'
            },
            {
                'name': 'OSLC Endpoint - JSON',
                'url': f"{self.oslc_endpoint}/{method_name}",
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                'payload_type': 'json'
            },
            {
                'name': 'API Endpoint - Form Data',
                'url': f"{self.api_endpoint}/{method_name}",
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                'payload_type': 'form'
            },
            {
                'name': 'OSLC Endpoint - Form Data',
                'url': f"{self.oslc_endpoint}/{method_name}",
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                'payload_type': 'form'
            },
            {
                'name': 'API Query Parameter',
                'url': f"{self.api_endpoint}?wsmethod={method_name}",
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                'payload_type': 'json'
            },
            {
                'name': 'OSLC Query Parameter',
                'url': f"{self.oslc_endpoint}?wsmethod={method_name}",
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                'payload_type': 'json'
            }
        ]
        
        # Test payloads
        json_payload = {
            "itemnum": "5975-60-V00-0529",
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromlocation": "RIP001",
            "tolocation": "KWAJ-1058",
            "quantity": 1.0
        }
        
        form_payload = {
            "itemnum": "5975-60-V00-0529",
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "quantity": "1.0"
        }
        
        for config in test_configs:
            try:
                print(f"\n  🧪 Testing: {config['name']}")
                print(f"     URL: {config['url']}")
                
                test_result = {
                    'config_name': config['name'],
                    'url': config['url'],
                    'headers': config['headers'],
                    'payload_type': config['payload_type'],
                    'http_status': None,
                    'response_type': None,
                    'response_data': None,
                    'response_text': None,
                    'error': None,
                    'is_api_response': False
                }
                
                # Prepare payload based on type
                if config['payload_type'] == 'json':
                    response = self.session.post(
                        config['url'],
                        json=json_payload,
                        headers=config['headers'],
                        timeout=(3.05, 30)
                    )
                else:  # form
                    response = self.session.post(
                        config['url'],
                        data=form_payload,
                        headers=config['headers'],
                        timeout=(3.05, 30)
                    )
                
                test_result['http_status'] = response.status_code
                test_result['response_text'] = response.text[:300] if response.text else None
                
                print(f"     Status: {response.status_code}")
                print(f"     Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
                print(f"     Response Length: {len(response.text) if response.text else 0} chars")
                
                # Analyze response
                if response.status_code in [200, 201, 204]:
                    content_type = response.headers.get('Content-Type', '').lower()
                    
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            test_result['response_data'] = data
                            test_result['response_type'] = 'json'
                            test_result['is_api_response'] = True
                            print(f"     ✅ JSON API Response!")
                            print(f"     Data: {json.dumps(data, indent=6)[:200]}...")
                            
                            analysis['working_patterns'].append(test_result.copy())
                            
                        except json.JSONDecodeError:
                            test_result['response_type'] = 'invalid_json'
                            print(f"     ⚠️  Invalid JSON response")
                            
                    elif 'text/html' in content_type:
                        test_result['response_type'] = 'html'
                        print(f"     ⚠️  HTML response (likely web UI)")
                        
                    elif 'text/plain' in content_type:
                        test_result['response_type'] = 'text'
                        test_result['is_api_response'] = True
                        print(f"     ✅ Text API Response!")
                        print(f"     Text: {response.text[:200]}...")
                        
                        analysis['working_patterns'].append(test_result.copy())
                        
                    else:
                        test_result['response_type'] = 'unknown'
                        print(f"     ⚠️  Unknown content type")
                        
                elif response.status_code == 400:
                    print(f"     ⚠️  Bad Request - Method exists but payload invalid")
                    try:
                        error_data = response.json()
                        test_result['response_data'] = error_data
                        test_result['response_type'] = 'error_json'
                        test_result['is_api_response'] = True
                        print(f"     Error: {json.dumps(error_data, indent=6)[:200]}...")
                        
                        analysis['working_patterns'].append(test_result.copy())
                        
                    except json.JSONDecodeError:
                        test_result['response_type'] = 'error_non_json'
                        print(f"     Error text: {response.text[:200]}...")
                        
                elif response.status_code == 403:
                    print(f"     ❌ Forbidden - No access")
                    test_result['response_type'] = 'forbidden'
                    
                elif response.status_code == 404:
                    print(f"     ❌ Not Found")
                    test_result['response_type'] = 'not_found'
                    
                elif response.status_code == 405:
                    print(f"     ❌ Method Not Allowed")
                    test_result['response_type'] = 'method_not_allowed'
                    
                else:
                    print(f"     ⚠️  Unexpected status: {response.status_code}")
                    test_result['response_type'] = 'unexpected'
                
                analysis['test_results'].append(test_result)
                
            except requests.exceptions.Timeout:
                print(f"     ⏱️  Timeout")
                test_result['error'] = 'timeout'
                analysis['test_results'].append(test_result)
                
            except Exception as e:
                print(f"     ❌ Error: {str(e)}")
                test_result['error'] = str(e)
                analysis['test_results'].append(test_result)
        
        # Generate documentation for this method
        if analysis['working_patterns']:
            analysis['documentation'] = self.generate_method_documentation(method_name, analysis['working_patterns'])
        
        return analysis
    
    def generate_method_documentation(self, method_name, working_patterns):
        """Generate documentation for a working method."""
        doc = {
            'method_name': method_name,
            'status': 'CONFIRMED WORKING',
            'working_endpoints': [],
            'payload_examples': [],
            'response_examples': [],
            'usage_patterns': []
        }
        
        for pattern in working_patterns:
            if pattern['is_api_response']:
                endpoint_info = {
                    'url_pattern': pattern['url'],
                    'http_method': 'POST',
                    'headers': pattern['headers'],
                    'payload_type': pattern['payload_type'],
                    'response_type': pattern['response_type'],
                    'http_status': pattern['http_status']
                }
                doc['working_endpoints'].append(endpoint_info)
                
                if pattern.get('response_data'):
                    doc['response_examples'].append(pattern['response_data'])
                elif pattern.get('response_text'):
                    doc['response_examples'].append(pattern['response_text'])
        
        return doc
    
    def analyze_all_methods(self):
        """Analyze all confirmed methods."""
        print("\n🔬 COMPREHENSIVE TRANSFER WSMETHOD ANALYSIS")
        print("=" * 70)
        
        for i, method in enumerate(self.confirmed_methods, 1):
            print(f"\n[{i}/{len(self.confirmed_methods)}] Analyzing: {method}")
            
            analysis = self.test_method_comprehensive(method)
            self.results['method_analysis'].append(analysis)
            
            if analysis['working_patterns']:
                self.results['working_patterns'].extend(analysis['working_patterns'])
                self.results['api_documentation'][method] = analysis['documentation']
            
            # Brief pause between tests
            time.sleep(1)
    
    def generate_final_report(self):
        """Generate comprehensive final report."""
        print("\n📋 COMPREHENSIVE ANALYSIS REPORT")
        print("=" * 70)
        
        print(f"🕐 Investigation Date: {self.results['investigation_date']}")
        print(f"🔗 Endpoints Tested: {', '.join(self.results['endpoints_tested'])}")
        print(f"🔐 Authentication: {self.results['authentication']}")
        
        working_methods = len(self.results['api_documentation'])
        total_patterns = len(self.results['working_patterns'])
        
        print(f"\n📊 FINAL SUMMARY:")
        print(f"  ✅ Working API Methods: {working_methods}")
        print(f"  🔗 Working Patterns: {total_patterns}")
        print(f"  📝 Methods Analyzed: {len(self.confirmed_methods)}")
        
        if self.results['api_documentation']:
            print(f"\n🎯 CONFIRMED TRANSFER WSMETHODS:")
            
            for method_name, doc in self.results['api_documentation'].items():
                print(f"\n  ✅ {method_name.upper()}")
                print(f"     Status: {doc['status']}")
                print(f"     Working Endpoints: {len(doc['working_endpoints'])}")
                
                for endpoint in doc['working_endpoints']:
                    print(f"       • {endpoint['url_pattern']}")
                    print(f"         Method: {endpoint['http_method']}")
                    print(f"         Content-Type: {endpoint['headers']['Content-Type']}")
                    print(f"         Response: {endpoint['response_type']} ({endpoint['http_status']})")
                
                if doc['response_examples']:
                    print(f"     Response Example: {str(doc['response_examples'][0])[:100]}...")
        
        # Save comprehensive report
        report_filename = f"comprehensive_transfer_wsmethod_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Comprehensive report saved: {report_filename}")
        
        return self.results

def main():
    """Main analysis function."""
    print("🚀 COMPREHENSIVE MXAPIINVENTORY TRANSFER WSMETHOD ANALYSIS")
    print("=" * 80)
    print("Objective: Final comprehensive analysis of transfer wsmethods")
    print("Authentication: OSLC Token Session (MaximoTokenManager)")
    print("Scope: Confirmed transfer methods with multiple endpoint patterns")
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    analyzer = ComprehensiveTransferWSMethodAnalyzer(base_url)
    
    # Initialize authentication
    if not analyzer.initialize_authentication():
        print("❌ Authentication failed. Cannot proceed.")
        return
    
    # Analyze all methods
    analyzer.analyze_all_methods()
    
    # Generate final report
    results = analyzer.generate_final_report()
    
    print("\n🎯 COMPREHENSIVE ANALYSIS COMPLETE")
    print("=" * 80)
    
    if results['api_documentation']:
        print("✅ Transfer wsmethods confirmed! Check the comprehensive report for full API documentation.")
    else:
        print("❌ No API-compatible transfer wsmethods found.")

if __name__ == "__main__":
    main()
