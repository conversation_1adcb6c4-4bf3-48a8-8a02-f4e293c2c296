[{"test_case": {"name": "Test 1: Minimal transfer", "description": "Basic transfer without bins or lots", "payload": {"itemnum": "5975-60-V00-0529", "from_siteid": "LCVKWT", "to_siteid": "IKWAJ", "from_storeroom": "RIP001", "to_storeroom": "KWAJ-1058", "quantity": 1.0, "from_issue_unit": "RO"}}, "response": {"message": "Inventory transfer submitted successfully to Maxim<PERSON>", "response": [{"_responsedata": {"Error": {"correlationid": "1", "errattrvalue": null, "errorattrname": "tostoreloc", "errorobjpath": "inventory/matrectrans", "extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA2694E"}}, "message": "BMXAA2694E - Location KWAJ-1058 does not exist in site LCVKWT.", "reasonCode": "BMXAA2694E", "statusCode": "400"}}, "_responsemeta": {"status": "400"}}], "status_code": 200, "success": true, "timestamp": "2025-07-16T11:16:01.990612"}}, {"test_case": {"name": "Test 2: With bins", "description": "Transfer with bin numbers", "payload": {"itemnum": "5975-60-V00-0529", "from_siteid": "LCVKWT", "to_siteid": "IKWAJ", "from_storeroom": "RIP001", "to_storeroom": "KWAJ-1058", "quantity": 1.0, "from_issue_unit": "RO", "from_bin": "28-800-0004", "to_bin": "1058-TEMP"}}, "response": {"message": "Inventory transfer submitted successfully to Maxim<PERSON>", "response": [{"_responsedata": {"Error": {"correlationid": "1", "errattrvalue": null, "errorattrname": "tostoreloc", "errorobjpath": "inventory/matrectrans", "extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA2694E"}}, "message": "BMXAA2694E - Location KWAJ-1058 does not exist in site LCVKWT.", "reasonCode": "BMXAA2694E", "statusCode": "400"}}, "_responsemeta": {"status": "400"}}], "status_code": 200, "success": true, "timestamp": "2025-07-16T11:16:04.275844"}}, {"test_case": {"name": "Test 3: With lots", "description": "Transfer with lot numbers", "payload": {"itemnum": "5975-60-V00-0529", "from_siteid": "LCVKWT", "to_siteid": "IKWAJ", "from_storeroom": "RIP001", "to_storeroom": "KWAJ-1058", "quantity": 1.0, "from_issue_unit": "RO", "from_lot": "TEST", "to_lot": "TEST"}}, "response": {"message": "Inventory transfer submitted successfully to Maxim<PERSON>", "response": [{"_responsedata": {"Error": {"correlationid": "1", "errattrvalue": null, "errorattrname": "tostoreloc", "errorobjpath": "inventory/matrectrans", "extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA2694E"}}, "message": "BMXAA2694E - Location KWAJ-1058 does not exist in site LCVKWT.", "reasonCode": "BMXAA2694E", "statusCode": "400"}}, "_responsemeta": {"status": "400"}}], "status_code": 200, "success": true, "timestamp": "2025-07-16T11:16:06.614566"}}, {"test_case": {"name": "Test 4: Complete transfer", "description": "Transfer with all optional fields", "payload": {"itemnum": "5975-60-V00-0529", "from_siteid": "LCVKWT", "to_siteid": "IKWAJ", "from_storeroom": "RIP001", "to_storeroom": "KWAJ-1058", "quantity": 1.0, "from_issue_unit": "RO", "from_bin": "28-800-0004", "to_bin": "1058-TEMP", "from_lot": "TEST", "to_lot": "TEST", "from_condition": "A1", "to_condition": "A1"}}, "response": {"message": "Inventory transfer submitted successfully to Maxim<PERSON>", "response": [{"_responsedata": {"Error": {"correlationid": "1", "errattrvalue": null, "errorattrname": "tostoreloc", "errorobjpath": "inventory/matrectrans", "extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA2694E"}}, "message": "BMXAA2694E - Location KWAJ-1058 does not exist in site LCVKWT.", "reasonCode": "BMXAA2694E", "statusCode": "400"}}, "_responsemeta": {"status": "400"}}], "status_code": 200, "success": true, "timestamp": "2025-07-16T11:16:08.915442"}}, {"test_case": {"name": "Test 5: Different bin combination", "description": "Transfer with alternative bin numbers", "payload": {"itemnum": "5975-60-V00-0529", "from_siteid": "LCVKWT", "to_siteid": "IKWAJ", "from_storeroom": "RIP001", "to_storeroom": "KWAJ-1058", "quantity": 1.0, "from_issue_unit": "RO", "from_bin": "28-800-0004", "to_bin": "58-A-A01-1"}}, "response": {"message": "Inventory transfer submitted successfully to Maxim<PERSON>", "response": [{"_responsedata": {"Error": {"correlationid": "1", "errattrvalue": null, "errorattrname": "tostoreloc", "errorobjpath": "inventory/matrectrans", "extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA2694E"}}, "message": "BMXAA2694E - Location KWAJ-1058 does not exist in site LCVKWT.", "reasonCode": "BMXAA2694E", "statusCode": "400"}}, "_responsemeta": {"status": "400"}}], "status_code": 200, "success": true, "timestamp": "2025-07-16T11:16:11.204758"}}]