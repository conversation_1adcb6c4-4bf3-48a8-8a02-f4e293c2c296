#!/usr/bin/env python3
"""
Test script to upload a small test file and see the upload payload in the logs
"""

import requests
import os

def test_upload():
    """Test uploading a small file to see the payload logging"""
    
    # Create a small test file
    test_content = b"This is a test file for attachment upload debugging.\nIt contains some sample text to test the upload functionality."
    
    # Prepare the upload
    files = {
        'file': ('test_upload.txt', test_content, 'text/plain')
    }
    
    data = {
        'description': 'Test upload for debugging',
        'doctype': 'Attachments'
    }
    
    # Upload to work order 2021-1744762
    url = 'http://127.0.0.1:5010/api/workorder/2021-1744762/attachments'
    
    print(f"Uploading test file to: {url}")
    print(f"File size: {len(test_content)} bytes")
    print(f"Description: {data['description']}")
    print(f"Doc type: {data['doctype']}")
    
    try:
        response = requests.post(url, files=files, data=data)
        print(f"Response status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Upload successful!")
        else:
            print("❌ Upload failed!")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_upload()
