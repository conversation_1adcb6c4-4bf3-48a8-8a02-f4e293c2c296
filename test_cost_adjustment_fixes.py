#!/usr/bin/env python3
"""
Test script to verify the cost adjustment fixes.
This script tests both the data display and API submission issues.
"""
import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_ITEM = "5975-60-V00-0529"
TEST_SITE = "LCVKWT"

def test_inventory_data_structure():
    """Test that inventory data contains the expected cost fields"""
    print("🔧 Testing Inventory Data Structure")
    print("=" * 50)
    
    try:
        # Get inventory data from the search API
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item = data['items'][0]
                
                print(f"✅ Found inventory item: {item.get('itemnum')}")
                
                # Check for cost fields that should be available for the UI
                cost_fields = {
                    'avgcost': item.get('avgcost'),
                    'stdcost': item.get('stdcost'),
                    'lastcost': item.get('lastcost'),
                    'conditioncode': item.get('conditioncode')
                }
                
                print(f"\n🔍 Cost fields in item data:")
                all_fields_present = True
                for field, value in cost_fields.items():
                    status = "✅" if value is not None else "❌"
                    print(f"  {status} {field}: {value}")
                    if value is None and field in ['avgcost', 'stdcost']:
                        all_fields_present = False
                
                # Check for the problematic invcost array (should NOT be present in flattened data)
                invcost_array = item.get('invcost')
                if invcost_array is None:
                    print(f"✅ invcost array correctly flattened (not present as nested array)")
                else:
                    print(f"⚠️  invcost array still present: {invcost_array}")
                
                # Test data structure for UI compatibility
                print(f"\n🔍 UI Compatibility Test:")
                ui_compatible = True
                
                # Test that we can extract cost data the way the UI expects it
                try:
                    current_avgcost = item.get('avgcost')
                    current_stdcost = item.get('stdcost')
                    condition_code = item.get('conditioncode', 'A1')
                    
                    if current_avgcost is not None and current_stdcost is not None:
                        print(f"✅ Cost data extractable for UI:")
                        print(f"   - avgcost: ${current_avgcost}")
                        print(f"   - stdcost: ${current_stdcost}")
                        print(f"   - conditioncode: {condition_code}")
                    else:
                        print(f"❌ Cost data not properly extractable for UI")
                        ui_compatible = False
                        
                except Exception as e:
                    print(f"❌ Error extracting cost data: {e}")
                    ui_compatible = False
                
                return all_fields_present and ui_compatible
            else:
                print(f"❌ No inventory items found")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_ui_payload_construction():
    """Test that the UI would construct the correct payload"""
    print("\n🔧 Testing UI Payload Construction")
    print("=" * 50)
    
    try:
        # Get inventory data (simulating what the UI gets)
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item_data = data['items'][0]
                
                # Simulate the UI's payload construction logic (after our fixes)
                print(f"🔍 Simulating UI payload construction...")
                
                # Extract cost data the way the fixed UI does it
                current_avgcost = item_data.get('avgcost')
                current_stdcost = item_data.get('stdcost')
                condition_code = item_data.get('conditioncode', 'A1')
                
                print(f"   - Extracted avgcost: {current_avgcost}")
                print(f"   - Extracted stdcost: {current_stdcost}")
                print(f"   - Extracted conditioncode: {condition_code}")
                
                # Construct avgcost adjustment payload
                new_avgcost = 999.99
                avgcost_payload = [
                    {
                        "_action": "AddChange",
                        "itemnum": item_data.get('itemnum'),
                        "itemsetid": item_data.get('itemsetid', 'ITEMSET'),
                        "siteid": item_data.get('siteid'),
                        "location": item_data.get('location'),
                        "invcost": [
                            {
                                "avgcost": str(new_avgcost),
                                "conditioncode": condition_code
                            }
                        ]
                    }
                ]
                
                # Construct stdcost adjustment payload
                new_stdcost = 888.88
                stdcost_payload = [
                    {
                        "_action": "AddChange",
                        "itemnum": item_data.get('itemnum'),
                        "itemsetid": item_data.get('itemsetid', 'ITEMSET'),
                        "siteid": item_data.get('siteid'),
                        "location": item_data.get('location'),
                        "invcost": [
                            {
                                "stdcost": new_stdcost,
                                "conditioncode": condition_code
                            }
                        ]
                    }
                ]
                
                print(f"\n✅ Successfully constructed payloads:")
                print(f"📋 AvgCost Payload:")
                print(json.dumps(avgcost_payload, indent=2))
                print(f"\n📋 StdCost Payload:")
                print(json.dumps(stdcost_payload, indent=2))
                
                # Validate payload structure
                required_fields = ['_action', 'itemnum', 'itemsetid', 'siteid', 'location', 'invcost']
                payload_valid = True
                
                for payload, name in [(avgcost_payload, 'avgcost'), (stdcost_payload, 'stdcost')]:
                    print(f"\n🔍 Validating {name} payload:")
                    record = payload[0]
                    for field in required_fields:
                        if field in record and record[field]:
                            print(f"   ✅ {field}: {record[field]}")
                        else:
                            print(f"   ❌ {field}: MISSING or EMPTY")
                            payload_valid = False
                    
                    # Check invcost structure
                    if 'invcost' in record and len(record['invcost']) > 0:
                        cost_record = record['invcost'][0]
                        cost_field = 'avgcost' if name == 'avgcost' else 'stdcost'
                        if cost_field in cost_record:
                            print(f"   ✅ invcost.{cost_field}: {cost_record[cost_field]}")
                        else:
                            print(f"   ❌ invcost.{cost_field}: MISSING")
                            payload_valid = False
                
                return payload_valid
            else:
                print(f"❌ No inventory items found")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_actual_api_submission():
    """Test actual API submission with UI-constructed payload"""
    print("\n🔧 Testing Actual API Submission")
    print("=" * 50)
    
    try:
        # Get fresh inventory data
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item_data = data['items'][0]
                
                # Test avgcost adjustment
                print(f"🔍 Testing avgcost adjustment...")
                
                new_avgcost = 777.77
                avgcost_payload = [
                    {
                        "_action": "AddChange",
                        "itemnum": item_data.get('itemnum'),
                        "itemsetid": item_data.get('itemsetid', 'ITEMSET'),
                        "siteid": item_data.get('siteid'),
                        "location": item_data.get('location'),
                        "invcost": [
                            {
                                "avgcost": str(new_avgcost),
                                "conditioncode": item_data.get('conditioncode', 'A1')
                            }
                        ]
                    }
                ]
                
                avgcost_response = requests.post(
                    f"{BASE_URL}/api/inventory/avgcost-adjustment",
                    json=avgcost_payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if avgcost_response.status_code == 200:
                    result = avgcost_response.json()
                    if result.get('success'):
                        print(f"✅ AvgCost adjustment successful: ${result.get('new_avgcost')}")
                        avgcost_success = True
                    else:
                        print(f"❌ AvgCost adjustment failed: {result.get('error')}")
                        avgcost_success = False
                else:
                    print(f"❌ AvgCost HTTP Error: {avgcost_response.status_code}")
                    avgcost_success = False
                
                # Test stdcost adjustment
                print(f"\n🔍 Testing stdcost adjustment...")
                
                new_stdcost = 666.66
                stdcost_payload = [
                    {
                        "_action": "AddChange",
                        "itemnum": item_data.get('itemnum'),
                        "itemsetid": item_data.get('itemsetid', 'ITEMSET'),
                        "siteid": item_data.get('siteid'),
                        "location": item_data.get('location'),
                        "invcost": [
                            {
                                "stdcost": new_stdcost,
                                "conditioncode": item_data.get('conditioncode', 'A1')
                            }
                        ]
                    }
                ]
                
                stdcost_response = requests.post(
                    f"{BASE_URL}/api/inventory/stdcost-adjustment",
                    json=stdcost_payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if stdcost_response.status_code == 200:
                    result = stdcost_response.json()
                    if result.get('success'):
                        print(f"✅ StdCost adjustment successful: ${result.get('new_stdcost')}")
                        stdcost_success = True
                    else:
                        print(f"❌ StdCost adjustment failed: {result.get('error')}")
                        stdcost_success = False
                else:
                    print(f"❌ StdCost HTTP Error: {stdcost_response.status_code}")
                    stdcost_success = False
                
                return avgcost_success and stdcost_success
            else:
                print(f"❌ No inventory items found")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 COST ADJUSTMENT FIXES VERIFICATION TEST")
    print("=" * 60)
    print(f"🎯 Target Item: {TEST_ITEM}")
    print(f"🏢 Target Site: {TEST_SITE}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    results = []
    
    # Test 1: Data structure
    results.append(("Data Structure & UI Compatibility", test_inventory_data_structure()))
    
    # Test 2: Payload construction
    results.append(("UI Payload Construction", test_ui_payload_construction()))
    
    # Test 3: Actual API submission
    results.append(("Actual API Submission", test_actual_api_submission()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Both critical issues have been resolved:")
        print("   ✅ Issue 1: Data display problem - FIXED")
        print("   ✅ Issue 2: False success response - FIXED")
        return True
    else:
        print("❌ Some tests failed. Issues may still exist.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
