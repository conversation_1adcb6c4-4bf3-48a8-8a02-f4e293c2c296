#!/usr/bin/env python3
"""
Test MXAPIINVENTORY Nested Object WSMethods

This script tests the actual nested objects within MXAPIINVENTORY to discover
their available wsmethods and proper usage patterns.

Key nested objects:
- spi:transfercuritem (for transfer operations)
- spi:invbalances (for balance adjustments)
- spi:invcost (for cost operations)
- spi:itemcondition (for condition operations)

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

def get_inventory_with_nested_objects():
    """Get an inventory record with all nested objects."""
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    try:
        response = requests.get(
            endpoint_url,
            params={
                "oslc.select": "*",
                "oslc.where": 'itemnum="5975-60-V00-0001" and siteid="LCVKWT"',
                "oslc.pageSize": "1",
                "lean": "0"
            },
            headers=headers,
            timeout=(3.05, 15)
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'rdfs:member' in data and data['rdfs:member']:
                return data['rdfs:member'][0]
                
    except Exception as e:
        print(f"Error getting inventory record: {str(e)}")
        
    return None

def test_transfercuritem_wsmethods(inventory_record):
    """Test wsmethods on the transfercuritem nested object."""
    print("🔍 Testing TRANSFERCURITEM WSMethods")
    print("=" * 60)
    
    if 'spi:transfercuritem' not in inventory_record:
        print("❌ No transfercuritem object found")
        return
        
    transfercuritem_array = inventory_record['spi:transfercuritem']
    if not transfercuritem_array:
        print("❌ Empty transfercuritem array")
        return
        
    # Test on the collection URL (for creating new transfers)
    base_inventory_url = inventory_record['rdf:about']
    transfercuritem_collection_url = f"{base_inventory_url}/transfercuritem"
    
    print(f"📋 Testing collection URL: {transfercuritem_collection_url}")
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test common transfer-related wsmethods
    transfer_methods = [
        "create",
        "transfercurrentitem", 
        "execute",
        "submit",
        "complete"
    ]
    
    for method in transfer_methods:
        test_url = f"{transfercuritem_collection_url}?action=wsmethod:{method}"
        
        print(f"  🧪 Testing {method}...")
        
        # Create realistic transfer payload
        transfer_payload = {
            "fromstoreloc": "LCVKWT-STORE",
            "tositeid": "LCVKWT",
            "quantity": 1.0,
            "unitcost": 10.0
        }
        
        try:
            response = requests.post(
                test_url,
                json=transfer_payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"    Status: {response.status_code}")
            
            if response.content:
                try:
                    data = response.json()
                    if 'oslc:Error' in data:
                        error_msg = data['oslc:Error'].get('oslc:message', '')
                        if 'not found' in error_msg.lower():
                            print(f"    ❌ Method not found")
                        else:
                            print(f"    ✅ Method exists - Error: {error_msg[:100]}...")
                    else:
                        print(f"    ✅ Success response: {str(data)[:100]}...")
                except:
                    print(f"    📄 Non-JSON response: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")

def test_invbalances_wsmethods(inventory_record):
    """Test wsmethods on the invbalances nested object."""
    print("\n🔍 Testing INVBALANCES WSMethods")
    print("=" * 60)
    
    if 'spi:invbalances' not in inventory_record:
        print("❌ No invbalances object found")
        return
        
    invbalances_array = inventory_record['spi:invbalances']
    if not invbalances_array:
        print("❌ Empty invbalances array")
        return
        
    # Test on the collection URL and specific balance records
    base_inventory_url = inventory_record['rdf:about']
    invbalances_collection_url = f"{base_inventory_url}/invbalances"
    
    print(f"📋 Testing collection URL: {invbalances_collection_url}")
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test common balance-related wsmethods
    balance_methods = [
        "create",
        "addchange",
        "adjustcurrentbalance",
        "adjustphysicalcount",
        "update",
        "reconcile"
    ]
    
    for method in balance_methods:
        test_url = f"{invbalances_collection_url}?action=wsmethod:{method}"
        
        print(f"  🧪 Testing {method}...")
        
        # Create realistic balance adjustment payload
        balance_payload = {
            "curbal": 100.0,
            "physcnt": 100.0,
            "conditioncode": "GOOD"
        }
        
        try:
            response = requests.post(
                test_url,
                json=balance_payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"    Status: {response.status_code}")
            
            if response.content:
                try:
                    data = response.json()
                    if 'oslc:Error' in data:
                        error_msg = data['oslc:Error'].get('oslc:message', '')
                        if 'not found' in error_msg.lower():
                            print(f"    ❌ Method not found")
                        else:
                            print(f"    ✅ Method exists - Error: {error_msg[:100]}...")
                    else:
                        print(f"    ✅ Success response: {str(data)[:100]}...")
                except:
                    print(f"    📄 Non-JSON response: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            
    # Test on specific balance record
    if invbalances_array:
        first_balance = invbalances_array[0]
        if 'rdf:about' in first_balance:
            balance_url = first_balance['rdf:about']
            print(f"\n📋 Testing specific balance URL: {balance_url}")
            
            for method in ['addchange', 'update']:
                test_url = f"{balance_url}?action=wsmethod:{method}"
                
                print(f"  🧪 Testing {method} on specific balance...")
                
                try:
                    response = requests.post(
                        test_url,
                        json={"curbal": 95.0},
                        headers=headers,
                        timeout=(3.05, 15)
                    )
                    
                    print(f"    Status: {response.status_code}")
                    
                    if response.content:
                        try:
                            data = response.json()
                            if 'oslc:Error' in data:
                                error_msg = data['oslc:Error'].get('oslc:message', '')
                                if 'not found' not in error_msg.lower():
                                    print(f"    ✅ Method exists - Error: {error_msg[:100]}...")
                                else:
                                    print(f"    ❌ Method not found")
                            else:
                                print(f"    ✅ Success response")
                        except:
                            print(f"    📄 Non-JSON response")
                            
                except Exception as e:
                    print(f"    ❌ Error: {str(e)}")

def test_standard_rest_operations(inventory_record):
    """Test standard REST operations on nested objects."""
    print("\n🔍 Testing Standard REST Operations on Nested Objects")
    print("=" * 60)
    
    base_inventory_url = inventory_record['rdf:about']
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test 1: POST to transfercuritem collection (create transfer)
    print("📋 Test 1: POST to transfercuritem collection")
    transfercuritem_url = f"{base_inventory_url}/transfercuritem"
    
    transfer_payload = {
        "fromstoreloc": "LCVKWT-STORE",
        "tositeid": "LCVKWT",
        "quantity": 1.0
    }
    
    try:
        response = requests.post(
            transfercuritem_url,
            json=transfer_payload,
            headers=headers,
            timeout=(3.05, 15)
        )
        
        print(f"  URL: {transfercuritem_url}")
        print(f"  Payload: {json.dumps(transfer_payload, indent=2)}")
        print(f"  Status: {response.status_code}")
        
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {json.dumps(data, indent=2)}")
            except:
                print(f"  Response (text): {response.text[:200]}...")
                
    except Exception as e:
        print(f"  Error: {str(e)}")
        
    # Test 2: POST to invbalances collection (create balance adjustment)
    print("\n📋 Test 2: POST to invbalances collection")
    invbalances_url = f"{base_inventory_url}/invbalances"
    
    balance_payload = {
        "curbal": 100.0,
        "conditioncode": "GOOD"
    }
    
    try:
        response = requests.post(
            invbalances_url,
            json=balance_payload,
            headers=headers,
            timeout=(3.05, 15)
        )
        
        print(f"  URL: {invbalances_url}")
        print(f"  Payload: {json.dumps(balance_payload, indent=2)}")
        print(f"  Status: {response.status_code}")
        
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {json.dumps(data, indent=2)}")
            except:
                print(f"  Response (text): {response.text[:200]}...")
                
    except Exception as e:
        print(f"  Error: {str(e)}")

def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Nested Object WSMethods Testing")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Get inventory record with nested objects
    print("📋 Getting inventory record with nested objects...")
    inventory_record = get_inventory_with_nested_objects()
    
    if not inventory_record:
        print("❌ Failed to get inventory record")
        return False
        
    print("✅ Got inventory record")
    print(f"   Inventory ID: {inventory_record.get('spi:inventoryid')}")
    print(f"   Item: {inventory_record.get('spi:itemnum')}")
    print(f"   Site: {inventory_record.get('spi:siteid')}")
    print(f"   Location: {inventory_record.get('spi:location')}")
    
    # Test nested object wsmethods
    test_transfercuritem_wsmethods(inventory_record)
    test_invbalances_wsmethods(inventory_record)
    
    # Test standard REST operations
    test_standard_rest_operations(inventory_record)
    
    print("\n📊 Summary")
    print("=" * 80)
    print("✅ Nested object wsmethods tested")
    print("✅ Standard REST operations tested")
    print("\n💡 Key Findings:")
    print("   • MXAPIINVENTORY uses nested objects for inventory operations")
    print("   • transfercuritem handles transfer operations")
    print("   • invbalances handles balance adjustments")
    print("   • Standard REST POST operations may be the primary interface")
    
    print("\n✅ Testing completed")

if __name__ == "__main__":
    main()
