#!/usr/bin/env python3
"""
Investigate MATRECTRANS Integration with MXAPIINVENTORY transfercuritem

This script investigates the relationship between MXAPIINVENTORY transfer data
and MATRECTRANS records, tests MATRECTRANS capabilities, and searches for
itemavailability wsmethods across Maximo endpoints.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

class MatrectransIntegrationInvestigator:
    """Investigates MATRECTRANS integration and wsmethod capabilities."""
    
    def __init__(self):
        """Initialize the investigator."""
        self.base_url = BASE_URL
        self.api_key = API_KEY
        self.session = None
        
    def initialize_session_auth(self):
        """Initialize session authentication if available."""
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
            from backend.auth.token_manager import MaximoTokenManager
            
            token_manager = MaximoTokenManager(self.base_url)
            if token_manager.is_logged_in():
                self.session = token_manager.session
                print("✅ Session authentication available")
                return True
            else:
                print("❌ Session authentication not available")
                return False
        except ImportError:
            print("❌ Token manager not available")
            return False
            
    def discover_matrectrans_endpoints(self):
        """Discover available MATRECTRANS endpoints."""
        print("🔍 Discovering MATRECTRANS Endpoints")
        print("=" * 60)
        
        # Test various MATRECTRANS endpoint patterns
        endpoints_to_test = [
            f"{self.base_url}/api/os/matrectrans",
            f"{self.base_url}/api/os/mxapimatrectrans", 
            f"{self.base_url}/oslc/os/matrectrans",
            f"{self.base_url}/oslc/os/mxapimatrectrans",
            f"{self.base_url}/api/os/invtrans",
            f"{self.base_url}/api/os/mxapiinvtrans"
        ]
        
        working_endpoints = []
        
        for endpoint in endpoints_to_test:
            endpoint_type = "OSLC" if "/oslc/" in endpoint else "API"
            endpoint_name = endpoint.split('/')[-1]
            
            print(f"\n📋 Testing {endpoint_type} - {endpoint_name}")
            print(f"   URL: {endpoint}")
            
            # Test with API key
            success = self._test_endpoint_access(endpoint, "API Key")
            if success:
                working_endpoints.append(endpoint)
                
            # Test with session if available
            if self.session:
                success = self._test_endpoint_access_session(endpoint, "Session")
                if success and endpoint not in working_endpoints:
                    working_endpoints.append(endpoint)
                    
        print(f"\n📊 Discovery Results:")
        print(f"   Working Endpoints: {len(working_endpoints)}")
        for endpoint in working_endpoints:
            print(f"   ✅ {endpoint}")
            
        return working_endpoints
        
    def _test_endpoint_access(self, endpoint: str, auth_type: str) -> bool:
        """Test endpoint access with API key."""
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        try:
            response = requests.get(
                endpoint,
                params={"oslc.pageSize": "1"},
                headers=headers,
                timeout=(3.05, 10)
            )
            
            print(f"   {auth_type} Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'rdfs:member' in data or 'oslc:responseInfo' in data:
                        print(f"   ✅ {auth_type} - Valid JSON response")
                        return True
                    else:
                        print(f"   ⚠️ {auth_type} - Unexpected JSON structure")
                        return False
                except:
                    print(f"   ❌ {auth_type} - Non-JSON response")
                    return False
            else:
                print(f"   ❌ {auth_type} - HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ {auth_type} - Error: {str(e)[:50]}")
            return False
            
    def _test_endpoint_access_session(self, endpoint: str, auth_type: str) -> bool:
        """Test endpoint access with session authentication."""
        if not self.session:
            return False
            
        headers = {
            "Accept": "application/json"
        }
        
        try:
            response = self.session.get(
                endpoint,
                params={"oslc.pageSize": "1"},
                headers=headers,
                timeout=(3.05, 10)
            )
            
            print(f"   {auth_type} Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'rdfs:member' in data or 'oslc:responseInfo' in data:
                        print(f"   ✅ {auth_type} - Valid JSON response")
                        return True
                    else:
                        print(f"   ⚠️ {auth_type} - Unexpected JSON structure")
                        return False
                except:
                    print(f"   ❌ {auth_type} - Non-JSON response")
                    return False
            else:
                print(f"   ❌ {auth_type} - HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ {auth_type} - Error: {str(e)[:50]}")
            return False
            
    def analyze_matrectrans_structure(self, working_endpoints: List[str]):
        """Analyze MATRECTRANS structure and capabilities."""
        print(f"\n🔍 Analyzing MATRECTRANS Structure")
        print("=" * 60)
        
        for endpoint in working_endpoints:
            endpoint_name = endpoint.split('/')[-1]
            print(f"\n📋 Analyzing: {endpoint_name}")
            print(f"   URL: {endpoint}")
            
            # Get sample records to analyze structure
            self._analyze_endpoint_structure(endpoint)
            
            # Test for wsmethods
            self._test_endpoint_wsmethods(endpoint)
            
    def _analyze_endpoint_structure(self, endpoint: str):
        """Analyze the structure of an endpoint."""
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        try:
            response = requests.get(
                endpoint,
                params={
                    "oslc.select": "*",
                    "oslc.pageSize": "3",
                    "lean": "0"
                },
                headers=headers,
                timeout=(3.05, 15)
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'rdfs:member' in data and data['rdfs:member']:
                    records = data['rdfs:member']
                    print(f"   ✅ Found {len(records)} sample records")
                    
                    # Analyze first record structure
                    if records:
                        first_record = records[0]
                        print(f"   📋 Sample Record Fields:")
                        
                        # Show key fields
                        key_fields = []
                        for key, value in first_record.items():
                            if key.startswith('spi:'):
                                field_name = key[4:]  # Remove spi: prefix
                                field_type = type(value).__name__
                                key_fields.append(f"{field_name} ({field_type})")
                                
                        # Display first 10 fields
                        for field in key_fields[:10]:
                            print(f"      • {field}")
                            
                        if len(key_fields) > 10:
                            print(f"      ... and {len(key_fields) - 10} more fields")
                            
                else:
                    print(f"   ⚠️ No records found")
            else:
                print(f"   ❌ Failed to get structure: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Structure analysis error: {str(e)}")
            
    def _test_endpoint_wsmethods(self, endpoint: str):
        """Test wsmethods on an endpoint."""
        print(f"   🔍 Testing WSMethods:")
        
        # Common transfer/inventory related wsmethods to test
        wsmethods_to_test = [
            "itemavailability",
            "transfercurrentitem",
            "issuecurrentitem",
            "create",
            "update",
            "delete",
            "addchange",
            "transfer",
            "issue",
            "receive"
        ]
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        valid_methods = []
        
        for method in wsmethods_to_test:
            test_url = f"{endpoint}?action=wsmethod:{method}"
            
            try:
                response = requests.post(
                    test_url,
                    json={"test": "discovery"},
                    headers=headers,
                    timeout=(3.05, 10)
                )
                
                if response.status_code in [200, 400, 422]:
                    try:
                        data = response.json()
                        if 'oslc:Error' in data:
                            error_msg = data['oslc:Error'].get('oslc:message', '').lower()
                            if 'not found' not in error_msg and 'method' not in error_msg:
                                valid_methods.append(method)
                                print(f"      ✅ {method} - Exists (parameter error)")
                            else:
                                print(f"      ❌ {method} - Not found")
                        else:
                            valid_methods.append(method)
                            print(f"      ✅ {method} - Success response")
                    except:
                        print(f"      ⚠️ {method} - Non-JSON response")
                else:
                    print(f"      ❌ {method} - HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ {method} - Error: {str(e)[:30]}")
                
        if valid_methods:
            print(f"   📊 Valid Methods Found: {', '.join(valid_methods)}")
        else:
            print(f"   📊 No valid wsmethods found")
            
        return valid_methods
        
    def search_itemavailability_across_endpoints(self):
        """Search for itemavailability wsmethod across multiple endpoints."""
        print(f"\n🔍 Searching for itemavailability WSMethod")
        print("=" * 60)
        
        # Test various endpoints that might have itemavailability
        endpoints_to_test = [
            f"{self.base_url}/api/os/mxapiinventory",
            f"{self.base_url}/api/os/mxapiitem",
            f"{self.base_url}/api/os/item",
            f"{self.base_url}/api/os/inventory",
            f"{self.base_url}/api/os/matrectrans",
            f"{self.base_url}/api/os/invbalances",
            f"{self.base_url}/api/os/locations"
        ]
        
        itemavailability_results = []
        
        for endpoint in endpoints_to_test:
            endpoint_name = endpoint.split('/')[-1]
            print(f"\n📋 Testing itemavailability on: {endpoint_name}")
            
            result = self._test_itemavailability_method(endpoint)
            if result['exists']:
                itemavailability_results.append({
                    'endpoint': endpoint,
                    'name': endpoint_name,
                    'result': result
                })
                
        print(f"\n📊 itemavailability Search Results:")
        if itemavailability_results:
            for result in itemavailability_results:
                print(f"   ✅ Found on: {result['name']}")
                print(f"      Status: {result['result']['status']}")
                print(f"      Response: {result['result']['response'][:100]}...")
        else:
            print(f"   ❌ itemavailability wsmethod not found on any tested endpoint")
            
        return itemavailability_results
        
    def _test_itemavailability_method(self, endpoint: str) -> Dict:
        """Test itemavailability method on a specific endpoint."""
        test_url = f"{endpoint}?action=wsmethod:itemavailability"
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        # Test with realistic payload
        test_payload = {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT",
            "location": "LCVK-CMW-AJ"
        }
        
        try:
            response = requests.post(
                test_url,
                json=test_payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.content:
                try:
                    data = response.json()
                    response_text = json.dumps(data)
                    
                    # Check if method exists
                    if 'oslc:Error' in data:
                        error_msg = data['oslc:Error'].get('oslc:message', '').lower()
                        if 'not found' in error_msg or 'method' in error_msg:
                            print(f"   ❌ Method not found")
                            return {'exists': False, 'status': response.status_code, 'response': response_text}
                        else:
                            print(f"   ✅ Method exists (parameter error)")
                            return {'exists': True, 'status': response.status_code, 'response': response_text}
                    else:
                        print(f"   ✅ Method exists (success)")
                        return {'exists': True, 'status': response.status_code, 'response': response_text}
                        
                except:
                    print(f"   ⚠️ Non-JSON response")
                    return {'exists': False, 'status': response.status_code, 'response': response.text}
            else:
                print(f"   ❌ No response content")
                return {'exists': False, 'status': response.status_code, 'response': ''}
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {'exists': False, 'status': None, 'response': str(e)}

def main():
    """Main execution function."""
    print("🔍 MATRECTRANS Integration and WSMethod Investigation")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Initialize investigator
    investigator = MatrectransIntegrationInvestigator()
    
    # Initialize session authentication
    investigator.initialize_session_auth()
    
    # Step 1: Discover MATRECTRANS endpoints
    working_endpoints = investigator.discover_matrectrans_endpoints()
    
    # Step 2: Analyze MATRECTRANS structure and wsmethods
    if working_endpoints:
        investigator.analyze_matrectrans_structure(working_endpoints)
    
    # Step 3: Search for itemavailability across endpoints
    itemavailability_results = investigator.search_itemavailability_across_endpoints()
    
    print(f"\n📊 Investigation Summary")
    print("=" * 80)
    print(f"✅ MATRECTRANS endpoints tested")
    print(f"✅ WSMethods discovery completed")
    print(f"✅ itemavailability search completed")
    
    print(f"\n💡 Key Findings:")
    print(f"   • Working endpoints: {len(working_endpoints)}")
    print(f"   • itemavailability found on: {len(itemavailability_results)} endpoints")
    
    print(f"\n✅ Investigation completed successfully")

if __name__ == "__main__":
    main()
