#!/usr/bin/env python3
"""
End-to-end test for negative labor hours functionality
This test verifies that negative labor hours are properly posted to Maximo
and appear in the labor records.
"""

import requests
import json
import sys
import time

def get_labor_records(task_wonum):
    """Get current labor records for a task"""
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/labor?status=COMP&refresh=true"
    
    try:
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            data = response.json()
            return data.get('labor_records', [])
        else:
            print(f"❌ Failed to get labor records: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error getting labor records: {e}")
        return []

def add_negative_labor(task_wonum, laborcode, negative_hours, siteid, taskid, parent_wonum, craft):
    """Add negative labor hours"""
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    data = {
        "laborcode": laborcode,
        "negative_hours": negative_hours,
        "siteid": siteid,
        "taskid": taskid,
        "parent_wonum": parent_wonum,
        "craft": craft
    }
    
    try:
        response = requests.post(
            url,
            json=data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('success', False), result
        else:
            print(f"❌ Failed to add negative labor: {response.status_code}")
            return False, {"error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ Error adding negative labor: {e}")
        return False, {"error": str(e)}

def test_negative_labor_end_to_end():
    """Test the complete negative labor workflow"""
    
    # Test parameters
    task_wonum = "2219754"
    laborcode = "TINU.THOMAS"
    negative_hours = -0.5  # Subtract 0.5 hours
    siteid = "LCVKWT"
    taskid = 10
    parent_wonum = "2219753"
    craft = "MATCTRLSPCSR"
    
    print("🚀 Starting end-to-end negative labor test")
    print("="*60)
    print(f"📋 Test parameters:")
    print(f"   Task: {task_wonum}")
    print(f"   Labor Code: {laborcode}")
    print(f"   Negative Hours: {negative_hours}")
    print(f"   Site ID: {siteid}")
    print(f"   Task ID: {taskid}")
    print(f"   Parent WO: {parent_wonum}")
    print(f"   Craft: {craft}")
    print()
    
    # Step 1: Get initial labor records
    print("📊 Step 1: Getting initial labor records...")
    initial_records = get_labor_records(task_wonum)
    initial_count = len(initial_records)
    
    print(f"   Found {initial_count} initial labor records")
    
    # Calculate total hours for the specific labor code
    initial_total_hours = 0
    for record in initial_records:
        if record.get('laborcode') == laborcode:
            hours = record.get('regularhrs', 0)
            initial_total_hours += hours
            print(f"   {laborcode}: {hours} hours")
    
    print(f"   Total hours for {laborcode}: {initial_total_hours}")
    print()
    
    # Step 2: Add negative labor hours
    print("🔧 Step 2: Adding negative labor hours...")
    success, result = add_negative_labor(
        task_wonum, laborcode, negative_hours, siteid, taskid, parent_wonum, craft
    )
    
    if not success:
        print(f"❌ Failed to add negative labor: {result}")
        return False
    
    print(f"✅ Successfully added negative labor: {result.get('message', 'No message')}")
    print()
    
    # Step 3: Wait a moment for the system to process
    print("⏳ Step 3: Waiting for system to process...")
    time.sleep(3)
    print()
    
    # Step 4: Get updated labor records
    print("📊 Step 4: Getting updated labor records...")
    updated_records = get_labor_records(task_wonum)
    updated_count = len(updated_records)
    
    print(f"   Found {updated_count} updated labor records")
    
    # Calculate new total hours for the specific labor code
    updated_total_hours = 0
    negative_record_found = False
    
    for record in updated_records:
        if record.get('laborcode') == laborcode:
            hours = record.get('regularhrs', 0)
            updated_total_hours += hours
            print(f"   {laborcode}: {hours} hours")
            
            # Check if this is the negative record we just added
            if hours == negative_hours:
                negative_record_found = True
                print(f"   ✅ Found our negative record: {hours} hours")
    
    print(f"   Total hours for {laborcode}: {updated_total_hours}")
    print()
    
    # Step 5: Verify the results
    print("🔍 Step 5: Verifying results...")
    
    expected_total = initial_total_hours + negative_hours
    
    print(f"   Initial total: {initial_total_hours}")
    print(f"   Added: {negative_hours}")
    print(f"   Expected total: {expected_total}")
    print(f"   Actual total: {updated_total_hours}")
    
    # Check if we have more records (should have at least one more)
    if updated_count <= initial_count:
        print(f"❌ Expected more labor records, but got {updated_count} vs {initial_count}")
        return False
    
    # Check if the negative record was found
    if not negative_record_found:
        print(f"❌ Negative labor record with {negative_hours} hours not found")
        return False
    
    # Check if the total hours are correct (with some tolerance for floating point)
    if abs(updated_total_hours - expected_total) > 0.001:
        print(f"❌ Total hours mismatch: expected {expected_total}, got {updated_total_hours}")
        return False
    
    print("✅ All verifications passed!")
    print()
    
    return True

if __name__ == "__main__":
    print("🧪 Negative Labor Hours End-to-End Test")
    print("="*60)
    
    success = test_negative_labor_end_to_end()
    
    print("="*60)
    if success:
        print("🎉 END-TO-END TEST PASSED!")
        print("✅ Negative labor hours are working correctly in Maximo")
        sys.exit(0)
    else:
        print("❌ END-TO-END TEST FAILED!")
        print("❌ There may be an issue with the negative labor functionality")
        sys.exit(1)
