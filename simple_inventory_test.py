#!/usr/bin/env python3
"""
Simple test to verify inventory field processing works.
"""
import os
import sys
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from auth.token_manager import MaximoTokenManager
from services.inventory_management_service import InventoryManagementService

def simple_test():
    """Simple test of inventory field processing."""
    
    print("🔧 SIMPLE INVENTORY FIELD TEST")
    print("=" * 40)
    
    # Initialize services
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo")
        return False
    
    print("✅ Authenticated with Maximo")
    
    # Initialize service
    inventory_service = InventoryManagementService(token_manager)
    
    # Test item number
    test_itemnum = "5975-60-V00-0001"
    test_site = "LCVKWT"
    
    print(f"\n🔍 Testing with item: {test_itemnum} at site: {test_site}")
    
    try:
        # Use the correct method name
        search_results, metadata = inventory_service.search_inventory_items(test_itemnum, test_site, limit=1)
        
        print(f"✅ Search completed. Found {len(search_results)} items")
        print(f"📊 Metadata: {metadata}")
        
        if search_results:
            item_data = search_results[0]
            print(f"\n📋 Item data has {len(item_data)} fields")
            
            # Check key fields
            key_fields = [
                'itemnum', 'siteid', 'location', 'status',
                'invcost_avgcost', 'invcost_lastcost', 'invcost_stdcost',
                'invbalances_curbal', 'invbalances_physcnt',
                'item_description', 'item_rotating'
            ]
            
            print(f"\n🔍 Key field values:")
            for field in key_fields:
                value = item_data.get(field, 'NOT FOUND')
                print(f"   {field}: {value}")
            
            # Save data for inspection
            with open('simple_inventory_test_data.json', 'w') as f:
                json.dump(item_data, f, indent=2, default=str)
            
            print(f"\n💾 Data saved to: simple_inventory_test_data.json")
            return True
        else:
            print("❌ No data found")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    if success:
        print(f"\n🎉 Simple test passed!")
    else:
        print(f"\n❌ Simple test failed")
        sys.exit(1)
