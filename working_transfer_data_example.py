#!/usr/bin/env python3
"""
Working Example: Retrieving and Analyzing Transfer Data from MXAPIINVENTORY

This script demonstrates the working patterns for accessing transfer data
from the spi:transfercuritem nested object, including proper authentication,
error handling, and data processing.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

class TransferDataRetriever:
    """Demonstrates working patterns for transfer data retrieval."""
    
    def __init__(self, api_key: str):
        """Initialize with API key."""
        self.api_key = api_key
        self.base_url = BASE_URL
        
    def get_inventory_with_transfers(self, site_id: str = None, item_num: str = None) -> List[Dict]:
        """
        Retrieve inventory records that have transfer data.
        
        Args:
            site_id: Optional site ID filter
            item_num: Optional item number filter
            
        Returns:
            List of inventory records with transfer data
        """
        print(f"🔍 Retrieving Inventory Records with Transfer Data")
        if site_id:
            print(f"   Site Filter: {site_id}")
        if item_num:
            print(f"   Item Filter: {item_num}")
        print("-" * 50)
        
        endpoint_url = f"{self.base_url}/api/os/mxapiinventory"
        
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        # Build parameters
        params = {
            "oslc.select": "inventoryid,itemnum,siteid,location,curbaltotal,avblbalance,transfercuritem",
            "lean": "0",  # Include nested objects
            "oslc.pageSize": "10"
        }
        
        # Build where clause
        where_conditions = []
        if site_id:
            where_conditions.append(f'siteid="{site_id}"')
        if item_num:
            where_conditions.append(f'itemnum="{item_num}"')
            
        if where_conditions:
            params["oslc.where"] = " and ".join(where_conditions)
        
        try:
            response = requests.get(
                endpoint_url,
                headers=headers,
                params=params,
                timeout=(3.05, 15)
            )
            
            success, result = self._handle_response(response)
            
            if success:
                records = result.get('rdfs:member', [])
                print(f"✅ Retrieved {len(records)} inventory records")
                
                # Filter records that actually have transfer data
                records_with_transfers = []
                for record in records:
                    if record.get('spi:transfercuritem'):
                        records_with_transfers.append(record)
                        
                print(f"✅ Found {len(records_with_transfers)} records with transfer data")
                return records_with_transfers
            else:
                print(f"❌ Error: {result}")
                return []
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
            return []
            
    def analyze_transfer_structure(self, records: List[Dict]) -> Dict:
        """
        Analyze the structure of transfer data from multiple records.
        
        Args:
            records: List of inventory records with transfer data
            
        Returns:
            Analysis summary
        """
        print(f"\n📋 Analyzing Transfer Data Structure")
        print("-" * 50)
        
        analysis = {
            'total_records': len(records),
            'total_transfers': 0,
            'field_frequency': {},
            'transfer_patterns': [],
            'sample_transfers': []
        }
        
        for record in records:
            transfers = record.get('spi:transfercuritem', [])
            analysis['total_transfers'] += len(transfers)
            
            for transfer in transfers:
                # Track field frequency
                for field in transfer.keys():
                    if field not in analysis['field_frequency']:
                        analysis['field_frequency'][field] = 0
                    analysis['field_frequency'][field] += 1
                
                # Collect sample transfers
                if len(analysis['sample_transfers']) < 3:
                    analysis['sample_transfers'].append({
                        'item': record.get('spi:itemnum'),
                        'location': record.get('spi:location'),
                        'transfer': transfer
                    })
                
                # Analyze transfer patterns
                pattern = {
                    'from_location': transfer.get('spi:fromstoreloc'),
                    'to_site': transfer.get('spi:tositeid'),
                    'quantity': transfer.get('spi:quantity'),
                    'cost': transfer.get('spi:unitcost')
                }
                analysis['transfer_patterns'].append(pattern)
        
        # Display analysis
        print(f"📊 Analysis Results:")
        print(f"   Total Records: {analysis['total_records']}")
        print(f"   Total Transfers: {analysis['total_transfers']}")
        
        print(f"\n📋 Field Frequency:")
        for field, count in sorted(analysis['field_frequency'].items()):
            percentage = (count / analysis['total_transfers']) * 100
            print(f"   {field}: {count}/{analysis['total_transfers']} ({percentage:.1f}%)")
            
        return analysis
        
    def display_sample_transfers(self, analysis: Dict):
        """Display sample transfer data in detail."""
        print(f"\n📋 Sample Transfer Data")
        print("-" * 50)
        
        for i, sample in enumerate(analysis['sample_transfers'], 1):
            print(f"\n🔸 Sample {i}: {sample['item']} at {sample['location']}")
            transfer = sample['transfer']
            
            for key, value in transfer.items():
                if key.startswith('spi:'):
                    display_key = key[4:]  # Remove spi: prefix
                else:
                    display_key = key
                print(f"   {display_key}: {value}")
                
    def decode_inventory_id(self, encoded_id: str) -> str:
        """
        Decode base64 encoded inventory ID from URL.
        
        Args:
            encoded_id: Base64 encoded ID from URL
            
        Returns:
            Decoded ID string
        """
        try:
            # Remove leading underscore and trailing dash if present
            clean_id = encoded_id.strip('_-')
            
            # Add padding if needed
            missing_padding = len(clean_id) % 4
            if missing_padding:
                clean_id += '=' * (4 - missing_padding)
            
            # Decode
            decoded_bytes = base64.b64decode(clean_id)
            decoded_str = decoded_bytes.decode('utf-8')
            
            return decoded_str
        except Exception as e:
            return f"Decode error: {str(e)}"
            
    def analyze_url_structure(self, records: List[Dict]):
        """Analyze the URL structure of transfer references."""
        print(f"\n🔍 URL Structure Analysis")
        print("-" * 50)
        
        for record in records[:2]:  # Analyze first 2 records
            transfers = record.get('spi:transfercuritem', [])
            
            for transfer in transfers:
                localref = transfer.get('localref', '')
                rdf_about = transfer.get('rdf:about', '')
                
                print(f"\n📋 Record: {record.get('spi:itemnum')}")
                print(f"   Local Ref: {localref}")
                print(f"   RDF About: {rdf_about}")
                
                # Extract and decode ID from localref
                if '_' in localref and '/transfercuritem' in localref:
                    start = localref.find('_') + 1
                    end = localref.find('/transfercuritem')
                    if start > 0 and end > start:
                        encoded_id = localref[start:end]
                        decoded_id = self.decode_inventory_id(encoded_id)
                        print(f"   Encoded ID: {encoded_id}")
                        print(f"   Decoded ID: {decoded_id}")
                        
                break  # Only analyze first transfer per record
                
    def _handle_response(self, response) -> tuple:
        """
        Handle API response with proper error checking.
        
        Args:
            response: requests.Response object
            
        Returns:
            Tuple of (success: bool, result: dict or str)
        """
        if response.status_code == 200:
            try:
                data = response.json()
                return True, data
            except:
                return False, f"Invalid JSON response: {response.text[:100]}..."
        else:
            try:
                error_data = response.json()
                if 'oslc:Error' in error_data:
                    error = error_data['oslc:Error']
                    reason_code = error.get('spi:reasonCode', 'Unknown')
                    message = error.get('oslc:message', 'No message')
                    return False, f"{reason_code}: {message}"
                else:
                    return False, f"HTTP {response.status_code}: {response.text[:100]}..."
            except:
                return False, f"HTTP {response.status_code}: {response.text[:100]}..."

def main():
    """Main execution function demonstrating transfer data retrieval."""
    print("🔍 MXAPIINVENTORY Transfer Data Retrieval - Working Example")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Initialize retriever
    retriever = TransferDataRetriever(API_KEY)
    
    # Step 1: Get inventory records with transfer data
    records = retriever.get_inventory_with_transfers(site_id="LCVKWT")
    
    if not records:
        print("❌ No records with transfer data found")
        return False
    
    # Step 2: Analyze transfer structure
    analysis = retriever.analyze_transfer_structure(records)
    
    # Step 3: Display sample transfers
    retriever.display_sample_transfers(analysis)
    
    # Step 4: Analyze URL structure
    retriever.analyze_url_structure(records)
    
    print(f"\n📊 Summary")
    print("=" * 80)
    print("✅ Successfully retrieved transfer data using GET operations")
    print("✅ Analyzed transfer field structure and patterns")
    print("✅ Demonstrated proper authentication and error handling")
    print("✅ Decoded URL structure and inventory IDs")
    
    print(f"\n💡 Key Takeaways:")
    print("   • Transfer data is accessible via GET operations with lean=0")
    print("   • transfercuritem is a nested array within inventory records")
    print("   • URLs use base64-encoded inventory identifiers")
    print("   • Direct transfer creation is not supported via this endpoint")
    print("   • API key authentication works reliably for data retrieval")
    
    print(f"\n✅ Working example completed successfully")
    return True

if __name__ == "__main__":
    main()
