#!/usr/bin/env python3
"""
Test the exact task status change flow that the frontend uses
This simulates what happens when user clicks to change task status to COMP
"""

import requests
import json

BASE_URL = "http://localhost:5010"
session = requests.Session()

def test_task_status_change():
    """Test the exact task status change call that frontend makes"""
    print("🧪 Testing Task Status Change (Frontend Simulation)")
    print("=" * 50)
    
    # First configure signature requirement for COMP
    print("📝 Step 1: Configure COMP status for signature...")
    config_data = {
        "statuses": ["COMP"],
        "scope": ["parent", "task"],
        "enabled": True
    }
    
    # Temporarily bypass auth for testing
    response = session.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=config_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Config response: {response.status_code}")
    if response.status_code != 200:
        print(f"❌ Config failed: {response.text}")
        return False
    
    # Now test the exact call that frontend makes
    print("\n📝 Step 2: Test task status change call (simulating frontend)...")
    
    task_wonum = "2021-1984593"  # Use a real task number from logs
    new_status = "COMP"
    
    print(f"Calling: POST /api/task/{task_wonum}/status")
    print(f"Payload: {{'status': '{new_status}'}}")
    
    response = session.post(
        f"{BASE_URL}/api/task/{task_wonum}/status",
        json={"status": new_status},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"Response Data: {json.dumps(data, indent=2)}")
            
            if data.get('signature_required'):
                print("✅ SUCCESS: Signature requirement detected correctly!")
                print("   - This should trigger signature modal in frontend")
                print(f"   - Status: {data.get('status')}")
                print(f"   - WO Type: {data.get('wo_type')}")
                print(f"   - Message: {data.get('message')}")
                return True
            elif data.get('success'):
                print("✅ SUCCESS: Status changed without signature requirement")
                return True
            else:
                print(f"❌ UNEXPECTED: {data}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON Parse Error: {e}")
            print(f"Raw Response: {response.text[:500]}")
            return False
    else:
        print(f"❌ HTTP Error: {response.status_code}")
        print(f"Response Text: {response.text[:500]}")
        return False

def test_without_signature_config():
    """Test task status change without signature configuration"""
    print("\n🧪 Testing Task Status Change WITHOUT Signature Config")
    print("=" * 50)
    
    # Configure no signature requirements
    print("📝 Disabling signature requirements...")
    config_data = {
        "statuses": [],
        "scope": ["parent", "task"],
        "enabled": False
    }
    
    response = session.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=config_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Config response: {response.status_code}")
    
    # Test status change
    task_wonum = "2021-1984593"
    new_status = "INPRG"  # Use different status
    
    print(f"\nCalling: POST /api/task/{task_wonum}/status")
    print(f"Payload: {{'status': '{new_status}'}}")
    
    response = session.post(
        f"{BASE_URL}/api/task/{task_wonum}/status",
        json={"status": new_status},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"\nResponse Status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"Response Data: {json.dumps(data, indent=2)}")
            
            if data.get('success'):
                print("✅ SUCCESS: Status changed without signature (as expected)")
                return True
            else:
                print(f"❌ FAILED: {data}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON Parse Error: {e}")
            print(f"Raw Response: {response.text[:500]}")
            return False
    else:
        print(f"❌ HTTP Error: {response.status_code}")
        print(f"Response Text: {response.text[:500]}")
        return False

if __name__ == "__main__":
    print("🚀 Task Status Change Test Suite")
    print("This simulates exactly what the frontend does")
    print("=" * 60)
    
    # Test 1: With signature requirement
    test1_success = test_task_status_change()
    
    # Test 2: Without signature requirement
    test2_success = test_without_signature_config()
    
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS")
    print("=" * 60)
    
    print(f"Test 1 (With Signature): {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"Test 2 (Without Signature): {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The task status change endpoint is working correctly.")
        print("If you're still getting network errors in the frontend,")
        print("the issue is likely in the JavaScript error handling.")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Check the Flask logs for detailed error information.")
    
    print("\n💡 Next Steps:")
    print("1. Check browser console for JavaScript errors")
    print("2. Check Flask logs for backend errors")
    print("3. Verify authentication status in browser")
    print("4. Test with a real task work order number")
