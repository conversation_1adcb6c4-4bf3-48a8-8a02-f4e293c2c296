<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Negative Hours Frontend</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bug me-2"></i>Negative Hours Frontend Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This test simulates the labor content structure to verify parameter extraction.
                        </div>

                        <!-- Simulated Labor Content Container -->
                        <div class="border p-3 mb-3">
                            <h6>Simulated Labor Content Container:</h6>
                            <div id="labor-content-2219754" 
                                 data-task-id="10"
                                 data-site-id="LCVKWT"
                                 data-task-wonum="2219754"
                                 data-parent-wonum="2219753">
                                
                                <!-- Simulated Labor Entry -->
                                <div class="border p-2 mb-2">
                                    <strong>Labor Entry:</strong> TINU.THOMAS - 8.0 hours
                                    <br>
                                    <button class="btn btn-sm btn-outline-warning mt-2"
                                            data-labor-code="TINU.THOMAS"
                                            data-labor-hours="8.0"
                                            data-craft="MATCTRLSPCSR"
                                            onclick="testDeleteLaborEntry(this)">
                                        <i class="fas fa-minus me-1"></i>Test Subtract Hours
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Test Results -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Test Results</h6>
                            </div>
                            <div class="card-body">
                                <div id="testResults">
                                    <p class="text-muted">Click the "Test Subtract Hours" button to test parameter extraction.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Expected API Call -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Expected API Call</h6>
                            </div>
                            <div class="card-body">
                                <pre id="expectedApiCall" class="bg-light p-2">
URL: POST /api/task/2219754/add-negative-labor

Payload:
{
  "laborcode": "TINU.THOMAS",
  "negative_hours": -1.0,
  "taskid": 10,
  "siteid": "LCVKWT", 
  "parent_wonum": "2219753",
  "craft": "MATCTRLSPCSR"
}
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testDeleteLaborEntry(button) {
            console.log('🧪 TESTING: Starting parameter extraction test...');
            
            const laborCode = button.getAttribute('data-labor-code');
            const laborHours = button.getAttribute('data-labor-hours');
            const craft = button.getAttribute('data-craft');

            console.log('🧪 TESTING: Button attributes:');
            console.log('  - Labor Code:', laborCode);
            console.log('  - Labor Hours:', laborHours);
            console.log('  - Craft:', craft);

            // Find the task wonum from the button's context
            const laborContent = button.closest('[id^="labor-content-"]');
            const taskWonum = laborContent ? laborContent.id.replace('labor-content-', '') : null;

            console.log('🧪 TESTING: Labor content container:', laborContent);
            console.log('🧪 TESTING: Extracted task wonum:', taskWonum);

            if (!taskWonum) {
                console.error('❌ TESTING: Could not determine task wonum');
                updateTestResults('❌ FAILED: Could not determine task wonum', 'danger');
                return;
            }

            // Get task data directly from the labor content data attributes
            const taskId = laborContent.getAttribute('data-task-id');
            const siteId = laborContent.getAttribute('data-site-id');
            const parentWonum = laborContent.getAttribute('data-parent-wonum');

            console.log('🧪 TESTING: Extracted data:');
            console.log('  - Task wonum:', taskWonum);
            console.log('  - Parent wonum:', parentWonum);
            console.log('  - Task ID:', taskId);
            console.log('  - Site ID:', siteId);
            console.log('  - Labor Code:', laborCode);
            console.log('  - Labor Hours:', laborHours);
            console.log('  - Craft:', craft);

            // Validate required parameters
            const missingParams = [];
            if (!taskId) missingParams.push('taskId');
            if (!parentWonum) missingParams.push('parentWonum');
            if (!siteId) missingParams.push('siteId');
            if (!laborCode) missingParams.push('laborCode');

            if (missingParams.length > 0) {
                console.error('❌ TESTING: Missing required parameters:', missingParams);
                updateTestResults(`❌ FAILED: Missing required parameters: ${missingParams.join(', ')}`, 'danger');
                return;
            }

            // Simulate the API call payload
            const requestData = {
                laborcode: laborCode,
                negative_hours: -1.0, // Example negative hours
                taskid: parseInt(taskId),
                siteid: siteId,
                parent_wonum: parentWonum,
                craft: craft
            };

            console.log('🧪 TESTING: Simulated API request data:', requestData);

            // Show success results
            const resultsHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check me-2"></i>✅ SUCCESS: All parameters extracted correctly!</h6>
                    <hr>
                    <strong>Extracted Parameters:</strong>
                    <ul class="mb-0">
                        <li><strong>Task Wonum:</strong> ${taskWonum}</li>
                        <li><strong>Parent Wonum:</strong> ${parentWonum}</li>
                        <li><strong>Task ID:</strong> ${taskId}</li>
                        <li><strong>Site ID:</strong> ${siteId}</li>
                        <li><strong>Labor Code:</strong> ${laborCode}</li>
                        <li><strong>Labor Hours:</strong> ${laborHours}</li>
                        <li><strong>Craft:</strong> ${craft}</li>
                    </ul>
                </div>
                <div class="alert alert-info">
                    <h6><i class="fas fa-code me-2"></i>Simulated API Call:</h6>
                    <pre class="mb-0">URL: POST /api/task/${taskWonum}/add-negative-labor

Payload: ${JSON.stringify(requestData, null, 2)}</pre>
                </div>
            `;

            updateTestResults(resultsHtml, 'success');
        }

        function updateTestResults(content, type) {
            const resultsDiv = document.getElementById('testResults');
            if (type === 'success') {
                resultsDiv.innerHTML = content;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-${type}">${content}</div>`;
            }
        }

        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 TESTING: Page loaded, ready for testing');
            console.log('🧪 TESTING: Labor content container exists:', !!document.getElementById('labor-content-2219754'));
        });
    </script>
</body>
</html>
