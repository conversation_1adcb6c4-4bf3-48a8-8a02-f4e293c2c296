#!/usr/bin/env python3
"""
Test authentication status via Flask app
"""

import requests
import json

def test_auth_status():
    """Test if user is authenticated via Flask"""
    
    print("🔧 Testing Authentication Status via Flask App")
    print("=" * 50)
    
    # Flask app base URL
    flask_base = "http://127.0.0.1:5010"
    
    try:
        # Test auth status endpoint
        print(f"🔧 Testing Auth Status Endpoint")
        print(f"URL: {flask_base}/api/auth-status")
        
        response = requests.get(f"{flask_base}/api/auth-status", timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                auth_data = response.json()
                print(f"✅ AUTH STATUS SUCCESS")
                print(f"Response: {json.dumps(auth_data, indent=2)}")
                return True
            except:
                print(f"✅ AUTH STATUS SUCCESS (non-JSON response)")
                print(f"Response: {response.text}")
                return True
        else:
            print(f"❌ AUTH STATUS FAILED")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Authentication Status Testing")
    print("=" * 50)
    
    success = test_auth_status()
    
    if success:
        print(f"\n🎉 SUCCESS: User is authenticated!")
    else:
        print(f"\n⚠️ FAILED: User is NOT authenticated")
    
    print(f"\n✅ Testing completed!")
