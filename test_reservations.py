#!/usr/bin/env python3
"""
Test the reservations functionality
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add backend path for imports
sys.path.append('backend/auth')

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError as e:
    print(f"❌ Cannot import MaximoTokenManager: {e}")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
TARGET_ITEM = "5975-60-V00-0001"
TARGET_SITE = "LCVKWT"
TEST_LOCATIONS = ["LCVKWT-AJ-IMD-ADPE-W", "LCVKWT-AJ-IMD-ADPE-S"]  # Example locations

def test_reservations_api():
    """Test the MXAPIINVRES API directly."""
    print("🔍 Testing MXAPIINVRES API")
    print("=" * 50)
    
    # Use API key for testing
    api_key = os.environ.get('MAXIMO_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return
    
    api_url = f"{BASE_URL}/api/os/mxapiinvres"
    
    # Build location filter
    location_filter = " or ".join([f'location="{loc}"' for loc in TEST_LOCATIONS])
    where_clause = f'itemnum="{TARGET_ITEM}" and siteid="{TARGET_SITE}" and ({location_filter})'
    
    params = {
        "oslc.select": "requestnum,reservationtype,itemnum,itemtype,description,storeroom,storroom.siteid,conditioncode,reservedqty,wonum,taskid,tostoreroom,tosite,ponum,requireddate",
        "oslc.where": where_clause,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    print(f"API URL: {api_url}")
    print(f"Where clause: {where_clause}")
    print(f"Params: {params}")
    
    try:
        response = requests.get(
            api_url,
            params=params,
            timeout=(3.05, 15),
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                reservations = data.get('member', [])
                
                print(f"Total reservations found: {len(reservations)}")
                
                if reservations:
                    print("\nReservations data:")
                    for i, reservation in enumerate(reservations, 1):
                        print(f"\n--- Reservation {i} ---")
                        print(f"Request: {reservation.get('requestnum', 'N/A')}")
                        print(f"Type: {reservation.get('reservationtype', 'N/A')}")
                        print(f"Item: {reservation.get('itemnum', 'N/A')}")
                        print(f"Reserved Qty: {reservation.get('reservedqty', 'N/A')}")
                        print(f"Work Order: {reservation.get('wonum', 'N/A')}")
                        print(f"Storeroom: {reservation.get('storeroom', 'N/A')}")
                        print(f"Required Date: {reservation.get('requireddate', 'N/A')}")
                else:
                    print("No reservations found")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_flask_app():
    """Test the Flask application reservations endpoint."""
    print("\n🔍 Testing Flask Application")
    print("=" * 50)
    
    try:
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/availability/{TARGET_ITEM}",
            params={'siteid': TARGET_SITE},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                reservations = data.get('reservations', [])
                print(f"Reservations from Flask: {len(reservations)}")
                
                if reservations:
                    print("\nFirst reservation:")
                    print(json.dumps(reservations[0], indent=2))
                else:
                    print("No reservations in Flask response")
            else:
                print(f"Flask error: {data.get('error', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Testing Reservations Functionality")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Test Locations: {TEST_LOCATIONS}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_reservations_api()
    test_flask_app()
    
    print(f"\n{'='*50}")
    print("🏁 Test Complete")
    print(f"{'='*50}")
