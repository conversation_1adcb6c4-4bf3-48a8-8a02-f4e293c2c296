#!/usr/bin/env python3
"""
MXAPIINVENTORY Method Discovery Script

This script tests the MXAPIINVENTORY endpoint using the same authentication
pattern as the working inventory services in the codebase.

Author: Augment Agent
Date: 2025-01-15
"""

import sys
import os
import json
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.auth.token_manager import MaximoToken<PERSON><PERSON><PERSON>

def test_get_operations():
    """Test GET operations on MXAPIINVENTORY endpoint."""
    print("🔍 Testing GET Operations")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return None
        
    print("✅ Authenticated successfully")
    
    # Test basic GET request
    base_url = token_manager.base_url
    api_url = f"{base_url}/oslc/os/mxapiinventory"
    
    # Test 1: Basic field discovery
    print("\n📋 Test 1: Basic Field Discovery")
    params = {
        "oslc.select": "*",
        "oslc.where": 'siteid="LCVKWT"',
        "oslc.pageSize": "1",
        "lean": "0"
    }
    
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 15),
            headers={"Accept": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            data = response.json()
            members = data.get('member', [])
            print(f"Records Found: {len(members)}")
            
            if members:
                first_record = members[0]
                print(f"Available Fields: {len(first_record.keys())}")
                
                # Show field categories
                field_categories = {
                    'identifiers': [],
                    'quantities': [],
                    'dates': [],
                    'locations': [],
                    'nested': []
                }
                
                for field_name, value in first_record.items():
                    if any(keyword in field_name.lower() for keyword in ['id', 'num', 'code']):
                        field_categories['identifiers'].append(field_name)
                    elif any(keyword in field_name.lower() for keyword in ['qty', 'bal', 'count']):
                        field_categories['quantities'].append(field_name)
                    elif any(keyword in field_name.lower() for keyword in ['date', 'time']):
                        field_categories['dates'].append(field_name)
                    elif any(keyword in field_name.lower() for keyword in ['location', 'site', 'bin']):
                        field_categories['locations'].append(field_name)
                    elif isinstance(value, (dict, list)):
                        field_categories['nested'].append(field_name)
                
                print("\n📊 Field Categories:")
                for category, fields in field_categories.items():
                    if fields:
                        print(f"  {category.title()}: {', '.join(fields[:5])}")
                        if len(fields) > 5:
                            print(f"    ... and {len(fields) - 5} more")
                            
                # Show sample nested structures
                print("\n🔗 Nested Structures:")
                for field_name, value in first_record.items():
                    if isinstance(value, list) and value and isinstance(value[0], dict):
                        print(f"  {field_name}: Array of objects with keys: {list(value[0].keys())}")
                    elif isinstance(value, dict):
                        print(f"  {field_name}: Object with keys: {list(value.keys())}")
                        
                return {
                    'success': True,
                    'total_fields': len(first_record.keys()),
                    'sample_record': first_record,
                    'field_categories': field_categories
                }
            else:
                print("No records found")
                return {'success': False, 'error': 'No records found'}
        else:
            print(f"Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return {'success': False, 'status_code': response.status_code, 'error': response.text[:200]}
            
    except Exception as e:
        print(f"Exception: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_post_operations():
    """Test POST operations on MXAPIINVENTORY endpoint."""
    print("\n🔍 Testing POST Operations")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return None
        
    base_url = token_manager.base_url
    api_url = f"{base_url}/oslc/os/mxapiinventory"
    
    # Test different POST actions
    test_actions = [
        {
            'name': 'AddChange',
            'payload': [
                {
                    "_action": "AddChange",
                    "itemnum": "TEST-DISCOVERY-001",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "TEST-LOC"
                }
            ]
        },
        {
            'name': 'Create',
            'payload': [
                {
                    "_action": "Create",
                    "itemnum": "TEST-DISCOVERY-002",
                    "siteid": "LCVKWT"
                }
            ]
        }
    ]
    
    results = {}
    
    for test in test_actions:
        print(f"\n📋 Testing Action: {test['name']}")
        
        try:
            response = token_manager.session.post(
                api_url,
                json=test['payload'],
                timeout=(3.05, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            print(f"Status Code: {response.status_code}")
            
            result = {
                'status_code': response.status_code,
                'success': response.status_code in [200, 201, 204],
                'payload_sent': test['payload']
            }
            
            if response.content:
                try:
                    result['response_data'] = response.json()
                    print(f"Response Type: JSON")
                    if 'Error' in result['response_data']:
                        print(f"API Error: {result['response_data']['Error']}")
                except:
                    result['response_text'] = response.text[:300]
                    print(f"Response Type: Text")
                    print(f"Response Preview: {response.text[:100]}")
            
            results[test['name']] = result
            
        except Exception as e:
            print(f"Exception: {str(e)}")
            results[test['name']] = {
                'success': False,
                'error': str(e),
                'payload_sent': test['payload']
            }
    
    return results

def test_options_method():
    """Test OPTIONS method to discover allowed methods."""
    print("\n🔍 Testing OPTIONS Method")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return None
        
    base_url = token_manager.base_url
    
    # Test both endpoint variants
    endpoints = [
        f"{base_url}/oslc/os/mxapiinventory",
        f"{base_url}/api/os/mxapiinventory"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        endpoint_name = "oslc" if "oslc" in endpoint else "api"
        print(f"\n📋 Testing {endpoint_name.upper()} endpoint")
        
        try:
            response = token_manager.session.options(
                endpoint,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )
            
            print(f"Status Code: {response.status_code}")
            
            # Check for Allow header
            allow_header = response.headers.get('Allow', '')
            if allow_header:
                print(f"Allowed Methods: {allow_header}")
            else:
                print("No Allow header found")
            
            results[endpoint_name] = {
                'status_code': response.status_code,
                'allowed_methods': allow_header.split(', ') if allow_header else [],
                'headers': dict(response.headers),
                'success': response.status_code == 200
            }
            
        except Exception as e:
            print(f"Exception: {str(e)}")
            results[endpoint_name] = {
                'success': False,
                'error': str(e)
            }
    
    return results

def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Method Discovery")
    print("=" * 80)
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'get_operations': None,
        'post_operations': None,
        'options_method': None
    }
    
    # Test GET operations
    get_results = test_get_operations()
    results['get_operations'] = get_results
    
    # Test POST operations
    post_results = test_post_operations()
    results['post_operations'] = post_results
    
    # Test OPTIONS method
    options_results = test_options_method()
    results['options_method'] = options_results
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"mxapiinventory_methods_{timestamp}.json"
    
    try:
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n✅ Results saved to: {filename}")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")
    
    # Print summary
    print("\n" + "=" * 80)
    print("🎯 DISCOVERY SUMMARY")
    print("=" * 80)
    
    if get_results and get_results.get('success'):
        print(f"✅ GET Operations: Working - {get_results.get('total_fields', 0)} fields discovered")
    else:
        print("❌ GET Operations: Failed")
    
    if post_results:
        successful_actions = [action for action, result in post_results.items() if result.get('success')]
        print(f"📊 POST Operations: {len(successful_actions)} successful actions out of {len(post_results)}")
        if successful_actions:
            print(f"   Successful: {', '.join(successful_actions)}")
    
    if options_results:
        for endpoint, result in options_results.items():
            if result.get('success') and result.get('allowed_methods'):
                print(f"✅ {endpoint.upper()} OPTIONS: {', '.join(result['allowed_methods'])}")
    
    return results

if __name__ == "__main__":
    main()
