#!/bin/bash

# Test Cross-Site with LCVKWT Storerooms
# =======================================

echo "🚀 TESTING CROSS-SITE WITH LCVKWT STOREROOMS AS DESTINATION"
echo "==========================================================="

echo "🎯 OBJECTIVE: Get 204 success by using storerooms that exist in LCVKWT"
echo "📋 STRATEGY: Test LCVKWT → IKWAJ but with LCVKWT storerooms as destination"
echo "🔍 HYPOTHESIS: The validation checks destination against source site"
echo ""

# First, let's check what storerooms exist in LCVKWT
echo "🔍 Checking available storerooms in LCVKWT..."
lcvkwt_storerooms=$(curl -X GET "http://127.0.0.1:5010/api/inventory/transfer-current-item/storerooms?siteid=LCVKWT" \
    -H "Accept: application/json" -s)

echo "📋 LCVKWT Storerooms:"
echo "$lcvkwt_storerooms" | head -20
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_cross_site_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting cross-site transfer..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Cross-site transfer worked with 204 status!"
        echo "$payload" > "successful_cross_site_lcvkwt_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: successful_cross_site_lcvkwt_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING WITH LCVKWT STOREROOMS AS DESTINATION"
echo "==============================================="

# Test 1: LCVKWT to IKWAJ but destination in LCVKWT (if RIP002 exists)
test_cross_site_payload "LCVKWT → IKWAJ with LCVKWT destination" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: Try with different LCVKWT location if it exists
test_cross_site_payload "LCVKWT → IKWAJ with RIP002" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP002",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: Try with WAREHOUSE if it exists in LCVKWT
test_cross_site_payload "LCVKWT → IKWAJ with WAREHOUSE" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "WAREHOUSE",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: Try with CENTRAL if it exists in LCVKWT
test_cross_site_payload "LCVKWT → IKWAJ with CENTRAL" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CENTRAL",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Try reverse direction - IKWAJ to LCVKWT
test_cross_site_payload "IKWAJ → LCVKWT (Reverse Direction)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "LCVKWT",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 6: Try with smaller quantity
test_cross_site_payload "Small Quantity Cross-Site" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 0.01,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 7: Try without bins and lots
test_cross_site_payload "Minimal Cross-Site" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 8: Try with different item that might have different validation
test_cross_site_payload "Different Item Cross-Site" '{
    "itemnum": "TEST-ITEM",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "EA"
}'

echo ""
echo "📊 CROSS-SITE TRANSFER TEST SUMMARY"
echo "==================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found working cross-site transfer patterns!"
    echo "======================================================"
    echo "💾 Check successful_cross_site_lcvkwt_*.json files for working patterns"
    echo ""
    echo "📋 Working curl commands:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "successful_cross_site_lcvkwt_$i.json" ]; then
            echo ""
            echo "# Working Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat successful_cross_site_lcvkwt_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
else
    echo ""
    echo "❌ Still no 204 success responses found"
    echo "🔄 Need to test with actual Flask service modification..."
fi

echo ""
echo "🎯 NEXT: If still no success, we need to modify the Flask service"
echo "     to use destination site context in the top-level record"
