#!/usr/bin/env python3
"""
Debug script to check task data structure
"""

import requests
import json
import sys

def debug_task_data():
    """Debug the task data structure from the API"""
    
    # Test with a known work order
    wonum = "2219753"  # Parent work order
    url = f"http://127.0.0.1:5010/api/workorder/{wonum}/tasks"
    
    print(f"🔍 Debugging task data for work order: {wonum}")
    print(f"🔗 URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Response Status: {response.status_code}")
            print(f"📋 Response Keys: {list(data.keys())}")
            
            if 'tasks' in data and data['tasks']:
                tasks = data['tasks']
                print(f"📊 Number of tasks: {len(tasks)}")
                
                # Examine the first task in detail
                if len(tasks) > 0:
                    first_task = tasks[0]
                    print(f"\n🔍 First Task Structure:")
                    print(f"📋 Task Keys: {list(first_task.keys())}")
                    
                    # Check critical fields
                    critical_fields = ['wonum', 'taskid', 'siteid', 'parent']
                    print(f"\n🎯 Critical Fields:")
                    for field in critical_fields:
                        value = first_task.get(field, 'MISSING')
                        print(f"  - {field}: {value} (type: {type(value)})")
                    
                    # Show full task data
                    print(f"\n📄 Full First Task Data:")
                    print(json.dumps(first_task, indent=2))
                    
                    # Check if any critical fields are None or empty
                    missing_fields = []
                    for field in critical_fields:
                        value = first_task.get(field)
                        if value is None or value == '' or value == 'null':
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"\n❌ Missing or empty critical fields: {missing_fields}")
                    else:
                        print(f"\n✅ All critical fields present")
                        
                    # Test the template data structure
                    print(f"\n🧪 Template Data Attributes Test:")
                    print(f"data-task-id=\"{first_task.get('taskid', 'MISSING')}\"")
                    print(f"data-site-id=\"{first_task.get('siteid', 'MISSING')}\"")
                    print(f"data-task-wonum=\"{first_task.get('wonum', 'MISSING')}\"")
                    print(f"data-parent-wonum=\"{first_task.get('parent', first_task.get('wonum', 'MISSING'))}\"")
                
                # Check all tasks for consistency
                print(f"\n📊 All Tasks Summary:")
                for i, task in enumerate(tasks):
                    wonum = task.get('wonum', 'MISSING')
                    taskid = task.get('taskid', 'MISSING')
                    siteid = task.get('siteid', 'MISSING')
                    parent = task.get('parent', 'MISSING')
                    print(f"  Task {i+1}: {wonum} (ID: {taskid}, Site: {siteid}, Parent: {parent})")
                    
            else:
                print(f"❌ No tasks found in response")
                print(f"📄 Full Response: {json.dumps(data, indent=2)}")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON Decode Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

def test_workorder_detail_page():
    """Test the actual workorder detail page"""
    
    wonum = "2219753"
    url = f"http://127.0.0.1:5010/workorder/{wonum}"
    
    print(f"\n🌐 Testing workorder detail page: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            html_content = response.text
            
            # Look for the tasks data JSON
            if 'id="tasksData"' in html_content:
                print(f"✅ Found tasksData script tag")
                
                # Extract the JSON data
                start_marker = 'id="tasksData">'
                end_marker = '</script>'
                
                start_idx = html_content.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = html_content.find(end_marker, start_idx)
                    
                    if end_idx != -1:
                        json_data = html_content[start_idx:end_idx].strip()
                        
                        try:
                            tasks_data = json.loads(json_data)
                            print(f"📊 Tasks data parsed successfully: {len(tasks_data)} tasks")
                            
                            if len(tasks_data) > 0:
                                first_task = tasks_data[0]
                                print(f"🔍 First task from HTML:")
                                critical_fields = ['wonum', 'taskid', 'siteid', 'parent']
                                for field in critical_fields:
                                    value = first_task.get(field, 'MISSING')
                                    print(f"  - {field}: {value}")
                                    
                        except json.JSONDecodeError as e:
                            print(f"❌ Failed to parse tasks JSON: {e}")
                            print(f"📄 Raw JSON data: {json_data[:200]}...")
                            
            else:
                print(f"❌ No tasksData script tag found")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing workorder detail page: {e}")

if __name__ == "__main__":
    print("🐛 Task Data Debug Script")
    print("=" * 50)
    
    debug_task_data()
    test_workorder_detail_page()
    
    print("\n" + "=" * 50)
    print("🏁 Debug complete")
