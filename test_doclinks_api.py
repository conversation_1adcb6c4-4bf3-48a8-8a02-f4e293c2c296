#!/usr/bin/env python3
"""
Test script to examine what MXAPIWODETAIL/DOCLINKS actually returns from Maximo.
"""
import os
import sys
import json
import requests
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get API key and base URL from environment variables
API_KEY = os.getenv('MAXIMO_API_KEY')
BASE_URL = os.getenv('MAXIMO_BASE_URL', 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')

def test_doclinks_api():
    """Test the MXAPIWODETAIL/DOCLINKS API to see what it actually returns."""
    
    if not API_KEY:
        print("MAXIMO_API_KEY not found in .env file")
        return
    
    # First, get a work order that might have attachments
    print("🔍 Step 1: Finding work orders...")
    endpoint = f"{BASE_URL}/oslc/os/mxapiwodetail"
    
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    # Get username from environment if available
    username = os.getenv('MAXIMO_USERNAME', '')
    if username:
        headers["x-user-context"] = username
    
    # Query for work orders
    params = {
        "oslc.select": "wonum,siteid,doclinks",
        "oslc.pageSize": "10",
        "oslc.where": 'siteid="LCVKWT"'  # Use a known site
    }
    
    try:
        response = requests.get(endpoint, params=params, headers=headers, timeout=30)
        print(f"Work orders query status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            work_orders = data.get('member', [])
            print(f"Found {len(work_orders)} work orders")
            
            # Look for work orders with doclinks
            for wo in work_orders:
                wonum = wo.get('wonum')
                doclinks = wo.get('doclinks')
                
                print(f"\n📋 Work Order: {wonum}")
                print(f"   Doclinks: {doclinks}")
                
                if doclinks and isinstance(doclinks, dict) and 'rdf:resource' in doclinks:
                    doclinks_url = doclinks['rdf:resource']
                    print(f"   Doclinks URL: {doclinks_url}")
                    
                    # Query the doclinks collection
                    print(f"\n🔍 Step 2: Querying doclinks for {wonum}...")
                    doclinks_response = requests.get(doclinks_url, headers=headers, timeout=30)
                    print(f"   Doclinks query status: {doclinks_response.status_code}")
                    
                    if doclinks_response.status_code == 200:
                        doclinks_data = doclinks_response.json()
                        print(f"   Doclinks response keys: {list(doclinks_data.keys())}")
                        
                        attachments = doclinks_data.get('member', [])
                        print(f"   Found {len(attachments)} attachment(s)")
                        
                        if attachments:
                            print(f"\n📎 First attachment details:")
                            first_attachment = attachments[0]
                            for key, value in first_attachment.items():
                                print(f"     {key}: {value}")
                            
                            # Save the response for analysis
                            with open(f'doclinks_response_{wonum}.json', 'w') as f:
                                json.dump(doclinks_data, f, indent=2)
                            print(f"   Saved response to doclinks_response_{wonum}.json")
                            
                            return  # Exit after finding first work order with attachments
                    else:
                        print(f"   Error querying doclinks: {doclinks_response.text[:200]}")
                
                elif doclinks:
                    print(f"   Doclinks format: {type(doclinks)} - {doclinks}")
        else:
            print(f"Error querying work orders: {response.status_code}")
            print(response.text[:500])
            
    except Exception as e:
        print(f"Error: {str(e)}")

def test_specific_workorder(wonum):
    """Test a specific work order for doclinks."""
    print(f"\n🔍 Testing specific work order: {wonum}")
    
    endpoint = f"{BASE_URL}/oslc/os/mxapiwodetail"
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    username = os.getenv('MAXIMO_USERNAME', '')
    if username:
        headers["x-user-context"] = username
    
    params = {
        "oslc.select": "wonum,siteid,doclinks",
        "oslc.where": f'wonum="{wonum}"',
        "oslc.pageSize": "1"
    }
    
    try:
        response = requests.get(endpoint, params=params, headers=headers, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'member' in data and data['member']:
                wo_data = data['member'][0]
                doclinks = wo_data.get('doclinks')
                print(f"Doclinks: {doclinks}")
                
                if doclinks and isinstance(doclinks, dict) and 'rdf:resource' in doclinks:
                    doclinks_url = doclinks['rdf:resource']
                    print(f"Querying: {doclinks_url}")
                    
                    doclinks_response = requests.get(doclinks_url, headers=headers, timeout=30)
                    print(f"Doclinks status: {doclinks_response.status_code}")
                    
                    if doclinks_response.status_code == 200:
                        doclinks_data = doclinks_response.json()
                        print(f"Response: {json.dumps(doclinks_data, indent=2)}")
                    else:
                        print(f"Error: {doclinks_response.text}")
            else:
                print("Work order not found")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == '__main__':
    print("🧪 Testing MXAPIWODETAIL/DOCLINKS API")
    print("=" * 50)
    
    # Test general doclinks discovery
    test_doclinks_api()
    
    # Test specific work order if provided
    if len(sys.argv) > 1:
        wonum = sys.argv[1]
        test_specific_workorder(wonum)
    
    print("\n✅ Test completed")
