#!/usr/bin/env python3
"""
Debug the reservations API to understand why no reservations are found
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add backend path for imports
sys.path.append('backend/auth')

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError as e:
    print(f"❌ Cannot import MaximoTokenManager: {e}")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
TARGET_ITEM = "8010-60-V00-0113"
TARGET_SITE = "LCVKWT"

def test_reservations_api_variations():
    """Test different variations of the reservations API query."""
    print("🔍 Testing MXAPIINVRES API Variations")
    print("=" * 60)
    
    api_key = os.environ.get('MAXIMO_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return
    
    api_url = f"{BASE_URL}/api/os/mxapiinvres"
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    # Test 1: Basic query - just item number
    print("\n📋 Test 1: Basic query (itemnum only)")
    where_clause_1 = f'itemnum="{TARGET_ITEM}"'
    params_1 = {
        "oslc.select": "requestnum,reservationtype,itemnum,itemtype,description,storeroom,conditioncode,reservedqty,wonum,taskid,tostoreroom,tosite,ponum,requireddate",
        "oslc.where": where_clause_1,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    print(f"Where clause: {where_clause_1}")
    test_api_call(api_url, params_1, headers, "Test 1")
    
    # Test 2: Item + Site
    print("\n📋 Test 2: Item + Site")
    where_clause_2 = f'itemnum="{TARGET_ITEM}" and siteid="{TARGET_SITE}"'
    params_2 = {
        "oslc.select": "requestnum,reservationtype,itemnum,itemtype,description,storeroom,conditioncode,reservedqty,wonum,taskid,tostoreroom,tosite,ponum,requireddate,siteid",
        "oslc.where": where_clause_2,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    print(f"Where clause: {where_clause_2}")
    test_api_call(api_url, params_2, headers, "Test 2")
    
    # Test 3: Just storeroom filter
    print("\n📋 Test 3: Storeroom filter")
    where_clause_3 = f'storeroom="{TARGET_SITE}"'
    params_3 = {
        "oslc.select": "requestnum,reservationtype,itemnum,itemtype,description,storeroom,conditioncode,reservedqty,wonum,taskid,tostoreroom,tosite,ponum,requireddate",
        "oslc.where": where_clause_3,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    print(f"Where clause: {where_clause_3}")
    test_api_call(api_url, params_3, headers, "Test 3")
    
    # Test 4: No filters - get all reservations (limited)
    print("\n📋 Test 4: All reservations (first 10)")
    params_4 = {
        "oslc.select": "requestnum,reservationtype,itemnum,itemtype,description,storeroom,conditioncode,reservedqty,wonum,taskid,tostoreroom,tosite,ponum,requireddate",
        "oslc.pageSize": "10",
        "lean": "1"
    }
    
    print("Where clause: (none - all records)")
    test_api_call(api_url, params_4, headers, "Test 4")

def test_api_call(api_url, params, headers, test_name):
    """Helper function to test API calls."""
    try:
        response = requests.get(api_url, params=params, headers=headers, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                reservations = data.get('member', [])
                print(f"Reservations found: {len(reservations)}")
                
                if reservations:
                    print(f"Sample reservation data:")
                    for i, res in enumerate(reservations[:3], 1):
                        print(f"  {i}. Request: {res.get('requestnum', 'N/A')}")
                        print(f"     Item: {res.get('itemnum', 'N/A')}")
                        print(f"     Storeroom: {res.get('storeroom', 'N/A')}")
                        print(f"     Reserved Qty: {res.get('reservedqty', 'N/A')}")
                        print(f"     Type: {res.get('reservationtype', 'N/A')}")
                        
                    # Check if our target item is in the results
                    target_items = [r for r in reservations if r.get('itemnum') == TARGET_ITEM]
                    if target_items:
                        print(f"  ✅ Found {len(target_items)} reservations for target item {TARGET_ITEM}")
                    else:
                        print(f"  ❌ No reservations found for target item {TARGET_ITEM}")
                        
                else:
                    print("  No reservations found")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text[:200]}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception in {test_name}: {e}")

def test_flask_reservations():
    """Test what the Flask app returns for reservations."""
    print("\n🔍 Testing Flask App Reservations")
    print("=" * 60)
    
    try:
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/availability/{TARGET_ITEM}",
            params={'siteid': TARGET_SITE},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                reservations = data.get('reservations', [])
                print(f"Flask app returned {len(reservations)} reservations")
                
                if reservations:
                    print("Reservation data from Flask:")
                    for i, res in enumerate(reservations[:3], 1):
                        print(f"  {i}. Request: {res.get('request_num')}")
                        print(f"     Item: {res.get('item_num')}")
                        print(f"     Storeroom: {res.get('storeroom')}")
                        print(f"     Reserved Qty: {res.get('reserved_quantity')}")
                else:
                    print("No reservations returned by Flask app")
                    
                # Check inventory records for locations
                inventory_records = data.get('inventory_records', [])
                print(f"\nInventory records: {len(inventory_records)}")
                locations = []
                for record in inventory_records:
                    if 'invbalances' in record and isinstance(record['invbalances'], list):
                        for balance in record['invbalances']:
                            location = balance.get('location')
                            if location and location not in locations:
                                locations.append(location)
                
                print(f"Locations extracted for reservations query: {locations}")
                
            else:
                print(f"Flask error: {data.get('error')}")
        else:
            print(f"HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    print(f"🚀 Debugging Reservations API for Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_reservations_api_variations()
    test_flask_reservations()
    
    print(f"\n{'='*60}")
    print("🏁 Debug Complete")
    print(f"{'='*60}")
