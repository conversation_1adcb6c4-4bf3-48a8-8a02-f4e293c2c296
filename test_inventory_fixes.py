#!/usr/bin/env python3
"""
Test script to verify the three inventory management interface fixes:
1. Balance toggle functionality
2. Enhanced inventory section data
3. Cost data section improvements
"""
import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_ITEM = "5975-60-V00-0529"
TEST_SITE = "LCVKWT"

def test_inventory_data_enhancement():
    """Test that enhanced inventory fields are available"""
    print("🔧 Testing Enhanced Inventory Section Data")
    print("=" * 50)
    
    try:
        # Get inventory data from the search API
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item = data['items'][0]
                
                print(f"✅ Found inventory item: {item.get('itemnum')}")
                
                # Check for enhanced inventory fields
                enhanced_fields = {
                    'siteid': item.get('siteid'),
                    'itemsetid': item.get('itemsetid'),
                    'issueunit': item.get('issueunit'),
                    'orderunit': item.get('orderunit'),
                    'abctype': item.get('abctype'),
                    'glaccount': item.get('glaccount'),
                    'vendor': item.get('vendor'),
                    'manufacturer': item.get('manufacturer'),
                    'modelnum': item.get('modelnum'),
                    'lottype': item.get('lottype')
                }
                
                print(f"\n🔍 Enhanced inventory fields:")
                available_fields = 0
                for field, value in enhanced_fields.items():
                    if value is not None and value != '':
                        status = "✅"
                        available_fields += 1
                    else:
                        status = "❌"
                    print(f"  {status} {field}: {value}")
                
                print(f"\n📊 Summary: {available_fields}/{len(enhanced_fields)} enhanced fields available")
                
                # Test original fields are still present
                original_fields = {
                    'location': item.get('location'),
                    'curbaltotal': item.get('curbaltotal'),
                    'avblbalance': item.get('avblbalance')
                }
                
                print(f"\n🔍 Original inventory fields (should still be present):")
                original_available = 0
                for field, value in original_fields.items():
                    if value is not None:
                        status = "✅"
                        original_available += 1
                    else:
                        status = "❌"
                    print(f"  {status} {field}: {value}")
                
                return available_fields > 0 and original_available > 0, item
            else:
                print(f"❌ No inventory items found")
                return False, None
        else:
            print(f"❌ API Error: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False, None

def test_cost_data_improvements():
    """Test cost data section improvements"""
    print("\n🔧 Testing Cost Data Section Improvements")
    print("=" * 50)
    
    try:
        # Get inventory data from the search API
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item = data['items'][0]
                
                print(f"✅ Found inventory item: {item.get('itemnum')}")
                
                # Check for cost fields including the new conditionrate field
                cost_fields = {
                    'avgcost': item.get('avgcost'),
                    'stdcost': item.get('stdcost'),
                    'lastcost': item.get('lastcost'),
                    'unitcost': item.get('unitcost'),
                    'conditioncode': item.get('conditioncode'),
                    'orgid': item.get('orgid'),
                    'invcostid': item.get('invcostid'),
                    'condrate': item.get('condrate'),
                    'conditionrate': item.get('conditionrate')  # New field
                }
                
                print(f"\n🔍 Cost fields (including new conditionrate):")
                available_fields = 0
                for field, value in cost_fields.items():
                    if value is not None and value != '':
                        status = "✅"
                        available_fields += 1
                        if field == 'conditionrate':
                            print(f"  {status} {field}: {value} (NEW FIELD)")
                        else:
                            print(f"  {status} {field}: {value}")
                    else:
                        status = "❌"
                        if field == 'conditionrate':
                            print(f"  {status} {field}: Not available (NEW FIELD)")
                        else:
                            print(f"  {status} {field}: Not available")
                
                print(f"\n📊 Summary: {available_fields}/{len(cost_fields)} cost fields available")
                
                # Test that conditionrate field is properly defined
                has_conditionrate = item.get('conditionrate') is not None
                print(f"\n🔍 New conditionrate field test:")
                if has_conditionrate:
                    print(f"  ✅ conditionrate field is available: {item.get('conditionrate')}")
                else:
                    print(f"  ⚠️  conditionrate field not available in this item (may be normal)")
                
                return available_fields > 0, item
            else:
                print(f"❌ No inventory items found")
                return False, None
        else:
            print(f"❌ API Error: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False, None

def test_ui_integration():
    """Test UI integration and page loading"""
    print("\n🔧 Testing UI Integration")
    print("=" * 50)
    
    try:
        # Test if the inventory management page loads
        response = requests.get(f"{BASE_URL}/inventory-management", timeout=30)
        
        if response.status_code == 200:
            print("✅ Inventory management page loads successfully")
            
            # Check if the page contains the expected updates
            content = response.text
            
            checks = [
                ('inventory_management.js', 'JavaScript file included'),
                ('style.css', 'CSS file included'),
                ('cost-data-section', 'Cost data section CSS class'),
                ('inventory-field', 'Inventory field CSS class'),
                ('balance-toggle-btn', 'Balance toggle button class'),
                ('generateCostDataSection', 'Cost data generation function'),
                ('handleBalanceToggle', 'Balance toggle handler function'),
            ]
            
            print(f"\n🔍 Page content checks:")
            all_checks_passed = True
            
            for check_item, description in checks:
                if check_item in content:
                    print(f"  ✅ {description}")
                else:
                    print(f"  ❌ {description}")
                    all_checks_passed = False
            
            return all_checks_passed
        else:
            print(f"❌ Page load failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_css_improvements():
    """Test CSS improvements for contrast"""
    print("\n🔧 Testing CSS Contrast Improvements")
    print("=" * 50)
    
    try:
        # Test if the CSS file loads and contains the improved styles
        response = requests.get(f"{BASE_URL}/static/css/style.css", timeout=30)
        
        if response.status_code == 200:
            print("✅ CSS file loads successfully")
            
            css_content = response.text
            
            # Check for improved contrast styles
            contrast_checks = [
                ('cost-data-table .table th', 'Cost table header styling'),
                ('background-color: var(--primary-color)', 'Primary color background'),
                ('color: white', 'White text for contrast'),
                ('mobile-cost-item', 'Mobile cost item styling'),
                ('box-shadow:', 'Box shadow for depth'),
                ('[data-bs-theme="dark"]', 'Dark theme support'),
            ]
            
            print(f"\n🔍 CSS contrast improvement checks:")
            all_checks_passed = True
            
            for check_item, description in contrast_checks:
                if check_item in css_content:
                    print(f"  ✅ {description}")
                else:
                    print(f"  ❌ {description}")
                    all_checks_passed = False
            
            return all_checks_passed
        else:
            print(f"❌ CSS load failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 INVENTORY MANAGEMENT INTERFACE FIXES TEST")
    print("=" * 60)
    print(f"🎯 Target Item: {TEST_ITEM}")
    print(f"🏢 Target Site: {TEST_SITE}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    results = []
    
    # Test 1: Enhanced inventory section data
    inventory_ok, item_data = test_inventory_data_enhancement()
    results.append(("Enhanced Inventory Section Data", inventory_ok))
    
    # Test 2: Cost data improvements
    cost_ok, cost_item_data = test_cost_data_improvements()
    results.append(("Cost Data Section Improvements", cost_ok))
    
    # Test 3: UI integration
    ui_ok = test_ui_integration()
    results.append(("UI Integration", ui_ok))
    
    # Test 4: CSS improvements
    css_ok = test_css_improvements()
    results.append(("CSS Contrast Improvements", css_ok))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if item_data:
        print(f"\n📋 Sample Data for {item_data.get('itemnum')}:")
        print(f"  🏢 Site ID: {item_data.get('siteid')}")
        print(f"  📦 Item Set: {item_data.get('itemsetid')}")
        print(f"  📍 Location: {item_data.get('location')}")
        print(f"  💰 Average Cost: ${item_data.get('avgcost', 0)}")
        print(f"  💰 Standard Cost: ${item_data.get('stdcost', 0)}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! All three issues have been resolved:")
        print("   ✅ Issue 1: Balance toggle functionality - FIXED")
        print("   ✅ Issue 2: Enhanced inventory section data - IMPLEMENTED")
        print("   ✅ Issue 3: Cost data section improvements - FIXED")
        print("\n📝 Manual Testing Steps:")
        print("   1. Navigate to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for an inventory item")
        print("   3. Test 'Show Balances' and 'Hide Balances' toggle functionality")
        print("   4. Verify enhanced inventory fields are displayed")
        print("   5. Check cost data section has improved contrast and conditionrate field")
        print("   6. Test responsive design on mobile and desktop")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
