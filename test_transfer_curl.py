#!/usr/bin/env python3
"""
Extract authentication details and create curl command for testing Transfer Current Item
"""

import requests
import json
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configuration
BASE_URL = "http://127.0.0.1:5010"
MAXIMO_BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def extract_session_cookies():
    """Extract session cookies from the running Flask application"""
    print("🔍 Extracting session cookies from Flask application...")
    
    try:
        # Try to get auth status to see if user is logged in
        response = requests.get(f"{BASE_URL}/api/auth-status")
        print(f"Auth status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"User logged in: {data.get('logged_in', False)}")
            print(f"Username: {data.get('username', 'Unknown')}")
            
            # Get session cookies
            session_cookies = response.cookies
            print(f"Session cookies: {dict(session_cookies)}")
            
            return session_cookies
        else:
            print("❌ User not logged in or Flask app not running")
            return None
            
    except Exception as e:
        print(f"❌ Error extracting session: {e}")
        return None

def create_curl_command():
    """Create curl command for testing transfer"""
    
    # Object Structure payload from terminal logs
    payload = {
        "itemnum": "5975-60-V00-0529",
        "siteid": "LCVKWT",
        "location": "RIP001",
        "transfercuritem": [
            {
                "itemnum": "5975-60-V00-0529",
                "fromsiteid": "LCVKWT",
                "tositeid": "IKWAJ",
                "fromlocation": "RIP001",
                "tolocation": "KWAJ-1058",
                "quantity": 1.0,
                "fromissueunit": "RO",
                "toissueunit": "RO",
                "frombinnum": "28-800-0004",
                "fromlotnum": "TEST",
                "tolotnum": "TEST",
                "fromconditioncode": "A1",
                "toconditioncode": "A1"
            }
        ]
    }
    
    # OSLC endpoint
    endpoint = f"{MAXIMO_BASE_URL}/oslc/os/mxapiinventory"
    
    # Create curl command
    curl_command = f"""curl -X POST '{endpoint}' \\
  -H 'Accept: application/json' \\
  -H 'Content-Type: application/json' \\
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36' \\
  --cookie-jar cookies.txt \\
  --cookie cookies.txt \\
  -d '{json.dumps(payload, indent=2)}'"""
    
    print("\n🔧 CURL Command for Object Structure Transfer:")
    print("=" * 80)
    print(curl_command)
    print("=" * 80)
    
    return curl_command, payload

def test_alternative_approaches():
    """Test alternative approaches based on IBM Community research"""
    
    print("\n🔍 Alternative Approaches to Test:")
    print("=" * 50)
    
    # Approach 1: Use POST to create transfer record directly
    print("\n1️⃣ Direct Transfer Record Creation:")
    transfer_payload = {
        "itemnum": "5975-60-V00-0529",
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ", 
        "fromlocation": "RIP001",
        "tolocation": "KWAJ-1058",
        "quantity": 1.0,
        "fromissueunit": "RO",
        "toissueunit": "RO",
        "frombinnum": "28-800-0004",
        "fromlotnum": "TEST",
        "tolotnum": "TEST",
        "fromconditioncode": "A1",
        "toconditioncode": "A1"
    }
    
    transfer_endpoint = f"{MAXIMO_BASE_URL}/oslc/os/mxapitransfer"
    print(f"Endpoint: {transfer_endpoint}")
    print(f"Payload: {json.dumps(transfer_payload, indent=2)}")
    
    # Approach 2: Use MXAPIINVTRANS (Inventory Transaction) endpoint
    print("\n2️⃣ Inventory Transaction Approach:")
    invtrans_endpoint = f"{MAXIMO_BASE_URL}/oslc/os/mxapiinvtrans"
    print(f"Endpoint: {invtrans_endpoint}")
    
    # Approach 3: Use action-based approach with proper action parameter
    print("\n3️⃣ Action-Based Approach:")
    action_endpoint = f"{MAXIMO_BASE_URL}/oslc/os/mxapiinventory?action=TransferCurrentItem"
    print(f"Endpoint: {action_endpoint}")
    
    return [
        (transfer_endpoint, transfer_payload),
        (invtrans_endpoint, transfer_payload),
        (action_endpoint, transfer_payload)
    ]

def create_test_script():
    """Create a comprehensive test script"""
    
    print("\n📝 Creating comprehensive test script...")
    
    test_script = '''#!/bin/bash

# Transfer Current Item Test Script
# Based on IBM Community research and terminal analysis

echo "🚀 Testing Transfer Current Item with Multiple Approaches"
echo "========================================================"

# Base configuration
MAXIMO_BASE="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
HEADERS="-H 'Accept: application/json' -H 'Content-Type: application/json'"

# Test payload from terminal logs
PAYLOAD='{
  "itemnum": "5975-60-V00-0529",
  "fromsiteid": "LCVKWT",
  "tositeid": "IKWAJ",
  "fromlocation": "RIP001",
  "tolocation": "KWAJ-1058",
  "quantity": 1.0,
  "fromissueunit": "RO",
  "toissueunit": "RO",
  "frombinnum": "28-800-0004",
  "fromlotnum": "TEST",
  "tolotnum": "TEST",
  "fromconditioncode": "A1",
  "toconditioncode": "A1"
}'

echo "\\n1️⃣ Testing Object Structure Approach (Current Implementation)"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinventory"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory" \\
  $HEADERS \\
  --cookie-jar cookies.txt --cookie cookies.txt \\
  -d "$PAYLOAD" \\
  -w "\\nStatus: %{http_code}\\n" \\
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\\n2️⃣ Testing Transfer Endpoint"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapitransfer"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapitransfer" \\
  $HEADERS \\
  --cookie-jar cookies.txt --cookie cookies.txt \\
  -d "$PAYLOAD" \\
  -w "\\nStatus: %{http_code}\\n" \\
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\\n3️⃣ Testing Inventory Transaction Endpoint"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinvtrans"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinvtrans" \\
  $HEADERS \\
  --cookie-jar cookies.txt --cookie cookies.txt \\
  -d "$PAYLOAD" \\
  -w "\\nStatus: %{http_code}\\n" \\
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\\n4️⃣ Testing Action Parameter Approach"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem" \\
  $HEADERS \\
  --cookie-jar cookies.txt --cookie cookies.txt \\
  -d "$PAYLOAD" \\
  -w "\\nStatus: %{http_code}\\n" \\
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\\n✅ Test completed!"
'''
    
    with open('test_transfer_approaches.sh', 'w') as f:
        f.write(test_script)
    
    os.chmod('test_transfer_approaches.sh', 0o755)
    print("✅ Created test_transfer_approaches.sh")
    
    return 'test_transfer_approaches.sh'

if __name__ == "__main__":
    print("🔧 Transfer Current Item Curl Test Generator")
    print("=" * 60)
    
    # Extract session cookies
    cookies = extract_session_cookies()
    
    # Create curl command
    curl_cmd, payload = create_curl_command()
    
    # Test alternative approaches
    alternatives = test_alternative_approaches()
    
    # Create comprehensive test script
    test_script = create_test_script()
    
    print(f"\\n📋 Next Steps:")
    print("1. Ensure you're logged into the Flask app (http://127.0.0.1:5010)")
    print("2. Run the test script: ./test_transfer_approaches.sh")
    print("3. Check which endpoint returns success vs errors")
    print("4. Analyze the responses to determine correct approach")
    
    print(f"\\n🎯 Key Issue to Resolve:")
    print("Current error: BMXAA4129E - Record already exists")
    print("This suggests Maximo is trying to CREATE inventory instead of TRANSFER")
    print("Need to find the correct endpoint/method for transfer operations")
