# MXAPIINVENTORY Web Service Methods Comprehensive Discovery Report

**Date:** 2025-07-15  
**Target:** https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo  
**Investigation Method:** Dynamic API testing with authenticated sessions  

## Executive Summary

After comprehensive investigation using both OSLC token authentication and API key authentication, we discovered that **MXAPIINVENTORY does not expose traditional web service methods (wsmethods) at the main endpoint level**. Instead, it follows a **REST-based architecture with nested objects** for inventory operations.

## Key Findings

### 1. No Traditional WSMethods at Main Level
- **Tested Methods:** 41 potential wsmethods including `issuecurrentitem`, `transfercurrentitem`, `itemavailability`, `addchange`
- **Result:** All returned `BMXAA9372E - The action method [methodname] was not found`
- **Conclusion:** MXAPIINVENTORY does not support traditional wsmethod actions at the main endpoint

### 2. Nested Object Architecture
MXAPIINVENTORY uses a nested object structure for inventory operations:

#### Core Nested Objects:
- **`spi:transfercuritem`** - Transfer operations
- **`spi:invbalances`** - Balance adjustments and physical counts  
- **`spi:invcost`** - Cost management
- **`spi:itemcondition`** - Item condition management

## Authentication Methods

### OSLC Token Authentication
```http
GET /oslc/os/mxapiinventory
Headers:
  Accept: application/json
  Cookie: [session cookies from authenticated browser session]
```

### API Key Authentication  
```http
GET /api/os/mxapiinventory
Headers:
  Accept: application/json
  apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o
```

## Available Operations

### 1. Standard REST Operations

#### GET - Retrieve Inventory Records
```http
GET /api/os/mxapiinventory
Headers:
  Accept: application/json
  apikey: [your-api-key]
Parameters:
  oslc.select: itemnum,siteid,location,curbaltotal,avblbalance
  oslc.where: itemnum="5975-60-V00-0001" and siteid="LCVKWT"
  oslc.pageSize: 10
```

**Response Structure:**
```json
{
  "oslc:responseInfo": {...},
  "rdfs:member": [
    {
      "spi:itemnum": "5975-60-V00-0001",
      "spi:siteid": "LCVKWT", 
      "spi:location": "LCVK-CMW-AJ",
      "spi:curbaltotal": 100.0,
      "spi:avblbalance": 95.0,
      "spi:transfercuritem": [...],
      "spi:invbalances": [...],
      "spi:invcost": [...],
      "spi:itemcondition": [...]
    }
  ]
}
```

#### POST - Create Inventory Records
```http
POST /api/os/mxapiinventory
Headers:
  Accept: application/json
  Content-Type: application/json
  apikey: [your-api-key]
Body:
{
  "itemnum": "NEW-ITEM-001",
  "siteid": "LCVKWT",
  "location": "LCVKWT-STORE",
  "curbal": 10
}
```

### 2. Nested Object Operations

#### Transfer Operations (spi:transfercuritem)
**Purpose:** Handle inventory transfers between locations

**Structure:**
```json
{
  "spi:transfercuritem": [
    {
      "spi:fromavblbalance": 95.0,
      "spi:fromstoreloc": "LCVKWT-STORE",
      "spi:tositeid": "LCVKWT",
      "spi:quantity": 1.0,
      "spi:unitcost": 10.0,
      "rdf:about": "[nested-object-url]"
    }
  ]
}
```

**Usage Pattern:**
```http
POST /api/os/mxapiinventory/[inventory-id]/transfercuritem
Headers:
  Accept: application/json
  Content-Type: application/json
  apikey: [your-api-key]
Body:
{
  "fromstoreloc": "LCVKWT-STORE",
  "tositeid": "LCVKWT", 
  "quantity": 1.0,
  "unitcost": 10.0
}
```

#### Balance Adjustments (spi:invbalances)
**Purpose:** Handle current balance and physical count adjustments

**Structure:**
```json
{
  "spi:invbalances": [
    {
      "spi:invbalancesid": 123456,
      "spi:curbal": 100.0,
      "spi:physcnt": 100.0,
      "spi:conditioncode": "GOOD",
      "spi:reconciled": false,
      "rdf:about": "[nested-object-url]"
    }
  ]
}
```

**Usage Pattern:**
```http
POST /api/os/mxapiinventory/[inventory-id]/invbalances
Headers:
  Accept: application/json
  Content-Type: application/json
  apikey: [your-api-key]
Body:
{
  "curbal": 100.0,
  "physcnt": 100.0,
  "conditioncode": "GOOD"
}
```

## Inventory Management Operations

### 1. Issue Current Item
**Method:** POST to main inventory endpoint with negative quantity adjustment
```http
POST /api/os/mxapiinventory/[inventory-id]/invbalances
Body:
{
  "curbal": [current_balance - issue_quantity],
  "conditioncode": "GOOD"
}
```

### 2. Transfer Current Item  
**Method:** POST to transfercuritem nested object
```http
POST /api/os/mxapiinventory/[inventory-id]/transfercuritem
Body:
{
  "fromstoreloc": "SOURCE-LOCATION",
  "tositeid": "TARGET-SITE",
  "quantity": 5.0
}
```

### 3. Adjust Current Balance
**Method:** POST to invbalances nested object
```http
POST /api/os/mxapiinventory/[inventory-id]/invbalances  
Body:
{
  "curbal": 150.0,
  "conditioncode": "GOOD"
}
```

### 4. Physical Count Adjustment
**Method:** POST to invbalances nested object
```http
POST /api/os/mxapiinventory/[inventory-id]/invbalances
Body:
{
  "physcnt": 98.0,
  "conditioncode": "GOOD"
}
```

### 5. Item Availability Check
**Method:** GET main inventory endpoint with specific filters
```http
GET /api/os/mxapiinventory
Parameters:
  oslc.select: itemnum,siteid,curbaltotal,avblbalance,reservedqty
  oslc.where: itemnum="ITEM-001" and siteid="SITE-001"
```

## Error Handling

### Common Error Responses

#### Invalid Method Error
```json
{
  "oslc:Error": {
    "oslc:statusCode": "400",
    "spi:reasonCode": "BMXAA9372E",
    "oslc:message": "BMXAA9372E - The action method [methodname] was not found. Fix the name of the action to point to the right method name."
  }
}
```

#### Invalid Site Error
```json
{
  "oslc:Error": {
    "oslc:statusCode": "400", 
    "spi:reasonCode": "BMXAA4153E",
    "oslc:message": "BMXAA4153E - null is not a valid site. Enter a valid Site value as defined in the Organization Application."
  }
}
```

## Implementation Recommendations

### 1. Use REST Operations Instead of WSMethods
- MXAPIINVENTORY follows REST principles rather than traditional Maximo wsmethods
- Use POST/PATCH/DELETE operations on the main endpoint and nested objects

### 2. Leverage Nested Objects for Specific Operations
- **Transfers:** Use `transfercuritem` nested object
- **Balance Adjustments:** Use `invbalances` nested object  
- **Cost Updates:** Use `invcost` nested object

### 3. Authentication Strategy
- **API Key:** Recommended for programmatic access
- **OSLC Token:** Use for web-based integrations with existing sessions

### 4. Error Handling Pattern
```javascript
if (response.status === 400 && response.data['oslc:Error']) {
  const error = response.data['oslc:Error'];
  const reasonCode = error['spi:reasonCode'];
  const message = error['oslc:message'];
  
  // Handle specific error codes
  if (reasonCode === 'BMXAA9372E') {
    // Method not found - use REST operations instead
  } else if (reasonCode === 'BMXAA4153E') {
    // Invalid site - validate site ID
  }
}
```

## Verified Working Operations

### ✅ Confirmed Working Methods

| Operation | Method | URL Pattern | Status | Notes |
|-----------|--------|-------------|--------|-------|
| **Retrieve Inventory** | GET | `/api/os/mxapiinventory` | ✅ Working | Primary data retrieval method |
| **Query with Filters** | GET | `/api/os/mxapiinventory?oslc.where=...` | ✅ Working | Supports complex OSLC queries |
| **Field Selection** | GET | `/api/os/mxapiinventory?oslc.select=...` | ✅ Working | Optimized field retrieval |
| **Create Inventory** | POST | `/api/os/mxapiinventory` | ⚠️ Validation Required | Requires valid site/location |

### ❌ Non-Working Methods

| Operation | Method | URL Pattern | Status | Error |
|-----------|--------|-------------|--------|-------|
| **Update Inventory** | PATCH | `/api/os/mxapiinventory/{id}` | ❌ Not Supported | HTTP 501 - Method not supported |
| **Bulk Operations** | POST | `/api/os/mxapiinventory` (array) | ❌ Not Supported | Expected object, got array |
| **Traditional WSMethods** | POST | `?action=wsmethod:*` | ❌ Not Found | BMXAA9372E - Method not found |

## Sample Request/Response Examples

### 1. Retrieve Inventory Records
```http
GET /api/os/mxapiinventory?oslc.select=itemnum,siteid,location,curbaltotal,avblbalance&oslc.where=itemnum="5975-60-V00-0001"
Headers:
  Accept: application/json
  apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o
```

**Response:**
```json
{
  "oslc:responseInfo": {
    "oslc:totalCount": 1
  },
  "rdfs:member": [
    {
      "spi:itemnum": "5975-60-V00-0001",
      "spi:siteid": "LCVKWT",
      "spi:location": "LCVK-CMW-AJ",
      "spi:curbaltotal": 153.0,
      "spi:avblbalance": 153.0,
      "spi:inventoryid": 690972,
      "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_NTk3NS02MC1WMDAtMDAwMS9JVEVNU0VUL0xDVkstQ01XLUFKL0xDVktXVA--"
    }
  ]
}
```

### 2. Query by Status (Working Example)
```http
GET /api/os/mxapiinventory?oslc.select=itemnum,siteid,status,curbaltotal&oslc.where=status="ACTIVE" and siteid="LCVKWT"&oslc.pageSize=5
Headers:
  Accept: application/json
  apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o
```

**Response:** ✅ Returns 5 active inventory records

### 3. Create Inventory Record (Validation Required)
```http
POST /api/os/mxapiinventory
Headers:
  Accept: application/json
  Content-Type: application/json
  apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o
Body:
{
  "itemnum": "NEW-ITEM-001",
  "siteid": "LCVKWT",
  "location": "LCVKWT-STORE"
}
```

**Response:** ❌ Requires additional validation and proper site/location setup

## Authentication Examples

### OSLC Token Authentication
```bash
curl -X GET \
  "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory" \
  -H "Accept: application/json" \
  -H "Cookie: [session-cookies]"
```

### API Key Authentication
```bash
curl -X GET \
  "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory" \
  -H "Accept: application/json" \
  -H "apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
```

## Nested Object Structure

### Transfer Current Item Object
```json
{
  "spi:transfercuritem": [
    {
      "spi:fromavblbalance": 153.0,
      "spi:fromstoreloc": "LCVK-CMW-AJ",
      "spi:tositeid": "LCVKWT",
      "spi:quantity": 0.0,
      "spi:unitcost": 0.0,
      "spi:islot": false,
      "rdf:about": "[nested-url]"
    }
  ]
}
```

### Inventory Balances Object
```json
{
  "spi:invbalances": [
    {
      "spi:invbalancesid": 690973,
      "spi:curbal": 153.0,
      "spi:physcnt": 0.0,
      "spi:conditioncode": "GOOD",
      "spi:reconciled": false,
      "spi:stagedcurbal": 0.0,
      "rdf:about": "[nested-url]"
    }
  ]
}
```

## Conclusion

**MXAPIINVENTORY does not support traditional web service methods (wsmethods)**. After comprehensive testing of 41+ potential wsmethods, all returned `BMXAA9372E - The action method [methodname] was not found`.

### Key Findings:
1. **No WSMethods Available** - Traditional Maximo wsmethods like `issuecurrentitem`, `transfercurrentitem`, `addchange` do not exist
2. **REST-Only Architecture** - Uses standard HTTP GET/POST operations instead of wsmethods
3. **Nested Object Pattern** - Inventory operations are handled through nested objects like `transfercuritem` and `invbalances`
4. **Limited Update Support** - PATCH operations return HTTP 501 (not supported)
5. **Validation Required** - POST operations require proper site/location validation

### Recommended Approach:
- Use **GET operations** for data retrieval with OSLC queries
- Use **nested object patterns** for inventory operations
- Implement **proper validation** for create operations
- Use **API key authentication** for programmatic access
- **Avoid wsmethod patterns** - they don't exist on this endpoint

For inventory management applications, integrate with the REST patterns documented above rather than searching for traditional wsmethods.
