{"investigation_date": "2025-07-16T10:33:40.287991", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory", "authentication": "OSLC Token Session", "test_payload": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}, "method_results": [{"method": "transfercurrentitem", "timestamp": "2025-07-16T10:33:41.643169", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercurrentitem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}, {"method": "transfercuritem", "timestamp": "2025-07-16T10:33:44.592239", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercuritem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}, {"method": "issuecurrentitem", "timestamp": "2025-07-16T10:33:46.740898", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/issuecurrentitem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}, {"method": "receivecurrentitem", "timestamp": "2025-07-16T10:33:49.203437", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/receivecurrentitem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}], "working_methods": [{"method": "transfercurrentitem", "timestamp": "2025-07-16T10:33:41.643169", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercurrentitem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}, {"method": "transfercuritem", "timestamp": "2025-07-16T10:33:44.592239", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercuritem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}, {"method": "issuecurrentitem", "timestamp": "2025-07-16T10:33:46.740898", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/issuecurrentitem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}, {"method": "receivecurrentitem", "timestamp": "2025-07-16T10:33:49.203437", "status": "working_non_json", "http_status": 200, "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h", "error": null, "url_tested": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/receivecurrentitem", "payload_used": {"itemnum": "5975-60-V00-0529", "fromsiteid": "LCVKWT", "tositeid": "IKWAJ", "fromlocation": "RIP001", "tolocation": "KWAJ-1058", "quantity": 1.0, "fromissueunit": "RO", "toissueunit": "RO", "frombinnum": "28-800-0004", "fromlotnum": "TEST", "tolotnum": "TEST", "fromconditioncode": "A1", "toconditioncode": "A1"}}], "error_methods": []}