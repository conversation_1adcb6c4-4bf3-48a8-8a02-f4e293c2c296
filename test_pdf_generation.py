#!/usr/bin/env python3
"""
Test script to verify and debug PDF generation issues
"""
import base64
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.utils import ImageReader
from reportlab.lib.colors import black, blue
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime

def create_test_signature_image():
    """Create a test signature image to simulate handwritten signature"""
    # Create a white background image
    img = Image.new('RGBA', (600, 200), (255, 255, 255, 0))  # Transparent background
    draw = ImageDraw.Draw(img)
    
    # Draw a simple signature-like curve
    # Simulate handwritten signature
    points = [
        (50, 150), (80, 120), (120, 140), (160, 110), (200, 130),
        (240, 100), (280, 120), (320, 90), (360, 110), (400, 80),
        (440, 100), (480, 70), (520, 90), (550, 60)
    ]
    
    # Draw signature lines
    for i in range(len(points) - 1):
        draw.line([points[i], points[i + 1]], fill=(0, 0, 0, 255), width=3)
    
    # Add some flourishes
    draw.ellipse([520, 55, 560, 95], outline=(0, 0, 0, 255), width=2)
    
    return img

def create_digital_signature_text(customer_name):
    """Create a digital signature from customer name"""
    # Create an image for the digital signature
    img = Image.new('RGBA', (400, 60), (255, 255, 255, 0))  # Transparent background
    draw = ImageDraw.Draw(img)
    
    try:
        # Try to use a script-like font (fallback to default if not available)
        font = ImageFont.truetype("/System/Library/Fonts/Brush Script MT.ttc", 36)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 32)
        except:
            font = ImageFont.load_default()
    
    # Draw the customer name in a signature style
    text = f"✓ {customer_name}"
    
    # Get text dimensions
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Center the text
    x = (400 - text_width) // 2
    y = (60 - text_height) // 2
    
    # Draw the text in blue color for digital signature
    draw.text((x, y), text, fill=(0, 0, 139, 255), font=font)  # Dark blue
    
    return img

def generate_enhanced_signature_pdf(wonum, status, signature_data, customer_name, comments, date_time, username):
    """Generate enhanced PDF with both handwritten and digital signatures"""
    try:
        print(f"🔧 Generating enhanced PDF for WO {wonum}")
        
        # Create PDF buffer
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        width, height = letter
        
        # Title
        p.setFont("Helvetica-Bold", 18)
        p.drawString(50, height - 50, f"Digital Signature Document")
        
        # Work order info
        p.setFont("Helvetica-Bold", 14)
        p.drawString(50, height - 80, f"Work Order: {wonum}")
        p.drawString(50, height - 100, f"Status Change: {status}")
        
        # Draw a border
        p.rect(40, 40, width - 80, height - 80, stroke=1, fill=0)
        
        # Customer information
        y_pos = height - 140
        p.setFont("Helvetica-Bold", 12)
        p.drawString(50, y_pos, "Customer Information:")
        
        p.setFont("Helvetica", 11)
        y_pos -= 25
        p.drawString(70, y_pos, f"Name: {customer_name}")
        y_pos -= 20
        p.drawString(70, y_pos, f"Date & Time: {date_time}")
        y_pos -= 20
        p.drawString(70, y_pos, f"Authorized by: {username}")
        
        # Comments section
        if comments:
            y_pos -= 30
            p.setFont("Helvetica-Bold", 12)
            p.drawString(50, y_pos, "Comments:")
            p.setFont("Helvetica", 11)
            y_pos -= 20
            # Handle long comments by wrapping
            comment_lines = [comments[i:i+80] for i in range(0, len(comments), 80)]
            for line in comment_lines:
                p.drawString(70, y_pos, line)
                y_pos -= 15
        
        # Digital Signature Section
        y_pos -= 40
        p.setFont("Helvetica-Bold", 14)
        p.setFillColor(blue)
        p.drawString(50, y_pos, "Digital Signature (Customer Name):")
        p.setFillColor(black)
        
        # Create and add digital signature
        digital_sig_img = create_digital_signature_text(customer_name)
        digital_sig_buffer = io.BytesIO()
        digital_sig_img.save(digital_sig_buffer, format='PNG')
        digital_sig_buffer.seek(0)
        digital_sig_reader = ImageReader(digital_sig_buffer)
        
        y_pos -= 70
        p.drawImage(digital_sig_reader, 50, y_pos, 400, 60)
        
        # Handwritten Signature Section
        y_pos -= 80
        p.setFont("Helvetica-Bold", 14)
        p.setFillColor(blue)
        p.drawString(50, y_pos, "Handwritten Signature:")
        p.setFillColor(black)
        
        # Process handwritten signature
        if signature_data and signature_data.startswith('data:image/png;base64,'):
            print("🖊️ Processing handwritten signature...")
            signature_base64 = signature_data.split(',')[1]
            signature_bytes = base64.b64decode(signature_base64)
            
            # Open and process the signature image
            signature_image = Image.open(io.BytesIO(signature_bytes))
            print(f"📏 Original signature size: {signature_image.size}, mode: {signature_image.mode}")
            
            # Convert to RGBA first to handle transparency properly
            if signature_image.mode != 'RGBA':
                signature_image = signature_image.convert('RGBA')
            
            # Create a white background and paste the signature
            white_bg = Image.new('RGB', signature_image.size, (255, 255, 255))
            white_bg.paste(signature_image, mask=signature_image.split()[-1])  # Use alpha channel as mask
            
            # Create image reader
            sig_buffer = io.BytesIO()
            white_bg.save(sig_buffer, format='PNG')
            sig_buffer.seek(0)
            sig_reader = ImageReader(sig_buffer)
            
            # Draw handwritten signature
            y_pos -= 120
            sig_width = 500
            sig_height = 100
            p.drawImage(sig_reader, 50, y_pos, sig_width, sig_height)
            print("✅ Handwritten signature added to PDF")
        else:
            print("⚠️ No valid handwritten signature data provided")
            # Create a test signature if no real signature provided
            test_sig = create_test_signature_image()
            test_sig_buffer = io.BytesIO()
            test_sig.save(test_sig_buffer, format='PNG')
            test_sig_buffer.seek(0)
            test_sig_reader = ImageReader(test_sig_buffer)
            
            y_pos -= 120
            p.drawImage(test_sig_reader, 50, y_pos, 500, 100)
            print("🧪 Test signature added to PDF")
        
        # Signature box border
        p.rect(45, y_pos - 5, 510, 110, stroke=1, fill=0)
        
        # Verification section - RIGHT UNDERNEATH the handwritten signature
        y_pos -= 30  # Small gap after signature box
        p.setFont("Helvetica-Bold", 12)
        p.drawString(50, y_pos, "Verification:")
        p.setFont("Helvetica", 10)
        y_pos -= 20
        p.drawString(70, y_pos, "☑ Customer identity verified")
        y_pos -= 15
        p.drawString(70, y_pos, "☑ Digital signature captured")
        y_pos -= 15
        p.drawString(70, y_pos, "☑ Handwritten signature captured")
        y_pos -= 15
        p.drawString(70, y_pos, f"☑ Document generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Footer with left and right alignment to prevent overlap
        footer_y = 90  # Start higher to avoid overlap
        p.setFont("Helvetica", 8)

        # Line 1: Left side - Generated by info, Right side - Date/Time
        p.drawString(50, footer_y, "Generated by Maximo Mobile App")
        p.drawRightString(width - 50, footer_y, f"Date: {date_time}")

        # Line 2: Left side - Compliance statement, Right side - Document type
        footer_y -= 18  # Increased spacing
        p.drawString(50, footer_y, "This is a digitally signed document for audit compliance.")
        p.drawRightString(width - 50, footer_y, f"Type: {status} Signature")

        # Line 3: Left side - Document ID (shortened), Right side - Timestamp
        footer_y -= 18  # Increased spacing
        doc_id_short = f"Document ID: {status}SIGNATURE_{wonum}"
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        p.drawString(50, footer_y, doc_id_short)
        p.drawRightString(width - 50, footer_y, f"Generated: {timestamp}")
        
        p.save()
        
        pdf_data = buffer.getvalue()
        buffer.close()
        
        print(f"✅ Enhanced PDF generated successfully ({len(pdf_data)} bytes)")
        
        return {
            'success': True,
            'pdf_data': pdf_data,
            'filename': f"{status}SIGNATURE_{wonum}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        }
        
    except Exception as e:
        print(f"❌ Error generating enhanced PDF: {str(e)}")
        return {
            'success': False,
            'error': f'Enhanced PDF generation failed: {str(e)}'
        }

def test_pdf_generation():
    """Test the enhanced PDF generation"""
    print("🧪 Testing Enhanced PDF Generation")
    print("=" * 50)
    
    # Test data
    test_data = {
        'wonum': '2021-1994269',
        'status': 'COMP',
        'signature_data': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'customer_name': 'John Smith',
        'comments': 'Work completed successfully. All requirements met.',
        'date_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'username': '<EMAIL>'
    }
    
    # Generate PDF
    result = generate_enhanced_signature_pdf(**test_data)
    
    if result['success']:
        # Save test PDF
        filename = f"test_enhanced_signature_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        with open(filename, 'wb') as f:
            f.write(result['pdf_data'])
        
        print(f"✅ Test PDF saved as: {filename}")
        print(f"📄 PDF size: {len(result['pdf_data'])} bytes")
        print("🔍 Please open the PDF to verify:")
        print("   - Customer name appears as digital signature")
        print("   - Handwritten signature is visible (not dark)")
        print("   - All information is properly formatted")
        
        return True
    else:
        print(f"❌ Test failed: {result['error']}")
        return False

if __name__ == "__main__":
    success = test_pdf_generation()
    if success:
        print("\n🎉 Enhanced PDF generation test completed!")
        print("📝 Review the generated PDF and then apply changes to the main app.")
    else:
        print("\n💥 Test failed. Check the errors above.")
