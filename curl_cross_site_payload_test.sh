#!/bin/bash

# Curl Cross-Site Payload Test
# =============================

echo "🚀 CURL CROSS-SITE PAYLOAD STRUCTURE TEST"
echo "=========================================="

echo "🎯 OBJECTIVE: Test cross-site transfer payload structures directly"
echo "📋 STRATEGY: Use exact payload structures against Maximo API"
echo "🔍 HYPOTHESIS: Top-level site context determines validation scope"
echo ""

# Maximo API endpoint
API_ENDPOINT="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"

# Cookie file
COOKIE_FILE="cookies.txt"

echo "🔗 API Endpoint: $API_ENDPOINT"
echo "🍪 Cookie file: $COOKIE_FILE"
echo ""

# Function to test payload
test_payload() {
    local test_name="$1"
    local description="$2"
    local payload="$3"
    
    echo "🧪 $test_name"
    echo "📝 $description"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "📋 Payload:"
    echo "$payload" | jq '.' 2>/dev/null || echo "$payload"
    echo ""
    
    echo "🔄 Submitting to Maximo API..."
    
    response=$(curl -X POST "$API_ENDPOINT" \
        -H "Accept: application/json" \
        -H "Content-Type: application/json" \
        -H "x-method-override: BULK" \
        --cookie "$COOKIE_FILE" \
        --cookie-jar "$COOKIE_FILE" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Cross-site transfer worked!"
        echo "$payload" > "successful_cross_site_payload_$(date +%s).json"
        echo "💾 Successful payload saved"
        return 0
    elif echo "$response" | grep -q "HTTP_STATUS:403"; then
        echo "❌ Authentication failed (403)"
        return 1
    elif echo "$response" | grep -q '"Error"'; then
        echo "❌ Business logic error (but API accessible)"
        return 1
    else
        echo "⚠️  Unexpected response"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING CROSS-SITE PAYLOAD STRUCTURES"
echo "========================================"

# Test Case 1: Destination Site as Top-Level (IKWAJ)
test_payload "TEST CASE 1: Destination Site as Top-Level (IKWAJ)" \
    "Top-level inventory record in destination site" \
    '[
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "itemsetid": "ITEMSET",
        "siteid": "IKWAJ",
        "location": "KWAJ-1058",
        "issueunit": "RO",
        "matrectrans": [
          {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "issuetype": "TRANSFER",
            "quantity": 1.0,
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromstoreloc": "RIP001",
            "tostoreloc": "KWAJ-1058",
            "transdate": "2025-07-16T12:00:00+00:00",
            "issueunit": "RO",
            "frombinnum": "DEFAULT",
            "tobinnum": "DEFAULT",
            "fromlotnum": "DEFAULT",
            "tolotnum": "DEFAULT",
            "fromconditioncode": "A1",
            "toconditioncode": "A1"
          }
        ]
      }
    ]'

# Test Case 2: Source Site as Top-Level (LCVKWT)
test_payload "TEST CASE 2: Source Site as Top-Level (LCVKWT)" \
    "Top-level inventory record in source site" \
    '[
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "itemsetid": "ITEMSET",
        "siteid": "LCVKWT",
        "location": "RIP001",
        "issueunit": "RO",
        "matrectrans": [
          {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "issuetype": "TRANSFER",
            "quantity": 1.0,
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromstoreloc": "RIP001",
            "tostoreloc": "KWAJ-1058",
            "transdate": "2025-07-16T12:00:00+00:00",
            "issueunit": "RO",
            "frombinnum": "DEFAULT",
            "tobinnum": "DEFAULT",
            "fromlotnum": "DEFAULT",
            "tolotnum": "DEFAULT",
            "fromconditioncode": "A1",
            "toconditioncode": "A1"
          }
        ]
      }
    ]'

# Test Case 3: Minimal Destination Site Context
test_payload "TEST CASE 3: Minimal Destination Site Context" \
    "Minimal payload with destination site context" \
    '[
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "siteid": "IKWAJ",
        "location": "KWAJ-1058",
        "matrectrans": [
          {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "issuetype": "TRANSFER",
            "quantity": 1.0,
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromstoreloc": "RIP001",
            "tostoreloc": "KWAJ-1058"
          }
        ]
      }
    ]'

# Test Case 4: Dual Record Approach
test_payload "TEST CASE 4: Dual Record Approach" \
    "Create records in both sites" \
    '[
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "siteid": "LCVKWT",
        "location": "RIP001",
        "issueunit": "RO",
        "matrectrans": [
          {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "issuetype": "TRANSFER",
            "quantity": 1.0,
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromstoreloc": "RIP001",
            "tostoreloc": "KWAJ-1058",
            "issueunit": "RO"
          }
        ]
      },
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "siteid": "IKWAJ",
        "location": "KWAJ-1058",
        "issueunit": "RO"
      }
    ]'

# Test Case 5: No Top-Level Site (Let Maximo Infer)
test_payload "TEST CASE 5: No Top-Level Site" \
    "Let Maximo infer site context from transfer details" \
    '[
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "matrectrans": [
          {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "issuetype": "TRANSFER",
            "quantity": 1.0,
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromstoreloc": "RIP001",
            "tostoreloc": "KWAJ-1058",
            "issueunit": "RO",
            "frombinnum": "DEFAULT",
            "tobinnum": "DEFAULT",
            "fromlotnum": "DEFAULT",
            "tolotnum": "DEFAULT",
            "fromconditioncode": "A1",
            "toconditioncode": "A1"
          }
        ]
      }
    ]'

# Test Case 6: Alternative Transfer Type
test_payload "TEST CASE 6: Alternative Transfer Type" \
    "Using different transfer approach" \
    '[
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "siteid": "IKWAJ",
        "location": "KWAJ-1058",
        "issueunit": "RO",
        "matrectrans": [
          {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "issuetype": "TRANSFER",
            "quantity": 1.0,
            "fromsiteid": "LCVKWT",
            "tositeid": "IKWAJ",
            "fromstoreloc": "RIP001",
            "tostoreloc": "KWAJ-1058",
            "issueunit": "RO",
            "transdate": "2025-07-16T12:00:00+00:00"
          }
        ]
      }
    ]'

echo ""
echo "📊 CROSS-SITE PAYLOAD TEST SUMMARY"
echo "=================================="
echo "🔍 All test cases completed"
echo "📋 Check responses above for success patterns"
echo ""
echo "🎯 SUCCESS CRITERIA:"
echo "  ✅ HTTP_STATUS:200"
echo "  ✅ Response contains: \"status\": \"204\""
echo "  ✅ No Error objects in response"
echo ""
echo "📋 IF NO SUCCESS FOUND:"
echo "  • Cross-site validation is deeply embedded in Maximo"
echo "  • Alternative approaches may be needed"
echo "  • Consider using different endpoints or methods"
echo ""
echo "💡 NEXT STEPS IF SUCCESSFUL:"
echo "  1. Save the working payload structure"
echo "  2. Update Flask application to use successful pattern"
echo "  3. Test with different item/location combinations"
echo "  4. Implement in production application"
