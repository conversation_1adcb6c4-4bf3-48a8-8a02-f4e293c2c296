#!/usr/bin/env python3
"""
MXAPIINVENTORY OSLC Actions Investigation

This script investigates the OSLC MXAPIINVENTORY endpoint to discover
actual inventory management actions like:
- Issue current item
- Transfer current item  
- Item availability
- Other inventory operations

Author: Augment Agent
Date: 2025-01-15
"""

import sys
import os
import json
import requests
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.auth.token_manager import MaximoTokenManager

def test_oslc_endpoint_actions():
    """Test OSLC endpoint to discover available actions."""
    print("🔍 MXAPIINVENTORY OSLC Actions Investigation")
    print("=" * 80)

    # Use API key authentication
    api_key = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

    # Test OSLC endpoint - try both /oslc/os and /api/os
    endpoints_to_test = [
        f"{base_url}/oslc/os/mxapiinventory",
        f"{base_url}/api/os/mxapiinventory"
    ]

    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    # Test both endpoints to see which one works
    working_endpoint = None

    for endpoint_url in endpoints_to_test:
        endpoint_name = "OSLC" if "/oslc/" in endpoint_url else "API"
        print(f"� Testing {endpoint_name} URL: {endpoint_url}")

        try:
            response = requests.get(
                endpoint_url,
                headers=headers,
                timeout=30
            )

            print(f"  Status Code: {response.status_code}")
            print(f"  Content Type: {response.headers.get('content-type', 'Unknown')}")

            if response.status_code == 200 and 'application/json' in response.headers.get('content-type', ''):
                try:
                    data = response.json()
                    print(f"  ✅ {endpoint_name} endpoint accessible with JSON response")
                    print(f"  Response keys: {list(data.keys())}")
                    working_endpoint = endpoint_url
                    break

                except json.JSONDecodeError:
                    print(f"  ❌ {endpoint_name} response is not valid JSON")
            else:
                print(f"  ❌ {endpoint_name} endpoint not returning JSON")

        except Exception as e:
            print(f"  ❌ {endpoint_name} Exception: {str(e)}")

        print()

    if not working_endpoint:
        print("❌ No working JSON endpoint found")
        return None

    print(f"✅ Using working endpoint: {working_endpoint}")
    return working_endpoint

def discover_inventory_actions(endpoint_url, api_key):
    """Discover available inventory management actions."""
    print("\n" + "="*80)
    print("📋 DISCOVERING INVENTORY MANAGEMENT ACTIONS")
    print("="*80)

    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }

    # Test 1: Look for wsmethod actions with POST
    print("🔍 Test 1: WebService Method Actions Discovery")

    # Known inventory actions to test
    inventory_actions = [
        "issuecurrentitem",
        "transfercurrentitem",
        "itemavailability",
        "addchange",
        "create",
        "update",
        "delete",
        "sync"
    ]

    working_actions = []

    for action in inventory_actions:
        # Test wsmethod format
        test_url = f"{endpoint_url}?action=wsmethod:{action}"
        print(f"\n  Testing: wsmethod:{action}")

        try:
            # Try POST with minimal payload
            response = requests.post(
                test_url,
                headers={**headers, "Content-Type": "application/json"},
                json={"test": "discovery"},
                timeout=15
            )

            print(f"    POST Status: {response.status_code}")

            if response.status_code in [200, 400, 422]:  # 400/422 might indicate valid action with wrong payload
                try:
                    data = response.json()
                    if 'error' in str(data).lower() or 'Error' in data:
                        error_msg = str(data)
                        if 'method' in error_msg.lower() or 'action' in error_msg.lower():
                            print(f"    ✅ Valid action (error indicates method exists)")
                            working_actions.append(f"wsmethod:{action}")
                        else:
                            print(f"    ❌ Invalid action")
                    else:
                        print(f"    ✅ Valid action (success response)")
                        working_actions.append(f"wsmethod:{action}")
                except:
                    print(f"    ⚠️ Non-JSON response: {response.text[:100]}...")
            else:
                print(f"    ❌ Invalid action (status {response.status_code})")

        except Exception as e:
            print(f"    ❌ Exception: {str(e)}")

    print(f"\n✅ Working Actions Found: {working_actions}")

    # Test 2: Look for action parameters
    print("\n🔍 Test 2: Action Parameter Discovery")

    for action in working_actions[:3]:  # Test first 3 working actions
        test_url = f"{endpoint_url}?action={action}"
        print(f"\n  Analyzing: {action}")

        try:
            # Try with empty payload to see required parameters
            response = requests.post(
                test_url,
                headers={**headers, "Content-Type": "application/json"},
                json={},
                timeout=15
            )

            if response.content:
                try:
                    data = response.json()
                    if 'error' in str(data).lower() or 'Error' in data:
                        print(f"    Error response: {str(data)[:200]}...")
                        # Look for required field information
                        error_str = str(data).lower()
                        if 'required' in error_str or 'missing' in error_str:
                            print(f"    ⚠️ Indicates required parameters")
                except:
                    pass

        except Exception as e:
            print(f"    Exception: {str(e)}")

    return working_actions

def test_specific_inventory_operations(endpoint_url, api_key):
    """Test specific inventory operations with proper payloads."""
    print("\n" + "="*80)
    print("📋 TESTING SPECIFIC INVENTORY OPERATIONS")
    print("="*80)

    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": api_key
    }

    # Test Issue Current Item
    print("🔍 Test 1: Issue Current Item Operation")
    issue_url = f"{endpoint_url}?action=wsmethod:issuecurrentitem"

    issue_payload = {
        "itemnum": "TEST-ITEM",
        "siteid": "LCVKWT",
        "storeloc": "TEST-LOC",
        "quantity": 1
    }

    try:
        response = requests.post(
            issue_url,
            headers=headers,
            json=issue_payload,
            timeout=15
        )

        print(f"  Status: {response.status_code}")
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {str(data)[:300]}...")
            except:
                print(f"  Response text: {response.text[:300]}...")

    except Exception as e:
        print(f"  Exception: {str(e)}")

    # Test Transfer Current Item
    print("\n🔍 Test 2: Transfer Current Item Operation")
    transfer_url = f"{endpoint_url}?action=wsmethod:transfercurrentitem"

    transfer_payload = {
        "itemnum": "TEST-ITEM",
        "fromsiteid": "LCVKWT",
        "tositeid": "LCVKWT",
        "fromstoreloc": "TEST-LOC-1",
        "tostoreloc": "TEST-LOC-2",
        "quantity": 1
    }

    try:
        response = requests.post(
            transfer_url,
            headers=headers,
            json=transfer_payload,
            timeout=15
        )

        print(f"  Status: {response.status_code}")
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {str(data)[:300]}...")
            except:
                print(f"  Response text: {response.text[:300]}...")

    except Exception as e:
        print(f"  Exception: {str(e)}")

    # Test Item Availability
    print("\n🔍 Test 3: Item Availability Operation")
    availability_url = f"{endpoint_url}?action=wsmethod:itemavailability"

    availability_payload = {
        "itemnum": "TEST-ITEM",
        "siteid": "LCVKWT",
        "storeloc": "TEST-LOC"
    }

    try:
        response = requests.post(
            availability_url,
            headers=headers,
            json=availability_payload,
            timeout=15
        )

        print(f"  Status: {response.status_code}")
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {str(data)[:300]}...")
            except:
                print(f"  Response text: {response.text[:300]}...")

    except Exception as e:
        print(f"  Exception: {str(e)}")

def test_oslc_service_document(endpoint_url, api_key):
    """Test for OSLC service document."""
    print("\n" + "="*60)
    print("📋 Test: OSLC Service Document Discovery")
    print("📋 Test 2: OSLC Service Document Discovery")
    service_doc_url = f"{base_url}/oslc/os/mxapiinventory/servicedocument"
    
    try:
        response = requests.get(
            service_doc_url,
            headers=headers,
            timeout=30
        )
        
        print(f"Service Document Status: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ Service document found")
                print(f"Service document keys: {list(data.keys())}")
            except:
                print("Service document not JSON")
        else:
            print("❌ No service document found")
            
    except Exception as e:
        print(f"❌ Service document error: {str(e)}")
    
    print("\n" + "="*60)
    
    # Test 3: Look for available actions/methods
    print("📋 Test 3: Available Actions Discovery")
    
    # Common OSLC action patterns to test
    action_tests = [
        "issuecurrentitem",
        "transfercurrentitem", 
        "itemavailability",
        "actions",
        "operations",
        "methods",
        "wsmethod"
    ]
    
    for action in action_tests:
        test_url = f"{oslc_url}?action={action}"
        print(f"\nTesting action: {action}")
        
        try:
            response = requests.get(
                test_url,
                headers=headers,
                timeout=15
            )
            
            print(f"  Status: {response.status_code}")
            if response.status_code in [200, 400]:  # 400 might give us error info about valid actions
                try:
                    data = response.json()
                    if 'error' in str(data).lower() or 'Error' in data:
                        print(f"  Error response (might indicate valid action): {str(data)[:100]}...")
                    else:
                        print(f"  ✅ Possible valid action")
                except:
                    print(f"  Response: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"  Exception: {str(e)}")
    
    print("\n" + "="*60)
    
    # Test 4: OPTIONS on OSLC endpoint
    print("📋 Test 4: OSLC OPTIONS Discovery")
    
    try:
        response = requests.options(
            oslc_url,
            headers=headers,
            timeout=15
        )
        
        print(f"OPTIONS Status: {response.status_code}")
        
        # Check headers for allowed methods or actions
        relevant_headers = ['Allow', 'Access-Control-Allow-Methods', 'X-Supported-Actions', 'OSLC-Actions']
        for header in relevant_headers:
            value = response.headers.get(header)
            if value:
                print(f"  {header}: {value}")
                
        # Check for any response body
        if response.content:
            try:
                data = response.json()
                print(f"  OPTIONS response data: {data}")
            except:
                print(f"  OPTIONS response text: {response.text[:200]}")
                
    except Exception as e:
        print(f"❌ OPTIONS error: {str(e)}")
    
    print("\n" + "="*60)
    
    # Test 5: Look for wsmethod actions
    print("📋 Test 5: WebService Method Discovery")
    
    wsmethod_tests = [
        "wsmethod:issuecurrentitem",
        "wsmethod:transfercurrentitem",
        "wsmethod:itemavailability",
        "wsmethod:addchange",
        "wsmethod:create"
    ]
    
    for wsmethod in wsmethod_tests:
        test_url = f"{oslc_url}?action={wsmethod}"
        print(f"\nTesting wsmethod: {wsmethod}")
        
        try:
            response = requests.post(  # Try POST for wsmethod
                test_url,
                headers={**headers, "Content-Type": "application/json"},
                json={},  # Empty payload to see what's required
                timeout=15
            )
            
            print(f"  POST Status: {response.status_code}")
            if response.content:
                try:
                    data = response.json()
                    print(f"  Response: {str(data)[:200]}...")
                except:
                    print(f"  Response text: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"  Exception: {str(e)}")

def test_with_session_auth():
    """Test using session authentication instead of API key."""
    print("\n" + "="*80)
    print("🔍 Testing with Session Authentication")
    print("=" * 80)
    
    # Initialize token manager
    token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated with session")
        return
        
    print("✅ Session authenticated")
    
    # Test OSLC endpoint with session
    oslc_url = f"{token_manager.base_url}/oslc/os/mxapiinventory"
    
    print(f"🔗 Testing OSLC URL with session: {oslc_url}")
    
    try:
        response = token_manager.session.get(
            oslc_url,
            timeout=(3.05, 30),
            headers={"Accept": "application/json"}
        )
        
        print(f"Session Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ OSLC endpoint accessible with session")
                print(f"Response structure: {list(data.keys())}")
                
                # Look for action-related information
                if isinstance(data, dict):
                    for key, value in data.items():
                        if 'action' in key.lower() or 'method' in key.lower() or 'operation' in key.lower():
                            print(f"Found action-related key: {key} = {value}")
                            
            except json.JSONDecodeError:
                print("Response is not JSON")
                print(f"Response preview: {response.text[:200]}")
        else:
            print(f"❌ Session Error: {response.status_code}")
            print(f"Response: {response.text[:300]}")
            
    except Exception as e:
        print(f"❌ Session Exception: {str(e)}")

def main():
    """Main execution function."""
    print("🚀 MXAPIINVENTORY OSLC Actions Investigation")
    print("=" * 80)
    print("Investigating OSLC endpoint for inventory management actions:")
    print("- Issue current item operations")
    print("- Transfer current item operations")
    print("- Item availability operations")
    print("- Other inventory management operations")
    print("=" * 80)

    api_key = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

    # Step 1: Find working endpoint
    working_endpoint = test_oslc_endpoint_actions()

    if working_endpoint:
        # Step 2: Discover available actions
        working_actions = discover_inventory_actions(working_endpoint, api_key)

        # Step 3: Test specific operations
        test_specific_inventory_operations(working_endpoint, api_key)

        print("\n" + "="*80)
        print("🎯 INVESTIGATION SUMMARY")
        print("=" * 80)
        print(f"✅ Working Endpoint: {working_endpoint}")
        print(f"✅ Working Actions: {len(working_actions)}")
        for action in working_actions:
            print(f"   - {action}")
        print("\nInvestigation completed successfully!")

    else:
        print("\n❌ No working endpoint found. Investigation failed.")

        # Fallback: Test with session authentication
        print("\n🔄 Trying session authentication as fallback...")
        test_with_session_auth()

    return True

if __name__ == "__main__":
    main()
