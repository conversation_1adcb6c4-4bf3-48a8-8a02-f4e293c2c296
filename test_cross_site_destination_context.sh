#!/bin/bash

# Test Cross-Site with Destination Site Context
# ==============================================

echo "🚀 TESTING CROSS-SITE TRANSFERS WITH DESTINATION SITE CONTEXT"
echo "=============================================================="

echo "🎯 OBJECTIVE: Get 204 success for LCVKWT → IKWAJ transfers"
echo "📋 STRATEGY: Test with destination site context and proper storeroom filtering"
echo "🔍 HYPOTHESIS: Use destination site in top-level record with proper tostoreloc"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_cross_site_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting cross-site transfer..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Cross-site transfer worked with 204 status!"
        echo "$payload" > "successful_cross_site_payload_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: successful_cross_site_payload_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING MULTIPLE CROSS-SITE PAYLOAD VARIATIONS"
echo "================================================="

# Test 1: Basic cross-site with destination site filtering
test_cross_site_payload "Basic Cross-Site (LCVKWT → IKWAJ)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 2: With bins and lots
test_cross_site_payload "Cross-Site with Bins/Lots" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT"
}'

# Test 3: With condition codes
test_cross_site_payload "Cross-Site with Conditions" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: Different destination storeroom
test_cross_site_payload "Cross-Site to KWAJ-1115" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Different destination storeroom with space
test_cross_site_payload "Cross-Site to 8051 FLOOR-X" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "8051 FLOOR-X",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 6: Smaller quantity
test_cross_site_payload "Cross-Site Small Quantity" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 0.1,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 7: Different bins
test_cross_site_payload "Cross-Site Different Bins" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "28-800-0004",
    "to_bin": "58-A-A01-1",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 8: Different lots
test_cross_site_payload "Cross-Site Different Lots" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "LOT123",
    "to_lot": "TEST",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 9: Without lots and conditions
test_cross_site_payload "Cross-Site Minimal Fields" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT"
}'

# Test 10: Different quantity and storeroom combination
test_cross_site_payload "Cross-Site Alternative Combo" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1500",
    "quantity": 0.5,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 CROSS-SITE TRANSFER TEST SUMMARY"
echo "==================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found working cross-site transfer patterns!"
    echo "======================================================"
    echo "💾 Check successful_cross_site_payload_*.json files for working patterns"
    echo ""
    echo "📋 Working curl commands:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "successful_cross_site_payload_$i.json" ]; then
            echo ""
            echo "# Working Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat successful_cross_site_payload_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
else
    echo ""
    echo "❌ No 204 success responses found yet"
    echo "🔄 Continue testing with more variations..."
fi

echo ""
echo "🎯 NEXT: If no success, test with more storeroom combinations from IKWAJ site"
