#!/usr/bin/env python3
"""
Analyze MXAPIINVENTORY Error Responses to Understand WSMethod Requirements

This script examines the detailed error responses from MXAPIINVENTORY wsmethods
to understand what parameters and payload structures are required for each method.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

def analyze_wsmethod_errors():
    """Analyze error responses from key inventory wsmethods."""
    print("🔍 Analyzing MXAPIINVENTORY WSMethod Error Responses")
    print("=" * 80)
    
    # Focus on the most important inventory methods
    key_methods = [
        "issuecurrentitem",
        "transfercurrentitem", 
        "itemavailability",
        "addchange"
    ]
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    for method in key_methods:
        print(f"\n🔍 Analyzing Method: {method}")
        print("-" * 60)
        
        test_url = f"{endpoint_url}?action=wsmethod:{method}"
        
        # Test 1: Empty payload
        print("📋 Test 1: Empty payload")
        try:
            response = requests.post(
                test_url,
                json={},
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"  Status: {response.status_code}")
            if response.content:
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"  Response (text): {response.text}")
        except Exception as e:
            print(f"  Error: {str(e)}")
            
        # Test 2: Minimal realistic payload
        print("\n📋 Test 2: Minimal realistic payload")
        minimal_payload = get_minimal_payload(method)
        
        try:
            response = requests.post(
                test_url,
                json=minimal_payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"  Payload: {json.dumps(minimal_payload, indent=2)}")
            print(f"  Status: {response.status_code}")
            if response.content:
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"  Response (text): {response.text}")
        except Exception as e:
            print(f"  Error: {str(e)}")
            
        # Test 3: Array format payload
        print("\n📋 Test 3: Array format payload")
        try:
            response = requests.post(
                test_url,
                json=[minimal_payload],
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"  Payload: {json.dumps([minimal_payload], indent=2)}")
            print(f"  Status: {response.status_code}")
            if response.content:
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"  Response (text): {response.text}")
        except Exception as e:
            print(f"  Error: {str(e)}")

def get_minimal_payload(method: str) -> Dict:
    """Get minimal realistic payload for each method."""
    if method == "issuecurrentitem":
        return {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT",
            "location": "LCVKWT-STORE",
            "quantity": 1
        }
    elif method == "transfercurrentitem":
        return {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT", 
            "fromlocation": "LCVKWT-STORE",
            "tolocation": "LCVKWT-STORE2",
            "quantity": 1
        }
    elif method == "itemavailability":
        return {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT"
        }
    elif method == "addchange":
        return {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT",
            "location": "LCVKWT-STORE"
        }
    else:
        return {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT"
        }

def test_direct_rest_operations():
    """Test direct REST operations on MXAPIINVENTORY."""
    print("\n🔍 Testing Direct REST Operations")
    print("=" * 80)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test 1: GET with specific item
    print("📋 Test 1: GET specific inventory record")
    try:
        response = requests.get(
            endpoint_url,
            params={
                "oslc.select": "*",
                "oslc.where": 'itemnum="5975-60-V00-0001" and siteid="LCVKWT"',
                "oslc.pageSize": "1"
            },
            headers={"Accept": "application/json", "apikey": API_KEY},
            timeout=(3.05, 15)
        )
        
        print(f"  Status: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                if 'rdfs:member' in data and data['rdfs:member']:
                    record = data['rdfs:member'][0]
                    print(f"  Found inventory record with fields: {list(record.keys())}")
                    
                    # Look for nested objects that might contain actions
                    for key, value in record.items():
                        if isinstance(value, dict) and 'href' in value:
                            print(f"    Nested object: {key} -> {value.get('href', '')}")
                else:
                    print("  No inventory records found")
            except Exception as e:
                print(f"  Error parsing response: {str(e)}")
        else:
            print(f"  Failed with status {response.status_code}")
            print(f"  Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"  Error: {str(e)}")
        
    # Test 2: POST to create/update
    print("\n📋 Test 2: POST operation")
    try:
        test_payload = {
            "itemnum": "TEST-ITEM-001",
            "siteid": "LCVKWT",
            "location": "LCVKWT-STORE",
            "curbal": 10
        }
        
        response = requests.post(
            endpoint_url,
            json=test_payload,
            headers=headers,
            timeout=(3.05, 15)
        )
        
        print(f"  Payload: {json.dumps(test_payload, indent=2)}")
        print(f"  Status: {response.status_code}")
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {json.dumps(data, indent=2)}")
            except:
                print(f"  Response (text): {response.text[:200]}...")
                
    except Exception as e:
        print(f"  Error: {str(e)}")

def test_known_working_patterns():
    """Test patterns that are known to work from other Maximo endpoints."""
    print("\n🔍 Testing Known Working Patterns")
    print("=" * 80)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Pattern 1: Using specific inventory ID
    print("📋 Pattern 1: Using specific inventory ID in URL")
    try:
        # First get an inventory ID
        response = requests.get(
            endpoint_url,
            params={
                "oslc.select": "inventoryid,itemnum,siteid,location",
                "oslc.where": 'itemnum="5975-60-V00-0001" and siteid="LCVKWT"',
                "oslc.pageSize": "1"
            },
            headers={"Accept": "application/json", "apikey": API_KEY},
            timeout=(3.05, 15)
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'rdfs:member' in data and data['rdfs:member']:
                inventory_record = data['rdfs:member'][0]
                inventory_id = inventory_record.get('inventoryid')
                
                if inventory_id:
                    print(f"  Found inventory ID: {inventory_id}")
                    
                    # Test wsmethod with specific ID
                    specific_url = f"{endpoint_url}/{inventory_id}?action=wsmethod:addchange"
                    
                    test_payload = {
                        "curbal": 100,
                        "physcnt": 100
                    }
                    
                    response = requests.post(
                        specific_url,
                        json=test_payload,
                        headers=headers,
                        timeout=(3.05, 15)
                    )
                    
                    print(f"  URL: {specific_url}")
                    print(f"  Payload: {json.dumps(test_payload, indent=2)}")
                    print(f"  Status: {response.status_code}")
                    if response.content:
                        try:
                            data = response.json()
                            print(f"  Response: {json.dumps(data, indent=2)}")
                        except:
                            print(f"  Response (text): {response.text[:200]}...")
                else:
                    print("  No inventory ID found in record")
            else:
                print("  No inventory records found")
        else:
            print(f"  Failed to get inventory record: {response.status_code}")
            
    except Exception as e:
        print(f"  Error: {str(e)}")

def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Error Analysis and Pattern Testing")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Analyze wsmethod errors
    analyze_wsmethod_errors()
    
    # Test direct REST operations
    test_direct_rest_operations()
    
    # Test known working patterns
    test_known_working_patterns()
    
    print("\n✅ Analysis completed")

if __name__ == "__main__":
    main()
