#!/bin/bash

# Test 3: With lots
# Transfer with lot numbers

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"itemnum":"5975-60-V00-0529","from_siteid":"LCVKWT","to_siteid":"IKWAJ","from_storeroom":"RIP001","to_storeroom":"KWAJ-1058","quantity":1.0,"from_issue_unit":"RO","from_lot":"TEST","to_lot":"TEST"}' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s
