#!/usr/bin/env python3
"""
Discover MXAPIINVENTORY Actions

This script tests various action names to discover what inventory management
operations are available through the MXAPIINVENTORY endpoint.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json

def test_inventory_actions():
    """Test various inventory action names to discover available operations."""
    print("🔍 MXAPIINVENTORY Actions Discovery")
    print("=" * 80)
    
    api_key = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    api_url = f"{base_url}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": api_key
    }
    
    # Test various action names that might be available
    actions_to_test = [
        # Standard CRUD actions
        "AddChange",
        "Create", 
        "Update",
        "Delete",
        "Sync",
        
        # Inventory-specific actions
        "IssueCurrentItem",
        "TransferCurrentItem",
        "ItemAvailability",
        "PhysicalCount",
        "AdjustBalance",
        "IssueItem",
        "TransferItem",
        "ReceiveItem",
        "ReturnItem",
        
        # Possible variations
        "ISSUECURRENTITEM",
        "TRANSFERCURRENTITEM", 
        "ITEMAVAILABILITY",
        "issue_current_item",
        "transfer_current_item",
        "item_availability",
        
        # Other possible actions
        "Reserve",
        "Unreserve",
        "Move",
        "Allocate",
        "Deallocate",
        "Count",
        "Recount",
        "Adjust"
    ]
    
    valid_actions = []
    invalid_actions = []
    error_actions = []
    
    print(f"Testing {len(actions_to_test)} potential actions...")
    print()
    
    for action in actions_to_test:
        print(f"Testing action: {action}")
        
        # Create test payload
        test_payload = {
            "_action": action,
            "itemnum": "TEST-ITEM",
            "siteid": "LCVKWT",
            "location": "TEST-LOC"
        }
        
        try:
            response = requests.post(
                api_url,
                headers=headers,
                json=test_payload,
                timeout=15
            )
            
            print(f"  Status: {response.status_code}")
            
            if response.content:
                try:
                    data = response.json()
                    
                    if 'oslc:Error' in data:
                        error_info = data['oslc:Error']
                        reason_code = error_info.get('spi:reasonCode', '')
                        message = error_info.get('oslc:message', '')
                        
                        if 'BMXAA9487E' in reason_code:
                            # Action not found
                            print(f"  ❌ Action not found")
                            invalid_actions.append(action)
                        elif 'BMXAA4153E' in reason_code or 'site' in message.lower():
                            # Site validation error - means action exists but data is invalid
                            print(f"  ✅ Valid action (site validation error)")
                            valid_actions.append(action)
                        else:
                            # Other error - action might exist but has other validation issues
                            print(f"  ⚠️ Action exists but has validation error: {reason_code}")
                            error_actions.append((action, reason_code, message))
                    else:
                        # Success response
                        print(f"  ✅ Valid action (success)")
                        valid_actions.append(action)
                        
                except json.JSONDecodeError:
                    print(f"  ⚠️ Non-JSON response")
                    error_actions.append((action, "NON_JSON", response.text[:100]))
            else:
                print(f"  ⚠️ Empty response")
                
        except Exception as e:
            print(f"  ❌ Exception: {str(e)}")
            error_actions.append((action, "EXCEPTION", str(e)))
        
        print()
    
    # Print summary
    print("=" * 80)
    print("🎯 DISCOVERY SUMMARY")
    print("=" * 80)
    
    print(f"✅ Valid Actions Found ({len(valid_actions)}):")
    for action in valid_actions:
        print(f"   - {action}")
    
    print(f"\n⚠️ Actions with Validation Errors ({len(error_actions)}):")
    for action, code, msg in error_actions:
        print(f"   - {action}: {code}")
        if len(msg) < 100:
            print(f"     {msg}")
    
    print(f"\n❌ Invalid Actions ({len(invalid_actions)}):")
    for action in invalid_actions:
        print(f"   - {action}")
    
    return valid_actions, error_actions

def test_valid_action_parameters(valid_actions):
    """Test valid actions with different parameter combinations."""
    if not valid_actions:
        print("\nNo valid actions to test parameters for.")
        return
        
    print("\n" + "=" * 80)
    print("🔍 TESTING ACTION PARAMETERS")
    print("=" * 80)
    
    api_key = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    api_url = f"{base_url}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": api_key
    }
    
    # Test the first few valid actions with different parameter sets
    for action in valid_actions[:3]:
        print(f"\n📋 Testing parameters for action: {action}")
        
        # Test different parameter combinations
        param_tests = [
            {
                "name": "minimal_params",
                "payload": {
                    "_action": action,
                    "itemnum": "5975-01-V00-0001",  # Use a real item
                    "siteid": "LCVKWT"
                }
            },
            {
                "name": "with_location",
                "payload": {
                    "_action": action,
                    "itemnum": "5975-01-V00-0001",
                    "siteid": "LCVKWT",
                    "location": "LCVK-CMW-CAS"
                }
            },
            {
                "name": "with_quantity",
                "payload": {
                    "_action": action,
                    "itemnum": "5975-01-V00-0001",
                    "siteid": "LCVKWT",
                    "location": "LCVK-CMW-CAS",
                    "quantity": 1
                }
            }
        ]
        
        for test in param_tests:
            print(f"\n  Testing {test['name']}:")
            
            try:
                response = requests.post(
                    api_url,
                    headers=headers,
                    json=test['payload'],
                    timeout=15
                )
                
                print(f"    Status: {response.status_code}")
                
                if response.content:
                    try:
                        data = response.json()
                        
                        if 'oslc:Error' in data:
                            error_info = data['oslc:Error']
                            reason_code = error_info.get('spi:reasonCode', '')
                            message = error_info.get('oslc:message', '')
                            print(f"    Error: {reason_code}")
                            print(f"    Message: {message[:100]}...")
                        else:
                            print(f"    ✅ Success response")
                            print(f"    Response keys: {list(data.keys())}")
                            
                    except json.JSONDecodeError:
                        print(f"    Non-JSON response: {response.text[:100]}...")
                        
            except Exception as e:
                print(f"    Exception: {str(e)}")

def main():
    """Main execution function."""
    print("🚀 MXAPIINVENTORY Actions Discovery")
    print("Discovering available inventory management actions...")
    print()
    
    # Discover valid actions
    valid_actions, error_actions = test_inventory_actions()
    
    # Test parameters for valid actions
    test_valid_action_parameters(valid_actions)
    
    print("\n" + "=" * 80)
    print("🎯 FINAL SUMMARY")
    print("=" * 80)
    print("Investigation completed!")
    print(f"Found {len(valid_actions)} confirmed valid actions")
    print(f"Found {len(error_actions)} actions with validation errors (may still be valid)")
    
    if valid_actions:
        print("\nRecommended actions for inventory management:")
        for action in valid_actions:
            print(f"  - {action}")

if __name__ == "__main__":
    main()
