#!/usr/bin/env python3
"""
Simple script to generate a test QR code for testing the upload functionality
"""

import qrcode
import json
from datetime import datetime

# Create test inventory data
test_data = {
    'itemnum': 'TEST-ITEM-001',
    'description': 'Test Item for QR Scanner Upload',
    'storeloc': 'TEST001',
    'siteid': 'TEST',
    'currentbalance': 100,
    'conditioncode': 'A1',
    'binnum': 'TEST-BIN',
    'inventoryid': 999999,
    'issueunit': 'EA',
    'generated_timestamp': datetime.now().isoformat()
}

# Convert to JSON string
qr_content = json.dumps(test_data, separators=(',', ':'))

# Create QR code
qr = qrcode.QRCode(
    version=1,
    error_correction=qrcode.constants.ERROR_CORRECT_M,
    box_size=10,
    border=4,
)

qr.add_data(qr_content)
qr.make(fit=True)

# Create image
qr_image = qr.make_image(fill_color="black", back_color="white")

# Save the image
qr_image.save('test_qr_code.png')
print("Test QR code saved as 'test_qr_code.png'")
print(f"QR content: {qr_content}")
