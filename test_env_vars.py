#!/usr/bin/env python3
"""
Test environment variable loading
"""

import os
from dotenv import load_dotenv

print("🔍 Testing Environment Variable Loading")
print("=" * 50)

# Load environment variables from .env file (same as Flask app)
load_dotenv()

# Test the API key
api_key = os.environ.get('MAXIMO_API_KEY')
base_url = os.environ.get('MAXIMO_BASE_URL')

print(f"MAXIMO_API_KEY: {'✅ Found' if api_key else '❌ Not found'}")
if api_key:
    print(f"  Value: {api_key[:10]}...{api_key[-10:] if len(api_key) > 20 else api_key}")

print(f"MAXIMO_BASE_URL: {'✅ Found' if base_url else '❌ Not found'}")
if base_url:
    print(f"  Value: {base_url}")

# Test all environment variables
print(f"\nAll environment variables:")
for key, value in os.environ.items():
    if key.startswith('MAXIMO_'):
        print(f"  {key}: {value}")

# Test if .env file exists
env_file_exists = os.path.exists('.env')
print(f"\n.env file exists: {'✅ Yes' if env_file_exists else '❌ No'}")

if env_file_exists:
    with open('.env', 'r') as f:
        content = f.read()
        print(f".env file content preview:")
        print(content[:200] + "..." if len(content) > 200 else content)
