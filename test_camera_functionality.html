<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .camera-preview {
            width: 100%;
            max-width: 400px;
            height: 300px;
            object-fit: cover;
            border-radius: 12px;
            background: #000;
        }
        .camera-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 1rem 0;
        }
        .camera-controls .btn {
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .camera-controls .btn-lg {
            width: 60px;
            height: 60px;
        }
        .camera-status {
            padding: 0.5rem 1rem;
            background: rgba(13, 110, 253, 0.1);
            border-radius: 8px;
            color: #0d6efd;
            margin: 1rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-camera me-2"></i>Camera Functionality Test</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="camera-preview-wrapper mb-3">
                            <video id="cameraPreview" autoplay playsinline muted class="camera-preview"></video>
                            <canvas id="captureCanvas" style="display: none;"></canvas>
                            <div id="capturedImagePreview" style="display: none;">
                                <img id="capturedImage" class="camera-preview" alt="Captured photo">
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" onclick="retakePhoto()">
                                        <i class="fas fa-redo me-1"></i>Retake
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="camera-controls">
                            <button type="button" class="btn btn-outline-secondary" id="switchCameraBtn" title="Switch Camera" style="display: none;">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-lg" id="captureBtn" title="Start Camera">
                                <i class="fas fa-camera"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" id="stopCameraBtn" title="Stop Camera" style="display: none;">
                                <i class="fas fa-stop"></i>
                            </button>
                        </div>
                        
                        <div class="camera-status" id="cameraStatus">
                            <i class="fas fa-info-circle me-1"></i>
                            <span>Click "Start Camera" to test camera functionality</span>
                        </div>

                        <div class="mt-3">
                            <h6>Test Results:</h6>
                            <ul class="list-unstyled text-start" id="testResults">
                                <li><i class="fas fa-clock text-warning"></i> Camera access: Not tested</li>
                                <li><i class="fas fa-clock text-warning"></i> Camera switching: Not tested</li>
                                <li><i class="fas fa-clock text-warning"></i> Photo capture: Not tested</li>
                                <li><i class="fas fa-clock text-warning"></i> Image quality: Not tested</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let cameraStream = null;
        let currentFacingMode = 'environment';
        let capturedImageBlob = null;
        let testResults = {
            cameraAccess: false,
            cameraSwitching: false,
            photoCapture: false,
            imageQuality: false
        };

        document.addEventListener('DOMContentLoaded', function() {
            const captureBtn = document.getElementById('captureBtn');
            const switchCameraBtn = document.getElementById('switchCameraBtn');
            const stopCameraBtn = document.getElementById('stopCameraBtn');

            captureBtn.addEventListener('click', function() {
                if (cameraStream) {
                    capturePhoto();
                } else {
                    startCamera();
                }
            });

            switchCameraBtn.addEventListener('click', switchCamera);
            stopCameraBtn.addEventListener('click', stopCamera);

            // Check camera availability on load
            checkCameraAvailability();
        });

        async function startCamera() {
            try {
                updateCameraStatus('Starting camera...');
                
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera not supported in this browser');
                }
                
                const constraints = {
                    video: {
                        facingMode: currentFacingMode,
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                };

                cameraStream = await navigator.mediaDevices.getUserMedia(constraints);
                const video = document.getElementById('cameraPreview');
                video.srcObject = cameraStream;
                
                video.onloadedmetadata = function() {
                    updateCameraStatus('Camera ready - Click capture button to take photo');
                    updateTestResult('cameraAccess', true);
                };
                
                const captureBtn = document.getElementById('captureBtn');
                captureBtn.innerHTML = '<i class="fas fa-camera"></i>';
                captureBtn.title = 'Capture Photo';
                
                const stopCameraBtn = document.getElementById('stopCameraBtn');
                if (stopCameraBtn) stopCameraBtn.style.display = 'flex';
                
                checkCameraAvailability();
                
            } catch (error) {
                console.error('Error accessing camera:', error);
                let errorMessage = 'Unable to access camera. ';
                
                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please allow camera permissions and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else {
                    errorMessage += error.message;
                }
                
                updateCameraStatus(errorMessage, 'error');
                updateTestResult('cameraAccess', false);
            }
        }

        function capturePhoto() {
            const video = document.getElementById('cameraPreview');
            const canvas = document.getElementById('captureCanvas');
            const context = canvas.getContext('2d');
            
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            context.drawImage(video, 0, 0);
            
            canvas.toBlob(function(blob) {
                capturedImageBlob = blob;
                
                const capturedImagePreview = document.getElementById('capturedImagePreview');
                const capturedImage = document.getElementById('capturedImage');
                const cameraPreview = document.getElementById('cameraPreview');
                
                capturedImage.src = URL.createObjectURL(blob);
                cameraPreview.style.display = 'none';
                capturedImagePreview.style.display = 'block';
                
                updateCameraStatus('Photo captured successfully!');
                updateTestResult('photoCapture', true);
                updateTestResult('imageQuality', blob.size > 10000); // Basic quality check
                
            }, 'image/jpeg', 0.8);
        }

        async function switchCamera() {
            if (!cameraStream) {
                return;
            }
            
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                if (videoDevices.length < 2) {
                    updateCameraStatus('Only one camera available');
                    return;
                }
                
                currentFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
                updateCameraStatus('Switching camera...');
                
                stopCamera();
                await startCamera();
                
                updateTestResult('cameraSwitching', true);
                
            } catch (error) {
                console.error('Error switching camera:', error);
                updateTestResult('cameraSwitching', false);
            }
        }

        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                
                const video = document.getElementById('cameraPreview');
                video.srcObject = null;
                
                updateCameraStatus('Camera stopped');
                
                const captureBtn = document.getElementById('captureBtn');
                captureBtn.innerHTML = '<i class="fas fa-camera"></i>';
                captureBtn.title = 'Start Camera';
                
                const switchCameraBtn = document.getElementById('switchCameraBtn');
                const stopCameraBtn = document.getElementById('stopCameraBtn');
                if (switchCameraBtn) switchCameraBtn.style.display = 'none';
                if (stopCameraBtn) stopCameraBtn.style.display = 'none';
            }
        }

        function retakePhoto() {
            const capturedImagePreview = document.getElementById('capturedImagePreview');
            const cameraPreview = document.getElementById('cameraPreview');
            
            capturedImagePreview.style.display = 'none';
            cameraPreview.style.display = 'block';
            
            capturedImageBlob = null;
            
            const capturedImage = document.getElementById('capturedImage');
            if (capturedImage.src) {
                URL.revokeObjectURL(capturedImage.src);
                capturedImage.src = '';
            }
            
            updateCameraStatus('Camera ready - Click capture button to take photo');
        }

        function updateCameraStatus(message, type = 'info') {
            const statusElement = document.getElementById('cameraStatus');
            if (statusElement) {
                const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
                statusElement.innerHTML = `<i class="${icon} me-1"></i><span>${message}</span>`;
                
                if (type === 'error') {
                    statusElement.style.background = 'rgba(220, 53, 69, 0.1)';
                    statusElement.style.color = '#dc3545';
                } else {
                    statusElement.style.background = 'rgba(13, 110, 253, 0.1)';
                    statusElement.style.color = '#0d6efd';
                }
            }
        }

        async function checkCameraAvailability() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                const switchCameraBtn = document.getElementById('switchCameraBtn');
                if (switchCameraBtn) {
                    if (videoDevices.length > 1) {
                        switchCameraBtn.style.display = 'flex';
                        switchCameraBtn.title = `Switch Camera (${videoDevices.length} available)`;
                    } else {
                        switchCameraBtn.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Error checking camera availability:', error);
            }
        }

        function updateTestResult(test, passed) {
            testResults[test] = passed;
            const resultsElement = document.getElementById('testResults');
            
            const icons = {
                cameraAccess: passed ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>',
                cameraSwitching: passed ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>',
                photoCapture: passed ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>',
                imageQuality: passed ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'
            };
            
            resultsElement.innerHTML = `
                <li>${icons.cameraAccess} Camera access: ${testResults.cameraAccess ? 'Passed' : 'Failed'}</li>
                <li>${icons.cameraSwitching} Camera switching: ${testResults.cameraSwitching ? 'Passed' : 'Not tested'}</li>
                <li>${icons.photoCapture} Photo capture: ${testResults.photoCapture ? 'Passed' : 'Not tested'}</li>
                <li>${icons.imageQuality} Image quality: ${testResults.imageQuality ? 'Passed' : 'Not tested'}</li>
            `;
        }
    </script>
</body>
</html>
