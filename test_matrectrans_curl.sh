#!/bin/bash

# Test MXAPIINVENTORY with MATRECTRANS child object using curl
# Based on MXAPIINVENTORY.xml structure found in codebase

echo "🔧 Testing MXAPIINVENTORY with MATRECTRANS child object"
echo "======================================================"

# Maximo base URL
MAXIMO_BASE="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

# MXAPIINVENTORY endpoint with MxLoader pattern (like successful cost adjustments)
ENDPOINT="$MAXIMO_BASE/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"

# Headers for MxLoader pattern
HEADERS='-H "Accept: application/json" -H "Content-Type: application/json" -H "x-method-override: BULK"'

# Payload with MXAPIINVENTORY parent and MATRECTRANS child (from MXAPIINVENTORY.xml)
PAYLOAD='[
  {
    "_action": "AddChange",
    "itemnum": "5975-60-V00-0529",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKWT",
    "location": "RIP001",
    "issueunit": "RO",
    "matrectrans": [
      {
        "_action": "Replace",
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058",
        "transdate": "'$(date -u +"%Y-%m-%dT%H:%M:%S+00:00")'",
        "issueunit": "RO",
        "frombinnum": "28-800-0004",
        "tobinnum": "1058-TEMP",
        "fromlotnum": "TEST",
        "tolotnum": "TEST",
        "fromconditioncode": "A1",
        "toconditioncode": "A1"
      }
    ]
  }
]'

echo "Endpoint: $ENDPOINT"
echo ""
echo "Payload:"
echo "$PAYLOAD" | jq '.' 2>/dev/null || echo "$PAYLOAD"
echo ""

echo "🚀 Executing curl request..."
echo ""

# Execute curl with session cookies and show detailed response
curl -X POST "$ENDPOINT" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\n\n📊 Response Details:\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nContent Type: %{content_type}\n" \
  -v 2>&1

echo ""
echo "✅ Curl test completed!"
echo ""
echo "📋 Expected Results:"
echo "- HTTP 200 with _responsemeta.status=204 = Success"
echo "- HTTP 400 with error details = Payload/field issues"
echo "- HTTP 401/302 = Authentication issues"
echo "- HTTP 404 = Endpoint doesn't exist"
