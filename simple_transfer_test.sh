#!/bin/bash

# Simple Transfer Test - Get actual responses
echo "🔍 Simple Transfer Test - Getting Actual Responses"
echo "=================================================="

MAXIMO_BASE="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

# Simple payload
PAYLOAD='{"itemnum":"5975-60-V00-0529","fromsiteid":"LCVKWT","tositeid":"IKWAJ","fromlocation":"RIP001","tolocation":"KWAJ-1058","quantity":1.0}'

echo ""
echo "1️⃣ Testing MXAPIMATRECTRANS (Material Transaction) - Most Likely Correct"
echo "======================================================================="
curl -X POST "$MAXIMO_BASE/oslc/os/mxapimatrectrans" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD" \
  --max-time 10 \
  -s -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Request failed or timed out"

echo ""
echo "2️⃣ Testing MXAPITRANSFER"
echo "========================"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapitransfer" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD" \
  --max-time 10 \
  -s -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Request failed or timed out"

echo ""
echo "3️⃣ Testing MXAPIINVTRANS"
echo "========================"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinvtrans" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD" \
  --max-time 10 \
  -s -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Request failed or timed out"

echo ""
echo "4️⃣ Testing Action Parameter"
echo "==========================="
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD" \
  --max-time 10 \
  -s -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Request failed or timed out"

echo ""
echo "5️⃣ Testing Current Implementation (Object Structure)"
echo "===================================================="
OBJECT_PAYLOAD='{"itemnum":"5975-60-V00-0529","siteid":"LCVKWT","location":"RIP001","transfercuritem":[{"itemnum":"5975-60-V00-0529","fromsiteid":"LCVKWT","tositeid":"IKWAJ","fromlocation":"RIP001","tolocation":"KWAJ-1058","quantity":1.0}]}'

curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d "$OBJECT_PAYLOAD" \
  --max-time 10 \
  -s -w "\nHTTP Status: %{http_code}\n" 2>/dev/null || echo "Request failed or timed out"

echo ""
echo "✅ Test completed!"
echo ""
echo "📋 Key Insights:"
echo "- HTTP 401 = Authentication required (need to login first)"
echo "- HTTP 404 = Endpoint doesn't exist"
echo "- HTTP 400 = Bad request (wrong payload format)"
echo "- HTTP 200/201 = Success"
echo "- Timeout = Endpoint exists but may be processing or redirecting"
