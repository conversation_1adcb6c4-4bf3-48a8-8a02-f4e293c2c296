// Main JavaScript for Maximo OAuth Login

// Universal Theme Manager
class ThemeManager {
    constructor() {
        this.init();
    }

    init() {
        // Initialize theme immediately to prevent flash
        this.applyInitialTheme();

        // Set up theme switch when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupThemeSwitch());
        } else {
            this.setupThemeSwitch();
        }
    }

    applyInitialTheme() {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // Determine initial theme
        const theme = savedTheme || (prefersDark ? 'dark' : 'light');

        // Apply theme immediately
        document.documentElement.setAttribute('data-bs-theme', theme);

        console.log(`🎨 Theme initialized: ${theme}`);
    }

    setupThemeSwitch() {
        const themeSwitch = document.getElementById('themeSwitch');
        if (!themeSwitch) {
            console.warn('⚠️ Theme switch not found on this page');
            return;
        }

        // Set initial switch state
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        themeSwitch.checked = currentTheme === 'dark';

        // Handle toggle changes
        themeSwitch.addEventListener('change', (e) => {
            const theme = e.target.checked ? 'dark' : 'light';
            this.setTheme(theme);
        });

        console.log('🎨 Theme switch initialized');
    }

    setTheme(theme) {
        // Apply theme
        document.documentElement.setAttribute('data-bs-theme', theme);
        localStorage.setItem('theme', theme);

        // Update all theme switches on the page
        const themeSwitches = document.querySelectorAll('#themeSwitch');
        themeSwitches.forEach(sw => {
            sw.checked = theme === 'dark';
        });

        // Update mobile theme button icon
        const mobileThemeButtons = document.querySelectorAll('.mobile-nav .nav-link i.fa-moon, .mobile-nav .nav-link i.fa-sun');
        mobileThemeButtons.forEach(icon => {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        });

        console.log(`🎨 Theme changed to: ${theme}`);

        // Dispatch custom event for other scripts to listen to
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    getCurrentTheme() {
        return document.documentElement.getAttribute('data-bs-theme') || 'light';
    }
}

// Initialize theme manager immediately
window.themeManager = new ThemeManager();

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Main.js loaded - Universal theme system active');

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Handle mobile navigation active states
    const mobileNavLinks = document.querySelectorAll('.mobile-nav .nav-link');
    const currentPath = window.location.pathname;

    mobileNavLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
});
