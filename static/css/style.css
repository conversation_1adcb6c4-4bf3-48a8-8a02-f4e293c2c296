/* Base styles for <PERSON><PERSON>gin - Mobile First Design */

:root {
    /* Light theme colors */
    --primary-color: #2c3e50;
    --primary-color-rgb: 44, 62, 80;
    --secondary-color: #3498db;
    --secondary-color-rgb: 52, 152, 219;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #ecf0f1;
    --light-color-rgb: 236, 240, 241;
    --dark-color: #34495e;
    --dark-color-rgb: 52, 73, 94;
    --background-color: #f5f7fa;
    --text-color: #333333;
    --border-color: #dfe6e9;
    --card-bg: #ffffff;
    --header-bg: #2c3e50;
    --footer-bg: #2c3e50;
    --mobile-nav-bg: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    /* Additional theme variables for better theming */
    --header-text-color: #ffffff;
    --header-text-color-rgb: 255, 255, 255;
    --nav-text-color: rgba(255, 255, 255, 0.7);
    --nav-text-active-color: #ffffff;
    --nav-hover-bg: rgba(255, 255, 255, 0.1);
    --border-light: rgba(255, 255, 255, 0.1);
    --footer-text-color: rgba(255, 255, 255, 0.8);
}

/* Dark theme colors */
[data-bs-theme="dark"] {
    --primary-color: #3498db;
    --primary-color-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-color-rgb: 44, 62, 80;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #34495e;
    --light-color-rgb: 52, 73, 94;
    --dark-color: #ecf0f1;
    --dark-color-rgb: 236, 240, 241;
    --background-color: #1a1a2e;
    --text-color: #ecf0f1;
    --border-color: #34495e;
    --card-bg: #16213e;
    --header-bg: #0f3460;
    --footer-bg: #0f3460;
    --mobile-nav-bg: #0f3460;
    --shadow-color: rgba(0, 0, 0, 0.3);
    /* Dark theme specific overrides */
    --header-text-color: #ecf0f1;
    --header-text-color-rgb: 236, 240, 241;
    --nav-text-color: rgba(236, 240, 241, 0.7);
    --nav-text-active-color: #ecf0f1;
    --nav-hover-bg: rgba(236, 240, 241, 0.1);
    --border-light: rgba(236, 240, 241, 0.1);
    --footer-text-color: rgba(236, 240, 241, 0.8);
}

/* Base styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
    padding-top: 60px; /* Space for fixed header */
    padding-bottom: 70px; /* Space for mobile nav */
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header styles */
.app-header {
    background-color: var(--header-bg);
    box-shadow: var(--box-shadow);
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 1030;
    border-bottom: 1px solid var(--border-light);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.app-title a {
    color: var(--header-text-color);
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
}

.user-info {
    color: var(--header-text-color);
    margin-right: 15px;
    opacity: 0.9;
}

/* Theme switch styling */
.app-header .form-check-input {
    background-color: rgba(var(--header-text-color-rgb), 0.2);
    border-color: rgba(var(--header-text-color-rgb), 0.3);
}

.app-header .form-check-input:checked {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.app-header .form-check-label {
    color: var(--header-text-color);
    opacity: 0.9;
}

/* Mobile bottom navigation */
.mobile-nav {
    background-color: var(--mobile-nav-bg);
    box-shadow: 0 -2px 10px var(--shadow-color);
    height: 60px;
    z-index: 1020;
    border-top: 1px solid var(--border-light);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--nav-text-color);
    padding: 0.5rem 0;
    font-size: 0.75rem;
    transition: var(--transition);
    flex: 1;
    text-decoration: none;
}

.mobile-nav .nav-link i {
    font-size: 1.3rem;
    margin-bottom: 0.25rem;
}

.mobile-nav .nav-link.active {
    color: var(--nav-text-active-color);
}

.mobile-nav .nav-link:hover {
    color: var(--nav-text-active-color);
    background-color: var(--nav-hover-bg);
}

/* Main content area */
.app-content {
    flex: 1;
    padding: 1.75rem 0;
    margin-top: 0.5rem;
}

/* Footer styles */
.app-footer {
    background-color: var(--footer-bg);
    padding: 1rem 0;
    border-top: 1px solid var(--border-light);
    color: var(--footer-text-color);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Card styles */
.card {
    background-color: var(--card-bg);
    border: none;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Alert customization */
.alert {
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

/* Form controls */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.input-group-text {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--header-text-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

/* Badge styling */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 50px;
}

/* Theme toggle */
.theme-toggle {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    cursor: pointer;
}

/* Welcome page specific styles */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Enhanced mobile navigation for welcome page */
.welcome-mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--mobile-nav-bg);
    padding: 0.5rem;
    box-shadow: 0 -2px 10px var(--shadow-color);
    z-index: 1020;
    display: none;
}

.welcome-mobile-nav .nav-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    max-width: 400px;
    margin: 0 auto;
}

.welcome-mobile-nav .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    color: var(--footer-text-color);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.75rem;
}

.welcome-mobile-nav .nav-item:hover {
    color: var(--nav-text-active-color);
    background: var(--nav-hover-bg);
}

.welcome-mobile-nav .nav-item i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

/* Enhanced button styles for mobile */
.btn-mobile-friendly {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-mobile-friendly:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced card styles for mobile */
.card-mobile-optimized {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 1rem;
    overflow: hidden;
}

.card-mobile-optimized .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--header-text-color);
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.card-mobile-optimized .card-body {
    padding: 1rem;
}

/* Helper classes for colors */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Dark mode specific improvements for better contrast */
[data-bs-theme="dark"] .table-light {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .table-light th {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .badge.bg-light {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .card-header.bg-dark {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

[data-bs-theme="dark"] .btn-outline-light {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .btn-outline-light:hover {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

/* NUCLEAR OPTION: Kill the table-light white background completely */
[data-bs-theme="dark"] .table-light,
[data-bs-theme="dark"] thead.table-light,
[data-bs-theme="dark"] .table-light th,
[data-bs-theme="dark"] .table-light td,
[data-bs-theme="dark"] thead.table-light th,
[data-bs-theme="dark"] thead.table-light td {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-color: var(--border-color) !important;
}

/* Force override Bootstrap's table headers */
[data-bs-theme="dark"] .table thead th {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-bottom: 2px solid var(--border-color) !important;
}

/* Target any table with table-light class */
[data-bs-theme="dark"] table thead.table-light,
[data-bs-theme="dark"] table .table-light {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

/* Enhanced responsive grid */
.responsive-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 576px) {
    .responsive-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .responsive-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 992px) {
    .responsive-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .welcome-mobile-nav {
        display: block;
    }

    body {
        padding-bottom: 80px; /* Extra space for welcome nav */
    }

    .app-content {
        padding: 1rem 0.5rem;
    }

    .container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .card-mobile-optimized .card-body {
        padding: 0.75rem;
    }

    .btn-mobile-friendly {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .app-content {
        padding: 1.5rem 0;
    }
}

@media (min-width: 992px) {
    .app-content {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-mobile-optimized .card-body {
        padding: 1.25rem;
    }
}
