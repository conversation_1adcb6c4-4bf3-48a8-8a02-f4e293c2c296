#!/usr/bin/env python3
"""
Debug the PO response structure to understand why processing finds 0 lines
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add backend path for imports
sys.path.append('backend/auth')

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError as e:
    print(f"❌ Cannot import MaximoTokenManager: {e}")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
TARGET_ITEM = "5975-60-V00-0001"
TARGET_SITE = "LCVKWT"

def debug_po_response_structure():
    """Debug the PO response structure."""
    print("🔍 Debugging PO Response Structure")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    if not hasattr(token_manager, 'username') or not token_manager.username:
        print("❌ User not authenticated")
        return
    
    print(f"✅ User authenticated: {token_manager.username}")
    
    # Use the exact same query as the Flask app
    api_url = f"{BASE_URL}/oslc/os/mxapipo"
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}" and status!="CAN" and status!="CLOSE"'
    
    params = {
        "oslc.select": "ponum,status,status_description,currencycode,totalcost,pretaxtotal,siteid,vendor,description,orderdate,requireddate,receipts,internal,poline",
        "oslc.where": where_clause,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    headers = {"Accept": "application/json"}
    
    print(f"API URL: {api_url}")
    print(f"Where clause: {where_clause}")
    
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(3.05, 15),
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                po_records = data.get('member', [])
                
                print(f"Total PO records: {len(po_records)}")
                
                if po_records:
                    print("\nAnalyzing PO records:")
                    for i, po in enumerate(po_records, 1):
                        print(f"\n--- PO Record {i} ---")
                        print(f"PO Number: {po.get('ponum', 'N/A')}")
                        print(f"Status: {po.get('status', 'N/A')}")
                        print(f"Site ID: {po.get('siteid', 'N/A')}")
                        
                        # Check poline structure
                        poline_data = po.get('poline')
                        print(f"POLINE data type: {type(poline_data)}")
                        print(f"POLINE data: {poline_data}")
                        
                        if isinstance(poline_data, list):
                            print(f"POLINE list length: {len(poline_data)}")
                            for j, line in enumerate(poline_data, 1):
                                print(f"  Line {j}:")
                                print(f"    Item: {line.get('itemnum', 'N/A')}")
                                print(f"    Site: {line.get('siteid', 'N/A')}")
                                print(f"    Order Qty: {line.get('orderqty', 'N/A')}")
                                print(f"    Unit Cost: {line.get('unitcost', 'N/A')}")
                                
                                # Check if this matches our filter
                                item_match = line.get('itemnum') == TARGET_ITEM
                                site_match = line.get('siteid') == TARGET_SITE
                                print(f"    Item match: {item_match}")
                                print(f"    Site match: {site_match}")
                                print(f"    Would be processed: {item_match and site_match}")
                        elif poline_data:
                            print(f"POLINE is not a list: {poline_data}")
                        else:
                            print("POLINE is None or empty")
                
                # Show raw JSON for first record
                if po_records:
                    print(f"\nRaw JSON for first record:")
                    print(json.dumps(po_records[0], indent=2)[:1000] + "..." if len(json.dumps(po_records[0], indent=2)) > 1000 else json.dumps(po_records[0], indent=2))
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Debugging PO Response Structure")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    debug_po_response_structure()
    
    print(f"\n{'='*50}")
    print("🏁 Debug Complete")
    print(f"{'='*50}")
