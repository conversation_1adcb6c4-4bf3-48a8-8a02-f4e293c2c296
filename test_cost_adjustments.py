#!/usr/bin/env python3
"""
Test script for the new cost adjustment functionality.
This script tests both average cost and standard cost adjustments.
"""
import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_ITEM = "5975-60-V00-0529"
TEST_SITE = "LCVKWT"
TEST_LOCATION = "RIP001"

def test_avgcost_adjustment():
    """Test average cost adjustment endpoint"""
    print("🔧 Testing Average Cost Adjustment")
    print("=" * 50)
    
    # Create test payload matching the expected structure
    payload = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": TEST_LOCATION,
            "invcost": [
                {
                    "avgcost": "1000.00",
                    "conditioncode": "A1"
                }
            ]
        }
    ]
    
    print(f"📋 Test Payload:")
    print(json.dumps(payload, indent=2))
    print()
    
    try:
        # Test the API endpoint
        response = requests.post(
            f"{BASE_URL}/api/inventory/avgcost-adjustment",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📡 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Average Cost Adjustment Response:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                print(f"🎉 SUCCESS: Average cost adjustment submitted successfully!")
                print(f"   - Item: {result.get('itemnum')}")
                print(f"   - New Average Cost: ${result.get('new_avgcost')}")
                print(f"   - Condition Code: {result.get('conditioncode')}")
                print(f"   - Submitted By: {result.get('submitted_by')}")
                return True
            else:
                print(f"❌ FAILED: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_stdcost_adjustment():
    """Test standard cost adjustment endpoint"""
    print("\n🔧 Testing Standard Cost Adjustment")
    print("=" * 50)
    
    # Create test payload matching the expected structure
    payload = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": TEST_LOCATION,
            "invcost": [
                {
                    "stdcost": 200.00,
                    "conditioncode": "A1"
                }
            ]
        }
    ]
    
    print(f"📋 Test Payload:")
    print(json.dumps(payload, indent=2))
    print()
    
    try:
        # Test the API endpoint
        response = requests.post(
            f"{BASE_URL}/api/inventory/stdcost-adjustment",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📡 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Standard Cost Adjustment Response:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                print(f"🎉 SUCCESS: Standard cost adjustment submitted successfully!")
                print(f"   - Item: {result.get('itemnum')}")
                print(f"   - New Standard Cost: ${result.get('new_stdcost')}")
                print(f"   - Condition Code: {result.get('conditioncode')}")
                print(f"   - Submitted By: {result.get('submitted_by')}")
                return True
            else:
                print(f"❌ FAILED: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_payload_validation():
    """Test payload validation"""
    print("\n🔧 Testing Payload Validation")
    print("=" * 50)
    
    # Test invalid payloads
    test_cases = [
        {
            "name": "Empty payload",
            "payload": [],
            "expected_error": "Invalid payload structure"
        },
        {
            "name": "Missing _action",
            "payload": [{"itemnum": TEST_ITEM}],
            "expected_error": "Missing required field: _action"
        },
        {
            "name": "Missing invcost",
            "payload": [{
                "_action": "AddChange",
                "itemnum": TEST_ITEM,
                "itemsetid": "ITEMSET",
                "siteid": TEST_SITE,
                "location": TEST_LOCATION
            }],
            "expected_error": "Missing required field: invcost"
        },
        {
            "name": "Empty invcost array",
            "payload": [{
                "_action": "AddChange",
                "itemnum": TEST_ITEM,
                "itemsetid": "ITEMSET",
                "siteid": TEST_SITE,
                "location": TEST_LOCATION,
                "invcost": []
            }],
            "expected_error": "Invalid invcost structure"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/inventory/avgcost-adjustment",
                json=test_case['payload'],
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 400:
                result = response.json()
                if test_case['expected_error'] in result.get('error', ''):
                    print(f"✅ Validation working: {result.get('error')}")
                else:
                    print(f"❌ Unexpected error: {result.get('error')}")
            else:
                print(f"❌ Expected 400 status, got {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")

def main():
    """Main test function"""
    print("🚀 COST ADJUSTMENT FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"🎯 Target Item: {TEST_ITEM}")
    print(f"🏢 Target Site: {TEST_SITE}")
    print(f"📍 Target Location: {TEST_LOCATION}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    results = []
    
    # Test average cost adjustment
    results.append(("Average Cost Adjustment", test_avgcost_adjustment()))
    
    # Test standard cost adjustment  
    results.append(("Standard Cost Adjustment", test_stdcost_adjustment()))
    
    # Test payload validation
    test_payload_validation()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Cost adjustment functionality is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
