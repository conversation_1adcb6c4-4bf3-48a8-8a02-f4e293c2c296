#!/usr/bin/env python3
"""
Test script to verify the availability API authentication fix.
"""

import requests
import json
import sys

def test_unauthenticated_availability_request():
    """Test availability API without authentication to verify proper error handling."""
    print("🔐 Testing Unauthenticated Availability API Request")
    print("=" * 60)
    
    api_url = "http://127.0.0.1:5010/api/inventory/availability/5975-60-V00-0001"
    params = {"siteid": "LCVKWT"}
    
    try:
        # Make request without authentication cookies
        response = requests.get(api_url, params=params, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
                
                if data.get('success'):
                    print("❌ UNEXPECTED: API returned success without authentication")
                    return False
                else:
                    error_msg = data.get('error', 'Unknown error')
                    print(f"✅ EXPECTED: API returned error: {error_msg}")
                    
                    # Check if it's the expected authentication error
                    if 'Not logged in' in error_msg or 'Authentication' in error_msg:
                        print("✅ CORRECT: Proper authentication error returned")
                        return True
                    else:
                        print("⚠️ WARNING: Unexpected error message format")
                        return True  # Still acceptable as long as it's an error
            except json.JSONDecodeError:
                print(f"❌ ERROR: Invalid JSON response: {response.text[:200]}")
                return False
        else:
            print(f"✅ EXPECTED: HTTP Error {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return True
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        return False

def test_session_status_endpoint():
    """Test the session status endpoint."""
    print("\n🔍 Testing Session Status Endpoint")
    print("=" * 60)
    
    try:
        response = requests.get("http://127.0.0.1:5010/api/session-status", timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 401:
            try:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
                
                if not data.get('authenticated', True):
                    print("✅ CORRECT: Session status correctly reports not authenticated")
                    return True
                else:
                    print("❌ ERROR: Session status incorrectly reports authenticated")
                    return False
            except json.JSONDecodeError:
                print(f"❌ ERROR: Invalid JSON response: {response.text}")
                return False
        else:
            print(f"⚠️ UNEXPECTED: Status code {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        return False

def test_inventory_page_redirect():
    """Test if inventory management page redirects to login."""
    print("\n🔄 Testing Inventory Management Page Redirect")
    print("=" * 60)
    
    try:
        response = requests.get("http://127.0.0.1:5010/inventory-management", 
                              allow_redirects=False, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f"Redirect Location: {location}")
            
            if 'login' in location.lower():
                print("✅ CORRECT: Inventory page redirects to login")
                return True
            else:
                print("⚠️ WARNING: Redirects to unexpected location")
                return False
        elif response.status_code == 200:
            print("❌ ERROR: Page loads without authentication (security issue)")
            return False
        else:
            print(f"⚠️ UNEXPECTED: Status code {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        return False

def provide_solution_guidance():
    """Provide guidance on how to resolve the authentication issue."""
    print("\n💡 SOLUTION GUIDANCE")
    print("=" * 60)
    
    print("To resolve the 'Network error occurred while fetching availability data' issue:")
    print()
    print("1. 🔐 AUTHENTICATION REQUIRED:")
    print("   • The availability API requires proper authentication")
    print("   • You must be logged in through the web interface")
    print()
    print("2. 📋 LOGIN STEPS:")
    print("   • Go to: http://127.0.0.1:5010/login/login")
    print("   • Enter your Maximo username and password")
    print("   • Wait for authentication to complete")
    print("   • You should be redirected to the welcome page")
    print()
    print("3. 🔍 ACCESS INVENTORY MANAGEMENT:")
    print("   • After successful login, go to: http://127.0.0.1:5010/inventory-management")
    print("   • You should now see the inventory search interface")
    print()
    print("4. 🧪 TEST AVAILABILITY MODAL:")
    print("   • Search for test items: '5975-60-V00-0001' or '8010-60-V00-0113'")
    print("   • Click the green 'View Availability' button")
    print("   • The availability modal should now load successfully")
    print("   • You should see the new diary-style vertical sidebar navigation")
    print()
    print("5. 🐛 IF STILL GETTING ERRORS:")
    print("   • Check browser developer console for detailed error messages")
    print("   • Ensure cookies are enabled in your browser")
    print("   • Try refreshing the page after login")
    print("   • Clear browser cache if necessary")
    print()
    print("6. 📱 MOBILE TESTING:")
    print("   • Use browser developer tools to simulate mobile device")
    print("   • Verify the Summary tab is visible and accessible")
    print("   • Test tab switching without scrolling")

def main():
    """Run all authentication tests."""
    print("🧪 Testing Availability API Authentication Fix")
    print("=" * 70)
    
    tests = [
        test_unauthenticated_availability_request,
        test_session_status_endpoint,
        test_inventory_page_redirect
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed >= total * 0.8:  # 80% pass rate
        print(f"✅ {passed}/{total} tests passed! Authentication fix is working correctly.")
        print("\n📋 Key improvements:")
        print("   • Fixed authentication check to use Flask session")
        print("   • Enhanced error messages for better user guidance")
        print("   • Proper session validation with token manager")
        print("   • Clear authentication requirements")
    else:
        print(f"❌ {passed}/{total} tests passed. Please review the authentication fix.")
    
    # Always provide solution guidance
    provide_solution_guidance()
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
