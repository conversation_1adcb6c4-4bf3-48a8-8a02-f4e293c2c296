#!/usr/bin/env python3
"""
Comprehensive MXAPIINVENTORY Endpoint Investigation Script

This script systematically investigates the MXAPIINVENTORY endpoint to discover:
1. All available HTTP methods and their capabilities
2. Complete field schemas and data types
3. Business rules and validation constraints
4. Request/response structures
5. Error handling patterns

Author: Augment Agent
Date: 2025-01-15
"""

import sys
import os
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.auth.token_manager import MaximoTokenManager

class MXAPIInventoryInvestigator:
    """Comprehensive investigator for MXAPIINVENTORY endpoint capabilities."""
    
    def __init__(self, base_url: str):
        """Initialize the investigator with Maximo base URL."""
        self.base_url = base_url
        self.token_manager = MaximoTokenManager(base_url)
        self.results = {
            'investigation_timestamp': datetime.now().isoformat(),
            'base_url': base_url,
            'endpoint': 'MXAPIINVENTORY',
            'prerequisites': {},
            'http_methods': {},
            'field_schemas': {},
            'business_rules': {},
            'error_patterns': {},
            'examples': {}
        }
        
    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def verify_prerequisites(self) -> bool:
        """Verify that all prerequisites are met for investigation."""
        self.log("🔍 STEP 1: Verifying Prerequisites", "INFO")
        self.log("=" * 60)
        
        # Check authentication
        if not self.token_manager.is_logged_in():
            self.log("❌ Authentication failed - not logged in", "ERROR")
            self.results['prerequisites']['authentication'] = False
            return False
            
        self.log("✅ Authentication verified", "SUCCESS")
        self.results['prerequisites']['authentication'] = True
        
        # Get user info
        try:
            profile = self.token_manager.get_user_profile()
            if profile:
                username = profile.get('personid', 'unknown')
                self.log(f"✅ Authenticated as: {username}", "SUCCESS")
                self.results['prerequisites']['username'] = username
            else:
                self.log("⚠️ Could not retrieve user profile", "WARNING")
        except Exception as e:
            self.log(f"⚠️ Error getting user profile: {e}", "WARNING")
            
        # Test basic endpoint accessibility
        test_url = f"{self.base_url}/oslc/os/mxapiinventory"
        try:
            response = self.token_manager.session.head(
                test_url,
                timeout=(3.05, 10),
                allow_redirects=False
            )
            
            if response.status_code in [200, 405, 501]:  # 405/501 means endpoint exists but method not allowed
                self.log("✅ MXAPIINVENTORY endpoint is accessible", "SUCCESS")
                self.results['prerequisites']['endpoint_accessible'] = True
            else:
                self.log(f"⚠️ Endpoint returned status {response.status_code}", "WARNING")
                self.results['prerequisites']['endpoint_accessible'] = False
                
        except Exception as e:
            self.log(f"❌ Error testing endpoint accessibility: {e}", "ERROR")
            self.results['prerequisites']['endpoint_accessible'] = False
            return False
            
        self.log("✅ All prerequisites verified successfully", "SUCCESS")
        return True
        
    def discover_http_methods(self) -> Dict[str, Any]:
        """Discover all supported HTTP methods and their capabilities."""
        self.log("🔍 STEP 2: Discovering HTTP Methods", "INFO")
        self.log("=" * 60)
        
        methods_to_test = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD']
        endpoint_variants = [
            f"{self.base_url}/oslc/os/mxapiinventory",
            f"{self.base_url}/api/os/mxapiinventory"
        ]
        
        method_results = {}
        
        for endpoint in endpoint_variants:
            self.log(f"Testing endpoint: {endpoint}")
            endpoint_key = "oslc" if "oslc" in endpoint else "api"
            method_results[endpoint_key] = {}
            
            for method in methods_to_test:
                self.log(f"  Testing {method} method...")
                result = self._test_http_method(endpoint, method)
                method_results[endpoint_key][method] = result
                
                if result['supported']:
                    self.log(f"    ✅ {method} supported (status: {result['status_code']})")
                else:
                    self.log(f"    ❌ {method} not supported (status: {result['status_code']})")
                    
        self.results['http_methods'] = method_results
        return method_results
        
    def _test_http_method(self, endpoint: str, method: str) -> Dict[str, Any]:
        """Test a specific HTTP method on the endpoint."""
        try:
            # Prepare minimal test parameters for GET requests
            params = {}
            headers = {"Accept": "application/json"}
            data = None
            
            if method == 'GET':
                params = {
                    "oslc.select": "itemnum",
                    "oslc.pageSize": "1",
                    "lean": "1"
                }
            elif method in ['POST', 'PUT', 'PATCH']:
                headers["Content-Type"] = "application/json"
                # Use minimal test data
                data = json.dumps([{"_action": "AddChange", "itemnum": "TEST"}])
                
            # Make the request
            response = self.token_manager.session.request(
                method=method,
                url=endpoint,
                params=params if method == 'GET' else None,
                data=data if method in ['POST', 'PUT', 'PATCH'] else None,
                headers=headers,
                timeout=(3.05, 15),
                allow_redirects=False
            )
            
            # Analyze response
            result = {
                'supported': response.status_code not in [404, 405, 501],
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'content_type': response.headers.get('content-type', ''),
                'response_size': len(response.content),
                'allows_method': method.upper() in response.headers.get('Allow', '').upper()
            }
            
            # Try to parse response if it's JSON
            if 'application/json' in result['content_type'] and response.content:
                try:
                    result['sample_response'] = response.json()
                except:
                    result['sample_response'] = response.text[:200]
            else:
                result['sample_response'] = response.text[:200] if response.text else None
                
            return result
            
        except Exception as e:
            return {
                'supported': False,
                'status_code': None,
                'error': str(e),
                'headers': {},
                'content_type': '',
                'response_size': 0,
                'allows_method': False
            }
            
    def analyze_get_operations(self) -> Dict[str, Any]:
        """Analyze GET operation capabilities and field discovery."""
        self.log("🔍 STEP 3: Analyzing GET Operations", "INFO")
        self.log("=" * 60)
        
        get_results = {
            'field_discovery': {},
            'filtering_capabilities': {},
            'pagination': {},
            'response_structure': {}
        }
        
        # Test field discovery with wildcard
        self.log("Testing field discovery with wildcard selection...")
        field_discovery = self._discover_fields_via_get()
        get_results['field_discovery'] = field_discovery
        
        # Test filtering capabilities
        self.log("Testing filtering capabilities...")
        filtering = self._test_filtering_capabilities()
        get_results['filtering_capabilities'] = filtering
        
        # Test pagination
        self.log("Testing pagination capabilities...")
        pagination = self._test_pagination()
        get_results['pagination'] = pagination
        
        self.results['get_operations'] = get_results
        return get_results
        
    def _discover_fields_via_get(self) -> Dict[str, Any]:
        """Discover all available fields using GET with wildcard selection."""
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"
        
        # Test with wildcard to get all fields
        params = {
            "oslc.select": "*",
            "oslc.pageSize": "1",
            "lean": "0"  # Get full response to see all nested structures
        }
        
        try:
            response = self.token_manager.session.get(
                endpoint,
                params=params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                members = data.get('member', [])
                
                if members:
                    first_item = members[0]
                    fields = self._analyze_field_structure(first_item)
                    
                    return {
                        'success': True,
                        'total_fields': len(fields),
                        'fields': fields,
                        'sample_record': first_item
                    }
                else:
                    return {
                        'success': False,
                        'error': 'No inventory records found',
                        'response_structure': data
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': response.text[:500]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
            
    def _analyze_field_structure(self, record: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """Recursively analyze field structure and data types."""
        fields = {}
        
        for key, value in record.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            field_info = {
                'data_type': type(value).__name__,
                'sample_value': value,
                'is_nested': isinstance(value, (dict, list))
            }
            
            if isinstance(value, dict):
                field_info['nested_fields'] = self._analyze_field_structure(value, full_key)
            elif isinstance(value, list) and value and isinstance(value[0], dict):
                field_info['array_item_structure'] = self._analyze_field_structure(value[0], f"{full_key}[0]")
                
            fields[full_key] = field_info
            
        return fields

    def _test_filtering_capabilities(self) -> Dict[str, Any]:
        """Test various filtering capabilities of the GET endpoint."""
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"
        filtering_tests = {}

        # Test basic field filtering
        test_cases = [
            {
                'name': 'site_filter',
                'params': {
                    "oslc.select": "itemnum,siteid",
                    "oslc.where": 'siteid="LCVKWT"',
                    "oslc.pageSize": "5",
                    "lean": "1"
                }
            },
            {
                'name': 'status_filter',
                'params': {
                    "oslc.select": "itemnum,status",
                    "oslc.where": 'status="ACTIVE"',
                    "oslc.pageSize": "5",
                    "lean": "1"
                }
            },
            {
                'name': 'item_pattern_filter',
                'params': {
                    "oslc.select": "itemnum,description",
                    "oslc.where": 'itemnum like "5975%"',
                    "oslc.pageSize": "5",
                    "lean": "1"
                }
            },
            {
                'name': 'combined_filter',
                'params': {
                    "oslc.select": "itemnum,siteid,status",
                    "oslc.where": 'siteid="LCVKWT" and status="ACTIVE"',
                    "oslc.pageSize": "5",
                    "lean": "1"
                }
            }
        ]

        for test_case in test_cases:
            try:
                response = self.token_manager.session.get(
                    endpoint,
                    params=test_case['params'],
                    timeout=(3.05, 15),
                    headers={"Accept": "application/json"}
                )

                filtering_tests[test_case['name']] = {
                    'success': response.status_code == 200,
                    'status_code': response.status_code,
                    'params_used': test_case['params'],
                    'record_count': len(response.json().get('member', [])) if response.status_code == 200 else 0
                }

            except Exception as e:
                filtering_tests[test_case['name']] = {
                    'success': False,
                    'error': str(e),
                    'params_used': test_case['params']
                }

        return filtering_tests

    def _test_pagination(self) -> Dict[str, Any]:
        """Test pagination capabilities."""
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"

        # Test different page sizes
        pagination_tests = {}

        for page_size in [1, 5, 10, 50]:
            try:
                params = {
                    "oslc.select": "itemnum",
                    "oslc.pageSize": str(page_size),
                    "lean": "1"
                }

                response = self.token_manager.session.get(
                    endpoint,
                    params=params,
                    timeout=(3.05, 15),
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    pagination_tests[f'page_size_{page_size}'] = {
                        'success': True,
                        'returned_count': len(data.get('member', [])),
                        'has_next_page': 'oslc:nextPage' in data,
                        'total_count': data.get('oslc:totalCount', 'unknown')
                    }
                else:
                    pagination_tests[f'page_size_{page_size}'] = {
                        'success': False,
                        'status_code': response.status_code
                    }

            except Exception as e:
                pagination_tests[f'page_size_{page_size}'] = {
                    'success': False,
                    'error': str(e)
                }

        return pagination_tests

    def analyze_post_operations(self) -> Dict[str, Any]:
        """Analyze POST operation capabilities for creating/updating inventory."""
        self.log("🔍 STEP 4: Analyzing POST Operations", "INFO")
        self.log("=" * 60)

        post_results = {
            'supported_actions': {},
            'required_fields': {},
            'validation_rules': {},
            'response_patterns': {}
        }

        # Test different POST actions
        actions_to_test = [
            'AddChange',
            'Create',
            'Update',
            'Sync'
        ]

        for action in actions_to_test:
            self.log(f"Testing POST action: {action}")
            result = self._test_post_action(action)
            post_results['supported_actions'][action] = result

        self.results['post_operations'] = post_results
        return post_results

    def _test_post_action(self, action: str) -> Dict[str, Any]:
        """Test a specific POST action with minimal payload."""
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"

        # Create minimal test payload
        test_payload = [
            {
                "_action": action,
                "itemnum": "TEST-ITEM-001",
                "siteid": "LCVKWT",
                "location": "TEST-LOC"
            }
        ]

        try:
            response = self.token_manager.session.post(
                endpoint,
                json=test_payload,
                timeout=(3.05, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )

            result = {
                'status_code': response.status_code,
                'success': response.status_code in [200, 201, 204],
                'headers': dict(response.headers),
                'response_size': len(response.content)
            }

            # Parse response
            if response.content:
                try:
                    result['response_data'] = response.json()
                except:
                    result['response_text'] = response.text[:500]

            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'status_code': None
            }

    def analyze_put_patch_operations(self) -> Dict[str, Any]:
        """Analyze PUT and PATCH operation capabilities."""
        self.log("🔍 STEP 5: Analyzing PUT/PATCH Operations", "INFO")
        self.log("=" * 60)

        update_results = {
            'put_capabilities': {},
            'patch_capabilities': {},
            'update_patterns': {}
        }

        # Test PUT operations
        self.log("Testing PUT operations...")
        put_result = self._test_update_method('PUT')
        update_results['put_capabilities'] = put_result

        # Test PATCH operations
        self.log("Testing PATCH operations...")
        patch_result = self._test_update_method('PATCH')
        update_results['patch_capabilities'] = patch_result

        self.results['update_operations'] = update_results
        return update_results

    def _test_update_method(self, method: str) -> Dict[str, Any]:
        """Test PUT or PATCH method capabilities."""
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"

        # Test with minimal update payload
        test_payload = {
            "itemnum": "TEST-ITEM-001",
            "siteid": "LCVKWT",
            "status": "ACTIVE"
        }

        try:
            response = self.token_manager.session.request(
                method=method,
                url=endpoint,
                json=test_payload,
                timeout=(3.05, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )

            return {
                'status_code': response.status_code,
                'supported': response.status_code not in [404, 405, 501],
                'headers': dict(response.headers),
                'response_size': len(response.content),
                'response_preview': response.text[:200] if response.text else None
            }

        except Exception as e:
            return {
                'supported': False,
                'error': str(e),
                'status_code': None
            }

    def analyze_delete_operations(self) -> Dict[str, Any]:
        """Analyze DELETE operation capabilities."""
        self.log("🔍 STEP 6: Analyzing DELETE Operations", "INFO")
        self.log("=" * 60)

        delete_results = {
            'delete_support': {},
            'deletion_patterns': {}
        }

        # Test DELETE method support
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"

        try:
            # Test DELETE without specific resource (should return method info)
            response = self.token_manager.session.delete(
                endpoint,
                timeout=(3.05, 15),
                headers={"Accept": "application/json"}
            )

            delete_results['delete_support'] = {
                'status_code': response.status_code,
                'supported': response.status_code not in [404, 405, 501],
                'headers': dict(response.headers),
                'response_preview': response.text[:200] if response.text else None
            }

        except Exception as e:
            delete_results['delete_support'] = {
                'supported': False,
                'error': str(e),
                'status_code': None
            }

        self.results['delete_operations'] = delete_results
        return delete_results

    def document_field_schemas(self) -> Dict[str, Any]:
        """Create comprehensive field documentation."""
        self.log("🔍 STEP 7: Documenting Field Schemas", "INFO")
        self.log("=" * 60)

        # Get field information from previous GET analysis
        get_ops = self.results.get('get_operations', {})
        field_discovery = get_ops.get('field_discovery', {})

        if field_discovery.get('success'):
            fields = field_discovery.get('fields', {})

            schema_doc = {
                'total_fields': len(fields),
                'field_categories': self._categorize_fields(fields),
                'data_types': self._analyze_data_types(fields),
                'nested_structures': self._identify_nested_structures(fields),
                'required_fields': self._identify_required_fields(),
                'field_documentation': fields
            }

            self.results['field_schemas'] = schema_doc
            return schema_doc
        else:
            self.log("⚠️ No field discovery data available", "WARNING")
            return {'error': 'No field discovery data available'}

    def _categorize_fields(self, fields: Dict[str, Any]) -> Dict[str, List[str]]:
        """Categorize fields by their apparent purpose."""
        categories = {
            'identifiers': [],
            'quantities': [],
            'dates': [],
            'status': [],
            'locations': [],
            'financial': [],
            'nested_objects': [],
            'other': []
        }

        for field_name, field_info in fields.items():
            if any(keyword in field_name.lower() for keyword in ['id', 'num', 'code']):
                categories['identifiers'].append(field_name)
            elif any(keyword in field_name.lower() for keyword in ['qty', 'bal', 'count', 'level']):
                categories['quantities'].append(field_name)
            elif any(keyword in field_name.lower() for keyword in ['date', 'time']):
                categories['dates'].append(field_name)
            elif any(keyword in field_name.lower() for keyword in ['status', 'state']):
                categories['status'].append(field_name)
            elif any(keyword in field_name.lower() for keyword in ['location', 'site', 'bin']):
                categories['locations'].append(field_name)
            elif any(keyword in field_name.lower() for keyword in ['cost', 'price', 'acc']):
                categories['financial'].append(field_name)
            elif field_info.get('is_nested'):
                categories['nested_objects'].append(field_name)
            else:
                categories['other'].append(field_name)

        return categories

    def _analyze_data_types(self, fields: Dict[str, Any]) -> Dict[str, int]:
        """Analyze distribution of data types."""
        type_counts = {}

        for field_info in fields.values():
            data_type = field_info.get('data_type', 'unknown')
            type_counts[data_type] = type_counts.get(data_type, 0) + 1

        return type_counts

    def _identify_nested_structures(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """Identify and document nested object structures."""
        nested = {}

        for field_name, field_info in fields.items():
            if field_info.get('is_nested'):
                nested[field_name] = {
                    'type': field_info.get('data_type'),
                    'structure': field_info.get('nested_fields') or field_info.get('array_item_structure')
                }

        return nested

    def _identify_required_fields(self) -> List[str]:
        """Identify likely required fields based on common patterns."""
        # This would need to be enhanced with actual validation testing
        common_required = [
            'itemnum',
            'siteid',
            'location',
            'itemsetid'
        ]

        return common_required

    def test_business_rules(self) -> Dict[str, Any]:
        """Test and document business rules and validation constraints."""
        self.log("🔍 STEP 8: Testing Business Rules", "INFO")
        self.log("=" * 60)

        business_rules = {
            'validation_tests': {},
            'constraint_patterns': {},
            'error_responses': {}
        }

        # Test various validation scenarios
        validation_tests = [
            {
                'name': 'missing_required_fields',
                'payload': [{"_action": "AddChange"}],
                'expected': 'validation_error'
            },
            {
                'name': 'invalid_site',
                'payload': [{"_action": "AddChange", "itemnum": "TEST", "siteid": "INVALID"}],
                'expected': 'business_rule_error'
            },
            {
                'name': 'invalid_action',
                'payload': [{"_action": "InvalidAction", "itemnum": "TEST", "siteid": "LCVKWT"}],
                'expected': 'action_error'
            }
        ]

        for test in validation_tests:
            self.log(f"Testing: {test['name']}")
            result = self._test_validation_rule(test['payload'])
            business_rules['validation_tests'][test['name']] = result

        self.results['business_rules'] = business_rules
        return business_rules

    def _test_validation_rule(self, payload: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test a specific validation rule."""
        endpoint = f"{self.base_url}/oslc/os/mxapiinventory"

        try:
            response = self.token_manager.session.post(
                endpoint,
                json=payload,
                timeout=(3.05, 15),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )

            result = {
                'status_code': response.status_code,
                'payload_sent': payload,
                'response_size': len(response.content)
            }

            if response.content:
                try:
                    result['response_data'] = response.json()
                except:
                    result['response_text'] = response.text[:300]

            return result

        except Exception as e:
            return {
                'error': str(e),
                'payload_sent': payload,
                'status_code': None
            }

    def generate_final_report(self) -> Dict[str, Any]:
        """Generate comprehensive final report of all findings."""
        self.log("🔍 STEP 9: Generating Final Report", "INFO")
        self.log("=" * 60)

        report = {
            'executive_summary': self._create_executive_summary(),
            'method_inventory': self._create_method_inventory(),
            'field_reference': self._create_field_reference(),
            'usage_examples': self._create_usage_examples(),
            'recommendations': self._create_recommendations()
        }

        self.results['final_report'] = report
        return report

    def _create_executive_summary(self) -> Dict[str, Any]:
        """Create executive summary of findings."""
        http_methods = self.results.get('http_methods', {})
        field_schemas = self.results.get('field_schemas', {})

        # Count supported methods
        supported_methods = []
        for endpoint_type, methods in http_methods.items():
            for method, details in methods.items():
                if details.get('supported'):
                    supported_methods.append(f"{method} ({endpoint_type})")

        return {
            'endpoint_accessible': self.results.get('prerequisites', {}).get('endpoint_accessible', False),
            'supported_methods': supported_methods,
            'total_fields_discovered': field_schemas.get('total_fields', 0),
            'investigation_timestamp': self.results.get('investigation_timestamp'),
            'key_capabilities': self._identify_key_capabilities()
        }

    def _identify_key_capabilities(self) -> List[str]:
        """Identify key capabilities based on investigation results."""
        capabilities = []

        http_methods = self.results.get('http_methods', {})

        # Check for read capabilities
        if any(methods.get('GET', {}).get('supported') for methods in http_methods.values()):
            capabilities.append("Read inventory data")

        # Check for write capabilities
        if any(methods.get('POST', {}).get('supported') for methods in http_methods.values()):
            capabilities.append("Create/modify inventory records")

        # Check for update capabilities
        if any(methods.get('PUT', {}).get('supported') or methods.get('PATCH', {}).get('supported')
               for methods in http_methods.values()):
            capabilities.append("Update existing records")

        # Check for delete capabilities
        if any(methods.get('DELETE', {}).get('supported') for methods in http_methods.values()):
            capabilities.append("Delete inventory records")

        return capabilities

    def _create_method_inventory(self) -> Dict[str, Any]:
        """Create detailed method inventory."""
        return {
            'http_methods': self.results.get('http_methods', {}),
            'get_operations': self.results.get('get_operations', {}),
            'post_operations': self.results.get('post_operations', {}),
            'update_operations': self.results.get('update_operations', {}),
            'delete_operations': self.results.get('delete_operations', {})
        }

    def _create_field_reference(self) -> Dict[str, Any]:
        """Create comprehensive field reference."""
        return self.results.get('field_schemas', {})

    def _create_usage_examples(self) -> Dict[str, Any]:
        """Create practical usage examples."""
        examples = {}

        # GET example
        if self.results.get('get_operations', {}).get('field_discovery', {}).get('success'):
            examples['get_inventory_data'] = {
                'method': 'GET',
                'url': f"{self.base_url}/oslc/os/mxapiinventory",
                'params': {
                    "oslc.select": "itemnum,siteid,location,curbal,status",
                    "oslc.where": 'siteid="LCVKWT" and status="ACTIVE"',
                    "oslc.pageSize": "10",
                    "lean": "1"
                },
                'description': "Retrieve active inventory items for a specific site"
            }

        # POST example (if supported)
        post_ops = self.results.get('post_operations', {})
        if post_ops.get('supported_actions', {}).get('AddChange', {}).get('success'):
            examples['create_inventory_adjustment'] = {
                'method': 'POST',
                'url': f"{self.base_url}/oslc/os/mxapiinventory",
                'payload': [
                    {
                        "_action": "AddChange",
                        "itemnum": "EXAMPLE-ITEM",
                        "itemsetid": "ITEMSET",
                        "siteid": "LCVKWT",
                        "location": "STORE-001",
                        "issueunit": "EA",
                        "invbalances": [
                            {
                                "binnum": "BIN-001",
                                "curbal": 100,
                                "physcnt": 95,
                                "conditioncode": "A1"
                            }
                        ]
                    }
                ],
                'description': "Create inventory adjustment with physical count"
            }

        return examples

    def _create_recommendations(self) -> List[str]:
        """Create implementation recommendations."""
        recommendations = []

        # Authentication recommendation
        if self.results.get('prerequisites', {}).get('authentication'):
            recommendations.append("Use session-based authentication for API calls")

        # Method recommendations
        http_methods = self.results.get('http_methods', {})
        if http_methods.get('oslc', {}).get('GET', {}).get('supported'):
            recommendations.append("Use OSLC endpoint (/oslc/os/mxapiinventory) for GET operations")

        if http_methods.get('oslc', {}).get('POST', {}).get('supported'):
            recommendations.append("Use OSLC endpoint for POST operations with proper _action field")

        # Field recommendations
        field_schemas = self.results.get('field_schemas', {})
        if field_schemas.get('required_fields'):
            recommendations.append(f"Always include required fields: {', '.join(field_schemas['required_fields'])}")

        # Error handling
        recommendations.append("Implement proper error handling for API responses")
        recommendations.append("Preserve and return actual Maximo error messages to users")

        return recommendations

    def run_full_investigation(self) -> Dict[str, Any]:
        """Run the complete investigation process."""
        self.log("🚀 Starting Comprehensive MXAPIINVENTORY Investigation", "INFO")
        self.log("=" * 80)

        try:
            # Step 1: Verify prerequisites
            if not self.verify_prerequisites():
                self.log("❌ Prerequisites not met, aborting investigation", "ERROR")
                return self.results

            # Step 2: Discover HTTP methods
            self.discover_http_methods()

            # Step 3: Analyze GET operations
            self.analyze_get_operations()

            # Step 4: Analyze POST operations
            self.analyze_post_operations()

            # Step 5: Analyze PUT/PATCH operations
            self.analyze_put_patch_operations()

            # Step 6: Analyze DELETE operations
            self.analyze_delete_operations()

            # Step 7: Document field schemas
            self.document_field_schemas()

            # Step 8: Test business rules
            self.test_business_rules()

            # Step 9: Generate final report
            self.generate_final_report()

            self.log("✅ Investigation completed successfully!", "SUCCESS")

        except Exception as e:
            self.log(f"❌ Investigation failed: {str(e)}", "ERROR")
            self.results['investigation_error'] = str(e)

        return self.results

    def save_results(self, filename: str = None) -> str:
        """Save investigation results to JSON file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mxapiinventory_investigation_{timestamp}.json"

        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            self.log(f"✅ Results saved to: {filename}", "SUCCESS")
            return filename
        except Exception as e:
            self.log(f"❌ Error saving results: {e}", "ERROR")
            return None


def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Comprehensive Investigation")
    print("=" * 80)

    # Initialize investigator
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    investigator = MXAPIInventoryInvestigator(base_url)

    # Run investigation
    results = investigator.run_full_investigation()

    # Save results
    filename = investigator.save_results()

    # Print summary
    print("\n" + "=" * 80)
    print("🎯 INVESTIGATION SUMMARY")
    print("=" * 80)

    final_report = results.get('final_report', {})
    executive_summary = final_report.get('executive_summary', {})

    print(f"📊 Endpoint Accessible: {executive_summary.get('endpoint_accessible', 'Unknown')}")
    print(f"📊 Supported Methods: {len(executive_summary.get('supported_methods', []))}")
    print(f"📊 Fields Discovered: {executive_summary.get('total_fields_discovered', 0)}")
    print(f"📊 Key Capabilities: {', '.join(executive_summary.get('key_capabilities', []))}")

    if filename:
        print(f"📄 Detailed results saved to: {filename}")

    return results


if __name__ == "__main__":
    main()
