#!/usr/bin/env python3
"""
Investigate Purchase Order Filtering for Item 5975-60-V00-0001
This script tests different filtering approaches to understand why POs aren't showing
"""

import os
import requests
import json
from datetime import datetime
from collections import Counter

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
TARGET_ITEM = "5975-60-V00-0001"
TARGET_SITE = "LCVKWT"

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def print_subsection(title):
    """Print a formatted subsection header."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def test_no_filtering():
    """Test with no status filtering to see all POs for the item."""
    print_subsection("Step 1: No Status Filtering - Show ALL POs")
    
    # Basic where clause without status filtering
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}"'
    
    return test_mxapipo_query(where_clause, "No Status Filtering")

def test_current_filtering():
    """Test with current filtering logic (excludes CAN and CLOSE)."""
    print_subsection("Step 2: Current Filtering - Exclude CAN and CLOSE")
    
    # Current enhanced where clause
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}" and status!="CAN" and status!="CLOSE"'
    
    return test_mxapipo_query(where_clause, "Current Enhanced Filtering")

def test_positive_filtering():
    """Test with positive filtering (only include known active statuses)."""
    print_subsection("Step 3: Positive Filtering - Include Only Known Active Statuses")
    
    # Common active PO statuses in Maximo
    active_statuses = ["APPR", "INPRG", "WAPPR", "WMATL", "PARTIAL", "ORDERED"]
    status_filter = " or ".join([f'status="{status}"' for status in active_statuses])
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}" and ({status_filter})'
    
    return test_mxapipo_query(where_clause, "Positive Status Filtering")

def test_mxapipo_query(where_clause, description):
    """Test MXAPIPO endpoint with given where clause."""
    
    print(f"Description: {description}")
    print(f"Where clause: {where_clause}")
    
    # API endpoint with API key (most reliable)
    api_url = f"{BASE_URL}/api/os/mxapipo"
    
    # Parameters
    params = {
        "oslc.select": "ponum,status,status_description,siteid,vendor,orderdate,totalcost,poline",
        "oslc.where": where_clause,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    # Headers
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    # Show curl command for manual testing
    curl_cmd = f"""curl -s -H "Accept: application/json" -H "apikey: {API_KEY}" \\
 "{api_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}" """
    print(f"Curl command:\n{curl_cmd}")
    
    # Make request
    try:
        response = requests.get(api_url, params=params, headers=headers, timeout=(10, 60))
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            member_count = len(data.get('member', []))
            print(f"✅ Success: Found {member_count} records")
            
            if member_count == 0:
                print("❌ No records found with this filtering")
                return []
            
            # Analyze the results
            po_records = []
            status_counts = Counter()
            
            for record in data.get('member', []):
                status = record.get('status', 'UNKNOWN')
                status_counts[status] += 1
                
                # Check if this PO has the target item in poline
                poline_records = record.get('poline', [])
                for poline in poline_records:
                    if poline.get('itemnum') == TARGET_ITEM:
                        po_records.append({
                            'ponum': record.get('ponum'),
                            'status': record.get('status'),
                            'status_description': record.get('status_description'),
                            'vendor': record.get('vendor'),
                            'orderdate': record.get('orderdate'),
                            'totalcost': record.get('totalcost'),
                            'itemnum': poline.get('itemnum'),
                            'orderqty': poline.get('orderqty'),
                            'unitcost': poline.get('unitcost'),
                            'receivedqty': poline.get('receivedqty', 0)
                        })
                        break
            
            print(f"📊 Status distribution: {dict(status_counts)}")
            print(f"📦 POs with target item: {len(po_records)}")
            
            # Show detailed PO information
            if po_records:
                print(f"\nDetailed Purchase Order Information:")
                for i, po in enumerate(po_records, 1):
                    print(f"{i}. PO #{po['ponum']}")
                    print(f"   Status: {po['status']} ({po['status_description']})")
                    print(f"   Vendor: {po['vendor']}")
                    print(f"   Order Date: {po['orderdate']}")
                    print(f"   Total Cost: ${po['totalcost']}")
                    print(f"   Item: {po['itemnum']}")
                    print(f"   Order Qty: {po['orderqty']}")
                    print(f"   Unit Cost: ${po['unitcost']}")
                    print(f"   Received Qty: {po['receivedqty']}")
                    print()
            
            return po_records
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return []
                
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return []

def analyze_results(no_filter_results, current_filter_results, positive_filter_results):
    """Analyze the results from different filtering approaches."""
    print_section("ANALYSIS OF FILTERING RESULTS")
    
    print(f"📊 Results Summary:")
    print(f"   No Filtering: {len(no_filter_results)} POs found")
    print(f"   Current Filtering (exclude CAN/CLOSE): {len(current_filter_results)} POs found")
    print(f"   Positive Filtering (include active only): {len(positive_filter_results)} POs found")
    
    if len(no_filter_results) > 0:
        print(f"\n🔍 All PO Statuses Found:")
        all_statuses = set()
        for po in no_filter_results:
            all_statuses.add(po['status'])
        print(f"   Statuses: {sorted(all_statuses)}")
        
        # Check what's being filtered out by current logic
        filtered_out = []
        for po in no_filter_results:
            if po['status'] in ['CAN', 'CLOSE']:
                filtered_out.append(po)
        
        print(f"\n❌ Records filtered out by current logic:")
        if filtered_out:
            for po in filtered_out:
                print(f"   PO #{po['ponum']} - Status: {po['status']} ({po['status_description']})")
        else:
            print("   None - current filtering is not removing any records")
        
        # Check what should be considered active
        potentially_active = []
        for po in no_filter_results:
            if po['status'] not in ['CAN', 'CLOSE']:
                potentially_active.append(po)
        
        print(f"\n✅ Potentially Active POs (not CAN/CLOSE):")
        if potentially_active:
            for po in potentially_active:
                print(f"   PO #{po['ponum']} - Status: {po['status']} ({po['status_description']})")
                print(f"      Order Qty: {po['orderqty']}, Received: {po['receivedqty']}")
        else:
            print("   None found")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if len(no_filter_results) == 0:
        print("   ❌ No POs found for this item - check if item number or site is correct")
    elif len(current_filter_results) == len(no_filter_results):
        print("   ✅ Current filtering is working correctly - no CAN/CLOSE records to filter")
    elif len(current_filter_results) < len(no_filter_results):
        print("   ⚠️ Current filtering may be too restrictive")
        print("   📝 Consider adjusting the where clause to include more active statuses")
    
    return no_filter_results

if __name__ == "__main__":
    print(f"🚀 Investigating Purchase Order Filtering")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Test different filtering approaches
    no_filter_results = test_no_filtering()
    current_filter_results = test_current_filtering()
    positive_filter_results = test_positive_filtering()
    
    # Analyze results
    analyze_results(no_filter_results, current_filter_results, positive_filter_results)
    
    print(f"\n{'='*80}")
    print("🏁 Investigation Complete")
    print(f"{'='*80}")
