# No-Balance Inventory Adjustment Payload Examples

## Overview

This document provides the exact payload structures for both Physical Count and Current Balance adjustments when no existing inventory balances (invbalances) exist.

## Key Requirements Implemented

✅ **Location Auto-Population**: Location is taken from inventory record (read-only field)  
✅ **Maximo Error Display**: Exact Maximo error responses shown to user  
✅ **204 Response Handling**: Success returns 204 status code  
✅ **Form Preservation**: User input preserved on errors for correction and resubmission  

## API Endpoints

### 1. Physical Count Adjustment (No Existing Balances)
**Endpoint**: `POST /api/inventory/no-balance-physical-count`

### 2. Current Balance Adjustment (No Existing Balances)  
**Endpoint**: `POST /api/inventory/no-balance-current-balance`

## Payload Structure

Both endpoints use the standard MXAPIINVENTORY format with `_action: "AddChange"`.

**IMPORTANT**: Field separation is enforced based on adjustment type and reason code.

### Physical Count Adjustment Payload (Standard)

For most reason codes, only `physcnt` and `physcntdate` are included:

```json
[
    {
        "_action": "AddChange",
        "itemnum": "TEST-ITEM-001",
        "itemsetid": "ITEMSET",
        "siteid": "LCVKNT",
        "location": "RIP001",
        "issueunit": "EA",
        "minlevel": 0,
        "orderqty": 1,
        "invbalances": [
            {
                "binnum": "BIN-001",
                "physcnt": 25.0,
                "physcntdate": "2024-01-15T14:30:00",
                "conditioncode": "A1",
                "memo": "CYCLE_COUNT",
                "reconciled": true
            }
        ]
    }
]
```

### Current Balance Adjustment Payload (Standard)

For most reason codes, only `curbal` is included:

```json
[
    {
        "_action": "AddChange",
        "itemnum": "TEST-ITEM-001",
        "itemsetid": "ITEMSET",
        "siteid": "LCVKNT",
        "location": "RIP001",
        "issueunit": "EA",
        "minlevel": 0,
        "orderqty": 1,
        "invbalances": [
            {
                "binnum": "BIN-002",
                "curbal": 15.0,
                "conditioncode": "A1",
                "memo": "ADJUSTMENT",
                "reconciled": true
            }
        ]
    }
]
```

### Exception: Both Fields Populated

When reason code is one of these values, both `physcnt` and `curbal` are populated with equal values:
- **"INITIAL_BALANCE"**
- **"NEW_INVENTORY"**
- **"OPENING_BALANCE"**
- **"INITIAL_COUNT"**

#### Physical Count with Exception Reason Code

```json
[
    {
        "_action": "AddChange",
        "itemnum": "TEST-ITEM-001",
        "itemsetid": "ITEMSET",
        "siteid": "LCVKNT",
        "location": "RIP001",
        "issueunit": "EA",
        "minlevel": 0,
        "orderqty": 1,
        "invbalances": [
            {
                "binnum": "BIN-001",
                "physcnt": 25.0,
                "curbal": 25.0,
                "physcntdate": "2024-01-15T14:30:00",
                "conditioncode": "A1",
                "memo": "INITIAL_COUNT",
                "reconciled": true
            }
        ]
    }
]
```

#### Current Balance with Exception Reason Code

```json
[
    {
        "_action": "AddChange",
        "itemnum": "TEST-ITEM-001",
        "itemsetid": "ITEMSET",
        "siteid": "LCVKNT",
        "location": "RIP001",
        "issueunit": "EA",
        "minlevel": 0,
        "orderqty": 1,
        "invbalances": [
            {
                "binnum": "BIN-002",
                "curbal": 15.0,
                "physcnt": 15.0,
                "physcntdate": "2024-01-15T14:30:00",
                "conditioncode": "A1",
                "memo": "INITIAL_BALANCE",
                "reconciled": true
            }
        ]
    }
]
```

## Field Descriptions

### Main Record Fields
- **_action**: Always "AddChange" for creating new records
- **itemnum**: Item number from inventory record
- **itemsetid**: Always "ITEMSET" (standard value)
- **siteid**: Site ID from inventory record
- **location**: **Auto-populated from inventory record** (not user input)
- **issueunit**: Default "EA" (Each)
- **minlevel**: Default 0
- **orderqty**: Default 1

### Balance Record Fields (invbalances array)

#### Standard Field Separation
- **Physical Count Adjustments**:
  - **physcnt**: Physical count value (required)
  - **physcntdate**: Timestamp for physical count (auto-generated)
  - **curbal**: OMITTED (unless exception reason code)

- **Current Balance Adjustments**:
  - **curbal**: Current balance value (required)
  - **physcnt**: OMITTED (unless exception reason code)
  - **physcntdate**: OMITTED (unless exception reason code)

#### Exception Reason Codes
When memo field contains one of these values, both fields are populated with equal values:
- "INITIAL_BALANCE", "NEW_INVENTORY", "OPENING_BALANCE", "INITIAL_COUNT"

#### Common Fields
- **binnum**: Optional bin number (user input)
- **conditioncode**: Item condition (default "A1", user can modify)
- **memo**: Reason code from user selection
- **reconciled**: Always true for new records

## Response Handling

### Success Response (HTTP 200 with 204 status)
```json
{
    "success": true,
    "message": "Inventory adjustment submitted successfully to Maximo",
    "timestamp": "2024-01-15T14:30:00.000Z",
    "status_code": 204,
    "response": [
        {
            "_responsemeta": {
                "status": "204"
            }
        }
    ],
    "operation": "no_balance_physical_count",
    "itemnum": "TEST-ITEM-001",
    "physical_count": 25.0,
    "location": "RIP001",
    "binnum": "BIN-001",
    "memo": "INITIAL_COUNT",
    "submitted_by": "username"
}
```

### Error Response Examples

#### Validation Error (HTTP 400)
```json
{
    "success": false,
    "error": "Missing required field: itemnum"
}
```

#### Maximo API Error (HTTP 400/500)
```json
{
    "success": false,
    "error": "********** - The item TEST-ITEM-001 does not exist in site LCVKNT",
    "status_code": 400,
    "response": {
        "oslc:Error": {
            "oslc:message": "********** - The item TEST-ITEM-001 does not exist in site LCVKNT",
            "spi:reasonCode": "**********"
        }
    }
}
```

#### Network Error
```json
{
    "success": false,
    "error": "Network error: Connection timeout"
}
```

## Error Handling Implementation

### Frontend Error Display
- Errors shown **within modal** (modal stays open)
- User input **preserved** for correction
- Detailed Maximo error messages displayed
- Expandable debug information available
- Users can correct errors and resubmit

### Error Extraction Logic
The system extracts errors from multiple Maximo response formats:
1. Direct Error object: `response.Error.message`
2. OSLC Error format: `response['oslc:Error']['oslc:message']`
3. Array with error objects: `response[0].Error.message`
4. Reason codes: `response['oslc:Error']['spi:reasonCode']`

## Form Behavior

### Location Field
- **Source**: Auto-populated from `item.location` in inventory record
- **Display**: Read-only input field
- **Validation**: No user validation required (comes from system)

### Required Fields
- **Physical Count**: Physical count value, reason code
- **Current Balance**: Current balance value, reason code
- **Optional**: Bin number, condition code, notes

### Form Preservation
- On error: All user input preserved
- Modal remains open for corrections
- Error alert appears at top of modal
- Users can dismiss error and retry

## Testing Examples

### Test Physical Count Creation
```bash
curl -X POST http://127.0.0.1:5010/api/inventory/no-balance-physical-count \
  -H "Content-Type: application/json" \
  -d '[{
    "_action": "AddChange",
    "itemnum": "TEST-ITEM-001",
    "itemsetid": "ITEMSET", 
    "siteid": "LCVKNT",
    "location": "RIP001",
    "issueunit": "EA",
    "minlevel": 0,
    "orderqty": 1,
    "invbalances": [{
      "binnum": "TEST-BIN",
      "physcnt": 10.0,
      "curbal": 10.0,
      "physcntdate": "2024-01-15T14:30:00",
      "conditioncode": "A1",
      "memo": "INITIAL_COUNT",
      "reconciled": true
    }]
  }]'
```

### Test Current Balance Creation
```bash
curl -X POST http://127.0.0.1:5010/api/inventory/no-balance-current-balance \
  -H "Content-Type: application/json" \
  -d '[{
    "_action": "AddChange",
    "itemnum": "TEST-ITEM-001",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKNT", 
    "location": "RIP001",
    "issueunit": "EA",
    "minlevel": 0,
    "orderqty": 1,
    "invbalances": [{
      "binnum": "TEST-BIN",
      "curbal": 15.0,
      "physcnt": 15.0,
      "conditioncode": "A1",
      "memo": "INITIAL_BALANCE", 
      "reconciled": true
    }]
  }]'
```

## Integration Notes

- **Authentication**: Session-based via token_manager
- **Endpoint**: Uses `/api/os/MXAPIINVENTORY` with proper headers
- **Method**: POST with `x-method-override: BULK`
- **Timeout**: 30 seconds for API calls
- **Logging**: Comprehensive logging for debugging
