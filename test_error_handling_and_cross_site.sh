#!/bin/bash

# Test Error Handling and Cross-Site Implementation
# =================================================

echo "🚀 TESTING ERROR HANDLING AND CROSS-SITE IMPLEMENTATION"
echo "======================================================="

echo "🎯 OBJECTIVE: Test error display and successful cross-site patterns"
echo "📋 STRATEGY: Test both error scenarios and working cross-site transfers"
echo "🔍 IMPLEMENTATION: Detailed error messages + confirmed working patterns"
echo ""

# Counter for tests
SUCCESS_COUNT=0
ERROR_COUNT=0
TEST_COUNT=0

# Function to test transfer and analyze response
test_transfer_with_analysis() {
    local test_name="$1"
    local endpoint="$2"
    local payload="$3"
    local expected_result="$4"  # "success" or "error"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    echo "🔗 Endpoint: $endpoint"
    echo "🎯 Expected: $expected_result"
    
    echo "🔄 Submitting transfer..."
    
    response=$(curl -X POST "http://127.0.0.1:5010$endpoint" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Analyze response
    if echo "$response" | grep -q '"success": true'; then
        if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
            echo "🎉 SUCCESS! Transfer worked with 204 status!"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            echo "$payload" > "success_test_$TEST_COUNT.json"
            echo "💾 Successful payload saved"
        else
            echo "✅ SUCCESS! Transfer accepted by system"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        fi
    elif echo "$response" | grep -q '"success": false'; then
        echo "❌ ERROR RESPONSE (as expected for error tests)"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        
        # Check if detailed error information is provided
        if echo "$response" | grep -q '"detailed_error"'; then
            echo "✅ DETAILED ERROR: Error details provided to user"
        fi
        if echo "$response" | grep -q '"error_guidance"'; then
            echo "✅ ERROR GUIDANCE: User guidance provided"
        fi
        if echo "$response" | grep -q '"user_action_required"'; then
            echo "✅ USER ACTION: User action flag set"
        fi
        
        echo "$response" > "error_test_$TEST_COUNT.json"
        echo "💾 Error response saved for analysis"
    else
        echo "⚠️  Unexpected response format"
    fi
    
    echo ""
}

echo "🚀 TESTING ERROR HANDLING IMPLEMENTATION"
echo "======================================="

# Test 1: Intentional duplicate error (should show detailed error)
test_transfer_with_analysis "Duplicate Error Test - Same Site" \
    "/api/inventory/transfer-same-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    "error"

# Test 2: Unit conversion error (should show detailed error)
test_transfer_with_analysis "Unit Conversion Error Test" \
    "/api/inventory/transfer-same-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "INVALID_UNIT",
        "to_issue_unit": "ANOTHER_INVALID_UNIT",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    "error"

echo "🚀 TESTING SUCCESSFUL CROSS-SITE PATTERNS"
echo "========================================="

# Test 3: Working Cross-Site Pattern 1 (CMW-AJ → KWAJ-1058)
test_transfer_with_analysis "Cross-Site Success Pattern 1 - CMW-AJ → KWAJ-1058" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    "success"

# Test 4: Working Cross-Site Pattern 2 (RO to RO)
test_transfer_with_analysis "Cross-Site Success Pattern 2 - RO to RO" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 0.5,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    "success"

# Test 5: Working Cross-Site Pattern 3 (Small Quantity)
test_transfer_with_analysis "Cross-Site Success Pattern 3 - Small Quantity" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 0.1,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    "success"

# Test 6: Working Same-Site Pattern (Baseline)
test_transfer_with_analysis "Same-Site Success Pattern - Baseline" \
    "/api/inventory/transfer-same-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    "success"

echo ""
echo "📊 IMPLEMENTATION TEST SUMMARY"
echo "============================="
echo "✅ Successful transfers: $SUCCESS_COUNT"
echo "❌ Error responses: $ERROR_COUNT"
echo "📝 Total tests completed: $TEST_COUNT"

echo ""
echo "🎯 ERROR HANDLING ANALYSIS:"
if [ $ERROR_COUNT -gt 0 ]; then
    echo "✅ Error handling is working - errors are being caught and detailed"
    echo "📋 Check error_test_*.json files for detailed error responses"
else
    echo "⚠️  No errors encountered - may need to test with more problematic scenarios"
fi

echo ""
echo "🎯 CROSS-SITE TRANSFER ANALYSIS:"
if [ $SUCCESS_COUNT -gt 0 ]; then
    echo "✅ Cross-site transfers are working with the implemented patterns"
    echo "📋 Check success_test_*.json files for working patterns"
else
    echo "❌ Cross-site transfers may need further investigation"
fi

echo ""
echo "🎯 NEXT STEPS:"
echo "• Test the UI at http://127.0.0.1:5010/inventory-management"
echo "• Verify detailed error messages appear in the transfer modal"
echo "• Test both Same Site and Cross Site transfer buttons"
echo "• Confirm error guidance helps users fix issues"
echo "• Verify successful transfers complete with 204 status"
