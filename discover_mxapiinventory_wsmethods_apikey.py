#!/usr/bin/env python3
"""
MXAPIINVENTORY Web Service Methods Discovery using API Key Authentication

This script uses the known working API key to systematically discover and test
all available web service methods (wsmethods) for the MXAPIINVENTORY endpoint.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"  # Known working API key

class MXAPIInventoryWSMethodDiscoveryAPIKey:
    """Discovers MXAPIINVENTORY web service methods using API key authentication."""
    
    def __init__(self):
        """Initialize the discovery tool."""
        self.base_url = BASE_URL
        self.api_key = API_KEY
        self.discovered_methods = {}
        
    def test_endpoint_access(self):
        """Test basic endpoint access with API key."""
        print("🔍 Testing Endpoint Access")
        print("=" * 60)
        
        endpoints = [
            f"{self.base_url}/api/os/mxapiinventory",
            f"{self.base_url}/oslc/os/mxapiinventory"
        ]
        
        headers = {
            "Accept": "application/json",
            "apikey": self.api_key
        }
        
        working_endpoints = []
        
        for endpoint_url in endpoints:
            endpoint_type = "API" if "/api/" in endpoint_url else "OSLC"
            print(f"\n📋 Testing {endpoint_type} Endpoint: {endpoint_url}")
            
            try:
                # Test basic GET
                response = requests.get(
                    endpoint_url,
                    params={"oslc.select": "itemnum,siteid", "oslc.pageSize": "1"},
                    headers=headers,
                    timeout=(3.05, 15)
                )
                
                print(f"  Status: {response.status_code}")
                print(f"  Content-Type: {response.headers.get('content-type', 'Unknown')}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"  ✅ JSON Response - Structure: {list(data.keys()) if isinstance(data, dict) else 'Array'}")
                        working_endpoints.append(endpoint_url)
                    except:
                        print(f"  ❌ Non-JSON Response: {response.text[:100]}...")
                else:
                    print(f"  ❌ Failed with status {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Exception: {str(e)}")
                
        return working_endpoints
        
    def discover_wsmethods(self, endpoint_url: str):
        """Discover web service methods for a specific endpoint."""
        print(f"\n🔍 Discovering WSMethods for: {endpoint_url}")
        print("=" * 80)
        
        # Comprehensive list of potential inventory wsmethods
        potential_wsmethods = [
            # Core inventory operations
            "issuecurrentitem",
            "transfercurrentitem", 
            "itemavailability",
            "addchange",
            "adjustcurrentbalance",
            "adjustphysicalcount",
            
            # Standard CRUD operations
            "create",
            "update", 
            "delete",
            "sync",
            "merge",
            
            # Inventory management operations
            "receivecurrentitem",
            "returncurrentitem",
            "reservecurrentitem",
            "unreservecurrentitem",
            "movecurrentitem",
            "splitcurrentitem",
            "combinecurrentitem",
            
            # Validation and checking
            "validate",
            "check",
            "verify",
            "calculatecost",
            "calculateavailability",
            
            # Reporting and analysis
            "getinventorydetails",
            "getinventoryhistory",
            "getinventorysummary",
            "getinventorybalance",
            
            # Workflow operations
            "approve",
            "reject",
            "submit",
            "complete",
            "cancel",
            
            # Additional potential methods
            "count",
            "recount",
            "reconcile",
            "allocate",
            "deallocate",
            "freeze",
            "unfreeze",
            "lock",
            "unlock"
        ]
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        valid_methods = []
        invalid_methods = []
        error_methods = []
        
        print(f"🧪 Testing {len(potential_wsmethods)} potential methods...")
        
        for i, wsmethod in enumerate(potential_wsmethods, 1):
            test_url = f"{endpoint_url}?action=wsmethod:{wsmethod}"
            
            print(f"  [{i:2d}/{len(potential_wsmethods)}] Testing: {wsmethod}", end=" ")
            
            try:
                # Test with minimal payload
                response = requests.post(
                    test_url,
                    json={"test": "discovery"},
                    headers=headers,
                    timeout=(3.05, 10)
                )
                
                print(f"(Status: {response.status_code})", end=" ")
                
                # Analyze response to determine if method exists
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if self._is_valid_method_response(data):
                            print("✅ VALID")
                            valid_methods.append({
                                'method': wsmethod,
                                'status': response.status_code,
                                'response': data
                            })
                        else:
                            print("❌ Invalid")
                            invalid_methods.append(wsmethod)
                    except:
                        print("⚠️ Non-JSON")
                        error_methods.append((wsmethod, "Non-JSON response"))
                        
                elif response.status_code in [400, 422]:
                    # These might indicate valid method with wrong parameters
                    try:
                        data = response.json()
                        if self._indicates_valid_method(data):
                            print("✅ VALID (parameter error)")
                            valid_methods.append({
                                'method': wsmethod,
                                'status': response.status_code,
                                'response': data
                            })
                        else:
                            print("❌ Invalid")
                            invalid_methods.append(wsmethod)
                    except:
                        print("⚠️ Non-JSON error")
                        error_methods.append((wsmethod, f"Status {response.status_code}, non-JSON"))
                        
                else:
                    print("❌ Invalid")
                    invalid_methods.append(wsmethod)
                    
            except Exception as e:
                print(f"❌ Error: {str(e)[:50]}")
                error_methods.append((wsmethod, str(e)))
                
        # Display results
        print(f"\n📊 Discovery Results:")
        print(f"  ✅ Valid Methods: {len(valid_methods)}")
        print(f"  ❌ Invalid Methods: {len(invalid_methods)}")
        print(f"  ⚠️ Error Methods: {len(error_methods)}")
        
        return valid_methods, invalid_methods, error_methods
        
    def _is_valid_method_response(self, data: Any) -> bool:
        """Check if response indicates a valid method."""
        if isinstance(data, dict):
            # Look for success indicators
            if 'member' in data or 'href' in data or 'responseInfo' in data:
                return True
            # Look for error messages that indicate method exists but has wrong parameters
            if 'Error' in data or 'error' in data:
                error_text = str(data).lower()
                return any(indicator in error_text for indicator in [
                    'required', 'parameter', 'field', 'missing', 'invalid'
                ])
        return False
        
    def _indicates_valid_method(self, data: Any) -> bool:
        """Check if error response indicates method exists but has wrong parameters."""
        if isinstance(data, dict):
            error_text = str(data).lower()
            # Look for parameter-related errors (method exists but wrong params)
            valid_indicators = [
                'required', 'parameter', 'field', 'missing', 'invalid',
                'must be', 'cannot be', 'should be'
            ]
            # Look for method not found errors
            invalid_indicators = [
                'not found', 'unknown method', 'invalid action', 'not supported'
            ]
            
            has_valid = any(indicator in error_text for indicator in valid_indicators)
            has_invalid = any(indicator in error_text for indicator in invalid_indicators)
            
            return has_valid and not has_invalid
            
        return False

def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Web Service Methods Discovery (API Key)")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Initialize discovery tool
    discovery = MXAPIInventoryWSMethodDiscoveryAPIKey()
    
    # Test endpoint access
    working_endpoints = discovery.test_endpoint_access()
    
    if not working_endpoints:
        print("❌ No working endpoints found")
        return False
        
    # Discover methods for each working endpoint
    all_results = {}
    
    for endpoint_url in working_endpoints:
        endpoint_type = "API" if "/api/" in endpoint_url else "OSLC"
        valid_methods, invalid_methods, error_methods = discovery.discover_wsmethods(endpoint_url)
        
        all_results[endpoint_type] = {
            'endpoint': endpoint_url,
            'valid_methods': valid_methods,
            'invalid_methods': invalid_methods,
            'error_methods': error_methods
        }
        
    # Generate summary report
    print("\n📋 COMPREHENSIVE DISCOVERY SUMMARY")
    print("=" * 80)
    
    for endpoint_type, results in all_results.items():
        print(f"\n🔗 {endpoint_type} Endpoint: {results['endpoint']}")
        print(f"  ✅ Valid Methods ({len(results['valid_methods'])}):")
        
        for method_info in results['valid_methods']:
            method = method_info['method']
            status = method_info['status']
            print(f"    • {method} (Status: {status})")
            
        if results['error_methods']:
            print(f"  ⚠️ Methods with Errors ({len(results['error_methods'])}):")
            for method, error in results['error_methods'][:5]:  # Show first 5
                print(f"    • {method}: {error[:50]}...")
                
    print("\n✅ Discovery completed successfully")
    return True

if __name__ == "__main__":
    main()
