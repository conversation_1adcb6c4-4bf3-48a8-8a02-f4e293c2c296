<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QR Download Logic</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>QR Download Logic Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Balance-Specific QR (with binnum)</h3>
        <button onclick="testBalanceQRWithBin()">Test Balance QR with Bin</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Balance-Specific QR (without binnum)</h3>
        <button onclick="testBalanceQRWithoutBin()">Test Balance QR without Bin</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Inventory-Level QR</h3>
        <button onclick="testInventoryQR()">Test Inventory QR</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        // Simulate the download logic
        function generateFilename(qrData, balanceRecord, qrType) {
            const itemnum = qrData.itemnum || 'UNKNOWN_ITEM';
            
            let filename;
            if (qrType === 'balance') {
                // Balance-specific QR: itemnum_binnum.png
                let binnum = qrData.binnum || (balanceRecord && balanceRecord.binnum);
                
                if (!binnum) {
                    // Generate random binnum if not available
                    const randomBin = 'BIN' + Math.random().toString(36).substr(2, 6).toUpperCase();
                    binnum = randomBin;
                }
                filename = `${itemnum}_${binnum}.png`;
            } else {
                // Inventory-level QR: itemnum.png
                filename = `${itemnum}.png`;
            }

            // Clean filename to remove invalid characters
            filename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
            return filename;
        }

        function testBalanceQRWithBin() {
            const qrData = {
                itemnum: 'TEST-ITEM-123',
                binnum: 'A-01-01',
                curbal: 100,
                qr_type: 'balance_specific'
            };
            const balanceRecord = {
                binnum: 'A-01-01',
                curbal: 100
            };
            const filename = generateFilename(qrData, balanceRecord, 'balance');
            
            document.getElementById('result1').innerHTML = `
                <strong>Expected:</strong> TEST-ITEM-123_A-01-01.png<br>
                <strong>Generated:</strong> ${filename}<br>
                <strong>Status:</strong> ${filename === 'TEST-ITEM-123_A-01-01.png' ? '✅ PASS' : '❌ FAIL'}
            `;
        }

        function testBalanceQRWithoutBin() {
            const qrData = {
                itemnum: 'TEST-ITEM-456',
                qr_type: 'balance_specific'
                // No binnum
            };
            const balanceRecord = {
                curbal: 50
                // No binnum
            };
            const filename = generateFilename(qrData, balanceRecord, 'balance');
            
            const isValid = filename.startsWith('TEST-ITEM-456_BIN') && filename.endsWith('.png');
            
            document.getElementById('result2').innerHTML = `
                <strong>Expected Pattern:</strong> TEST-ITEM-456_BIN[RANDOM].png<br>
                <strong>Generated:</strong> ${filename}<br>
                <strong>Status:</strong> ${isValid ? '✅ PASS' : '❌ FAIL'}
            `;
        }

        function testInventoryQR() {
            const qrData = {
                itemnum: 'INVENTORY-ITEM-789',
                qr_type: 'inventory_level'
            };
            const filename = generateFilename(qrData, null, 'inventory');
            
            document.getElementById('result3').innerHTML = `
                <strong>Expected:</strong> INVENTORY-ITEM-789.png<br>
                <strong>Generated:</strong> ${filename}<br>
                <strong>Status:</strong> ${filename === 'INVENTORY-ITEM-789.png' ? '✅ PASS' : '❌ FAIL'}
            `;
        }
    </script>
</body>
</html>
