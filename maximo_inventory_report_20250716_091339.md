# Maximo Inventory Service Discovery Report

**Generated:** 2025-07-16T09:13:29.033176
**Base URL:** https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo

## Authentication Methods

- **Session Authentication:** ✅ Available
- **API Key Authentication:** ❌ Not Available

## SOAP/WSDL Services

❌ WSDL discovery failed

## REST/OSLC Methods

### Discovered WSMethods

| Method | Status | Exists | Description |
|--------|--------|--------|--------------|
| ADDCHANGE | 403 | ✅ | Inventory operation |
| ADDITEM | 403 | ✅ | Inventory operation |
| ADJUSTCURRENTBALANCE | 403 | ✅ | Inventory operation |
| ADJUSTPHYSICALCOUNT | 403 | ✅ | Inventory operation |
| TRANSFERCURITEM | 403 | ✅ | Inventory operation |
| ISSUECURITEM | 403 | ✅ | Inventory operation |
| RECEIVECURITEM | 403 | ✅ | Inventory operation |
| RETURNCURITEM | 403 | ✅ | Inventory operation |
| CREATE | 403 | ✅ | Inventory operation |
| UPDATE | 403 | ✅ | Inventory operation |
| DELETE | 403 | ✅ | Inventory operation |
| SAVE | 403 | ✅ | Inventory operation |
| VALIDATE | 403 | ✅ | Inventory operation |
| GETINVENTORY | 403 | ✅ | Inventory operation |
| GETBALANCE | 403 | ✅ | Inventory operation |
| GETAVAILABILITY | 403 | ✅ | Inventory operation |
| PROCESSREQUEST | 403 | ✅ | Inventory operation |
| SUBMITREQUEST | 403 | ✅ | Inventory operation |
| APPROVEREQUEST | 403 | ✅ | Inventory operation |

## All Available Methods

**Total Methods:** 19

| Name | Type | Method | Endpoint | Authentication |
|------|------|--------|----------|----------------|
| ADDCHANGE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADDCHANGE | Session, API Key |
| ADDITEM | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADDITEM | Session, API Key |
| ADJUSTCURRENTBALANCE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADJUSTCURRENTBALANCE | Session, API Key |
| ADJUSTPHYSICALCOUNT | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ADJUSTPHYSICALCOUNT | Session, API Key |
| TRANSFERCURITEM | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:TRANSFERCURITEM | Session, API Key |
| ISSUECURITEM | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:ISSUECURITEM | Session, API Key |
| RECEIVECURITEM | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:RECEIVECURITEM | Session, API Key |
| RETURNCURITEM | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:RETURNCURITEM | Session, API Key |
| CREATE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:CREATE | Session, API Key |
| UPDATE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:UPDATE | Session, API Key |
| DELETE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:DELETE | Session, API Key |
| SAVE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:SAVE | Session, API Key |
| VALIDATE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:VALIDATE | Session, API Key |
| GETINVENTORY | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:GETINVENTORY | Session, API Key |
| GETBALANCE | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:GETBALANCE | Session, API Key |
| GETAVAILABILITY | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:GETAVAILABILITY | Session, API Key |
| PROCESSREQUEST | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:PROCESSREQUEST | Session, API Key |
| SUBMITREQUEST | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:SUBMITREQUEST | Session, API Key |
| APPROVEREQUEST | WSMethod | POST | https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?action=wsmethod:APPROVEREQUEST | Session, API Key |

## Integration Examples

### Session Authentication
```python
from backend.auth.token_manager import MaximoTokenManager
token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
response = token_manager.session.get(endpoint_url, params=params)
```

