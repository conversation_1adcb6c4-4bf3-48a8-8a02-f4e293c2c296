#!/usr/bin/env python3
"""
Test script to validate workorder performance improvements.
This script tests the optimized workorder module to ensure performance improvements
while maintaining all existing functionality.
"""

import sys
import os
import time
import json
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_workorder_performance():
    """Test workorder performance improvements."""
    print("🚀 WORKORDER PERFORMANCE TEST")
    print("=" * 60)
    
    try:
        # Import required modules
        from backend.auth.token_manager import MaximoTokenManager
        from backend.services.enhanced_workorder_service import EnhancedWorkOrderService
        from backend.services.material_request_service import MaterialRequestService
        from backend.services.labor_request_service import LaborRequestService
        
        # Initialize token manager
        base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
        token_manager = MaximoTokenManager(base_url)
        
        if not token_manager.is_logged_in():
            print("❌ Not authenticated. Please login through the web interface first.")
            return False
            
        print("✅ Authentication successful")
        
        # Initialize services with required dependencies
        from backend.services.enhanced_profile_service import EnhancedProfileService

        profile_service = EnhancedProfileService(token_manager)
        workorder_service = EnhancedWorkOrderService(token_manager, profile_service)
        material_service = MaterialRequestService(token_manager, None, profile_service, None)
        labor_service = LaborRequestService(token_manager)
        
        print("✅ Services initialized")
        
        # Test 1: Workorder Data Retrieval Performance
        print("\n📊 TEST 1: Workorder Data Retrieval Performance")
        print("-" * 50)
        
        start_time = time.time()
        workorders, stats = workorder_service.get_assigned_workorders(use_cache=False, force_refresh=True)
        retrieval_time = time.time() - start_time
        
        print(f"⏱️  Retrieval Time: {retrieval_time:.3f} seconds")
        print(f"📋 Work Orders Found: {len(workorders)}")
        print(f"📈 Performance Stats: {stats}")
        
        if retrieval_time < 5.0:  # Should be under 5 seconds
            print("✅ PASS: Workorder retrieval performance is good")
        else:
            print("⚠️  WARNING: Workorder retrieval is slower than expected")
            
        # Test 2: Cache Performance
        print("\n📊 TEST 2: Cache Performance")
        print("-" * 50)
        
        start_time = time.time()
        cached_workorders, cached_stats = workorder_service.get_assigned_workorders(use_cache=True)
        cache_time = time.time() - start_time
        
        print(f"⏱️  Cache Retrieval Time: {cache_time:.3f} seconds")
        print(f"📋 Cached Work Orders: {len(cached_workorders)}")
        
        if cache_time < 0.5:  # Cache should be very fast
            print("✅ PASS: Cache performance is excellent")
        else:
            print("⚠️  WARNING: Cache performance could be better")
            
        # Test 3: Search Performance
        print("\n📊 TEST 3: Search Performance")
        print("-" * 50)
        
        if workorders:
            # Use first workorder for search test
            test_wonum = workorders[0].get('wonum', '')
            if test_wonum:
                start_time = time.time()
                search_result = workorder_service.search_workorders({
                    'wonum': test_wonum[:5]  # Partial search
                })
                search_time = time.time() - start_time
                
                print(f"⏱️  Search Time: {search_time:.3f} seconds")
                print(f"📋 Search Results: {len(search_result.get('workorders', []))}")
                
                if search_time < 3.0:  # Search should be under 3 seconds
                    print("✅ PASS: Search performance is good")
                else:
                    print("⚠️  WARNING: Search performance could be better")
            else:
                print("⚠️  SKIP: No work order number available for search test")
        else:
            print("⚠️  SKIP: No work orders available for search test")
            
        # Test 4: Material Posting Performance (if workorders available)
        print("\n📊 TEST 4: Material Posting Performance")
        print("-" * 50)
        
        if workorders:
            test_wo = workorders[0]
            wonum = test_wo.get('wonum')
            siteid = test_wo.get('siteid')
            
            if wonum and siteid:
                print(f"🔧 Testing material posting to WO: {wonum}")
                
                # Note: This is a dry run test - we won't actually post materials
                # We'll test the payload construction and validation logic
                start_time = time.time()
                
                try:
                    # Test payload construction (without actual posting)
                    wo_data = material_service._get_work_order_full(wonum, siteid)
                    if wo_data:
                        # Test payload construction time
                        payload_time = time.time() - start_time
                        print(f"⏱️  Payload Construction Time: {payload_time:.3f} seconds")
                        
                        if payload_time < 1.0:  # Should be very fast
                            print("✅ PASS: Material posting preparation is fast")
                        else:
                            print("⚠️  WARNING: Material posting preparation is slow")
                    else:
                        print("⚠️  WARNING: Could not retrieve work order data")
                        
                except Exception as e:
                    print(f"❌ ERROR: Material posting test failed: {e}")
            else:
                print("⚠️  SKIP: No suitable work order for material posting test")
        else:
            print("⚠️  SKIP: No work orders available for material posting test")
            
        # Test 5: Labor Posting Performance (if workorders available)
        print("\n📊 TEST 5: Labor Posting Performance")
        print("-" * 50)
        
        if workorders:
            test_wo = workorders[0]
            wonum = test_wo.get('wonum')
            siteid = test_wo.get('siteid')
            
            if wonum and siteid:
                print(f"🔧 Testing labor posting to WO: {wonum}")
                
                # Note: This is a dry run test - we won't actually post labor
                start_time = time.time()
                
                try:
                    # Test work order retrieval for labor posting
                    wo_data = labor_service._get_work_order_full(wonum, siteid)
                    if wo_data:
                        # Test payload construction time
                        payload_time = time.time() - start_time
                        print(f"⏱️  Labor Preparation Time: {payload_time:.3f} seconds")
                        
                        if payload_time < 1.0:  # Should be very fast
                            print("✅ PASS: Labor posting preparation is fast")
                        else:
                            print("⚠️  WARNING: Labor posting preparation is slow")
                    else:
                        print("⚠️  WARNING: Could not retrieve work order data")
                        
                except Exception as e:
                    print(f"❌ ERROR: Labor posting test failed: {e}")
            else:
                print("⚠️  SKIP: No suitable work order for labor posting test")
        else:
            print("⚠️  SKIP: No work orders available for labor posting test")
            
        # Summary
        print("\n📊 PERFORMANCE TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Workorder retrieval: {retrieval_time:.3f}s")
        print(f"✅ Cache performance: {cache_time:.3f}s")
        print(f"✅ Total work orders: {len(workorders)}")
        
        if retrieval_time < 5.0 and cache_time < 0.5:
            print("\n🎉 OVERALL RESULT: PERFORMANCE IMPROVEMENTS SUCCESSFUL!")
            return True
        else:
            print("\n⚠️  OVERALL RESULT: Some performance issues detected")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_workorder_performance()
    sys.exit(0 if success else 1)
