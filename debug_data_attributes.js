// Debug script to check data attributes in browser console
// Copy and paste this into browser console when on the workorder page

console.log('🔍 DEBUGGING DATA ATTRIBUTES');
console.log('=' * 50);

// Find all labor content containers
const laborContainers = document.querySelectorAll('[id^="labor-content-"]');
console.log(`Found ${laborContainers.length} labor content containers:`);

laborContainers.forEach((container, index) => {
    console.log(`\n📋 Container ${index + 1}: ${container.id}`);
    console.log('  Attributes:');
    console.log('    data-task-id:', container.getAttribute('data-task-id'));
    console.log('    data-site-id:', container.getAttribute('data-site-id'));
    console.log('    data-task-wonum:', container.getAttribute('data-task-wonum'));
    console.log('    data-parent-wonum:', container.getAttribute('data-parent-wonum'));
    
    // Check if any subtract buttons exist
    const subtractButtons = container.querySelectorAll('.delete-labor-btn');
    console.log(`    Subtract buttons: ${subtractButtons.length}`);
    
    if (subtractButtons.length > 0) {
        console.log('    First button attributes:');
        const firstBtn = subtractButtons[0];
        console.log('      data-labor-code:', firstBtn.getAttribute('data-labor-code'));
        console.log('      data-labor-hours:', firstBtn.getAttribute('data-labor-hours'));
        console.log('      data-craft:', firstBtn.getAttribute('data-craft'));
    }
});

// Check tasks data
console.log('\n📊 TASKS DATA:');
const tasksDataElement = document.getElementById('tasksData');
if (tasksDataElement) {
    try {
        const tasksData = JSON.parse(tasksDataElement.textContent);
        console.log(`Found ${tasksData.length} tasks in data:`);
        tasksData.forEach((task, index) => {
            console.log(`  Task ${index + 1}:`);
            console.log(`    wonum: ${task.wonum}`);
            console.log(`    taskid: ${task.taskid} (type: ${typeof task.taskid})`);
            console.log(`    siteid: ${task.siteid} (type: ${typeof task.siteid})`);
            console.log(`    parent: ${task.parent} (type: ${typeof task.parent})`);
        });
    } catch (e) {
        console.error('Error parsing tasks data:', e);
    }
} else {
    console.log('❌ No tasksData element found');
}

// Test the deleteLaborEntry function
console.log('\n🧪 TESTING deleteLaborEntry function:');
const firstSubtractBtn = document.querySelector('.delete-labor-btn');
if (firstSubtractBtn) {
    console.log('Found subtract button, testing parameter extraction...');
    
    const laborCode = firstSubtractBtn.getAttribute('data-labor-code');
    const laborHours = firstSubtractBtn.getAttribute('data-labor-hours');
    const craft = firstSubtractBtn.getAttribute('data-craft');

    const laborContent = firstSubtractBtn.closest('[id^="labor-content-"]');
    const taskWonum = laborContent ? laborContent.id.replace('labor-content-', '') : null;

    const taskId = laborContent.getAttribute('data-task-id');
    const siteId = laborContent.getAttribute('data-site-id');
    const parentWonum = laborContent.getAttribute('data-parent-wonum');

    console.log('Extracted parameters:');
    console.log('  laborCode:', laborCode);
    console.log('  laborHours:', laborHours);
    console.log('  craft:', craft);
    console.log('  taskWonum:', taskWonum);
    console.log('  taskId:', taskId, '(type:', typeof taskId, ')');
    console.log('  siteId:', siteId, '(type:', typeof siteId, ')');
    console.log('  parentWonum:', parentWonum, '(type:', typeof parentWonum, ')');
    
    // Check what would fail validation
    const missing = [];
    if (!taskId || taskId === 'null' || taskId === 'undefined' || taskId === '') missing.push('taskId');
    if (!parentWonum || parentWonum === 'null' || parentWonum === 'undefined' || parentWonum === '') missing.push('parentWonum');
    if (!siteId || siteId === 'null' || siteId === 'undefined' || siteId === '') missing.push('siteId');
    
    if (missing.length > 0) {
        console.log('❌ VALIDATION WOULD FAIL - Missing:', missing);
    } else {
        console.log('✅ VALIDATION WOULD PASS - All parameters present');
    }
} else {
    console.log('❌ No subtract button found');
}

console.log('\n🔧 MANUAL FIX TEST:');
console.log('If data attributes are missing, you can manually set them:');
console.log('1. Find the current task data from tasksData');
console.log('2. Set attributes manually on labor content container');
console.log('Example:');
console.log('const container = document.getElementById("labor-content-TASKWONUM");');
console.log('container.setAttribute("data-task-id", "TASKID");');
console.log('container.setAttribute("data-site-id", "SITEID");');
console.log('container.setAttribute("data-parent-wonum", "PARENTWONUM");');
