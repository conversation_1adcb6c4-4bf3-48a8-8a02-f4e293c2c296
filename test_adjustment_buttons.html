<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Inventory Adjustment Buttons</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Inventory Adjustment Buttons</h1>
        <p>This page tests the new Physical Count and Current Balance adjustment buttons.</p>
        
        <!-- Test Balance Record -->
        <div class="card">
            <div class="card-header">
                <h5>Test Balance Record</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p><strong>Item:</strong> 5975-60-V00-0529</p>
                        <p><strong>Site:</strong> LCVKWT</p>
                        <p><strong>Location:</strong> RIP001</p>
                        <p><strong>Bin:</strong> 28-800-0004</p>
                        <p><strong>Current Balance:</strong> 30</p>
                        <p><strong>Physical Count:</strong> 30</p>
                        <p><strong>Condition Code:</strong> A1</p>
                    </div>
                    <div class="col-md-4">
                        <div class="btn-group-vertical w-100" role="group">
                            <button type="button"
                                    class="btn btn-primary btn-sm mb-1"
                                    onclick="openPhysicalCountModal()"
                                    title="Physical Count Adjustment">
                                <i class="fas fa-clipboard-check me-1"></i>Physical Count
                            </button>
                            <button type="button"
                                    class="btn btn-warning btn-sm"
                                    onclick="openCurrentBalanceModal()"
                                    title="Current Balance Adjustment">
                                <i class="fas fa-balance-scale me-1"></i>Current Balance
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Physical Count Adjustment Modal -->
    <div class="modal fade" id="physicalCountModal" tabindex="-1" aria-labelledby="physicalCountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="physicalCountModalLabel">
                        <i class="fas fa-clipboard-check me-2"></i>Physical Count Adjustment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="physicalCountForm">
                        <div class="row g-3">
                            <!-- Read-only fields -->
                            <div class="col-md-6">
                                <label for="pc_itemnum" class="form-label">Item Number</label>
                                <input type="text" class="form-control" id="pc_itemnum" value="5975-60-V00-0529" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="pc_itemsetid" class="form-label">Item Set ID</label>
                                <input type="text" class="form-control" id="pc_itemsetid" value="ITEMSET" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="pc_siteid" class="form-label">Site ID</label>
                                <input type="text" class="form-control" id="pc_siteid" value="LCVKWT" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="pc_location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="pc_location" value="RIP001" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="pc_binnum" class="form-label">Bin Number</label>
                                <input type="text" class="form-control" id="pc_binnum" value="28-800-0004" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="pc_conditioncode" class="form-label">Condition Code</label>
                                <input type="text" class="form-control" id="pc_conditioncode" value="A1" readonly>
                            </div>
                            
                            <!-- Editable field -->
                            <div class="col-md-6">
                                <label for="pc_physcnt" class="form-label">Physical Count <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="pc_physcnt" step="0.01" value="30" required>
                                <div class="form-text">Enter the actual physical count for this item</div>
                            </div>
                            
                            <!-- Additional fields -->
                            <div class="col-md-6">
                                <label for="pc_reason_code" class="form-label">Reason Code <span class="text-danger">*</span></label>
                                <select class="form-select" id="pc_reason_code" required>
                                    <option value="">Select reason...</option>
                                    <option value="CYCLE_COUNT">Cycle Count</option>
                                    <option value="PHYSICAL_INVENTORY">Physical Inventory</option>
                                    <option value="DISCREPANCY">Discrepancy Found</option>
                                    <option value="AUDIT">Audit</option>
                                    <option value="OTHER">Other</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label for="pc_notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="pc_notes" rows="3" placeholder="Optional notes about this adjustment..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="submitPhysicalCountAdjustment()">
                        <i class="fas fa-save me-1"></i>Submit Adjustment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Balance Adjustment Modal -->
    <div class="modal fade" id="currentBalanceModal" tabindex="-1" aria-labelledby="currentBalanceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="currentBalanceModalLabel">
                        <i class="fas fa-balance-scale me-2"></i>Current Balance Adjustment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="currentBalanceForm">
                        <div class="row g-3">
                            <!-- Read-only fields -->
                            <div class="col-md-6">
                                <label for="cb_itemnum" class="form-label">Item Number</label>
                                <input type="text" class="form-control" id="cb_itemnum" value="5975-60-V00-0529" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="cb_itemsetid" class="form-label">Item Set ID</label>
                                <input type="text" class="form-control" id="cb_itemsetid" value="ITEMSET" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="cb_siteid" class="form-label">Site ID</label>
                                <input type="text" class="form-control" id="cb_siteid" value="LCVKWT" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="cb_location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="cb_location" value="RIP001" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="cb_binnum" class="form-label">Bin Number</label>
                                <input type="text" class="form-control" id="cb_binnum" value="28-800-0004" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="cb_conditioncode" class="form-label">Condition Code</label>
                                <input type="text" class="form-control" id="cb_conditioncode" value="A1" readonly>
                            </div>
                            
                            <!-- Current balance display -->
                            <div class="col-md-6">
                                <label for="cb_current_balance" class="form-label">Current Balance</label>
                                <input type="text" class="form-control" id="cb_current_balance" value="30" readonly>
                                <div class="form-text">Current balance in the system</div>
                            </div>
                            
                            <!-- Editable field -->
                            <div class="col-md-6">
                                <label for="cb_new_balance" class="form-label">New Balance <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="cb_new_balance" step="0.01" required>
                                <div class="form-text">Enter the new balance amount</div>
                            </div>
                            
                            <!-- Additional fields -->
                            <div class="col-md-6">
                                <label for="cb_reason_code" class="form-label">Reason Code <span class="text-danger">*</span></label>
                                <select class="form-select" id="cb_reason_code" required>
                                    <option value="">Select reason...</option>
                                    <option value="ADJUSTMENT">Manual Adjustment</option>
                                    <option value="CORRECTION">Correction</option>
                                    <option value="DAMAGE">Damage</option>
                                    <option value="LOSS">Loss</option>
                                    <option value="FOUND">Found Items</option>
                                    <option value="OTHER">Other</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="cb_adjustment_type" class="form-label">Adjustment Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="cb_adjustment_type" required>
                                    <option value="">Select type...</option>
                                    <option value="ISSUE">Issue (Decrease)</option>
                                    <option value="RECEIPT">Receipt (Increase)</option>
                                    <option value="TRANSFER">Transfer</option>
                                    <option value="ADJUSTMENT">Direct Adjustment</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label for="cb_notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="cb_notes" rows="3" placeholder="Optional notes about this adjustment..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-warning" onclick="submitCurrentBalanceAdjustment()">
                        <i class="fas fa-save me-1"></i>Submit Adjustment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openPhysicalCountModal() {
            const modal = new bootstrap.Modal(document.getElementById('physicalCountModal'));
            modal.show();
        }

        function openCurrentBalanceModal() {
            const modal = new bootstrap.Modal(document.getElementById('currentBalanceModal'));
            modal.show();
        }

        function submitPhysicalCountAdjustment() {
            alert('Physical Count Adjustment would be submitted here!');
        }

        function submitCurrentBalanceAdjustment() {
            alert('Current Balance Adjustment would be submitted here!');
        }
    </script>
</body>
</html>
