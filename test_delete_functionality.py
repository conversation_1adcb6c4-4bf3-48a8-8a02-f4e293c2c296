#!/usr/bin/env python3
"""
Test script to debug delete functionality for attachments
"""

import requests
import json

def test_delete_functionality():
    """Test delete functionality for the Comments.pdf file"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2219753'
    docinfoid = '1926183'  # Comments.pdf that we know exists
    
    print(f"🔍 Testing delete functionality for Comments.pdf")
    print("=" * 60)
    
    # First, verify the file exists
    print(f"📋 Step 1: Verify file exists before deletion")
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        attachments_response = requests.get(attachments_url, timeout=30)
        print(f"   📤 Attachments URL: {attachments_url}")
        print(f"   🔄 Status: {attachments_response.status_code}")
        
        if attachments_response.status_code == 200:
            attachments_data = attachments_response.json()
            if attachments_data.get('success') and attachments_data.get('attachments'):
                attachments = attachments_data['attachments']
                
                # Find our PDF
                pdf_attachment = None
                for attachment in attachments:
                    if str(attachment.get('docinfoid')) == str(docinfoid):
                        pdf_attachment = attachment
                        break
                
                if pdf_attachment:
                    print(f"   ✅ File exists before deletion:")
                    print(f"      📄 Filename: {pdf_attachment.get('filename')}")
                    print(f"      🆔 DocInfoID: {pdf_attachment.get('docinfoid')}")
                    print(f"      📊 Size: {pdf_attachment.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 'Unknown')} bytes")
                    print(f"      👤 Created by: {pdf_attachment.get('changeby')}")
                    print(f"      📅 Created: {pdf_attachment.get('createdate')}")
                else:
                    print(f"   ❌ File not found! Cannot test deletion.")
                    return False
            else:
                print(f"   ❌ Failed to get attachments: {attachments_data}")
                return False
        else:
            print(f"   ❌ Attachments request failed: {attachments_response.status_code}")
            return False
    
    except Exception as e:
        print(f"   ❌ Exception getting attachments: {e}")
        return False
    
    # Now test the delete functionality
    print(f"\n🗑️ Step 2: Testing delete operation")
    delete_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/delete'
    
    try:
        delete_response = requests.delete(delete_url, timeout=60)
        print(f"   📤 Delete URL: {delete_url}")
        print(f"   🔄 Status: {delete_response.status_code}")
        print(f"   📊 Content Length: {len(delete_response.content)} bytes")
        print(f"   📋 Content Type: {delete_response.headers.get('content-type', 'Unknown')}")
        
        if delete_response.status_code == 200:
            try:
                delete_data = delete_response.json()
                print(f"   ✅ Delete response: {delete_data}")
                
                if delete_data.get('success'):
                    print(f"   🎉 DELETE SUCCESS!")
                else:
                    print(f"   ❌ Delete failed: {delete_data.get('error', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                print(f"   ⚠️  Delete returned non-JSON response: {delete_response.text[:200]}")
                return False
        else:
            print(f"   ❌ Delete failed with status: {delete_response.status_code}")
            try:
                error_data = delete_response.json()
                print(f"   📝 Delete Error: {error_data}")
                
                # Analyze the error
                if 'error' in error_data:
                    error_msg = error_data['error']
                    print(f"   🔍 Error analysis: {error_msg}")
                    
                    if 'not found' in error_msg.lower():
                        print(f"   💡 Suggestion: File may have been moved or deleted already")
                    elif 'permission' in error_msg.lower() or 'unauthorized' in error_msg.lower():
                        print(f"   💡 Suggestion: Check user permissions for deletion")
                    elif 'method not allowed' in error_msg.lower():
                        print(f"   💡 Suggestion: Check if DELETE method is supported")
                    else:
                        print(f"   💡 Suggestion: Check server logs for detailed error")
                        
            except:
                print(f"   📝 Delete Response: {delete_response.text[:200]}")
            return False
    
    except Exception as e:
        print(f"   ❌ Delete exception: {e}")
        return False
    
    # Verify the file was actually deleted
    print(f"\n✅ Step 3: Verify file was deleted")
    
    try:
        # Wait a moment for the deletion to be processed
        import time
        time.sleep(2)
        
        verify_response = requests.get(attachments_url, timeout=30)
        print(f"   📤 Verification URL: {attachments_url}")
        print(f"   🔄 Status: {verify_response.status_code}")
        
        if verify_response.status_code == 200:
            verify_data = verify_response.json()
            if verify_data.get('success') and verify_data.get('attachments'):
                attachments = verify_data['attachments']
                
                # Check if our file is still there
                file_still_exists = False
                for attachment in attachments:
                    if str(attachment.get('docinfoid')) == str(docinfoid):
                        file_still_exists = True
                        break
                
                if file_still_exists:
                    print(f"   ❌ VERIFICATION FAILED: File still exists after deletion!")
                    return False
                else:
                    print(f"   ✅ VERIFICATION SUCCESS: File was successfully deleted!")
                    print(f"   📊 Remaining attachments: {len(attachments)}")
                    return True
            else:
                print(f"   ⚠️  Could not verify deletion: {verify_data}")
                return False
        else:
            print(f"   ❌ Verification request failed: {verify_response.status_code}")
            return False
    
    except Exception as e:
        print(f"   ❌ Verification exception: {e}")
        return False

def test_delete_nonexistent_file():
    """Test deleting a file that doesn't exist to see error handling"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2219753'
    fake_docinfoid = '9999999'  # Non-existent file
    
    print(f"\n🔍 Testing delete of non-existent file (error handling)")
    print("=" * 60)
    
    delete_url = f'{base_url}/api/workorder/{wonum}/attachments/{fake_docinfoid}/delete'
    
    try:
        delete_response = requests.delete(delete_url, timeout=60)
        print(f"   📤 Delete URL: {delete_url}")
        print(f"   🔄 Status: {delete_response.status_code}")
        
        if delete_response.status_code == 404 or delete_response.status_code == 400:
            print(f"   ✅ Good error handling: Got expected error status")
            try:
                error_data = delete_response.json()
                print(f"   📝 Error message: {error_data.get('error', 'No error message')}")
            except:
                print(f"   📝 Response: {delete_response.text[:100]}")
        else:
            print(f"   ⚠️  Unexpected status for non-existent file: {delete_response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    print("🧪 Testing Delete Functionality")
    print("=" * 60)
    
    # Test deleting existing file
    success = test_delete_functionality()
    
    # Test error handling
    test_delete_nonexistent_file()
    
    print("\n" + "=" * 60)
    print(f"🎯 Delete Test Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if success:
        print("🎉 Delete functionality is working perfectly!")
    else:
        print("⚠️  Delete functionality needs investigation")
        print("\n📋 Common issues to check:")
        print("1. User permissions for deletion")
        print("2. OSLC API endpoint for deletion")
        print("3. Authentication/session issues")
        print("4. File ownership restrictions")
