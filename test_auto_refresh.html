<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto Refresh After Adjustment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Auto Refresh After Adjustment</h1>
        <p>This page tests the automatic refresh functionality after successful inventory adjustments.</p>
        
        <!-- Mock Search Results Container -->
        <div class="card">
            <div class="card-header">
                <h5>Mock Inventory Search Results</h5>
            </div>
            <div class="card-body">
                <div id="searchResults" style="min-height: 200px; position: relative;">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Item:</strong> 5975-60-V00-0529</p>
                            <p><strong>Site:</strong> LCVKWT</p>
                            <p><strong>Current Balance:</strong> <span id="currentBalance">30</span></p>
                            <p><strong>Physical Count:</strong> <span id="physicalCount">30</span></p>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary btn-sm me-2" onclick="testPhysicalCountRefresh()">
                                <i class="fas fa-clipboard-check me-1"></i>Test Physical Count Refresh
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="testCurrentBalanceRefresh()">
                                <i class="fas fa-balance-scale me-1"></i>Test Current Balance Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card mt-3">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p>Click the test buttons above to simulate successful adjustments and verify auto-refresh functionality.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Mock InventoryManager class with auto-refresh functionality
        class MockInventoryManager {
            constructor() {
                this.currentSearchTerm = "5975-60-V00-0529";
                this.currentSiteId = "LCVKWT";  // Fixed: use currentSiteId instead of currentSiteFilter
                this.currentPage = 0;
                this.itemsPerPage = 10;
            }

            showTemporaryMessage(message, type) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.insertBefore(alertDiv, document.body.firstChild);
                
                // Auto-dismiss after 3 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 3000);
            }

            showRefreshLoadingIndicator() {
                const resultsContainer = document.getElementById('searchResults');
                if (resultsContainer) {
                    const loadingOverlay = document.createElement('div');
                    loadingOverlay.id = 'refreshLoadingOverlay';
                    loadingOverlay.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                    loadingOverlay.style.cssText = `
                        background: rgba(255, 255, 255, 0.8);
                        z-index: 1000;
                        backdrop-filter: blur(2px);
                    `;
                    loadingOverlay.innerHTML = `
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="small text-muted">Refreshing inventory data...</div>
                        </div>
                    `;

                    if (getComputedStyle(resultsContainer).position === 'static') {
                        resultsContainer.style.position = 'relative';
                    }

                    resultsContainer.appendChild(loadingOverlay);
                }
            }

            hideRefreshLoadingIndicator() {
                const overlay = document.getElementById('refreshLoadingOverlay');
                if (overlay) {
                    overlay.remove();
                }
            }

            async performSearch(page = 0, searchTerm = '', siteFilter = '') {
                console.log('🔄 MOCK: Performing search with:', { page, searchTerm, siteFilter });
                
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                // Mock updated data
                const newBalance = Math.floor(Math.random() * 50) + 10;
                const newPhysCount = Math.floor(Math.random() * 50) + 10;
                
                document.getElementById('currentBalance').textContent = newBalance;
                document.getElementById('physicalCount').textContent = newPhysCount;
                
                console.log('✅ MOCK: Search completed with new values');
            }

            async refreshInventoryAfterAdjustment(adjustmentType) {
                try {
                    console.log(`🔄 REFRESH: Starting automatic refresh after ${adjustmentType} adjustment`);

                    // Check if we have an active search to refresh
                    if (!this.currentSearchTerm && !this.currentSiteId) {
                        console.log('🔄 REFRESH: No active search to refresh - no search term or site selected');
                        console.log(`🔄 REFRESH: currentSearchTerm: "${this.currentSearchTerm}", currentSiteId: "${this.currentSiteId}"`);
                        return;
                    }

                    // If we have a site but no search term, we still want to refresh if there are results displayed
                    const resultsContainer = document.getElementById('searchResults');
                    const hasResults = resultsContainer && resultsContainer.children.length > 0;

                    if (!this.currentSearchTerm && !hasResults) {
                        console.log('🔄 REFRESH: No search term and no results to refresh');
                        return;
                    }

                    console.log(`🔄 REFRESH: Current state - searchTerm: "${this.currentSearchTerm}", siteId: "${this.currentSiteId}", page: ${this.currentPage}`);

                    this.showRefreshLoadingIndicator();

                    await new Promise(resolve => setTimeout(resolve, 1000));

                    console.log(`🔄 REFRESH: Calling performSearch with page ${this.currentPage}`);
                    await this.performSearch(this.currentPage);

                    this.showTemporaryMessage(`${adjustmentType} adjustment completed and inventory data refreshed`, 'success');

                    console.log(`✅ REFRESH: Successfully refreshed inventory after ${adjustmentType} adjustment`);

                } catch (error) {
                    console.error(`❌ REFRESH: Error refreshing inventory after ${adjustmentType} adjustment:`, error);
                    this.showTemporaryMessage(`${adjustmentType} adjustment completed, but failed to refresh inventory data. Please refresh manually.`, 'warning');
                } finally {
                    this.hideRefreshLoadingIndicator();
                }
            }
        }

        // Create mock instance
        const mockInventoryManager = new MockInventoryManager();

        // Test functions
        async function testPhysicalCountRefresh() {
            addTestResult('🧪 Testing Physical Count Auto-Refresh...');
            
            // Simulate successful physical count adjustment
            mockInventoryManager.showTemporaryMessage('Physical count adjustment submitted successfully - refreshing inventory data...', 'success');
            
            // Trigger auto-refresh
            await mockInventoryManager.refreshInventoryAfterAdjustment('Physical Count');
            
            addTestResult('✅ Physical Count auto-refresh test completed');
        }

        async function testCurrentBalanceRefresh() {
            addTestResult('🧪 Testing Current Balance Auto-Refresh...');
            
            // Simulate successful current balance adjustment
            mockInventoryManager.showTemporaryMessage('Current balance adjustment submitted successfully - refreshing inventory data...', 'success');
            
            // Trigger auto-refresh
            await mockInventoryManager.refreshInventoryAfterAdjustment('Current Balance');
            
            addTestResult('✅ Current Balance auto-refresh test completed');
        }

        function addTestResult(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="small text-muted">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
