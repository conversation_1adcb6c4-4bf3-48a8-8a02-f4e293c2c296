# Comprehensive WSMethod Investigation Results

**Date:** 2025-07-15  
**Investigation Scope:** MXAPIINVENTORY transfercuritem integration, MATRECTRANS capabilities, and itemavailability wsmethod discovery  
**Target:** https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo  

## Executive Summary

After comprehensive investigation across multiple Maximo endpoints, **no functional wsmethods were found** for inventory transfer operations or item availability checks. The investigation revealed that:

1. **MXAPIINVENTORY transfercuritem** is read-only data, not functional for creating transfers
2. **MATRECTRANS endpoints** are not accessible via the tested API patterns
3. **itemavailability wsmethod** exists on multiple endpoints but requires object structures that don't exist
4. **Transfer-related wsmethods** exist but are not functional due to missing object structures

## 🔍 **1. MXAPIINVENTORY transfercuritem Integration**

### **Key Finding: Read-Only Transfer Data**
The `spi:transfercuritem` nested object in MXAPIINVENTORY contains **historical transfer data only**. It cannot be used to create new transfers.

### **Transfer Data Structure**
```json
{
  "spi:transfercuritem": [
    {
      "spi:fromavblbalance": 0.0,
      "spi:linecost": 46.2,
      "spi:fromstoreloc": "LCVK-CMW-CAS",
      "spi:tositeid": "LCVKWT",
      "spi:orgid": "USARMY",
      "spi:quantity": 1.0,
      "spi:unitcost": 46.2,
      "spi:islot": false,
      "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_[ENCODED_ID]/transfercuritem/0",
      "rdf:about": "http://childkey#[ENCODED_KEY]"
    }
  ]
}
```

### **Relationship to MATRECTRANS**
- Transfer data in MXAPIINVENTORY likely **references** MATRECTRANS records
- Direct correlation requires matching fields like `itemnum`, `siteid`, `quantity`, `unitcost`
- **No direct API integration** found between the two endpoints

## 🔍 **2. MATRECTRANS Table Investigation**

### **Endpoint Accessibility Results**
| Endpoint | URL Pattern | Status | Notes |
|----------|-------------|--------|-------|
| **matrectrans** | `/api/os/matrectrans` | ❌ 404 | Not available via API |
| **mxapimatrectrans** | `/api/os/mxapimatrectrans` | ❌ 404 | Not available via API |
| **OSLC matrectrans** | `/oslc/os/matrectrans` | ⚠️ 200 | Returns non-JSON content |
| **invtrans** | `/api/os/invtrans` | ❌ 404 | Not available |
| **mxapiinvtrans** | `/api/os/mxapiinvtrans` | ❌ 404 | Not available |

### **Key Finding: No Functional MATRECTRANS API**
- **No working MATRECTRANS endpoints** found for creating transfer transactions
- OSLC endpoints return HTML instead of JSON data
- Transfer creation likely requires **different API patterns** or **Maximo applications**

## 🔍 **3. itemavailability WSMethod Investigation**

### **Discovery Results**
The `itemavailability` wsmethod was found on **5 endpoints** but **none are functional**:

| Endpoint | WSMethod Status | Error Code | Error Message |
|----------|----------------|------------|---------------|
| **item** | ✅ Exists | BMXAA1281E | Object structure ITEM does not exist |
| **inventory** | ✅ Exists | BMXAA1281E | Object structure INVENTORY does not exist |
| **matrectrans** | ✅ Exists | BMXAA1281E | Object structure MATRECTRANS does not exist |
| **invbalances** | ✅ Exists | BMXAA1281E | Object structure INVBALANCES does not exist |
| **locations** | ✅ Exists | BMXAA1281E | Object structure LOCATIONS does not exist |

### **Working MXAPI Endpoints Test Results**
| Endpoint | Accessible | itemavailability | transfercurrentitem | Other WSMethods |
|----------|------------|------------------|-------------------|-----------------|
| **mxapiinventory** | ✅ Yes | ❌ Not Found | ❌ Not Found | ❌ None Found |
| **mxapiitem** | ✅ Yes | ❌ Not Found | ❌ Not Found | ❌ None Found |
| **mxapilabor** | ✅ Yes | ❌ Not Found | ❌ Not Found | ❌ None Found |
| **mxapiasset** | ✅ Yes | ❌ Not Found | ❌ Not Found | ❌ None Found |

## 📋 **4. Complete WSMethod Documentation**

### **❌ Non-Functional WSMethods**

#### **itemavailability**
- **WSMethod Name**: `wsmethod:itemavailability`
- **Endpoints**: item, inventory, matrectrans, invbalances, locations
- **Status**: Exists but not functional
- **Error**: `BMXAA1281E - The object structure [NAME] does not exist`

**Test Payload:**
```json
{
  "itemnum": "5975-60-V00-0001",
  "siteid": "LCVKWT",
  "location": "LCVK-CMW-AJ"
}
```

**API Response:**
```json
{
  "oslc:Error": {
    "oslc:statusCode": "400",
    "spi:reasonCode": "BMXAA1281E",
    "oslc:message": "BMXAA1281E - The object structure INVENTORY does not exist. Either create the object structure or reference an existing object structure."
  }
}
```

#### **transfercurrentitem**
- **WSMethod Name**: `wsmethod:transfercurrentitem`
- **Endpoints**: item, inventory, matrectrans, invbalances, locations
- **Status**: Exists but not functional
- **Error**: Same BMXAA1281E object structure error

#### **issuecurrentitem**
- **WSMethod Name**: `wsmethod:issuecurrentitem`
- **Endpoints**: item, inventory, matrectrans, invbalances, locations
- **Status**: Exists but not functional
- **Error**: Same BMXAA1281E object structure error

#### **addchange**
- **WSMethod Name**: `wsmethod:addchange`
- **Endpoints**: item, inventory, matrectrans, invbalances, locations
- **Status**: Exists but not functional
- **Error**: Same BMXAA1281E object structure error

## 🔐 **5. Authentication Examples**

### **API Key Authentication (Working for Data Retrieval)**
```bash
curl -X GET \
  "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory" \
  -H "Accept: application/json" \
  -H "apikey: dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
```

### **OSLC Token Authentication (Not Available)**
Session authentication was not available during testing, indicating the need for active browser sessions.

## 💻 **6. Working Implementation Examples**

### **✅ Retrieve Transfer Data from MXAPIINVENTORY**
```python
import requests

def get_transfer_data(api_key, site_id="LCVKWT"):
    """Retrieve transfer data from MXAPIINVENTORY."""
    
    url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    params = {
        "oslc.select": "itemnum,siteid,location,transfercuritem",
        "oslc.where": f'siteid="{site_id}"',
        "lean": "0"
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        data = response.json()
        return data.get('rdfs:member', [])
    else:
        return []

# Usage
api_key = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
transfers = get_transfer_data(api_key)

for record in transfers:
    if record.get('spi:transfercuritem'):
        print(f"Item: {record['spi:itemnum']}")
        for transfer in record['spi:transfercuritem']:
            print(f"  Transfer: {transfer['spi:quantity']} from {transfer['spi:fromstoreloc']} to {transfer['spi:tositeid']}")
```

### **❌ Non-Working Transfer Creation Attempts**
```python
# These patterns DO NOT WORK:

# 1. Direct nested object creation
POST /api/os/mxapiinventory/{id}/transfercuritem
# Result: 502 Bad Gateway

# 2. WSMethod on MXAPIINVENTORY
POST /api/os/mxapiinventory?action=wsmethod:transfercurrentitem
# Result: BMXAA9372E - Method not found

# 3. WSMethod on other endpoints
POST /api/os/inventory?action=wsmethod:transfercurrentitem
# Result: BMXAA1281E - Object structure does not exist
```

## 🎯 **7. Key Conclusions**

### **What Works**
1. **✅ Reading transfer data** from MXAPIINVENTORY nested objects
2. **✅ Basic inventory data retrieval** via GET operations
3. **✅ API key authentication** for data access

### **What Doesn't Work**
1. **❌ Creating transfers** via any discovered API endpoint
2. **❌ itemavailability wsmethod** (requires missing object structures)
3. **❌ MATRECTRANS API access** (endpoints not available)
4. **❌ All transfer-related wsmethods** (object structure errors)

### **Missing Components**
1. **Object Structures** - Required for wsmethod functionality
2. **MATRECTRANS API** - No functional endpoint found
3. **Transfer Creation API** - No working method discovered
4. **Session Authentication** - Not available during testing

## 💡 **8. Implementation Recommendations**

### **For Reading Transfer Data**
- ✅ Use MXAPIINVENTORY GET operations with `lean=0`
- ✅ Include `transfercuritem` in `oslc.select` parameter
- ✅ Use API key authentication for reliable access

### **For Creating Transfers**
- 🔍 **Investigate Maximo Applications** for transfer creation
- 🔍 **Look into Integration Object Services** with different patterns
- 🔍 **Consider direct database operations** if API access is insufficient
- 🔍 **Explore other Maximo transfer endpoints** not covered in this investigation

### **For Item Availability**
- 🔍 **Calculate availability** from MXAPIINVENTORY data (curbaltotal, reservedqty)
- 🔍 **Use inventory balance calculations** instead of wsmethod
- 🔍 **Implement custom availability logic** based on inventory fields

The investigation confirms that **traditional wsmethod patterns are not functional** in this Maximo environment, requiring alternative approaches for inventory transfer operations.
