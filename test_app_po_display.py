#!/usr/bin/env python3
"""
Test the application's PO display for item 5975-60-V00-0001
This tests the actual Flask application endpoint to see why POs aren't showing
"""

import os
import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TARGET_ITEM = "5975-60-V00-0001"
TARGET_SITE = "LCVKWT"

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def test_flask_availability_endpoint():
    """Test the Flask application's availability endpoint."""
    print_section("TESTING FLASK APPLICATION ENDPOINT")
    
    # Test the availability endpoint (correct Flask route)
    api_url = f"{BASE_URL}/api/inventory/availability/{TARGET_ITEM}"

    # Parameters
    params = {
        "siteid": TARGET_SITE
    }
    
    print(f"API URL: {api_url}")
    print(f"Parameters: {params}")
    
    try:
        response = requests.get(api_url, params=params, timeout=(10, 60))
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ API call successful")
                
                # Check availability summary
                summary = data.get('availability_summary', {})
                print(f"\n📊 Availability Summary:")
                print(f"   Total Available Balance: {summary.get('total_available_balance', 0)}")
                print(f"   Total Current Balance: {summary.get('total_current_balance', 0)}")
                print(f"   Total Locations: {summary.get('total_locations', 0)}")
                print(f"   Total Purchase Orders: {summary.get('total_purchase_orders', 0)}")
                print(f"   Total Purchase Requisitions: {summary.get('total_purchase_requisitions', 0)}")
                
                # Check purchase orders data
                po_data = data.get('purchase_orders', [])
                print(f"\n🛒 Purchase Orders from Flask App:")
                print(f"   Count: {len(po_data)}")
                
                if po_data:
                    print("   ✅ Purchase orders found in response!")
                    for i, po in enumerate(po_data, 1):
                        print(f"   {i}. PO #{po.get('ponum', 'N/A')} - Status: {po.get('status', 'N/A')}")
                        print(f"      Vendor: {po.get('vendor', 'N/A')} - Order Date: {po.get('orderdate', 'N/A')}")
                        
                        # Check poline data
                        poline_data = po.get('poline', [])
                        for poline in poline_data:
                            if poline.get('itemnum') == TARGET_ITEM:
                                print(f"      Item: {poline.get('itemnum')} - Qty: {poline.get('orderqty')} - Unit Cost: ${poline.get('unitcost')}")
                                break
                        print()
                else:
                    print("   ❌ No purchase orders found in Flask response!")
                    print("   🔍 This indicates an issue in the Flask application logic")
                
                # Check purchase requisitions data
                pr_data = data.get('purchase_requisitions', [])
                print(f"\n📋 Purchase Requisitions from Flask App:")
                print(f"   Count: {len(pr_data)}")
                
                if pr_data:
                    print("   ✅ Purchase requisitions found in response!")
                    for i, pr in enumerate(pr_data, 1):
                        print(f"   {i}. PR #{pr.get('prnum', 'N/A')} - Status: {pr.get('status', 'N/A')}")
                else:
                    print("   ❌ No purchase requisitions found in Flask response!")
                
                return data
                
            else:
                print(f"❌ API returned error: {data.get('error', 'Unknown error')}")
                print(f"Full response: {json.dumps(data, indent=2)}")
                return None
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:1000]}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Flask application is not running")
        print("   Please start the Flask app with: python3 app.py")
        return None
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None

def check_flask_app_running():
    """Check if the Flask application is running."""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print(f"🚀 Testing Flask Application PO Display")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Expected: 5 active purchase orders should be displayed")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Check if Flask app is running
    if not check_flask_app_running():
        print(f"\n❌ Flask application is not running at {BASE_URL}")
        print("Please start the application with: python3 app.py")
        exit(1)
    
    print(f"\n✅ Flask application is running at {BASE_URL}")
    
    # Test the availability endpoint
    result = test_flask_availability_endpoint()
    
    print(f"\n{'='*80}")
    print("🏁 Flask Application Test Complete")
    print(f"{'='*80}")
    
    if result:
        po_count = len(result.get('purchase_orders', []))
        if po_count > 0:
            print(f"✅ SUCCESS: Found {po_count} purchase orders in Flask response")
            print("   The issue may be in the frontend JavaScript display logic")
        else:
            print("❌ ISSUE: No purchase orders in Flask response")
            print("   The issue is in the Flask backend fetch_purchase_orders() function")
            print("   Need to debug the session authentication or API call logic")
    else:
        print("❌ FAILED: Could not get valid response from Flask application")
    
    print(f"\n📝 Next Steps:")
    print(f"   1. If POs found in Flask: Check frontend JavaScript display logic")
    print(f"   2. If no POs in Flask: Debug fetch_purchase_orders() function")
    print(f"   3. Check application logs for authentication or API errors")
    print(f"   4. Verify session token or API key authentication is working")
