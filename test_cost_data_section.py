#!/usr/bin/env python3
"""
Test script to verify the new cost data section implementation.
This script tests the cost data display functionality.
"""
import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_ITEM = "5975-60-V00-0529"
TEST_SITE = "LCVKWT"

def test_cost_data_availability():
    """Test that cost data is available in inventory search results"""
    print("🔧 Testing Cost Data Availability")
    print("=" * 50)
    
    try:
        # Get inventory data from the search API
        response = requests.get(
            f"{BASE_URL}/api/inventory/management/search",
            params={
                'siteid': TEST_SITE,
                'q': TEST_ITEM,
                'limit': 1,
                'page': 0
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items') and len(data['items']) > 0:
                item = data['items'][0]
                
                print(f"✅ Found inventory item: {item.get('itemnum')}")
                
                # Check for cost fields that should be available for the cost data section
                cost_fields = {
                    'avgcost': item.get('avgcost'),
                    'stdcost': item.get('stdcost'),
                    'lastcost': item.get('lastcost'),
                    'unitcost': item.get('unitcost'),
                    'conditioncode': item.get('conditioncode'),
                    'orgid': item.get('orgid'),
                    'invcostid': item.get('invcostid'),
                    'condrate': item.get('condrate')
                }
                
                print(f"\n🔍 Cost fields available for display:")
                available_fields = 0
                for field, value in cost_fields.items():
                    if value is not None and value != '':
                        status = "✅"
                        available_fields += 1
                    else:
                        status = "❌"
                    print(f"  {status} {field}: {value}")
                
                print(f"\n📊 Summary: {available_fields}/{len(cost_fields)} cost fields available")
                
                # Test the cost data section logic
                print(f"\n🔍 Cost Data Section Logic Test:")
                if available_fields > 0:
                    print(f"✅ Cost data section should be displayed ({available_fields} fields)")
                    return True, available_fields, item
                else:
                    print(f"❌ Cost data section should be hidden (no fields)")
                    return False, 0, item
            else:
                print(f"❌ No inventory items found")
                return False, 0, None
        else:
            print(f"❌ API Error: {response.status_code}")
            return False, 0, None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False, 0, None

def test_cost_data_formatting():
    """Test cost data formatting logic"""
    print("\n🔧 Testing Cost Data Formatting")
    print("=" * 50)
    
    # Test different value types and formatting
    test_cases = [
        # (value, type, expected_result_pattern)
        (123.45, 'currency', '$123.45'),
        (0, 'currency', '$0.00'),
        (None, 'currency', '-'),
        ('', 'currency', '-'),
        (85, 'percentage', '85%'),
        (0, 'percentage', '0%'),
        (12345, 'number', '12,345'),
        ('A1', 'text', 'A1'),
        ('USARMY', 'text', 'USARMY'),
    ]
    
    print("🔍 Testing value formatting:")
    all_passed = True
    
    for value, value_type, expected in test_cases:
        # Simulate the formatting logic from the JavaScript
        if value is None or value == '':
            formatted = '-'
        elif value_type == 'currency':
            try:
                num = float(value)
                formatted = f'${num:.2f}'
            except:
                formatted = '-'
        elif value_type == 'percentage':
            try:
                percent = float(value)
                formatted = f'{percent}%'
            except:
                formatted = '-'
        elif value_type == 'number':
            try:
                num = float(value)
                formatted = f'{num:,.0f}' if num == int(num) else f'{num:,}'
            except:
                formatted = str(value)
        else:  # text
            formatted = str(value)
        
        if expected in formatted or formatted == expected:
            status = "✅"
        else:
            status = "❌"
            all_passed = False
        
        print(f"  {status} {value} ({value_type}) -> {formatted} (expected: {expected})")
    
    return all_passed

def test_ui_integration():
    """Test UI integration by checking if the inventory management page loads"""
    print("\n🔧 Testing UI Integration")
    print("=" * 50)
    
    try:
        # Test if the inventory management page loads
        response = requests.get(f"{BASE_URL}/inventory-management", timeout=30)
        
        if response.status_code == 200:
            print("✅ Inventory management page loads successfully")
            
            # Check if the page contains the expected JavaScript and CSS
            content = response.text
            
            checks = [
                ('inventory_management.js', 'JavaScript file included'),
                ('style.css', 'CSS file included'),
                ('cost-data-section', 'Cost data section CSS class referenced'),
                ('generateCostDataSection', 'Cost data generation function referenced'),
            ]
            
            print(f"\n🔍 Page content checks:")
            all_checks_passed = True
            
            for check_item, description in checks:
                if check_item in content:
                    print(f"  ✅ {description}")
                else:
                    print(f"  ❌ {description}")
                    all_checks_passed = False
            
            return all_checks_passed
        else:
            print(f"❌ Page load failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_responsive_design():
    """Test responsive design considerations"""
    print("\n🔧 Testing Responsive Design")
    print("=" * 50)
    
    # Test different viewport scenarios
    viewports = [
        (1920, 1080, 'Desktop'),
        (1024, 768, 'Tablet'),
        (375, 667, 'Mobile'),
    ]
    
    print("🔍 Responsive design considerations:")
    
    for width, height, device in viewports:
        print(f"  📱 {device} ({width}x{height}):")
        
        if width < 768:
            print(f"    ✅ Should use mobile cost view")
            print(f"    ✅ Should use smaller fonts and padding")
        else:
            print(f"    ✅ Should use desktop table view")
            print(f"    ✅ Should use standard fonts and padding")
    
    return True

def main():
    """Main test function"""
    print("🚀 COST DATA SECTION IMPLEMENTATION TEST")
    print("=" * 60)
    print(f"🎯 Target Item: {TEST_ITEM}")
    print(f"🏢 Target Site: {TEST_SITE}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    results = []
    
    # Test 1: Cost data availability
    has_cost_data, field_count, item_data = test_cost_data_availability()
    results.append(("Cost Data Availability", has_cost_data))
    
    # Test 2: Cost data formatting
    formatting_ok = test_cost_data_formatting()
    results.append(("Cost Data Formatting", formatting_ok))
    
    # Test 3: UI integration
    ui_ok = test_ui_integration()
    results.append(("UI Integration", ui_ok))
    
    # Test 4: Responsive design
    responsive_ok = test_responsive_design()
    results.append(("Responsive Design", responsive_ok))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if has_cost_data and item_data:
        print(f"\n📋 Cost Data Summary for {item_data.get('itemnum')}:")
        cost_fields = ['avgcost', 'stdcost', 'lastcost', 'unitcost']
        for field in cost_fields:
            value = item_data.get(field)
            if value is not None and value != '':
                print(f"  💰 {field}: ${float(value):.2f}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Cost data section implementation is ready.")
        print("\n📝 Next Steps:")
        print("   1. Navigate to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for an inventory item")
        print("   3. Look for the 'Inventory Cost Data' section between item fields and balance details")
        print("   4. Click 'Show Costs' to expand the cost data table")
        print("   5. Verify all cost fields are displayed with proper formatting")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
