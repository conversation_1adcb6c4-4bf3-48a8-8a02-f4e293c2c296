#!/usr/bin/env python3
"""
Test script to try downloading recently uploaded files that should work
"""

import requests
import json

def test_recent_download():
    """Test downloading recently uploaded files"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing download of recently uploaded files for {wonum}")
    print("=" * 60)
    
    # Get attachments
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        response = requests.get(attachments_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('attachments'):
                attachments = data['attachments']
                
                # Find recently uploaded files (our test files)
                recent_files = []
                for attachment in attachments:
                    filename = attachment.get('filename', '')
                    changeby = attachment.get('changeby', '')
                    
                    # Look for files uploaded by SOFG118757 (our user) or test files
                    if (changeby == 'SOFG118757' or 
                        'test_upload' in filename.lower() or 
                        'mxloaderfromapp' in filename.lower() or
                        'maximo.png' in filename.lower()):
                        recent_files.append(attachment)
                
                print(f"📋 Found {len(recent_files)} recently uploaded files:")
                for i, file in enumerate(recent_files):
                    print(f"   {i+1}. {file.get('filename')} (ID: {file.get('docinfoid')}) - {file.get('doctype')} - Size: {file.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 'Unknown')}")
                
                # Test downloading the first recent file
                if recent_files:
                    test_file = recent_files[0]
                    docinfoid = test_file.get('docinfoid')
                    filename = test_file.get('filename')
                    
                    print(f"\n📥 Testing download of recent file: {filename} (ID: {docinfoid})")
                    
                    download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
                    
                    try:
                        download_response = requests.get(download_url, timeout=60)
                        print(f"   📤 Download URL: {download_url}")
                        print(f"   🔄 Status: {download_response.status_code}")
                        print(f"   📊 Content Length: {len(download_response.content)} bytes")
                        
                        if download_response.status_code == 200:
                            content = download_response.content
                            
                            # Check if it's actually file content
                            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                                print(f"   ❌ Got HTML response: {content[:100].decode('utf-8', errors='ignore')}")
                            else:
                                print(f"   ✅ Got file content! First 50 bytes: {content[:50]}")
                                
                                # Save the file
                                test_filename = f"downloaded_{filename}"
                                with open(test_filename, 'wb') as f:
                                    f.write(content)
                                print(f"   💾 Saved to: {test_filename}")
                                
                                # If it's a text file, show some content
                                if filename.endswith('.txt'):
                                    try:
                                        text_content = content.decode('utf-8')
                                        print(f"   📝 Text content preview: {text_content[:200]}")
                                    except:
                                        print(f"   📝 Binary content (not text)")
                        else:
                            print(f"   ❌ Download failed")
                            try:
                                error_data = download_response.json()
                                print(f"   📝 Error: {error_data.get('error')}")
                            except:
                                print(f"   📝 Response: {download_response.text[:200]}")
                    
                    except Exception as e:
                        print(f"   ❌ Exception: {e}")
                
                else:
                    print("❌ No recent files found to test")
            else:
                print(f"❌ Failed to get attachments: {data}")
        else:
            print(f"❌ Failed to get attachments: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Recent Download Test Complete")

if __name__ == "__main__":
    test_recent_download()
