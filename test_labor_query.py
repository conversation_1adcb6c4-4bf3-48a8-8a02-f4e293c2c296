#!/usr/bin/env python3
"""
Test script to query labor records for a specific parent work order
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from backend.auth import MaximoTokenManager

def test_labor_query():
    # Initialize token manager
    DEFAULT_MAXIMO_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    token_manager = MaximoTokenManager(DEFAULT_MAXIMO_URL)
    
    # Test query for tasks under parent work order
    parent_wonum = '2021-1744762'
    siteid = 'LCVKWT'
    
    print(f'🔍 Testing labor query for parent WO: {parent_wonum}, site: {siteid}')
    print('=' * 60)
    
    # Step 1: Get all tasks under this parent work order
    base_url = token_manager.base_url
    api_url = f'{base_url}/oslc/os/mxapiwodetail'
    
    # Query for tasks
    oslc_filter = f'parent="{parent_wonum}" and istask=1 and siteid="{siteid}"'
    params = {
        'oslc.select': 'wonum,siteid,parent,istask',
        'oslc.where': oslc_filter,
        'oslc.pageSize': '50',
        'lean': '1'
    }
    
    print(f'📋 Step 1: Getting tasks for parent WO {parent_wonum}')
    print(f'Filter: {oslc_filter}')
    
    response = token_manager.session.get(api_url, params=params, timeout=(5.0, 30))
    print(f'Response status: {response.status_code}')
    
    if response.status_code == 200:
        try:
            data = response.json()
            tasks = data.get('member', [])
        except Exception as e:
            print(f'Error parsing JSON: {e}')
            print(f'Response content: {response.text[:500]}')
            return
        print(f'Found {len(tasks)} tasks:')
        
        for i, task in enumerate(tasks):
            print(f'  {i+1}. Task: {task.get("wonum")}, Site: {task.get("siteid")}, Parent: {task.get("parent")}')
        
        print()
        print('🔍 Step 2: Querying labor records using MXAPILABTRANS...')
        print('=' * 60)
        
        total_records = 0
        total_hours = 0.0
        tasks_with_labor = 0
        
        for task in tasks:
            task_wonum = task.get('wonum')
            print(f'\n📊 Checking labor for task: {task_wonum}')
            
            # Query labor records for this specific task using MXAPILABTRANS
            labor_filter = f'refwo="{task_wonum}" and siteid="{siteid}"'
            labor_params = {
                'oslc.select': 'laborcode,regularhrs,refwo,siteid,labtransid',
                'oslc.where': labor_filter,
                'oslc.pageSize': '100',
                'lean': '1'
            }
            
            labor_url = f'{base_url}/oslc/os/mxapilabtrans'
            print(f'   Query: {labor_filter}')
            
            labor_response = token_manager.session.get(labor_url, params=labor_params, timeout=(5.0, 30))
            print(f'   Response status: {labor_response.status_code}')
            
            if labor_response.status_code == 200:
                labor_data = labor_response.json()
                labor_records = labor_data.get('member', [])
                task_records = len(labor_records)
                task_hours = 0.0
                
                print(f'   Found {task_records} labor records:')
                
                if task_records > 0:
                    tasks_with_labor += 1
                
                for j, labor in enumerate(labor_records):
                    regular_hrs = labor.get('regularhrs', 0)
                    try:
                        hrs_float = float(regular_hrs) if regular_hrs else 0.0
                        task_hours += hrs_float
                        print(f'     {j+1}. Labor: {labor.get("laborcode")}, Hours: {regular_hrs}, RefWO: {labor.get("refwo")}')
                    except (ValueError, TypeError):
                        print(f'     {j+1}. Labor: {labor.get("laborcode")}, Hours: {regular_hrs} (invalid), RefWO: {labor.get("refwo")}')
                
                total_records += task_records
                total_hours += task_hours
                print(f'   Task total: {task_hours} hours from {task_records} records')
            else:
                print(f'   Error: {labor_response.status_code}')
                if labor_response.text:
                    print(f'   Error details: {labor_response.text[:200]}')
        
        print()
        print('=' * 60)
        print(f'🎯 FINAL RESULTS for Parent WO {parent_wonum}:')
        print(f'   Total Labor Records: {total_records}')
        print(f'   Total Regular Hours: {total_hours:.2f}')
        print(f'   Tasks Found: {len(tasks)}')
        print(f'   Tasks with Labor: {tasks_with_labor}')
        
        # Also test direct query on parent work order
        print()
        print('🔍 Step 3: Testing direct query on parent work order...')
        print('=' * 60)
        
        parent_labor_filter = f'refwo="{parent_wonum}" and siteid="{siteid}"'
        parent_labor_params = {
            'oslc.select': 'laborcode,regularhrs,refwo,siteid,labtransid',
            'oslc.where': parent_labor_filter,
            'oslc.pageSize': '100',
            'lean': '1'
        }
        
        print(f'Query: {parent_labor_filter}')
        parent_labor_response = token_manager.session.get(labor_url, params=parent_labor_params, timeout=(5.0, 30))
        print(f'Response status: {parent_labor_response.status_code}')
        
        if parent_labor_response.status_code == 200:
            parent_labor_data = parent_labor_response.json()
            parent_labor_records = parent_labor_data.get('member', [])
            print(f'Found {len(parent_labor_records)} labor records on parent WO directly')
            
            parent_hours = 0.0
            for labor in parent_labor_records:
                regular_hrs = labor.get('regularhrs', 0)
                try:
                    hrs_float = float(regular_hrs) if regular_hrs else 0.0
                    parent_hours += hrs_float
                    print(f'   Labor: {labor.get("laborcode")}, Hours: {regular_hrs}, RefWO: {labor.get("refwo")}')
                except (ValueError, TypeError):
                    pass
            
            print(f'Parent WO direct total: {parent_hours} hours from {len(parent_labor_records)} records')
        
    else:
        print(f'Error getting tasks: {response.status_code}')
        print(response.text[:500])

if __name__ == '__main__':
    test_labor_query()
