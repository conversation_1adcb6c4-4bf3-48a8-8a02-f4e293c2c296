#!/usr/bin/env python3
"""
Comprehensive investigation of delete functionality in Maximo OSLC API
"""

import requests
import json

def investigate_delete_methods():
    """Investigate all possible delete methods for attachments"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2219753'
    
    print(f"🔍 Comprehensive Delete Investigation for {wonum}")
    print("=" * 60)
    
    # Get attachments first to find a test file
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        attachments_response = requests.get(attachments_url, timeout=30)
        print(f"   📤 Attachments URL: {attachments_response.status_code}")
        
        if attachments_response.status_code == 200:
            attachments_data = attachments_response.json()
            if attachments_data.get('success') and attachments_data.get('attachments'):
                attachments = attachments_data['attachments']
                
                # Find a file we can safely test with (uploaded by SOFG118757)
                test_file = None
                for attachment in attachments:
                    if attachment.get('changeby') == 'SOFG118757':
                        test_file = attachment
                        break
                
                if test_file:
                    docinfoid = test_file.get('docinfoid')
                    filename = test_file.get('filename')
                    identifier = test_file.get('original_data', {}).get('describedBy', {}).get('identifier')
                    href = test_file.get('href')
                    
                    print(f"   📄 Test file: {filename}")
                    print(f"   🆔 DocInfoID: {docinfoid}")
                    print(f"   🔢 Identifier: {identifier}")
                    print(f"   🔗 HREF: {href}")
                    
                    # Test various delete approaches
                    delete_tests = [
                        {
                            'name': 'DELETE /api/workorder/{wonum}/attachments/{docinfoid}',
                            'method': 'DELETE',
                            'url': f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}'
                        },
                        {
                            'name': 'DELETE direct OSLC doclinks with docinfoid',
                            'method': 'DELETE',
                            'url': f'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiwodetail/_TENWS1dULzIyMTk3NTM-/doclinks/{docinfoid}',
                            'direct_oslc': True
                        },
                        {
                            'name': 'DELETE direct OSLC doclinks with identifier',
                            'method': 'DELETE',
                            'url': f'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiwodetail/_TENWS1dULzIyMTk3NTM-/doclinks/{identifier}',
                            'direct_oslc': True
                        },
                        {
                            'name': 'POST with _action=Delete using docinfoid',
                            'method': 'POST',
                            'url': f'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiwodetail/_TENWS1dULzIyMTk3NTM-',
                            'payload': {
                                'doclinks': [{
                                    'docinfoid': int(docinfoid),
                                    '_action': 'Delete'
                                }]
                            },
                            'direct_oslc': True
                        },
                        {
                            'name': 'POST with _action=Delete using identifier',
                            'method': 'POST',
                            'url': f'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiwodetail/_TENWS1dULzIyMTk3NTM-',
                            'payload': {
                                'doclinks': [{
                                    'identifier': identifier,
                                    '_action': 'Delete'
                                }]
                            },
                            'direct_oslc': True
                        },
                        {
                            'name': 'PUT with empty/null content',
                            'method': 'PUT',
                            'url': f'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiwodetail/_TENWS1dULzIyMTk3NTM-/doclinks/{identifier}',
                            'payload': {},
                            'direct_oslc': True
                        }
                    ]
                    
                    print(f"\n🧪 Testing {len(delete_tests)} different delete approaches:")
                    print("=" * 60)
                    
                    for i, test in enumerate(delete_tests):
                        print(f"\n📋 Test {i+1}: {test['name']}")
                        print(f"   📤 Method: {test['method']}")
                        print(f"   🔗 URL: {test['url']}")
                        
                        try:
                            if test.get('direct_oslc'):
                                # For direct OSLC calls, we need to handle authentication differently
                                print(f"   ⚠️  Direct OSLC call - may require different authentication")
                                continue
                            
                            if test['method'] == 'DELETE':
                                response = requests.delete(test['url'], timeout=60)
                            elif test['method'] == 'POST':
                                headers = {'Content-Type': 'application/json'}
                                if 'payload' in test:
                                    print(f"   📦 Payload: {test['payload']}")
                                    response = requests.post(test['url'], json=test['payload'], headers=headers, timeout=60)
                                else:
                                    response = requests.post(test['url'], headers=headers, timeout=60)
                            elif test['method'] == 'PUT':
                                headers = {'Content-Type': 'application/json'}
                                payload = test.get('payload', {})
                                print(f"   📦 Payload: {payload}")
                                response = requests.put(test['url'], json=payload, headers=headers, timeout=60)
                            
                            print(f"   🔄 Status: {response.status_code}")
                            print(f"   📊 Content Length: {len(response.content)} bytes")
                            
                            if response.status_code in [200, 204]:
                                print(f"   ✅ SUCCESS! This method works!")
                                try:
                                    result = response.json()
                                    print(f"   📝 Response: {result}")
                                except:
                                    print(f"   📝 Response: {response.text[:200]}")
                                return True
                            elif response.status_code == 404:
                                print(f"   ❌ Not Found (404) - endpoint doesn't exist")
                            elif response.status_code == 405:
                                print(f"   ❌ Method Not Allowed (405) - method not supported")
                            elif response.status_code == 501:
                                print(f"   ❌ Not Implemented (501) - method not implemented")
                            else:
                                print(f"   ⚠️  Status {response.status_code}")
                                try:
                                    error_data = response.json()
                                    print(f"   📝 Error: {error_data}")
                                except:
                                    print(f"   📝 Response: {response.text[:200]}")
                        
                        except Exception as e:
                            print(f"   ❌ Exception: {e}")
                    
                    print(f"\n🔍 Additional Investigation: Check what methods are allowed")
                    print("=" * 60)
                    
                    # Test OPTIONS method to see what's allowed
                    options_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}'
                    try:
                        options_response = requests.options(options_url, timeout=30)
                        print(f"   📤 OPTIONS {options_url}")
                        print(f"   🔄 Status: {options_response.status_code}")
                        print(f"   📋 Headers: {dict(options_response.headers)}")
                        
                        if 'Allow' in options_response.headers:
                            allowed_methods = options_response.headers['Allow']
                            print(f"   ✅ Allowed methods: {allowed_methods}")
                        else:
                            print(f"   ⚠️  No 'Allow' header found")
                    
                    except Exception as e:
                        print(f"   ❌ OPTIONS exception: {e}")
                
                else:
                    print(f"   ❌ No test files found (no files by SOFG118757)")
                    
                    # Let's try to upload a test file first
                    print(f"\n📤 Uploading test file for deletion testing...")
                    return upload_test_file_for_deletion(base_url, wonum)
            else:
                print(f"   ❌ Failed to get attachments: {attachments_data}")
        else:
            print(f"   ❌ Attachments request failed: {attachments_response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return False

def upload_test_file_for_deletion(base_url, wonum):
    """Upload a test file specifically for deletion testing"""
    
    print(f"🔄 Uploading test file for deletion testing")
    print("=" * 40)
    
    # Create a simple test file
    test_content = "This file is created specifically for testing delete functionality.\nIt should be deleted after testing."
    test_filename = "delete_test.txt"
    
    with open(test_filename, 'w') as f:
        f.write(test_content)
    
    print(f"   📄 Created: {test_filename}")
    
    # Upload the file
    upload_url = f'{base_url}/api/workorder/{wonum}/attachments/upload'
    
    try:
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/plain')}
            data = {
                'description': 'Test file for delete functionality testing',
                'category': 'Attachments'
            }
            
            upload_response = requests.post(upload_url, files=files, data=data, timeout=60)
            print(f"   📤 Upload status: {upload_response.status_code}")
            
            if upload_response.status_code == 200:
                upload_data = upload_response.json()
                if upload_data.get('success'):
                    print(f"   ✅ Upload successful!")
                    
                    # Wait for processing
                    import time
                    time.sleep(3)
                    
                    # Now test delete on this new file
                    print(f"\n🗑️ Testing delete on newly uploaded file...")
                    return investigate_delete_methods()
                else:
                    print(f"   ❌ Upload failed: {upload_data}")
            else:
                print(f"   ❌ Upload failed: {upload_response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Upload exception: {e}")
    
    finally:
        # Clean up test file
        import os
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"   🧹 Cleaned up: {test_filename}")
    
    return False

if __name__ == "__main__":
    print("🧪 Comprehensive Delete Investigation")
    print("=" * 60)
    
    success = investigate_delete_methods()
    
    print("\n" + "=" * 60)
    print(f"🎯 Investigation Result: {'✅ FOUND WORKING METHOD' if success else '❌ NO WORKING METHOD FOUND'}")
    
    if not success:
        print("\n📋 Summary of findings:")
        print("1. Test all available HTTP methods (DELETE, POST, PUT)")
        print("2. Check different URL patterns and identifiers")
        print("3. Verify authentication and permissions")
        print("4. Look for alternative OSLC endpoints")
        print("5. Consider if deletion requires special headers or parameters")
