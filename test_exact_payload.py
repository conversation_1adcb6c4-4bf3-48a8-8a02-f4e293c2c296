#!/usr/bin/env python3
"""
Test with EXACT payload structure as specified by user - no modifications.
"""
import sys
import os
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def test_exact_payload():
    """Test with EXACT payload structure as specified."""
    
    print("🔧 Testing with EXACT payload structure")
    print("=" * 60)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Check if logged in
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo.")
        return False
    
    print(f"✅ Logged in as: {getattr(token_manager, 'username', 'Unknown')}")
    
    # EXACT payload as specified by user
    payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 0,
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "lotnum": "",
                    "reconciled": True,
                    "memo": "",
                    "controlacc": "",
                    "shrinkageacc": ""
                }
            ]
        }
    ]
    
    print(f"📋 EXACT Payload:")
    print(json.dumps(payload, indent=2))
    
    # Test with mxapiinventory endpoint
    url = f"{BASE_URL}/oslc/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    print(f"\n🌐 URL: {url}")
    print(f"📋 Headers: {headers}")
    print("")
    
    try:
        response = token_manager.session.post(
            url,
            json=payload,
            headers=headers,
            timeout=(5.0, 30)
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📤 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"✅ SUCCESS! HTTP 200 received")
            print(f"📋 Response length: {len(response.text)} characters")
            
            # Try to parse as JSON
            try:
                response_data = response.json()
                print(f"📋 JSON Response:")
                print(json.dumps(response_data, indent=2))
            except json.JSONDecodeError:
                print(f"📋 Non-JSON Response (first 500 chars):")
                print(response.text[:500])
                
            return True
            
        else:
            print(f"❌ Failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📋 Error Response:")
                print(json.dumps(error_data, indent=2))
            except json.JSONDecodeError:
                print(f"📋 Error Response (first 500 chars):")
                print(response.text[:500])
                
            return False
        
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return False

def main():
    """Main function."""
    print("🚀 Testing EXACT payload structure")
    print("=" * 50)
    
    success = test_exact_payload()
    
    if success:
        print("\n🎉 EXACT PAYLOAD TEST SUCCESSFUL!")
        print("✅ Maximo accepted the exact payload structure")
    else:
        print("\n❌ EXACT PAYLOAD TEST FAILED")
    
    return success

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
