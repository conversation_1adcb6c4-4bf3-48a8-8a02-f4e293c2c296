{"investigation_timestamp": "2025-07-15T17:57:43.399994", "base_url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo", "endpoint": "MXAPIINVENTORY", "prerequisites": {"authentication": true, "endpoint_accessible": false}, "http_methods": {"oslc": {"GET": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:46 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582466265mpMKCfLx1", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten711789544=001752582466265715fVWZFPTv9zjkvcitzHg3uID3G3ZZo82By5UP+kPM=; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn711789544=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?oslc.select=itemnum&oslc.pageSize=1&lean=1; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "POST": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:46 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582466515Bau8vIDfa", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStatep751742585=001752582466515K/TxwjfBSw2NG10doN3LkGLa/QrVSthl83q9/OeQxLA=; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASPostParam=L21heGltby9vc2xjL29zL214YXBpaW52ZW50b3J5.AAAAAAAAAC0=.AAAAEA==.YXBwbGljYXRpb24vanNvbg==.W3siX2FjdGlvbiI6ICJBZGRDaGFuZ2UiLCAiaXRlbW51bSI6ICJURVNUIn1d; Path=/maximo/oslc/os/mxapiinventory; Secure; HttpOnly, WASReqURLOidcp751742585=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "PUT": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:46 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582466762RwzaYYeu6", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten967646609=001752582466762ri4FNVgmv+xM1dsUh02tly52vu8gwbvWPyM9DAE+dg4=; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn967646609=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "PATCH": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467091R2i3nWPIe", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStatep2030815066=001752582467091UyxKH/gFTp5lnF2TtigaJOX2lFkVPFlFp9OK/6q8bSE=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcp2030815066=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "DELETE": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467393a3aPytrlW", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStatep462073021=001752582467393iWMvKr+4TL70OUL+xfcKbaOFW0qjue9dV9JvqhF6SoM=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcp462073021=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "OPTIONS": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467647FG4w3eQ3c", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten2015329253=001752582467647lLEfCABXo78HTH2qd2vDWgU/38G7qifVeJo8pVnEXR8=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn2015329253=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "HEAD": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467894o0Q55iEkB", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten1708363815=001752582467894riUdRjx/xTFX0nIJiKQojSjw+yuUB2ZvQc0FC2ohPvg=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn1708363815=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}}, "api": {"GET": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "POST": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "PUT": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "PATCH": {"supported": false, "status_code": 501, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "text/html;charset=ISO-8859-1", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "$wsep": "", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "text/html;charset=ISO-8859-1", "response_size": 92, "allows_method": false, "sample_response": "Error 501: Method PATCH is not defined in RFC 2068 and is not supported by the Servlet API \n"}, "DELETE": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:49 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "OPTIONS": {"supported": true, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:49 GMT", "Content-Length": "0", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding"}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "HEAD": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:49 GMT", "Content-Type": "application/json", "Content-Length": "284", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding"}, "content_type": "application/json", "response_size": 0, "allows_method": false, "sample_response": null}}}, "field_schemas": {}, "business_rules": {"validation_tests": {"missing_required_fields": {"status_code": 200, "payload_sent": [{"_action": "AddChange"}], "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link"}, "invalid_site": {"status_code": 200, "payload_sent": [{"_action": "AddChange", "itemnum": "TEST", "siteid": "INVALID"}], "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link"}, "invalid_action": {"status_code": 200, "payload_sent": [{"_action": "InvalidAction", "itemnum": "TEST", "siteid": "LCVKWT"}], "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link"}}, "constraint_patterns": {}, "error_responses": {}}, "error_patterns": {}, "examples": {}, "get_operations": {"field_discovery": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "filtering_capabilities": {"site_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,siteid", "oslc.where": "siteid=\"LCVKWT\"", "oslc.pageSize": "5", "lean": "1"}}, "status_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,status", "oslc.where": "status=\"ACTIVE\"", "oslc.pageSize": "5", "lean": "1"}}, "item_pattern_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,description", "oslc.where": "itemnum like \"5975%\"", "oslc.pageSize": "5", "lean": "1"}}, "combined_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,siteid,status", "oslc.where": "siteid=\"LCVKWT\" and status=\"ACTIVE\"", "oslc.pageSize": "5", "lean": "1"}}}, "pagination": {"page_size_1": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "page_size_5": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "page_size_10": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "page_size_50": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}}, "response_structure": {}}, "post_operations": {"supported_actions": {"AddChange": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:01 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}, "Create": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:02 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}, "Update": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:03 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}, "Sync": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:04 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}}, "required_fields": {}, "validation_rules": {}, "response_patterns": {}}, "update_operations": {"put_capabilities": {"status_code": 200, "supported": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:05 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_preview": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-"}, "patch_capabilities": {"status_code": 200, "supported": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:06 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_preview": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-"}, "update_patterns": {}}, "delete_operations": {"delete_support": {"status_code": 200, "supported": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:07 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_preview": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-"}, "deletion_patterns": {}}, "final_report": {"executive_summary": {"endpoint_accessible": false, "supported_methods": ["GET (oslc)", "POST (oslc)", "PUT (oslc)", "PATCH (oslc)", "DELETE (oslc)", "OPTIONS (oslc)", "HEAD (oslc)", "GET (api)", "POST (api)", "PUT (api)", "DELETE (api)", "OPTIONS (api)", "HEAD (api)"], "total_fields_discovered": 0, "investigation_timestamp": "2025-07-15T17:57:43.399994", "key_capabilities": ["Read inventory data", "Create/modify inventory records", "Update existing records", "Delete inventory records"]}, "method_inventory": {"http_methods": {"oslc": {"GET": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:46 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582466265mpMKCfLx1", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten711789544=001752582466265715fVWZFPTv9zjkvcitzHg3uID3G3ZZo82By5UP+kPM=; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn711789544=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?oslc.select=itemnum&oslc.pageSize=1&lean=1; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "POST": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:46 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582466515Bau8vIDfa", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStatep751742585=001752582466515K/TxwjfBSw2NG10doN3LkGLa/QrVSthl83q9/OeQxLA=; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASPostParam=L21heGltby9vc2xjL29zL214YXBpaW52ZW50b3J5.AAAAAAAAAC0=.AAAAEA==.YXBwbGljYXRpb24vanNvbg==.W3siX2FjdGlvbiI6ICJBZGRDaGFuZ2UiLCAiaXRlbW51bSI6ICJURVNUIn1d; Path=/maximo/oslc/os/mxapiinventory; Secure; HttpOnly, WASReqURLOidcp751742585=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "PUT": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:46 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582466762RwzaYYeu6", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten967646609=001752582466762ri4FNVgmv+xM1dsUh02tly52vu8gwbvWPyM9DAE+dg4=; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn967646609=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:46 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "PATCH": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467091R2i3nWPIe", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStatep2030815066=001752582467091UyxKH/gFTp5lnF2TtigaJOX2lFkVPFlFp9OK/6q8bSE=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcp2030815066=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "DELETE": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467393a3aPytrlW", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStatep462073021=001752582467393iWMvKr+4TL70OUL+xfcKbaOFW0qjue9dV9JvqhF6SoM=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcp462073021=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "OPTIONS": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467647FG4w3eQ3c", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten2015329253=001752582467647lLEfCABXo78HTH2qd2vDWgU/38G7qifVeJo8pVnEXR8=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn2015329253=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "HEAD": {"supported": true, "status_code": 302, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:47 GMT", "Connection": "keep-alive", "location": "https://auth.v2x.maximotest.gov2x.com/oidc/endpoint/MaximoAppSuite/authorize?scope=openid&response_type=code&client_id=manage&redirect_uri=https%3A%2F%2Fvectrustst01.manage.v2x.maximotest.gov2x.com%2Foidcclient%2Fredirect%2Foidc&state=001752582467894o0Q55iEkB", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "set-cookie": "WASOidcCode=\"\"; Expires=Thu, 01 Dec 1994 16:00:00 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASOidcStaten1708363815=001752582467894riUdRjx/xTFX0nIJiKQojSjw+yuUB2ZvQc0FC2ohPvg=; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly, WASReqURLOidcn1708363815=https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory; Expires=Tue, 15 Jul 2025 12:34:47 GMT; Path=/; Domain=v2x.maximotest.gov2x.com; Secure; HttpOnly", "expires": "Thu, 01 Dec 1994 16:00:00 GMT", "cache-control": "no-cache=\"set-cookie, set-cookie2\""}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}}, "api": {"GET": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "POST": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "PUT": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "PATCH": {"supported": false, "status_code": 501, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:48 GMT", "Content-Type": "text/html;charset=ISO-8859-1", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "$wsep": "", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "text/html;charset=ISO-8859-1", "response_size": 92, "allows_method": false, "sample_response": "Error 501: Method PATCH is not defined in RFC 2068 and is not supported by the Servlet API \n"}, "DELETE": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:49 GMT", "Content-Type": "application/json", "Content-Length": "215", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "content_type": "application/json", "response_size": 284, "allows_method": false, "sample_response": {"Error": {"extendedError": {"moreInfo": {"href": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/oslc/error/messages/BMXAA7901E"}}, "reasonCode": "BMXAA7901E", "message": "BMXAA7901E - You cannot log in at this time. Contact the system administrator.", "statusCode": "403"}}}, "OPTIONS": {"supported": true, "status_code": 200, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:49 GMT", "Content-Length": "0", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding"}, "content_type": "", "response_size": 0, "allows_method": false, "sample_response": null}, "HEAD": {"supported": true, "status_code": 403, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:27:49 GMT", "Content-Type": "application/json", "Content-Length": "284", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding"}, "content_type": "application/json", "response_size": 0, "allows_method": false, "sample_response": null}}}, "get_operations": {"field_discovery": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "filtering_capabilities": {"site_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,siteid", "oslc.where": "siteid=\"LCVKWT\"", "oslc.pageSize": "5", "lean": "1"}}, "status_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,status", "oslc.where": "status=\"ACTIVE\"", "oslc.pageSize": "5", "lean": "1"}}, "item_pattern_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,description", "oslc.where": "itemnum like \"5975%\"", "oslc.pageSize": "5", "lean": "1"}}, "combined_filter": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)", "params_used": {"oslc.select": "itemnum,siteid,status", "oslc.where": "siteid=\"LCVKWT\" and status=\"ACTIVE\"", "oslc.pageSize": "5", "lean": "1"}}}, "pagination": {"page_size_1": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "page_size_5": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "page_size_10": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "page_size_50": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}}, "response_structure": {}}, "post_operations": {"supported_actions": {"AddChange": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:01 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}, "Create": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:02 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}, "Update": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:03 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}, "Sync": {"status_code": 200, "success": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:04 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link rel=\"shortcut icon\" href=\"./favicon.svg\"/><link href=\"./static/css/npm.maximo-maximo-js-api.ef3a6c4b.chunk.css\" rel=\"stylesheet\"><link href=\"./static/css/main.85541544.chunk.css\" rel=\"stylesheet\"></h"}}, "required_fields": {}, "validation_rules": {}, "response_patterns": {}}, "update_operations": {"put_capabilities": {"status_code": 200, "supported": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:05 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_preview": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-"}, "patch_capabilities": {"status_code": 200, "supported": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:06 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_size": 1561, "response_preview": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-"}, "update_patterns": {}}, "delete_operations": {"delete_support": {"status_code": 200, "supported": true, "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:28:07 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "response_preview": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-"}, "deletion_patterns": {}}}, "field_reference": {}, "usage_examples": {"create_inventory_adjustment": {"method": "POST", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory", "payload": [{"_action": "AddChange", "itemnum": "EXAMPLE-ITEM", "itemsetid": "ITEMSET", "siteid": "LCVKWT", "location": "STORE-001", "issueunit": "EA", "invbalances": [{"binnum": "BIN-001", "curbal": 100, "physcnt": 95, "conditioncode": "A1"}]}], "description": "Create inventory adjustment with physical count"}}, "recommendations": ["Use session-based authentication for API calls", "Use OSLC endpoint (/oslc/os/mxapiinventory) for GET operations", "Use OSLC endpoint for POST operations with proper _action field", "Implement proper error handling for API responses", "Preserve and return actual Maximo error messages to users"]}}