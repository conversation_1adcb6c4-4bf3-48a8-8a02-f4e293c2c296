# Maximo Inventory Service Integration Guide

**Generated:** 2025-07-16T09:30:06.362649
**Base URL:** https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo
**API Key:** dj9sia0tu2...r0ahlsn70o

## Authentication

### API Key Authentication (Recommended)
```python
headers = {
    'Accept': 'application/json',
    'apikey': 'your-api-key-here'
}
```

## Available Methods (15 total)

### WSMethod (15 methods)

| Name | Method | Endpoint | Description |
|------|--------|----------|-------------|
| ADDCHANGE | POST | /api/os/mxapiinventory?action=wsmethod:ADDCHANGE | Web service method for inventory operations |
| ADJUSTCURRENTBALANCE | POST | /api/os/mxapiinventory?action=wsmethod:ADJUSTCURRENTBALANCE | Web service method for inventory operations |
| ADJUSTPHYSICALCOUNT | POST | /api/os/mxapiinventory?action=wsmethod:ADJUSTPHYSICALCOUNT | Web service method for inventory operations |
| TRANSFERCURITEM | POST | /api/os/mxapiinventory?action=wsmethod:TRANSFERCURITEM | Web service method for inventory operations |
| ISSUECURITEM | POST | /api/os/mxapiinventory?action=wsmethod:ISSUECURITEM | Web service method for inventory operations |
| RECEIVECURITEM | POST | /api/os/mxapiinventory?action=wsmethod:RECEIVECURITEM | Web service method for inventory operations |
| RETURNCURITEM | POST | /api/os/mxapiinventory?action=wsmethod:RETURNCURITEM | Web service method for inventory operations |
| CREATE | POST | /api/os/mxapiinventory?action=wsmethod:CREATE | Web service method for inventory operations |
| UPDATE | POST | /api/os/mxapiinventory?action=wsmethod:UPDATE | Web service method for inventory operations |
| DELETE | POST | /api/os/mxapiinventory?action=wsmethod:DELETE | Web service method for inventory operations |
| SAVE | POST | /api/os/mxapiinventory?action=wsmethod:SAVE | Web service method for inventory operations |
| VALIDATE | POST | /api/os/mxapiinventory?action=wsmethod:VALIDATE | Web service method for inventory operations |
| GETINVENTORY | POST | /api/os/mxapiinventory?action=wsmethod:GETINVENTORY | Web service method for inventory operations |
| GETBALANCE | POST | /api/os/mxapiinventory?action=wsmethod:GETBALANCE | Web service method for inventory operations |
| GETAVAILABILITY | POST | /api/os/mxapiinventory?action=wsmethod:GETAVAILABILITY | Web service method for inventory operations |

## Integration Examples

### Basic Inventory Query
```python
import requests

url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory'
headers = {'Accept': 'application/json', 'apikey': 'your-api-key'}
params = {
    'oslc.select': 'itemnum,siteid,location,curbaltotal',
    'oslc.where': 'siteid="YOURSITE"',
    'oslc.pageSize': '10'
}
response = requests.get(url, headers=headers, params=params)
```

### ADDCHANGE WSMethod
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:ADDCHANGE'
headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'apikey': 'your-api-key'
}
payload = {'your': 'data'}
response = requests.post(url, headers=headers, json=payload)
```

