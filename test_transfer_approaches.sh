#!/bin/bash

# Transfer Current Item Test Script
# Based on IBM Community research and terminal analysis

echo "🚀 Testing Transfer Current Item with Multiple Approaches"
echo "========================================================"

# Base configuration
MAXIMO_BASE="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
HEADERS="-H 'Accept: application/json' -H 'Content-Type: application/json'"

# Test payload from terminal logs
PAYLOAD='{
  "itemnum": "5975-60-V00-0529",
  "fromsiteid": "LCVKWT",
  "tositeid": "IKWAJ",
  "fromlocation": "RIP001",
  "tolocation": "KWAJ-1058",
  "quantity": 1.0,
  "fromissueunit": "RO",
  "toissueunit": "RO",
  "frombinnum": "28-800-0004",
  "fromlotnum": "TEST",
  "tolotnum": "TEST",
  "fromconditioncode": "A1",
  "toconditioncode": "A1"
}'

echo "\n1️⃣ Testing Object Structure Approach (Current Implementation)"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinventory"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\n2️⃣ Testing Transfer Endpoint"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapitransfer"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapitransfer" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\n3️⃣ Testing Inventory Transaction Endpoint"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinvtrans"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinvtrans" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\n4️⃣ Testing Action Parameter Approach"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem"
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response not JSON"

echo "\n✅ Test completed!"
