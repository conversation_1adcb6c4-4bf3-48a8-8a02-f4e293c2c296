#!/bin/bash

# Test Corrected Transfer - Fix Location Issue
# ============================================

echo "🚀 TESTING CORRECTED TRANSFER"
echo "============================="

# The issue is that Maxim<PERSON> is checking if KWAJ-1058 exists in LCVKWT instead of IKWAJ
# Let's try different approaches to fix this

echo "🔍 Issue Analysis:"
echo "  Current error: Location KWAJ-1058 does not exist in site LCVKWT"
echo "  Problem: Maximo is validating destination location against source site"
echo "  Solution: Try different payload structures"
echo ""

# Test 1: Use a location that exists in LCVKWT as destination
echo "📋 TEST 1: Use LCVKWT location as destination"
echo "=========================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "❌ Expected to fail - same location transfer"
echo ""

# Test 2: Try a different LCVKWT location
echo "📋 TEST 2: Different LCVKWT location"
echo "=================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CENTRAL",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🤔 Testing if CENTRAL exists in LCVKWT"
echo ""

# Test 3: Try without specifying destination site (let Maximo infer)
echo "📋 TEST 3: Minimal transfer (let Maximo infer destination)"
echo "======================================================"

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🧪 Testing without explicit to_siteid"
echo ""

# Test 4: Try with quantity 0.1 (smaller amount)
echo "📋 TEST 4: Smaller quantity transfer"
echo "=================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 0.1,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🔢 Testing with smaller quantity"
echo ""

# Test 5: Try with different item that might have different validation
echo "📋 TEST 5: Different item number"
echo "=============================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "TEST-ITEM",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "EA"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🔧 Testing with different item"
echo ""

# Test 6: Try with bins that might exist
echo "📋 TEST 6: With specific bins"
echo "============================"

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "📦 Testing with DEFAULT bins"
echo ""

echo "📊 SUMMARY"
echo "=========="
echo "All tests completed. Look for any responses with:"
echo "  ✅ HTTP_STATUS:200 AND _responsemeta.status: '204'"
echo "  ✅ No Error objects in _responsedata"
echo ""
echo "If all tests show the same location error, the issue is:"
echo "  🔍 KWAJ-1058 location doesn't exist in the Maximo system"
echo "  🔍 Or the location exists but not in the expected site"
echo "  🔍 Or there's a different validation rule we need to satisfy"
