#!/usr/bin/env python3
"""
Test script for negative labor hours API endpoint
"""

import requests
import json
import sys

def test_negative_labor_api():
    """Test the negative labor hours API endpoint"""
    
    # Test data - using the same work order from the logs
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -1.0,  # Negative 1 hour
        "siteid": "LCVKWT",
        "taskid": 10,
        "parent_wonum": "2219753",
        "craft": "MATCTRLSPCSR"
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    print(f"🔧 Testing negative labor API endpoint: {url}")
    print(f"📋 Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Make the API request
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 Response status code: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        # Try to parse JSON response
        try:
            response_data = response.json()
            print(f"✅ Response JSON: {json.dumps(response_data, indent=2)}")
            
            if response_data.get('success'):
                print("🎉 SUCCESS: Negative labor hours added successfully!")
                return True
            else:
                print(f"❌ FAILED: {response_data.get('error', 'Unknown error')}")
                return False
                
        except json.JSONDecodeError:
            print(f"📄 Response text: {response.text}")
            if response.status_code == 200:
                print("✅ SUCCESS: Got 200 status code (assuming success)")
                return True
            else:
                print(f"❌ FAILED: Non-200 status code: {response.status_code}")
                return False
                
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

def test_missing_parameters():
    """Test the API with missing required parameters"""
    
    print("\n" + "="*50)
    print("🧪 Testing missing parameters scenario")
    
    # Test data with missing required parameters
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -1.0,
        # Missing siteid, taskid, parent_wonum
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    print(f"🔧 Testing with missing parameters: {url}")
    print(f"📋 Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 Response status code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"📄 Response JSON: {json.dumps(response_data, indent=2)}")
            
            if not response_data.get('success') and 'Missing required parameters' in response_data.get('error', ''):
                print("✅ SUCCESS: Correctly detected missing parameters!")
                return True
            else:
                print("❌ FAILED: Should have detected missing parameters")
                return False
                
        except json.JSONDecodeError:
            print(f"📄 Response text: {response.text}")
            print("❌ FAILED: Expected JSON error response")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting negative labor hours API tests")
    print("="*50)
    
    # Test 1: Valid negative labor request
    success1 = test_negative_labor_api()
    
    # Test 2: Missing parameters
    success2 = test_missing_parameters()
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS:")
    print(f"✅ Valid request test: {'PASSED' if success1 else 'FAILED'}")
    print(f"✅ Missing params test: {'PASSED' if success2 else 'FAILED'}")
    
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED!")
        sys.exit(1)
