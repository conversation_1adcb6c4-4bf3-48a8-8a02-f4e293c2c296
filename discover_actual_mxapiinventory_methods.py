#!/usr/bin/env python3
"""
Discover Actual MXAPIINVENTORY Methods and Nested Objects

This script explores the actual structure of MXAPIINVENTORY to find:
1. Real nested objects that contain actions
2. Available wsmethods on those nested objects
3. Proper REST operations supported by the endpoint

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

def explore_inventory_structure():
    """Explore the complete structure of an inventory record."""
    print("🔍 Exploring MXAPIINVENTORY Structure")
    print("=" * 80)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    # Get a complete inventory record with all nested objects
    try:
        response = requests.get(
            endpoint_url,
            params={
                "oslc.select": "*",
                "oslc.where": 'itemnum="5975-60-V00-0001" and siteid="LCVKWT"',
                "oslc.pageSize": "1",
                "lean": "0"  # Include all nested objects
            },
            headers=headers,
            timeout=(3.05, 15)
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'rdfs:member' in data and data['rdfs:member']:
                record = data['rdfs:member'][0]
                
                print("📋 Main Record Fields:")
                for key, value in record.items():
                    if isinstance(value, dict):
                        if 'href' in value:
                            print(f"  🔗 {key}: {value.get('href', '')}")
                        else:
                            print(f"  📄 {key}: {type(value).__name__} with keys: {list(value.keys())}")
                    elif isinstance(value, list):
                        print(f"  📋 {key}: Array with {len(value)} items")
                        if value and isinstance(value[0], dict):
                            print(f"      First item keys: {list(value[0].keys())}")
                    else:
                        print(f"  📝 {key}: {type(value).__name__}")
                        
                # Explore nested objects that might have actions
                nested_objects = [
                    'spi:transfercuritem',
                    'spi:invcost', 
                    'spi:invbalances',
                    'spi:itemcondition'
                ]
                
                for nested_obj in nested_objects:
                    if nested_obj in record:
                        print(f"\n🔍 Exploring nested object: {nested_obj}")
                        explore_nested_object(record[nested_obj], nested_obj)
                        
            else:
                print("❌ No inventory records found")
        else:
            print(f"❌ Failed to get inventory record: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def explore_nested_object(nested_obj, obj_name):
    """Explore a nested object to find available actions."""
    print(f"  📋 {obj_name} structure:")
    
    if isinstance(nested_obj, dict):
        if 'href' in nested_obj:
            href = nested_obj['href']
            print(f"    🔗 HREF: {href}")
            
            # Try to access the nested object directly
            test_nested_object_access(href, obj_name)
        else:
            print(f"    📄 Direct object with keys: {list(nested_obj.keys())}")
            
    elif isinstance(nested_obj, list):
        print(f"    📋 Array with {len(nested_obj)} items")
        if nested_obj and isinstance(nested_obj[0], dict):
            if 'href' in nested_obj[0]:
                print(f"    🔗 First item HREF: {nested_obj[0]['href']}")
                test_nested_object_access(nested_obj[0]['href'], f"{obj_name}[0]")
            else:
                print(f"    📄 First item keys: {list(nested_obj[0].keys())}")

def test_nested_object_access(href, obj_name):
    """Test access to a nested object and look for available actions."""
    print(f"    🧪 Testing access to {obj_name}...")
    
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    try:
        response = requests.get(
            href,
            headers=headers,
            timeout=(3.05, 15)
        )
        
        print(f"      Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"      ✅ Accessible - Structure: {list(data.keys()) if isinstance(data, dict) else 'Array'}")
                
                # Test OPTIONS to see available methods
                options_response = requests.options(
                    href,
                    headers=headers,
                    timeout=(3.05, 10)
                )
                
                if 'Allow' in options_response.headers:
                    print(f"      🔧 Allowed methods: {options_response.headers['Allow']}")
                    
                # Test for common wsmethods on this nested object
                test_wsmethods_on_nested_object(href, obj_name)
                
            except Exception as e:
                print(f"      ⚠️ JSON parse error: {str(e)}")
        else:
            print(f"      ❌ Not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"      ❌ Error: {str(e)}")

def test_wsmethods_on_nested_object(href, obj_name):
    """Test wsmethods on a nested object."""
    print(f"      🔍 Testing wsmethods on {obj_name}...")
    
    # Common inventory-related wsmethods to test on nested objects
    test_methods = []
    
    if 'transfercuritem' in obj_name.lower():
        test_methods = ['create', 'update', 'delete', 'transfer', 'execute']
    elif 'invcost' in obj_name.lower():
        test_methods = ['create', 'update', 'delete', 'calculate']
    elif 'invbalances' in obj_name.lower():
        test_methods = ['create', 'update', 'delete', 'adjust', 'addchange']
    elif 'itemcondition' in obj_name.lower():
        test_methods = ['create', 'update', 'delete']
    else:
        test_methods = ['create', 'update', 'delete']
        
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    for method in test_methods:
        test_url = f"{href}?action=wsmethod:{method}"
        
        try:
            response = requests.post(
                test_url,
                json={"test": "discovery"},
                headers=headers,
                timeout=(3.05, 10)
            )
            
            if response.status_code != 400 or 'not found' not in response.text.lower():
                print(f"        ✅ {method}: Status {response.status_code}")
                if response.content:
                    try:
                        data = response.json()
                        if 'oslc:Error' in data:
                            error_msg = data['oslc:Error'].get('oslc:message', '')
                            if 'not found' not in error_msg.lower():
                                print(f"           Response: {error_msg[:100]}...")
                    except:
                        pass
            else:
                print(f"        ❌ {method}: Not found")
                
        except Exception as e:
            print(f"        ⚠️ {method}: Error - {str(e)[:50]}")

def test_direct_inventory_operations():
    """Test direct operations on the main MXAPIINVENTORY endpoint."""
    print("\n🔍 Testing Direct MXAPIINVENTORY Operations")
    print("=" * 80)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test 1: Standard REST operations without wsmethods
    print("📋 Test 1: Standard REST Operations")
    
    # GET
    print("  🔍 GET operation:")
    try:
        response = requests.get(
            endpoint_url,
            params={"oslc.select": "itemnum,siteid,location,curbaltotal", "oslc.pageSize": "3"},
            headers={"Accept": "application/json", "apikey": API_KEY},
            timeout=(3.05, 15)
        )
        print(f"    Status: {response.status_code} ✅" if response.status_code == 200 else f"    Status: {response.status_code} ❌")
    except Exception as e:
        print(f"    Error: {str(e)}")
        
    # POST (create)
    print("  🔍 POST operation (create):")
    try:
        test_payload = {
            "itemnum": "TEST-DISCOVERY-001",
            "siteid": "LCVKWT",
            "location": "LCVKWT-STORE"
        }
        
        response = requests.post(
            endpoint_url,
            json=test_payload,
            headers=headers,
            timeout=(3.05, 15)
        )
        print(f"    Status: {response.status_code}")
        if response.content:
            try:
                data = response.json()
                if 'oslc:Error' in data:
                    error_msg = data['oslc:Error'].get('oslc:message', '')
                    print(f"    Error: {error_msg[:100]}...")
                else:
                    print(f"    ✅ Success response")
            except:
                print(f"    Response: {response.text[:100]}...")
    except Exception as e:
        print(f"    Error: {str(e)}")
        
    # Test 2: Look for action parameters in OPTIONS
    print("\n📋 Test 2: OPTIONS Method Analysis")
    try:
        response = requests.options(
            endpoint_url,
            headers={"Accept": "application/json", "apikey": API_KEY},
            timeout=(3.05, 15)
        )
        
        print(f"  Status: {response.status_code}")
        print(f"  Headers: {dict(response.headers)}")
        
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {json.dumps(data, indent=2)}")
            except:
                print(f"  Response (text): {response.text}")
                
    except Exception as e:
        print(f"  Error: {str(e)}")

def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Actual Methods Discovery")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Explore the complete inventory structure
    explore_inventory_structure()
    
    # Test direct operations
    test_direct_inventory_operations()
    
    print("\n📊 Summary")
    print("=" * 80)
    print("✅ Inventory structure explored")
    print("✅ Nested objects analyzed")
    print("✅ Direct operations tested")
    print("\n💡 Key Findings:")
    print("   • MXAPIINVENTORY doesn't have the expected wsmethods at the main level")
    print("   • Inventory operations likely use nested objects or different patterns")
    print("   • Standard REST operations (GET/POST/PATCH/DELETE) are the primary interface")
    
    print("\n✅ Discovery completed")

if __name__ == "__main__":
    main()
