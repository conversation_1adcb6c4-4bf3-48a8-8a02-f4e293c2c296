#!/usr/bin/env python3
"""
Investigate the PO discrepancy for item 8010-60-V00-0113
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add backend path for imports
sys.path.append('backend/auth')

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError as e:
    print(f"❌ Cannot import MaximoTokenManager: {e}")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
TARGET_ITEM = "8010-60-V00-0113"
TARGET_SITE = "LCVKWT"

def test_po_api_direct():
    """Test the MXAPIPO API directly with different filters."""
    print("🔍 Testing MXAPIPO API Directly")
    print("=" * 60)
    
    api_key = os.environ.get('MAXIMO_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return
    
    api_url = f"{BASE_URL}/api/os/mxapipo"
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    # Test 1: Basic query without status filters
    print("\n📋 Test 1: Basic query (no status filters)")
    where_clause_basic = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}"'
    params_basic = {
        "oslc.select": "ponum,status,status_description,siteid,vendor,description,orderdate,poline",
        "oslc.where": where_clause_basic,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    print(f"Where clause: {where_clause_basic}")
    
    try:
        response = requests.get(api_url, params=params_basic, headers=headers, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            po_records = data.get('member', [])
            print(f"Total POs found: {len(po_records)}")
            
            # Analyze statuses
            status_counts = {}
            for po in po_records:
                status = po.get('status', 'UNKNOWN')
                status_counts[status] = status_counts.get(status, 0) + 1
                
            print("Status breakdown:")
            for status, count in sorted(status_counts.items()):
                print(f"  {status}: {count}")
                
            # Show first few POs
            print("\nFirst 5 POs:")
            for i, po in enumerate(po_records[:5], 1):
                print(f"  {i}. PO: {po.get('ponum')} - Status: {po.get('status')} - Site: {po.get('siteid')}")
                
        else:
            print(f"❌ Error: {response.status_code} - {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Query with current app filters
    print("\n📋 Test 2: Current app filters (excludes CAN, CLOSE, REVISD)")
    where_clause_filtered = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}" and status!="CAN" and status!="CLOSE" and status!="REVISD"'
    params_filtered = {
        "oslc.select": "ponum,status,status_description,siteid,vendor,description,orderdate,poline",
        "oslc.where": where_clause_filtered,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    print(f"Where clause: {where_clause_filtered}")
    
    try:
        response = requests.get(api_url, params=params_filtered, headers=headers, timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            po_records = data.get('member', [])
            print(f"Filtered POs found: {len(po_records)}")
            
            if po_records:
                print("Filtered POs:")
                for i, po in enumerate(po_records[:10], 1):
                    print(f"  {i}. PO: {po.get('ponum')} - Status: {po.get('status')} - Site: {po.get('siteid')}")
            else:
                print("No POs found after filtering")
                
        else:
            print(f"❌ Error: {response.status_code} - {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

    # Test 3: Try different site values
    print("\n📋 Test 3: Testing different site values")
    
    # Get all unique sites for this item
    where_clause_all_sites = f'poline.itemnum="{TARGET_ITEM}"'
    params_all_sites = {
        "oslc.select": "ponum,status,siteid,poline",
        "oslc.where": where_clause_all_sites,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    try:
        response = requests.get(api_url, params=params_all_sites, headers=headers, timeout=15)
        if response.status_code == 200:
            data = response.json()
            po_records = data.get('member', [])
            
            # Collect unique sites
            sites = set()
            for po in po_records:
                sites.add(po.get('siteid', 'UNKNOWN'))
                
            print(f"Sites found for item {TARGET_ITEM}: {sorted(sites)}")
            
            # Check if our target site is in the list
            if TARGET_SITE in sites:
                print(f"✅ Target site '{TARGET_SITE}' found in PO data")
            else:
                print(f"❌ Target site '{TARGET_SITE}' NOT found in PO data")
                
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_flask_po_processing():
    """Test the Flask app's PO processing logic."""
    print("\n🔍 Testing Flask App PO Processing")
    print("=" * 60)
    
    try:
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/availability/{TARGET_ITEM}",
            params={'siteid': TARGET_SITE},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                po_data = data.get('purchase_orders', [])
                print(f"Flask app returned {len(po_data)} purchase orders")
                
                if po_data:
                    print("PO data from Flask:")
                    for i, po in enumerate(po_data[:5], 1):
                        print(f"  {i}. PO: {po.get('ponum')} - Status: {po.get('status')}")
                else:
                    print("No PO data returned by Flask app")
                    
                # Check the raw response for debugging
                print(f"\nFlask response keys: {list(data.keys())}")
                
            else:
                print(f"Flask error: {data.get('error')}")
        else:
            print(f"HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_poline_structure():
    """Test the poline structure to understand the data format."""
    print("\n🔍 Testing POLINE Structure")
    print("=" * 60)
    
    api_key = os.environ.get('MAXIMO_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return
    
    api_url = f"{BASE_URL}/api/os/mxapipo"
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    # Get one PO with full poline data
    where_clause = f'poline.itemnum="{TARGET_ITEM}"'
    params = {
        "oslc.select": "ponum,status,siteid,poline",
        "oslc.where": where_clause,
        "oslc.pageSize": "1",
        "lean": "1"
    }
    
    try:
        response = requests.get(api_url, params=params, headers=headers, timeout=15)
        if response.status_code == 200:
            data = response.json()
            po_records = data.get('member', [])
            
            if po_records:
                po = po_records[0]
                print(f"Sample PO: {po.get('ponum')}")
                print(f"PO Status: {po.get('status')}")
                print(f"PO Site: {po.get('siteid')}")
                
                poline_data = po.get('poline')
                print(f"POLINE type: {type(poline_data)}")
                
                if isinstance(poline_data, list):
                    print(f"POLINE count: {len(poline_data)}")
                    if poline_data:
                        line = poline_data[0]
                        print(f"Sample POLINE keys: {list(line.keys()) if isinstance(line, dict) else 'Not a dict'}")
                        print(f"Sample POLINE itemnum: {line.get('itemnum') if isinstance(line, dict) else 'N/A'}")
                        print(f"Sample POLINE siteid: {line.get('siteid') if isinstance(line, dict) else 'N/A'}")
                else:
                    print(f"POLINE data: {poline_data}")
                    
            else:
                print("No PO records found")
                
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    print(f"🚀 Investigating PO Discrepancy for Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_po_api_direct()
    test_poline_structure()
    test_flask_po_processing()
    
    print(f"\n{'='*60}")
    print("🏁 Investigation Complete")
    print(f"{'='*60}")
