#!/usr/bin/env python3
"""
Test transfer endpoints by making direct API calls to Flask app
"""

import requests
import json
import time

def test_transfer_via_flask():
    """Test transfer by calling Flask app API directly"""
    
    print("🔍 Testing Transfer via Flask App API")
    print("=" * 50)
    
    # Flask app base URL
    flask_base = "http://127.0.0.1:5010"
    
    # Test data from terminal logs
    transfer_data = {
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "RIP001",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_bin": "28-800-0004",
        "to_bin": "1058-TEMP",
        "from_lot": "TEST",
        "to_lot": "TEST",
        "from_condition": "A1",
        "to_condition": "A1",
        "conversion_factor": 1.0
    }
    
    print(f"Transfer Data: {json.dumps(transfer_data, indent=2)}")
    
    # Test the Flask endpoint
    try:
        print(f"\n🔧 Testing Flask Transfer Endpoint")
        print(f"URL: {flask_base}/api/inventory/transfer-current-item")
        
        response = requests.post(
            f"{flask_base}/api/inventory/transfer-current-item",
            json=transfer_data,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ SUCCESS")
                print(f"Response: {json.dumps(response_data, indent=2)}")
                return True
            except:
                print(f"✅ SUCCESS (non-JSON response)")
                print(f"Response: {response.text}")
                return True
                
        elif response.status_code == 400:
            try:
                error_data = response.json()
                print(f"❌ BAD REQUEST")
                print(f"Error: {json.dumps(error_data, indent=2)}")
                return False
            except:
                print(f"❌ BAD REQUEST (non-JSON error)")
                print(f"Error: {response.text}")
                return False
                
        else:
            print(f"⚠️ OTHER STATUS: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_alternative_endpoints():
    """Test alternative approaches by modifying the service directly"""
    
    print(f"\n🔧 Testing Alternative Endpoint Approaches")
    print("=" * 50)
    
    # We'll test by temporarily modifying the service to use different endpoints
    # and then calling the Flask API
    
    endpoints_to_test = [
        ("MXAPIINVUSE", "/oslc/os/mxapiinvuse"),
        ("MXAPIINVRESERVE", "/oslc/os/mxapiinvreserve"),
        ("MXAPITRANSFER", "/oslc/os/mxapitransfer"),
        ("MXAPIINVTRANS", "/oslc/os/mxapiinvtrans"),
        ("MXAPIINVENTORY_ACTION", "/oslc/os/mxapiinventory?action=TransferCurrentItem")
    ]
    
    for name, endpoint_path in endpoints_to_test:
        print(f"\n🧪 Testing {name}")
        print(f"Endpoint: {endpoint_path}")
        
        # We would need to modify the service file to test each endpoint
        # For now, let's just document what we would test
        print(f"Would test: {name} with endpoint {endpoint_path}")
    
    return False

if __name__ == "__main__":
    print("🚀 Direct Transfer Testing")
    print("=" * 60)
    
    # Test current implementation
    success = test_transfer_via_flask()
    
    if not success:
        print(f"\n🔄 Current implementation failed, testing alternatives...")
        test_alternative_endpoints()
    
    print(f"\n✅ Testing completed!")
