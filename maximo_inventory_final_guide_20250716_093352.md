# Maximo Inventory Service Integration Guide

**Generated:** 2025-07-16T09:33:52.453471  
**Base URL:** https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo  
**API Key:** dj9sia0tu2...r0ahlsn70o

## 📊 Summary

- **Total Methods:** 10
- **SOAP/WSDL Available:** ❌
- **REST API Available:** ✅
- **OSLC Available:** ✅
- **WSMethods Available:** ❌
- **Nested Objects Available:** ✅

## 🔐 Authentication

### API Key Authentication (Recommended)
```python
headers = {
    'Accept': 'application/json',
    'apikey': 'your-api-key-here'
}
```

### Session Authentication
```python
from backend.auth.token_manager import MaximoTokenManager
token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
response = token_manager.session.get(url, params=params)
```

## 🔗 Available Methods

### REST/GET (1 methods)

#### ✅ GET_Inventory_Records

**Description:** Retrieve inventory records with OSLC filtering

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory`

**Method:** `GET`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED WORKING

**Example:**
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory'
headers = {
    "Accept": "application/json",
    "apikey": "your-api-key"
}
params = {
    "oslc.select": "itemnum,siteid,location,curbaltotal,avblbalance",
    "oslc.where": "siteid=\"YOURSITE\" and status!=\"OBSOLETE\"",
    "oslc.pageSize": "50",
    "lean": "1"
}
response = requests.get(url, headers=headers, params=params)
```

### REST/POST (1 methods)

#### ⚠️ POST_Create_Inventory

**Description:** Create new inventory records

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory`

**Method:** `POST`

**Authentication:** API Key, Session

**Status:** ⚠️ REQUIRES TESTING

**Example:**
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory'
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "apikey": "your-api-key"
}
payload = {
    "itemnum": "NEW-ITEM-001",
    "siteid": "YOURSITE",
    "location": "STOREROOM",
    "itemsetid": "ITEMSET"
}
response = requests.post(url, headers=headers, json=payload)
```

### REST/PATCH (1 methods)

#### ⚠️ PATCH_Update_Inventory

**Description:** Update existing inventory records

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}`

**Method:** `PATCH`

**Authentication:** API Key, Session

**Status:** ⚠️ REQUIRES TESTING

**Example:**
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_{base64_inventory_id}'
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "apikey": "your-api-key"
}
payload = {
    "curbaltotal": 100.0,
    "memo": "Updated via API"
}
response = requests.patch(url, headers=headers, json=payload)
```

### Nested Object (4 methods)

#### ✅ GET_Transfer_Current_Item

**Description:** Access transfer current item nested object

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/transfercuritem`

**Method:** `GET`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED ACCESSIBLE

**Example:**
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_{base64_inventory_id}/transfercuritem'
headers = {
    "Accept": "application/json",
    "apikey": "your-api-key"
}
response = requests.get(url, headers=headers)
```

#### ✅ GET_Inventory_Balances

**Description:** Access inventory balances nested object

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/invbalances`

**Method:** `GET`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED ACCESSIBLE

**Example:**
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_{base64_inventory_id}/invbalances'
headers = {
    "Accept": "application/json",
    "apikey": "your-api-key"
}
response = requests.get(url, headers=headers)
```

#### ✅ GET_Inventory_Cost

**Description:** Access inventory cost nested object

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/invcost`

**Method:** `GET`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED ACCESSIBLE

#### ✅ GET_Inventory_Vendor

**Description:** Access inventory vendor nested object

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/invvendor`

**Method:** `GET`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED ACCESSIBLE

### MxLoader Pattern (2 methods)

#### ✅ Current_Balance_Adjustment

**Description:** Adjust current balance using MxLoader pattern

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange`

**Method:** `POST`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED WORKING

**Example:**
```python
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange'
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "x-method-override": "BULK",
    "apikey": "your-api-key"
}
payload = [
    {
        "_action": "AddChange",
        "itemnum": "ITEM-001",
        "siteid": "SITE01",
        "location": "STORE01",
        "invbalances": [
            {
                "curbal": 100.0,
                "conditioncode": "GOOD"
            }
        ]
    }
]
response = requests.post(url, headers=headers, json=payload)
```

#### ✅ Physical_Count_Adjustment

**Description:** Adjust physical count using MxLoader pattern

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange`

**Method:** `POST`

**Authentication:** API Key, Session

**Status:** ✅ CONFIRMED WORKING

**Example:**
```python
payload = [
    {
        "_action": "AddChange",
        "itemnum": "ITEM-001",
        "siteid": "SITE01",
        "location": "STORE01",
        "invbalances": [
            {
                "physcnt": 95.0,
                "conditioncode": "GOOD"
            }
        ]
    }
]
response = requests.post(url, headers=headers, json=payload)
```

### OSLC (1 methods)

#### ✅ OSLC_Query_Inventory

**Description:** Query inventory using OSLC endpoint

**Endpoint:** `https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory`

**Method:** `GET`

**Authentication:** Session

**Status:** ✅ CONFIRMED ACCESSIBLE

## 💡 Integration Best Practices

1. **Use API Key Authentication** for automated integrations
2. **Use Session Authentication** for user-interactive applications
3. **Always include proper error handling** for Maximo API responses
4. **Use OSLC filtering** for efficient data retrieval
5. **Follow MxLoader patterns** for data modifications
6. **Test with small datasets** before production deployment

## 🚀 Quick Start Example

```python
import requests

# Basic inventory query
url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory'
headers = {
    'Accept': 'application/json',
    'apikey': 'your-api-key'
}
params = {
    'oslc.select': 'itemnum,siteid,location,curbaltotal',
    'oslc.where': 'siteid="YOURSITE"',
    'oslc.pageSize': '10'
}

response = requests.get(url, headers=headers, params=params)
if response.status_code == 200:
    data = response.json()
    for item in data.get('member', []):
        print(f"Item: {item.get('itemnum')}, Balance: {item.get('curbaltotal')}")
```

---
*Generated by Maximo Integration Explorer™*
