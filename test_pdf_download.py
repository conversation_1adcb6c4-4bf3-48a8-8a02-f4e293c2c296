#!/usr/bin/env python3
"""
Test script to debug PDF download functionality
"""

import requests
import json

def test_pdf_download():
    """Test downloading PDF files to see what's different from text files"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing PDF download functionality for {wonum}")
    print("=" * 60)
    
    # Get attachments first to find PDF files
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        response = requests.get(attachments_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('attachments'):
                attachments = data['attachments']
                
                # Find PDF files
                pdf_files = []
                for attachment in attachments:
                    filename = attachment.get('filename', '')
                    if filename.lower().endswith('.pdf'):
                        pdf_files.append(attachment)
                
                print(f"📋 Found {len(pdf_files)} PDF files:")
                for i, pdf in enumerate(pdf_files[:5]):  # Show first 5
                    size = pdf.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 'Unknown')
                    print(f"   {i+1}. {pdf.get('filename')} (ID: {pdf.get('docinfoid')}) - Size: {size}")
                
                # Test downloading the first PDF
                if pdf_files:
                    test_pdf = pdf_files[0]
                    docinfoid = test_pdf.get('docinfoid')
                    filename = test_pdf.get('filename')
                    expected_size = test_pdf.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
                    
                    print(f"\n📥 Testing download of PDF: {filename} (ID: {docinfoid})")
                    print(f"   Expected size: {expected_size} bytes")
                    
                    download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
                    
                    try:
                        download_response = requests.get(download_url, timeout=120)  # Longer timeout for PDFs
                        print(f"   📤 Download URL: {download_url}")
                        print(f"   🔄 Status: {download_response.status_code}")
                        print(f"   📊 Content Length: {len(download_response.content)} bytes")
                        print(f"   📋 Content Type: {download_response.headers.get('content-type', 'Unknown')}")
                        
                        if download_response.status_code == 200:
                            content = download_response.content
                            
                            # Check if it's HTML error page
                            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                                print(f"   ❌ Got HTML error page: {content[:100].decode('utf-8', errors='ignore')}")
                            # Check if it's a PDF file
                            elif content.startswith(b'%PDF'):
                                print(f"   ✅ SUCCESS! Got PDF content (starts with %PDF)")
                                print(f"   📄 PDF version: {content[:8].decode('utf-8', errors='ignore')}")
                                
                                # Save the PDF
                                test_filename = f"downloaded_{filename}"
                                with open(test_filename, 'wb') as f:
                                    f.write(content)
                                print(f"   💾 Saved PDF to: {test_filename}")
                                
                                # Compare sizes
                                if expected_size and expected_size > 0:
                                    size_diff = abs(len(content) - expected_size)
                                    if size_diff == 0:
                                        print(f"   🎉 PERFECT! Size matches exactly: {len(content)} bytes")
                                    elif size_diff < 100:
                                        print(f"   ✅ GOOD! Size close to expected: {len(content)} vs {expected_size} bytes (diff: {size_diff})")
                                    else:
                                        print(f"   ⚠️  Size differs significantly: {len(content)} vs {expected_size} bytes (diff: {size_diff})")
                                
                                return True
                            else:
                                print(f"   ❌ Got unexpected content (not PDF): {content[:50]}")
                                # Check if it's small and might be an error message
                                if len(content) < 1000:
                                    try:
                                        text_content = content.decode('utf-8', errors='ignore')
                                        print(f"   📝 Content as text: {text_content[:200]}")
                                    except:
                                        print(f"   📝 Binary content: {content[:100]}")
                        else:
                            print(f"   ❌ Download failed with status {download_response.status_code}")
                            try:
                                error_data = download_response.json()
                                print(f"   📝 Error: {error_data.get('error')}")
                            except:
                                print(f"   📝 Response: {download_response.text[:200]}")
                    
                    except Exception as e:
                        print(f"   ❌ Download exception: {e}")
                    
                    # Also test view functionality for PDF
                    print(f"\n👁️ Testing VIEW of PDF: {filename} (ID: {docinfoid})")
                    
                    view_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/view'
                    
                    try:
                        view_response = requests.get(view_url, timeout=120)
                        print(f"   📤 View URL: {view_url}")
                        print(f"   🔄 Status: {view_response.status_code}")
                        print(f"   📊 Content Length: {len(view_response.content)} bytes")
                        
                        if view_response.status_code == 200:
                            content = view_response.content
                            
                            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                                print(f"   ❌ Got HTML error page")
                            elif content.startswith(b'%PDF'):
                                print(f"   ✅ SUCCESS! Got PDF content for viewing")
                                return True
                            else:
                                print(f"   ❌ Got unexpected content for view")
                        else:
                            print(f"   ❌ View failed with status {view_response.status_code}")
                    
                    except Exception as e:
                        print(f"   ❌ View exception: {e}")
                
                else:
                    print("❌ No PDF files found to test")
            else:
                print(f"❌ Failed to get attachments: {data}")
        else:
            print(f"❌ Failed to get attachments: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Exception getting attachments: {e}")
    
    return False

if __name__ == "__main__":
    print("🧪 Testing PDF Download & View Functionality")
    print("=" * 60)
    
    success = test_pdf_download()
    
    print("\n" + "=" * 60)
    print(f"🎯 PDF Test Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if success:
        print("🎉 PDF download and view are working!")
    else:
        print("⚠️  PDF functionality needs investigation")
        print("\n📋 Next steps:")
        print("1. Check if PDFs are being rejected as 'small files'")
        print("2. Verify PDF content headers and detection")
        print("3. Test different PDF sizes")
        print("4. Check for authentication issues with larger files")
