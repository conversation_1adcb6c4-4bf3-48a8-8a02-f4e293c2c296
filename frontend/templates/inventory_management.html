{% extends "base.html" %}

{% block title %}Inventory Management{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/inventory_management.css') }}">
{% endblock %}

{% block extra_css %}
<!-- Select2 CSS for searchable dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-boxes me-2"></i>Inventory Management
                        </h1>
                        <p class="page-subtitle text-muted">
                            Search and manage inventory items across all locations
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-outline-secondary" onclick="clearInventoryCache()" title="Clear Cache">
                            <i class="fas fa-sync-alt me-1"></i>
                            <span class="d-none d-md-inline">Refresh</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-card">
                <div class="card-body">
                    <form id="inventoryManagementSearchForm">
                        <!-- Compact Search Layout -->
                        <div class="compact-search-layout">
                            <!-- Search Input Row -->
                            <div class="search-input-row">
                                <div class="search-input-group">
                                    <label for="inventorySearchTerm" class="compact-label">
                                        <i class="fas fa-search"></i>
                                        <span>Search Inventory</span>
                                    </label>
                                    <input type="text"
                                           class="form-control search-input-expanded"
                                           id="inventorySearchTerm"
                                           placeholder="Enter item number, description, location, or bin..."
                                           autocomplete="off">
                                    <div class="search-help-text">
                                        Search by item number, description, location, or bin. Supports partial matching.
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary search-btn">
                                    <i class="fas fa-search"></i>
                                    <span class="d-none d-sm-inline ms-1">Search</span>
                                </button>
                            </div>

                            <!-- Filters Grid -->
                            <div class="filters-grid">
                                <div class="filter-group">
                                    <label for="inventorySiteFilter" class="compact-label">
                                        <i class="fas fa-building"></i>
                                        <span>Site</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="inventorySiteFilter" name="site_id" required>
                                        <option value="">Loading...</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label for="inventoryStatusFilter" class="compact-label">
                                        <i class="fas fa-flag"></i>
                                        <span>Status</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="inventoryStatusFilter">
                                        <option value="">All Active</option>
                                        <option value="ACTIVE">Active</option>
                                        <option value="INACTIVE">Inactive</option>
                                        <option value="PENDING">Pending</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label for="inventoryTypeFilter" class="compact-label">
                                        <i class="fas fa-layer-group"></i>
                                        <span>Type</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="inventoryTypeFilter">
                                        <option value="">All Types</option>
                                        <option value="ITEM">Items</option>
                                        <option value="TOOL">Tools</option>
                                        <option value="SERVICE">Services</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label for="inventorySearchLimit" class="compact-label">
                                        <i class="fas fa-list-ol"></i>
                                        <span>Results</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="inventorySearchLimit">
                                        <option value="1">1</option>
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row">
        <div class="col-12">
            <div class="card results-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Inventory Items
                        </h5>
                        <div class="results-info" id="inventoryResultsInfo">
                            <!-- Results info will be displayed here -->
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="inventorySearchResults">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <p class="mb-0">Enter a search term and click "Search" to find inventory items</p>
                            <small class="text-muted">Search by item number or description</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination Section -->
    <div class="row mt-3">
        <div class="col-12">
            <div id="inventoryPaginationContainer" class="d-none">
                <nav aria-label="Inventory pagination">
                    <ul class="pagination justify-content-center" id="inventoryPagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="inventoryLoadingOverlay" class="loading-overlay d-none">
    <div class="loading-content">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Searching inventory...</p>
    </div>
</div>

<!-- Item Details Modal -->
<div class="modal fade" id="inventoryItemModal" tabindex="-1" aria-labelledby="inventoryItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inventoryItemModalLabel">
                    <i class="fas fa-cube me-2"></i>Item Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="inventoryItemModalBody" style="max-height: 70vh; overflow-y: auto;">
                <!-- Comprehensive item details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Close
                </button>
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>Print
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Physical Count Adjustment Modal -->
<div class="modal fade" id="physicalCountModal" tabindex="-1" aria-labelledby="physicalCountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="physicalCountModalLabel">
                    <i class="fas fa-clipboard-check me-2"></i>Physical Count Adjustment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="physicalCountForm">
                    <div class="row g-3">
                        <!-- Read-only fields -->
                        <div class="col-md-6">
                            <label for="pc_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="pc_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="pc_itemsetid" class="form-label">Item Set ID</label>
                            <input type="text" class="form-control" id="pc_itemsetid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="pc_siteid" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="pc_siteid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="pc_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="pc_location" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="pc_binnum" class="form-label">Bin Number</label>
                            <input type="text" class="form-control" id="pc_binnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="pc_conditioncode" class="form-label">Condition Code</label>
                            <input type="text" class="form-control" id="pc_conditioncode" readonly>
                        </div>

                        <!-- Editable field -->
                        <div class="col-md-6">
                            <label for="pc_physcnt" class="form-label">Physical Count <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="pc_physcnt" step="0.01" required>
                            <div class="form-text">Enter the actual physical count for this item</div>
                        </div>

                        <!-- Additional fields -->
                        <div class="col-md-6">
                            <label for="pc_reason_code" class="form-label">Reason Code <span class="text-danger">*</span></label>
                            <select class="form-select" id="pc_reason_code" required>
                                <option value="">Select reason...</option>
                                <option value="CYCLE_COUNT">Cycle Count</option>
                                <option value="PHYSICAL_INVENTORY">Physical Inventory</option>
                                <option value="DISCREPANCY">Discrepancy Found</option>
                                <option value="AUDIT">Audit</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label for="pc_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="pc_notes" rows="3" placeholder="Optional notes about this adjustment..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="window.inventoryManager.submitPhysicalCountAdjustment()">
                    <i class="fas fa-save me-1"></i>Submit Adjustment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Current Balance Adjustment Modal -->
<div class="modal fade" id="currentBalanceModal" tabindex="-1" aria-labelledby="currentBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currentBalanceModalLabel">
                    <i class="fas fa-balance-scale me-2"></i>Current Balance Adjustment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="currentBalanceForm">
                    <div class="row g-3">
                        <!-- Read-only fields -->
                        <div class="col-md-6">
                            <label for="cb_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="cb_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="cb_itemsetid" class="form-label">Item Set ID</label>
                            <input type="text" class="form-control" id="cb_itemsetid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="cb_siteid" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="cb_siteid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="cb_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="cb_location" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="cb_binnum" class="form-label">Bin Number</label>
                            <input type="text" class="form-control" id="cb_binnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="cb_conditioncode" class="form-label">Condition Code</label>
                            <input type="text" class="form-control" id="cb_conditioncode" readonly>
                        </div>

                        <!-- Current balance display -->
                        <div class="col-md-6">
                            <label for="cb_current_balance" class="form-label">Current Balance</label>
                            <input type="text" class="form-control" id="cb_current_balance" readonly>
                            <div class="form-text">Current balance in the system</div>
                        </div>

                        <!-- Editable field -->
                        <div class="col-md-6">
                            <label for="cb_new_balance" class="form-label">New Balance <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="cb_new_balance" step="0.01" required>
                            <div class="form-text">Enter the new balance amount</div>
                        </div>

                        <!-- Additional fields -->
                        <div class="col-md-6">
                            <label for="cb_reason_code" class="form-label">Reason Code <span class="text-danger">*</span></label>
                            <select class="form-select" id="cb_reason_code" required>
                                <option value="">Select reason...</option>
                                <option value="ADJUSTMENT">Manual Adjustment</option>
                                <option value="CORRECTION">Correction</option>
                                <option value="DAMAGE">Damage</option>
                                <option value="LOSS">Loss</option>
                                <option value="FOUND">Found Items</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label for="cb_adjustment_type" class="form-label">Adjustment Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="cb_adjustment_type" required>
                                <option value="">Select type...</option>
                                <option value="ISSUE">Issue (Decrease)</option>
                                <option value="RECEIPT">Receipt (Increase)</option>
                                <option value="TRANSFER">Transfer</option>
                                <option value="ADJUSTMENT">Direct Adjustment</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label for="cb_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="cb_notes" rows="3" placeholder="Optional notes about this adjustment..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-warning" onclick="window.inventoryManager.submitCurrentBalanceAdjustment()">
                    <i class="fas fa-save me-1"></i>Submit Adjustment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- No Balance Physical Count Adjustment Modal -->
<div class="modal fade" id="noBalancePhysicalCountModal" tabindex="-1" aria-labelledby="noBalancePhysicalCountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noBalancePhysicalCountModalLabel">
                    <i class="fas fa-clipboard-check me-2"></i>Create Physical Count Record
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Creating New Inventory Record:</strong> This item has no existing inventory balances.
                    This action will create a new inventory record with the specified physical count.
                </div>
                <form id="noBalancePhysicalCountForm">
                    <div class="row g-3">
                        <!-- Read-only fields -->
                        <div class="col-md-6">
                            <label for="nbpc_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="nbpc_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="nbpc_itemsetid" class="form-label">Item Set ID</label>
                            <input type="text" class="form-control" id="nbpc_itemsetid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="nbpc_siteid" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="nbpc_siteid" readonly>
                        </div>

                        <!-- Location from inventory record (read-only) -->
                        <div class="col-md-6">
                            <label for="nbpc_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="nbpc_location" readonly>
                            <div class="form-text">Storage location from inventory record</div>
                        </div>
                        <div class="col-md-6">
                            <label for="nbpc_binnum" class="form-label">Bin Number</label>
                            <input type="text" class="form-control" id="nbpc_binnum" placeholder="Optional bin number">
                            <div class="form-text">Specific bin within the location (optional)</div>
                        </div>
                        <div class="col-md-6">
                            <label for="nbpc_conditioncode" class="form-label">Condition Code</label>
                            <input type="text" class="form-control" id="nbpc_conditioncode" value="A1" placeholder="A1">
                            <div class="form-text">Item condition code (default: A1)</div>
                        </div>

                        <!-- Physical count field -->
                        <div class="col-md-6">
                            <label for="nbpc_physcnt" class="form-label">Physical Count <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="nbpc_physcnt" step="0.01" required min="0">
                            <div class="form-text">Actual physical count found</div>
                        </div>

                        <!-- Reason and notes -->
                        <div class="col-md-6">
                            <label for="nbpc_reason_code" class="form-label">Reason Code <span class="text-danger">*</span></label>
                            <select class="form-select" id="nbpc_reason_code" required>
                                <option value="">Select reason...</option>
                                <option value="INITIAL_COUNT">Initial Count</option>
                                <option value="NEW_INVENTORY">New Inventory</option>
                                <option value="CYCLE_COUNT">Cycle Count</option>
                                <option value="PHYSICAL_INVENTORY">Physical Inventory</option>
                                <option value="CORRECTION">Correction</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label for="nbpc_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="nbpc_notes" rows="3" placeholder="Optional notes about this new inventory record..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="window.inventoryManager.submitNoBalancePhysicalCount()">
                    <i class="fas fa-save me-1"></i>Create Inventory Record
                </button>
            </div>
        </div>
    </div>
</div>

<!-- No Balance Current Balance Adjustment Modal -->
<div class="modal fade" id="noBalanceCurrentBalanceModal" tabindex="-1" aria-labelledby="noBalanceCurrentBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noBalanceCurrentBalanceModalLabel">
                    <i class="fas fa-balance-scale me-2"></i>Create Current Balance Record
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Creating New Inventory Record:</strong> This item has no existing inventory balances.
                    This action will create a new inventory record with the specified current balance.
                </div>
                <form id="noBalanceCurrentBalanceForm">
                    <div class="row g-3">
                        <!-- Read-only fields -->
                        <div class="col-md-6">
                            <label for="nbcb_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="nbcb_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="nbcb_itemsetid" class="form-label">Item Set ID</label>
                            <input type="text" class="form-control" id="nbcb_itemsetid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="nbcb_siteid" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="nbcb_siteid" readonly>
                        </div>

                        <!-- Location from inventory record (read-only) -->
                        <div class="col-md-6">
                            <label for="nbcb_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="nbcb_location" readonly>
                            <div class="form-text">Storage location from inventory record</div>
                        </div>
                        <div class="col-md-6">
                            <label for="nbcb_binnum" class="form-label">Bin Number</label>
                            <input type="text" class="form-control" id="nbcb_binnum" placeholder="Optional bin number">
                            <div class="form-text">Specific bin within the location (optional)</div>
                        </div>
                        <div class="col-md-6">
                            <label for="nbcb_conditioncode" class="form-label">Condition Code</label>
                            <input type="text" class="form-control" id="nbcb_conditioncode" value="A1" placeholder="A1">
                            <div class="form-text">Item condition code (default: A1)</div>
                        </div>

                        <!-- Current balance field -->
                        <div class="col-md-6">
                            <label for="nbcb_curbal" class="form-label">Current Balance <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="nbcb_curbal" step="0.01" required min="0">
                            <div class="form-text">Current balance amount</div>
                        </div>

                        <!-- Reason and notes -->
                        <div class="col-md-6">
                            <label for="nbcb_reason_code" class="form-label">Reason Code <span class="text-danger">*</span></label>
                            <select class="form-select" id="nbcb_reason_code" required>
                                <option value="">Select reason...</option>
                                <option value="INITIAL_BALANCE">Initial Balance</option>
                                <option value="NEW_INVENTORY">New Inventory</option>
                                <option value="OPENING_BALANCE">Opening Balance</option>
                                <option value="ADJUSTMENT">Adjustment</option>
                                <option value="CORRECTION">Correction</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label for="nbcb_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="nbcb_notes" rows="3" placeholder="Optional notes about this new inventory record..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-warning" onclick="window.inventoryManager.submitNoBalanceCurrentBalance()">
                    <i class="fas fa-save me-1"></i>Create Inventory Record
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Average Cost Adjustment Modal -->
<div class="modal fade" id="avgCostModal" tabindex="-1" aria-labelledby="avgCostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="avgCostModalLabel">
                    <i class="fas fa-dollar-sign me-2"></i>Average Cost Adjustment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Average Cost Adjustment:</strong> This will update the average cost for this inventory item. All other fields are read-only and populated from the current inventory data.
                </div>

                <form id="avgCostForm" novalidate>
                    <div class="row g-3">
                        <!-- Read-only fields populated from inventory data -->
                        <div class="col-md-6">
                            <label for="ac_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="ac_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="ac_itemsetid" class="form-label">Item Set ID</label>
                            <input type="text" class="form-control" id="ac_itemsetid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="ac_siteid" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="ac_siteid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="ac_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="ac_location" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="ac_conditioncode" class="form-label">Condition Code</label>
                            <input type="text" class="form-control" id="ac_conditioncode" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="ac_current_avgcost" class="form-label">Current Average Cost</label>
                            <input type="text" class="form-control" id="ac_current_avgcost" readonly>
                        </div>

                        <!-- Editable field -->
                        <div class="col-12">
                            <label for="ac_new_avgcost" class="form-label">
                                <i class="fas fa-edit me-1"></i>New Average Cost <span class="text-danger">*</span>
                            </label>
                            <input type="number" step="0.01" min="0" class="form-control" id="ac_new_avgcost" required
                                   placeholder="Enter new average cost...">
                            <div class="invalid-feedback">
                                Please enter a valid average cost.
                            </div>
                        </div>

                        <div class="col-12">
                            <label for="ac_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="ac_notes" rows="3" placeholder="Optional notes about this cost adjustment..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-warning" onclick="window.inventoryManager.submitAvgCostAdjustment()">
                    <i class="fas fa-save me-1"></i>Submit Adjustment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Standard Cost Adjustment Modal -->
<div class="modal fade" id="stdCostModal" tabindex="-1" aria-labelledby="stdCostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stdCostModalLabel">
                    <i class="fas fa-chart-line me-2"></i>Standard Cost Adjustment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Standard Cost Adjustment:</strong> This will update the standard cost for this inventory item. All other fields are read-only and populated from the current inventory data.
                </div>

                <form id="stdCostForm" novalidate>
                    <div class="row g-3">
                        <!-- Read-only fields populated from inventory data -->
                        <div class="col-md-6">
                            <label for="sc_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="sc_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="sc_itemsetid" class="form-label">Item Set ID</label>
                            <input type="text" class="form-control" id="sc_itemsetid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="sc_siteid" class="form-label">Site ID</label>
                            <input type="text" class="form-control" id="sc_siteid" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="sc_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="sc_location" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="sc_conditioncode" class="form-label">Condition Code</label>
                            <input type="text" class="form-control" id="sc_conditioncode" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="sc_current_stdcost" class="form-label">Current Standard Cost</label>
                            <input type="text" class="form-control" id="sc_current_stdcost" readonly>
                        </div>

                        <!-- Editable field -->
                        <div class="col-12">
                            <label for="sc_new_stdcost" class="form-label">
                                <i class="fas fa-edit me-1"></i>New Standard Cost <span class="text-danger">*</span>
                            </label>
                            <input type="number" step="0.01" min="0" class="form-control" id="sc_new_stdcost" required
                                   placeholder="Enter new standard cost...">
                            <div class="invalid-feedback">
                                Please enter a valid standard cost.
                            </div>
                        </div>

                        <div class="col-12">
                            <label for="sc_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="sc_notes" rows="3" placeholder="Optional notes about this cost adjustment..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-success" onclick="window.inventoryManager.submitStdCostAdjustment()">
                    <i class="fas fa-save me-1"></i>Submit Adjustment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Current Item Modal -->
<div class="modal fade" id="transferCurrentItemModal" tabindex="-1" aria-labelledby="transferCurrentItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transferCurrentItemModalLabel">
                    <i class="fas fa-exchange-alt me-2"></i>Transfer Current Item
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="transferCurrentItemForm">
                    <div class="row g-3">
                        <!-- Item Information (Read-only) -->
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>Item Information
                            </h6>
                        </div>

                        <div class="col-md-6">
                            <label for="tci_itemnum" class="form-label">Item Number</label>
                            <input type="text" class="form-control" id="tci_itemnum" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="tci_description" class="form-label">Long Description</label>
                            <input type="text" class="form-control" id="tci_description" readonly>
                        </div>

                        <!-- Transfer Details -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-edit me-2"></i>Transfer Details
                            </h6>
                        </div>

                        <div class="col-md-6">
                            <label for="tci_quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="tci_quantity" step="0.01" value="1.00" required>
                            <div class="form-text">Quantity to transfer</div>
                        </div>
                        <div class="col-md-6">
                            <label for="tci_from_storeroom" class="form-label">From Storeroom</label>
                            <input type="text" class="form-control" id="tci_from_storeroom" readonly>
                        </div>

                        <!-- Destination Selection -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>Destination Selection
                            </h6>
                        </div>

                        <div class="col-md-6">
                            <label for="tci_to_site" class="form-label">To Site <span class="text-danger">*</span></label>
                            <select class="form-select" id="tci_to_site" required>
                                <option value="">Select destination site...</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="tci_to_storeroom" class="form-label">To Storeroom <span class="text-danger">*</span></label>
                            <select class="form-select" id="tci_to_storeroom" required>
                                <option value="">Select destination storeroom...</option>
                            </select>
                        </div>

                        <!-- Bin/Lot/Condition Selection -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-boxes me-2"></i>Bin/Lot/Condition Selection
                            </h6>
                        </div>

                        <div class="col-md-4">
                            <label for="tci_from_bin" class="form-label">From Bin</label>
                            <select class="form-select" id="tci_from_bin">
                                <option value="">Select from bin...</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_bin" class="form-label">To Bin</label>
                            <select class="form-select" id="tci_to_bin">
                                <option value="">Select to bin...</option>
                            </select>
                            <div class="form-text">Or enter new bin number</div>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_bin_manual" class="form-label">New Bin Number</label>
                            <input type="text" class="form-control" id="tci_to_bin_manual" placeholder="Enter new bin...">
                        </div>

                        <div class="col-md-4">
                            <label for="tci_from_lot" class="form-label">From Lot</label>
                            <select class="form-select" id="tci_from_lot">
                                <option value="">Select from lot...</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_lot" class="form-label">To Lot</label>
                            <select class="form-select" id="tci_to_lot">
                                <option value="">Select to lot...</option>
                            </select>
                            <div class="form-text">Or enter new lot number</div>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_lot_manual" class="form-label">New Lot Number</label>
                            <input type="text" class="form-control" id="tci_to_lot_manual" placeholder="Enter new lot...">
                        </div>

                        <div class="col-md-4">
                            <label for="tci_from_condition" class="form-label">From Condition Code</label>
                            <select class="form-select" id="tci_from_condition">
                                <option value="">Select from condition...</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_condition" class="form-label">To Condition Code</label>
                            <select class="form-select" id="tci_to_condition">
                                <option value="">Select to condition...</option>
                            </select>
                            <div class="form-text">Or enter new condition code</div>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_condition_manual" class="form-label">New Condition Code</label>
                            <input type="text" class="form-control" id="tci_to_condition_manual" placeholder="Enter new condition...">
                        </div>

                        <!-- Unit Conversion -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-calculator me-2"></i>Unit Conversion
                            </h6>
                        </div>

                        <div class="col-md-4">
                            <label for="tci_from_issue_unit" class="form-label">From Issue Unit</label>
                            <input type="text" class="form-control" id="tci_from_issue_unit" readonly>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_to_issue_unit" class="form-label">To Issue Unit</label>
                            <select class="form-select" id="tci_to_issue_unit">
                                <option value="EA">EA</option>
                                <option value="RO">RO</option>
                                <option value="FT">FT</option>
                                <option value="LB">LB</option>
                                <option value="GAL">GAL</option>
                            </select>
                            <div class="form-text">Unit for destination inventory</div>
                        </div>
                        <div class="col-md-4">
                            <label for="tci_conversion_factor" class="form-label">Conversion Factor</label>
                            <input type="number" class="form-control" id="tci_conversion_factor" step="0.01" value="1" required>
                            <div class="form-text">Multiplier for unit conversion</div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success" onclick="window.inventoryManager.submitSameSiteTransfer()"
                            id="sameSiteTransferBtn" title="Transfer within the same site using source context">
                        <i class="fas fa-building me-1"></i>Same Site Transfer
                    </button>
                    <button type="button" class="btn btn-primary" onclick="window.inventoryManager.submitCrossSiteTransfer()"
                            id="crossSiteTransferBtn" title="Transfer between different sites using destination context">
                        <i class="fas fa-exchange-alt me-1"></i>Cross Site Transfer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Issue Current Item Modal -->
<div class="modal fade" id="issueCurrentItemModal" tabindex="-1" aria-labelledby="issueCurrentItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="issueCurrentItemModalLabel">
                    <i class="fas fa-arrow-right me-2"></i>Issue Current Item
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="issueCurrentItemForm">
                    <!-- Storeroom Section (Collapsible) -->
                    <div class="card mb-3">
                        <div class="card-header" data-bs-toggle="collapse" data-bs-target="#storeroomSection" aria-expanded="true" aria-controls="storeroomSection" style="cursor: pointer;">
                            <h6 class="mb-0">
                                <i class="fas fa-warehouse me-2"></i>Storeroom Information
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="collapse show" id="storeroomSection">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="ici_itemnum" class="form-label">Item Number</label>
                                        <input type="text" class="form-control" id="ici_itemnum" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_description" class="form-label">Item Description</label>
                                        <input type="text" class="form-control" id="ici_description" readonly>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_storeroom" class="form-label">Storeroom</label>
                                        <input type="text" class="form-control" id="ici_storeroom" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_site" class="form-label">Site</label>
                                        <input type="text" class="form-control" id="ici_site" readonly>
                                    </div>

                                    <div class="col-md-4">
                                        <label for="ici_bin" class="form-label">Bin</label>
                                        <select class="form-select" id="ici_bin">
                                            <option value="">Select bin...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ici_rotating" class="form-label">Rotating?</label>
                                        <input type="text" class="form-control" id="ici_rotating" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ici_lot" class="form-label">Lot</label>
                                        <select class="form-select" id="ici_lot">
                                            <option value="">Select lot...</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_expiration_date" class="form-label">Expiration Date</label>
                                        <input type="text" class="form-control" id="ici_expiration_date" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_condition_code" class="form-label">Condition Code</label>
                                        <select class="form-select" id="ici_condition_code">
                                            <option value="">Select condition...</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Error container for inventory balance loading issues -->
                                <div id="inventory-balance-error" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Details Section (Collapsible) -->
                    <div class="card mb-3">
                        <div class="card-header" data-bs-toggle="collapse" data-bs-target="#detailsSection" aria-expanded="true" aria-controls="detailsSection" style="cursor: pointer;">
                            <h6 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Issue Details
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="collapse show" id="detailsSection">
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Basic Issue Information -->
                                    <div class="col-md-4">
                                        <label for="ici_quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="ici_quantity" step="0.01" value="1.00" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ici_transaction_type" class="form-label">Transaction Type</label>
                                        <select class="form-select" id="ici_transaction_type">
                                            <option value="ISSUE" selected>ISSUE</option>
                                            <option value="TRANSFER">TRANSFER</option>
                                            <option value="RETURN">RETURN</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ici_issue_unit" class="form-label">Issue Unit</label>
                                        <select class="form-select" id="ici_issue_unit">
                                            <option value="">Select unit...</option>
                                        </select>
                                    </div>

                                    <!-- Asset and Work Order Information -->
                                    <div class="col-md-6">
                                        <label for="ici_rotating_asset" class="form-label">Rotating Asset</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="ici_rotating_asset" placeholder="Enter asset number...">
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.inventoryManager.openAssetLookup()">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">For rotating items only</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_unit_cost" class="form-label">Unit Cost</label>
                                        <input type="text" class="form-control" id="ici_unit_cost" readonly>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_line_cost" class="form-label">Line Cost</label>
                                        <input type="text" class="form-control" id="ici_line_cost" readonly>
                                        <div class="form-text">Auto-calculated (Quantity × Unit Cost)</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_work_order" class="form-label">Work Order</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ici_work_order">
                                                <option value="">Select work order...</option>
                                            </select>
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.inventoryManager.refreshWorkOrders()">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">Work orders filtered by configured statuses (configurable via admin)</div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_wo_task" class="form-label">WO Task</label>
                                        <select class="form-select" id="ici_wo_task">
                                            <option value="">Select task...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_asset" class="form-label">Asset</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="ici_asset" placeholder="Enter asset number...">
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.inventoryManager.openAssetLookup()">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Requisition and Location Information -->
                                    <div class="col-md-6">
                                        <label for="ici_requisition" class="form-label">Requisition</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ici_requisition">
                                                <option value="">Select requisition...</option>
                                            </select>
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.inventoryManager.refreshRequisitions()">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_requisition_line" class="form-label">Requisition Line</label>
                                        <input type="number" class="form-control" id="ici_requisition_line" placeholder="Enter line number...">
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_location" class="form-label">Location</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ici_location">
                                                <option value="">Select location...</option>
                                            </select>
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.inventoryManager.refreshLocations()">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_gl_debit_account" class="form-label">GL Debit Account</label>
                                        <select class="form-select" id="ici_gl_debit_account">
                                            <option value="">Select debit account...</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_gl_credit_account" class="form-label">GL Credit Account</label>
                                        <select class="form-select" id="ici_gl_credit_account">
                                            <option value="">Select credit account...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_entered_by" class="form-label">Entered By</label>
                                        <input type="text" class="form-control" id="ici_entered_by" readonly>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_actual_date" class="form-label">Actual Date</label>
                                        <input type="datetime-local" class="form-control" id="ici_actual_date">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_issue_to" class="form-label">Issue To</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ici_issue_to">
                                                <option value="">Select person/organization...</option>
                                            </select>
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.inventoryManager.refreshPersons()">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="ici_to_site" class="form-label">To Site</label>
                                        <select class="form-select" id="ici_to_site">
                                            <option value="">Select destination site...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ici_memo" class="form-label">Memo</label>
                                        <textarea class="form-control" id="ici_memo" rows="2" placeholder="Enter notes or comments..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="window.inventoryManager.submitIssueCurrentItem()" id="submitIssueBtn">
                    <i class="fas fa-arrow-right me-1"></i>Submit Issue
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Inventory Management Specific Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.search-card {
    border: none;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.results-card {
    border: none;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.inventory-item-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: var(--card-bg);
    transition: var(--transition);
    cursor: pointer;
}

.inventory-item-card:hover {
    box-shadow: 0 4px 15px var(--shadow-color);
    transform: translateY(-2px);
}

.inventory-item-header {
    display: flex;
    justify-content-between;
    align-items-flex-start;
    margin-bottom: 1rem;
}

.inventory-item-info {
    display: flex;
    align-items-center;
    flex: 1;
}

.inventory-item-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.inventory-item-details h6 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.inventory-item-details p {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.inventory-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.inventory-item-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.inventory-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.inventory-field-label {
    font-weight: 500;
    color: var(--text-color);
    opacity: 0.8;
}

.inventory-field-value {
    font-weight: 600;
    color: var(--text-color);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    color: var(--text-color);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .inventory-item-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .inventory-status-badge {
        margin-top: 0.5rem;
        align-self: flex-start;
    }
    
    .inventory-item-fields {
        grid-template-columns: 1fr;
    }
    
    .header-actions {
        margin-top: 1rem;
    }
}

/* Balance Records Styles */
.balance-records-section {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.balance-records-table {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.balance-records-table .table {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.balance-records-table .table th {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
    font-size: 0.8rem;
    padding: 0.5rem;
    border: none;
    position: relative;
}

.balance-records-table .table th.sortable {
    cursor: pointer;
    user-select: none;
}

.balance-records-table .table th.sortable:hover {
    background: rgba(var(--primary-color-rgb), 0.8);
}

.balance-records-table .table th .sort-icon {
    font-size: 0.7rem;
    margin-left: 0.25rem;
    opacity: 0.6;
}

.balance-records-table .table th.sortable:hover .sort-icon {
    opacity: 1;
}

.balance-records-table .table td {
    padding: 0.5rem;
    vertical-align: middle;
    border-color: var(--border-color);
}

.balance-record-row:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.balance-toggle-btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.balance-toggle-btn:hover {
    transform: none;
}

/* Balance QR Code Button Styles */
.balance-qr-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #28a745;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.balance-qr-btn:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    border-color: #1e7e34;
    background: linear-gradient(45deg, #1e7e34, #17a2b8);
    color: white !important;
}

.balance-qr-btn:hover i {
    animation: qrPulse 0.6s ease-in-out;
    color: white !important;
}

.balance-qr-btn:active {
    transform: scale(1.05) translateY(-1px);
    transition: transform 0.1s ease;
    color: white !important;
}

.balance-qr-btn i {
    font-size: 0.875rem;
    transition: all 0.3s ease;
    color: white !important;
}

/* QR Button Pulse Animation */
@keyframes qrPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Enhanced QR Button for Mobile */
@media (max-width: 768px) {
    .balance-qr-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        min-width: 140px;
        color: white !important;
        background: linear-gradient(45deg, #28a745, #20c997) !important;
        border: 2px solid #28a745 !important;
    }

    .balance-qr-btn:hover {
        transform: scale(1.05) translateY(-1px);
        color: white !important;
        background: linear-gradient(45deg, #1e7e34, #17a2b8) !important;
    }

    .balance-qr-btn i {
        color: white !important;
    }

    .qr-code-btn {
        color: white !important;
        background: linear-gradient(45deg, #6c757d, #5a6268) !important;
        border: 2px solid #6c757d !important;
    }

    .qr-code-btn i {
        color: white !important;
    }
}

/* Loading state for QR buttons */
.balance-qr-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.balance-qr-btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Enhanced Inventory-Level QR Code Button Animations */
.qr-code-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white !important;
    border: 2px solid #6c757d;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.qr-code-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
    background: linear-gradient(45deg, #28a745, #20c997);
    border-color: #28a745;
    color: white !important;
}

.qr-code-btn:hover i {
    animation: qrBounce 0.6s ease-in-out;
    color: white !important;
}

.qr-code-btn:active {
    transform: translateY(-1px) scale(1.02);
    transition: transform 0.1s ease;
    color: white !important;
}

.qr-code-btn i {
    color: white !important;
}

/* QR Button Bounce Animation */
@keyframes qrBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

/* General button hover improvements */
.btn:hover {
    transition: all 0.3s ease;
}

.details-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Mobile Balance Records Styles */
.mobile-balance-records {
    padding: 0.5rem 0;
}

.mobile-balance-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mobile-balance-header h6 {
    color: var(--primary-color);
    font-weight: 600;
}

.mobile-balance-details .mobile-field {
    margin-bottom: 0.5rem;
}

/* Enhanced QR Code Modal Styles */
.enhanced-qr-modal {
    max-width: 100%;
    padding: 1rem;
}

.qr-header-badge {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-block;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.qr-location-info {
    margin-top: 1rem;
}

.qr-location-info .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    margin: 0.25rem;
}

.qr-image-wrapper {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 3px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    display: inline-block;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.qr-image-wrapper:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.qr-code-image {
    max-width: 280px;
    height: auto;
    border-radius: 8px;
}

.balance-details-grid {
    margin-top: 2rem;
}

.detail-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.25rem;
    height: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.detail-card-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.detail-value {
    font-weight: 600;
    color: #495057;
}

.shelf-life-card {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-color: #ffc107;
}

.shelf-life-card .detail-card-title {
    color: #856404;
}

.qr-metadata {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #dee2e6;
}

/* Responsive Design for QR Modal */
@media (max-width: 768px) {
    .enhanced-qr-modal {
        padding: 0.5rem;
    }

    .qr-header-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }

    .qr-location-info .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
        display: block;
        margin: 0.25rem 0;
        text-align: center;
    }

    .qr-code-image {
        max-width: 220px;
    }

    .detail-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .detail-label {
        margin-bottom: 0.25rem;
    }
}

/* Download Button Styling */
#downloadQRBtn {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: 2px solid #28a745;
    color: white !important;
    font-weight: 600;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

#downloadQRBtn:hover {
    background: linear-gradient(45deg, #1e7e34, #17a2b8);
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: white !important;
}

#downloadQRBtn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

#downloadQRBtn i {
    color: white !important;
}

/* Temporary Message Styling */
.alert.position-fixed {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.mobile-field .field-label {
    display: block;
    color: var(--text-muted);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.mobile-field .field-value {
    font-weight: 500;
    color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .balance-records-table .table {
        font-size: 0.75rem;
    }

    .balance-records-table .table th,
    .balance-records-table .table td {
        padding: 0.25rem;
    }

    .balance-records-table .table th {
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .mobile-balance-card {
        padding: 0.75rem;
    }

    .mobile-field .field-label {
        font-size: 0.75rem;
    }

    .mobile-field .field-value {
        font-size: 0.875rem;
    }
}

/* Action Buttons Styling */
.btn-group-vertical .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-group-vertical .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Extra small buttons for table actions */
.btn-xs {
    font-size: 0.65rem !important;
    padding: 0.15rem 0.3rem !important;
    line-height: 1.2 !important;
    border-radius: 0.2rem;
    font-weight: 500;
    white-space: nowrap;
}

.btn-xs:hover {
    transform: translateY(-1px);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.btn-group-vertical .btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-color: #007bff;
}

.btn-group-vertical .btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    border-color: #0056b3;
}

.btn-group-vertical .btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border-color: #ffc107;
    color: #212529;
}

.btn-group-vertical .btn-warning:hover {
    background: linear-gradient(45deg, #e0a800, #d39e00);
    border-color: #e0a800;
    color: #212529;
}

/* Modal Styling */
.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

.text-danger {
    color: #dc3545 !important;
}

/* Mobile adjustments for action buttons */
@media (max-width: 768px) {
    .btn-group-vertical .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        margin-bottom: 0.25rem;
    }

    .btn-group-vertical .btn:last-child {
        margin-bottom: 0;
    }
}

/* Dark theme support */
[data-bs-theme="dark"] .page-header {
    background: linear-gradient(135deg, var(--bs-dark), var(--bs-secondary));
}

[data-bs-theme="dark"] .inventory-item-card {
    background: var(--bs-dark);
    border-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .loading-content {
    background: var(--bs-dark);
    color: var(--bs-light);
}

[data-bs-theme="dark"] .balance-records-section {
    border-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .balance-records-table .table th {
    background: var(--bs-secondary);
}

[data-bs-theme="dark"] .balance-record-row:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .modal-content {
    background: var(--bs-dark);
    color: var(--bs-light);
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background: var(--bs-dark);
    border-color: var(--bs-secondary);
    color: var(--bs-light);
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
    background: var(--bs-dark);
    border-color: var(--primary-color);
    color: var(--bs-light);
}

/* Select2 Custom Styling for Issue Modal */
.select2-container--bootstrap-5 .select2-selection {
    min-height: calc(1.5em + 0.75rem + 2px);
}

.select2-container--bootstrap-5 .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px) !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    line-height: calc(1.5em + 0.75rem);
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: calc(1.5em + 0.75rem);
    right: 0.75rem;
}

/* Ensure Select2 dropdowns appear above modal */
.select2-container {
    z-index: 9999 !important;
}

.select2-dropdown {
    z-index: 9999 !important;
}

/* Force search box to always be visible */
.select2-search {
    display: block !important;
}

.select2-search--dropdown {
    display: block !important;
    padding: 4px;
}

.select2-search__field {
    width: 100% !important;
    padding: 4px 6px !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
}

/* Mobile responsiveness for Select2 */
@media (max-width: 768px) {
    .select2-container--bootstrap-5 .select2-selection--single {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .select2-dropdown {
        font-size: 16px;
    }
}

/* Dark theme support for Select2 */
[data-bs-theme="dark"] .select2-container--bootstrap-5 .select2-selection {
    background-color: var(--bs-dark);
    border-color: var(--bs-secondary);
    color: var(--bs-light);
}

[data-bs-theme="dark"] .select2-container--bootstrap-5 .select2-selection:focus {
    border-color: var(--primary-color);
}

[data-bs-theme="dark"] .select2-dropdown {
    background-color: var(--bs-dark);
    border-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .select2-results__option {
    color: var(--bs-light);
}

[data-bs-theme="dark"] .select2-results__option--highlighted {
    background-color: var(--primary-color);
}

/* Ensure dropdowns are clickable and properly styled */
#issueCurrentItemModal .form-select {
    cursor: pointer;
    pointer-events: auto;
    position: relative;
    z-index: 1;
}

#issueCurrentItemModal .form-select:disabled {
    cursor: not-allowed;
}

/* Enhanced styling for persons dropdown results */
.person-result {
    padding: 0.25rem 0;
}

.person-id {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.person-name {
    color: var(--bs-body-color);
    font-size: 0.85rem;
    margin-top: 0.1rem;
}

.person-meta {
    font-size: 0.75rem;
    color: var(--bs-secondary);
    margin-top: 0.1rem;
}

.person-meta .location {
    background: var(--bs-light);
    padding: 0.1rem 0.3rem;
    border-radius: 0.2rem;
    margin-right: 0.3rem;
}

.person-meta .status {
    background: var(--bs-warning);
    color: var(--bs-dark);
    padding: 0.1rem 0.3rem;
    border-radius: 0.2rem;
    font-weight: 500;
}

/* Ensure dropdown options are visible */
#issueCurrentItemModal .form-select option {
    color: var(--bs-body-color);
    background-color: var(--bs-body-bg);
}

/* Fix any potential z-index issues */
#issueCurrentItemModal .modal-body {
    position: relative;
    z-index: 1;
}

/* Ensure form controls are properly sized */
#issueCurrentItemModal .form-select,
#issueCurrentItemModal .form-control {
    min-height: calc(1.5em + 0.75rem + 2px);
}

/* Mobile-friendly Select2 styling for inventory balance dropdowns */
.select2-container-mobile-friendly {
    width: 100% !important;
}

.select2-dropdown-mobile-friendly {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.select2-container-mobile-friendly .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
}

.select2-container-mobile-friendly .select2-selection--single .select2-selection__rendered {
    color: #495057;
    line-height: 1.5;
    padding-left: 0;
    padding-right: 20px;
}

.select2-container-mobile-friendly .select2-selection--single .select2-selection__arrow {
    height: calc(1.5em + 0.75rem);
    right: 0.75rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .select2-dropdown-mobile-friendly {
        font-size: 16px; /* Prevents zoom on iOS */
        max-height: 200px;
    }

    .select2-container-mobile-friendly .select2-selection--single {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 44px; /* Touch-friendly minimum height */
    }

    .select2-results__option {
        padding: 12px 16px; /* Larger touch targets */
        font-size: 16px;
    }
}
</style>

<!-- Include the inventory management JavaScript -->
<script src="{{ url_for('static', filename='js/inventory_management.js') }}?v={{ range(1000, 9999) | random }}"></script>

{% endblock %}

{% block extra_js %}
<!-- Select2 JavaScript for searchable dropdowns -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// Simple and robust Issue Current Item Modal functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 ISSUE MODAL: Setting up modal functionality');

    // Wait for inventory manager to be ready
    function waitForInventoryManager() {
        if (!window.inventoryManager) {
            setTimeout(waitForInventoryManager, 100);
            return;
        }
        console.log('✅ ISSUE MODAL: Inventory manager ready');
    }

    waitForInventoryManager();
});

// Function to ensure dropdowns are working properly
function enhanceIssueDropdowns() {
    console.log('🔧 ISSUE MODAL: Ensuring dropdowns are working properly');

    // First, ensure all basic dropdowns are working
    ensureBasicDropdownsWork();

    // Then, optionally enhance with Select2 if available
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        try {
            // Only enhance larger dropdowns that benefit from search
            const dropdownsToEnhance = [
                '#ici_work_order', '#ici_location', '#ici_issue_to', '#ici_requisition'
            ];

            dropdownsToEnhance.forEach(selector => {
                const element = $(selector);
                if (element.length && element.find('option').length > 1 && !element.hasClass('select2-hidden-accessible')) {
                    element.select2({
                        theme: 'bootstrap-5',
                        width: '100%',
                        allowClear: true,
                        dropdownParent: $('#issueCurrentItemModal'),
                        placeholder: 'Type to search...'
                    });
                }
            });

            console.log('✅ ISSUE MODAL: Select2 enhancement applied to larger dropdowns');
        } catch (error) {
            console.warn('⚠️ ISSUE MODAL: Select2 enhancement failed, using basic dropdowns:', error);
        }
    }
}

// Function to ensure basic dropdowns are working
function ensureBasicDropdownsWork() {
    console.log('🔧 ISSUE MODAL: Ensuring basic dropdowns work');

    // List of critical dropdowns that must work
    const criticalDropdowns = [
        'ici_bin', 'ici_lot', 'ici_condition_code',
        'ici_transaction_type', 'ici_issue_unit'
    ];

    criticalDropdowns.forEach(id => {
        const dropdown = document.getElementById(id);
        if (dropdown) {
            // Ensure it's not disabled
            dropdown.disabled = false;

            // Ensure it has the correct styling
            dropdown.classList.add('form-select');

            // Ensure it has a change event listener
            dropdown.addEventListener('change', function() {
                console.log(`✅ DROPDOWN CHANGED: ${id} = ${this.value}`);
            });

            // If it has no options, show appropriate message - no hardcoded fallbacks
            if (dropdown.options.length <= 1) {
                console.log(`⚠️ ISSUE MODAL: Dropdown ${id} has no options, showing empty state`);

                // Show appropriate empty state based on dropdown type
                if (id === 'ici_bin') {
                    dropdown.innerHTML = '<option value="">No bins available</option>';
                    dropdown.disabled = true;
                } else if (id === 'ici_lot') {
                    dropdown.innerHTML = '<option value="">No lots available</option>';
                    dropdown.disabled = true;
                } else if (id === 'ici_condition_code') {
                    dropdown.innerHTML = '<option value="">No conditions available</option>';
                    dropdown.disabled = true;
                } else if (id === 'ici_transaction_type') {
                    // Transaction type can have default values as these are system-defined
                    dropdown.innerHTML = '<option value="">Select type...</option>';
                    dropdown.innerHTML += '<option value="ISSUE" selected>ISSUE</option>';
                    dropdown.innerHTML += '<option value="TRANSFER">TRANSFER</option>';
                } else if (id === 'ici_issue_unit') {
                    // Issue units should be loaded from API, but EA is a reasonable default
                    dropdown.innerHTML = '<option value="">Select unit...</option>';
                    dropdown.innerHTML += '<option value="EA" selected>Each (EA)</option>';
                }
            }

            console.log(`✅ ISSUE MODAL: Dropdown ${id} is ready with ${dropdown.options.length} options`);
        } else {
            console.error(`❌ ISSUE MODAL: Critical dropdown ${id} not found`);
        }
    });
}

// Simple cleanup function
function cleanupEnhancedDropdowns() {
    console.log('🔧 ISSUE MODAL: Cleaning up enhanced dropdowns');

    if (typeof $ !== 'undefined') {
        try {
            $('#issueCurrentItemModal select.select2-hidden-accessible').each(function() {
                try {
                    $(this).select2('destroy');
                } catch (error) {
                    // Ignore cleanup errors
                }
            });
        } catch (error) {
            // Ignore cleanup errors
        }
    }
}

// Make functions globally available
window.enhanceIssueDropdowns = enhanceIssueDropdowns;
window.cleanupEnhancedDropdowns = cleanupEnhancedDropdowns;
</script>
{% endblock %}
