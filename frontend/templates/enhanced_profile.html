{% extends 'base.html' %}

{% block title %}Enhanced User Profile - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
    .profile-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .profile-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
        background-size: 20px 20px;
        animation: moveStripes 20s linear infinite;
    }

    @keyframes moveStripes {
        0% { background-position: 0 0; }
        100% { background-position: 40px 40px; }
    }

    .profile-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 300;
        position: relative;
        z-index: 1;
    }

    .profile-header .subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        margin-top: 10px;
        position: relative;
        z-index: 1;
    }

    /* Enhanced Tab Navigation */
    .nav-tabs-enhanced {
        border: none;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 8px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-tabs-enhanced .nav-link {
        border: none;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin: 0 4px;
        color: #6c757d;
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        background: transparent;
    }

    .nav-tabs-enhanced .nav-link:hover {
        background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
        color: #495057;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .nav-tabs-enhanced .nav-link.active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        transform: translateY(-1px);
    }

    .nav-tabs-enhanced .nav-link i {
        font-size: 1.2rem;
        margin-right: 0.75rem;
        transition: transform 0.3s ease;
    }

    .nav-tabs-enhanced .nav-link:hover i {
        transform: scale(1.1);
    }

    .nav-tabs-enhanced .nav-link.active i {
        transform: scale(1.15);
    }

    /* Enhanced Tab Content */
    .tab-content-enhanced {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tab-pane-enhanced {
        padding: 2rem;
        min-height: 400px;
    }

    /* Enhanced Detail Cards */
    .detail-card-enhanced {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--box-shadow);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .detail-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .detail-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #28a745;
    }

    .detail-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .detail-card-header-enhanced {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .detail-card-icon {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
        transition: all 0.3s ease;
    }

    .detail-card-enhanced:hover .detail-card-icon {
        transform: rotate(10deg) scale(1.1);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .detail-card-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #495057;
        margin: 0;
    }

    /* Enhanced Detail Rows */
    .detail-row-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
    }

    .detail-row-enhanced:hover {
        background: #f8f9fa;
        padding-left: 1rem;
        padding-right: 1rem;
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 8px;
    }

    .detail-row-enhanced:last-child {
        border-bottom: none;
    }

    .detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        min-width: 200px;
        margin-right: 1rem;
    }

    .detail-label-enhanced i {
        font-size: 1.3rem;
        margin-right: 0.75rem;
        color: #28a745;
        width: 20px;
        text-align: center;
    }

    .detail-value-enhanced {
        font-size: 1rem;
        color: #212529;
        font-weight: 500;
        flex: 1;
        text-align: right;
        word-wrap: break-word;
    }

    .detail-value-enhanced.empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Performance Metrics Cards */
    .metric-card-enhanced {
        background: var(--card-bg);
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #28a745, #ffc107);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .metric-card-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .metric-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .metric-icon {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        margin: 0 auto 1rem;
        transition: all 0.3s ease;
    }

    .metric-card-enhanced:hover .metric-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 0.5rem;
    }

    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Site Access Enhanced Cards */
    .site-access-card-enhanced {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--box-shadow);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    /* Enhanced loading states */
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        padding: 2rem;
    }

    .loading-container .spinner-border {
        width: 3rem;
        height: 3rem;
        border-width: 0.3em;
    }

    /* Touch-friendly buttons */
    .btn-sm {
        min-height: 44px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    /* Enhanced badges */
    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
        border-radius: 6px;
    }

    .badge i {
        margin-right: 0.25rem;
    }

    /* Card hover effects */
    .detail-card-enhanced:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    /* Tab content spacing */
    .task-sub-content {
        padding: 1.5rem 0;
        min-height: 400px;
    }

    .site-access-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #17a2b8, #20c997, #28a745);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .site-access-card-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #17a2b8;
    }

    .site-access-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    /* Dark Mode Specific Overrides */
    [data-bs-theme="dark"] .profile-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--header-text-color);
    }

    [data-bs-theme="dark"] .nav-tabs-enhanced {
        background: var(--card-bg);
    }

    [data-bs-theme="dark"] .nav-tabs-enhanced .nav-link {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .nav-tabs-enhanced .nav-link:hover {
        background: rgba(var(--primary-color-rgb), 0.1);
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .nav-tabs-enhanced .nav-link.active {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--header-text-color);
    }

    [data-bs-theme="dark"] .detail-row-enhanced:hover {
        background: rgba(var(--primary-color-rgb), 0.05);
    }

    [data-bs-theme="dark"] .detail-label-enhanced {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .detail-value-enhanced {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .detail-value-enhanced.empty {
        color: rgba(var(--text-color-rgb), 0.6);
    }

    [data-bs-theme="dark"] .metric-value {
        color: var(--primary-color) !important;
    }

    [data-bs-theme="dark"] .metric-label {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .bg-light {
        background-color: rgba(var(--primary-color-rgb), 0.1) !important;
        color: var(--text-color) !important;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .profile-detail-container {
            padding: 15px;
        }

        .profile-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .profile-header h1 {
            font-size: 1.8rem;
        }

        .nav-tabs-enhanced {
            padding: 6px;
            margin-bottom: 1rem;
        }

        .nav-tabs-enhanced .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            margin: 0 2px;
        }

        .nav-tabs-enhanced .nav-link i {
            font-size: 1.1rem;
            margin-right: 0.5rem;
        }

        .tab-pane-enhanced {
            padding: 1.5rem;
            min-height: 300px;
        }

        .detail-row-enhanced {
            flex-direction: column;
            align-items: flex-start;
            padding: 1.25rem 0;
        }

        .detail-label-enhanced {
            font-size: 1.2rem;
            min-width: auto;
            margin-right: 0;
            margin-bottom: 0.5rem;
        }

        .detail-label-enhanced i {
            font-size: 1.4rem;
            margin-right: 0.75rem;
        }

        .detail-value-enhanced {
            font-size: 1.1rem;
            text-align: left;
            padding-left: 2.5rem;
        }

        .detail-card-header-enhanced {
            flex-direction: column;
            text-align: center;
        }

        .detail-card-icon {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .metric-card-enhanced {
            margin-bottom: 1.5rem;
        }

        .metric-icon {
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
        }

        .metric-value {
            font-size: 1.8rem;
        }

        /* Mobile scroll optimization */
        .tab-content-enhanced {
            max-height: 70vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .site-access-card-enhanced {
            margin-bottom: 2rem;
        }

        /* Mobile-specific site access styling */
        .task-sub-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-height: 60px;
        }

        .task-sub-tabs .nav-link i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
            margin-right: 0 !important;
        }

        .task-sub-tabs .nav-link.active {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-color: #28a745;
        }

        /* Enhanced mobile cards for site access data */
        .detail-card-enhanced {
            margin-bottom: 1rem;
            padding: 1rem;
        }

        .detail-card-icon {
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .detail-row-enhanced {
            padding: 0.75rem 0;
        }

        .detail-label-enhanced {
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .detail-value-enhanced {
            font-size: 1rem;
            padding-left: 0;
        }

        /* Loading and error states mobile optimization */
        .spinner-border {
            width: 2rem;
            height: 2rem;
        }

        /* Badge styling for mobile */
        .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }

        /* Summary cards for mobile */
        .bg-light {
            background-color: rgba(var(--primary-color-rgb), 0.05) !important;
            border: 1px solid rgba(var(--primary-color-rgb), 0.1);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-detail-container">
    <!-- Enhanced Profile Header -->
    <div class="profile-header">
        <h1>
            <i class="fas fa-rocket me-3"></i>Enhanced User Profile
        </h1>
        <div class="subtitle">
            <div class="badge bg-warning text-dark me-2 mb-2">
                <i class="fas fa-flask me-1"></i>Test (UAT) Environment
            </div>
            <div class="badge bg-light text-dark mb-2">
                <i class="fas fa-tachometer-alt me-1"></i>Optimized Performance
            </div>
        </div>
    </div>

    <!-- Enhanced Tab Navigation -->
    <ul class="nav nav-tabs nav-tabs-enhanced" id="profileTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                <i class="fas fa-tachometer-alt"></i>Performance Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="profile-info-tab" data-bs-toggle="tab" data-bs-target="#profile-info" type="button" role="tab" aria-controls="profile-info" aria-selected="false">
                <i class="fas fa-user-circle"></i>Profile Information
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="site-access-tab" data-bs-toggle="tab" data-bs-target="#site-access" type="button" role="tab" aria-controls="site-access" aria-selected="false">
                <i class="fas fa-key"></i>Site Access
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">
                <i class="fas fa-cogs"></i>Settings
            </button>
        </li>
    </ul>

    <!-- Enhanced Tab Content -->
    <div class="tab-content tab-content-enhanced" id="profileTabContent">
        <!-- Performance Overview Tab -->
        <div class="tab-pane fade show active tab-pane-enhanced" id="overview" role="tabpanel" aria-labelledby="overview-tab">
            <div class="row">
                <div class="col-md-3 col-6 mb-4">
                    <div class="metric-card-enhanced">
                        <div class="metric-icon">
                            <i class="fas fa-stopwatch"></i>
                        </div>
                        <div class="metric-value">{{ "%.3f"|format(page_load_time) }}s</div>
                        <div class="metric-label">Page Load Time</div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="metric-card-enhanced">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-value" style="color: #28a745;">{{ "%.1f"|format(performance_stats.cache_hit_rate) }}%</div>
                        <div class="metric-label">Cache Hit Rate</div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="metric-card-enhanced">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-value" style="color: #17a2b8;">{{ "%.3f"|format(performance_stats.average_response_time) }}s</div>
                        <div class="metric-label">Avg Response Time</div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="metric-card-enhanced">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="metric-value" style="color: #ffc107;">{{ performance_stats.total_requests }}</div>
                        <div class="metric-label">Total Requests</div>
                    </div>
                </div>
            </div>

            <!-- Performance Features -->
            <div class="detail-card-enhanced">
                <div class="detail-card-header-enhanced">
                    <div class="detail-card-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h5 class="detail-card-title">Performance Features</h5>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="detail-row-enhanced">
                            <div class="detail-label-enhanced">
                                <i class="fas fa-memory"></i>Smart Caching
                            </div>
                        </div>
                        <ul class="list-unstyled ms-4">
                            <li><i class="fas fa-check text-success me-2"></i>Redis integration</li>
                            <li><i class="fas fa-check text-success me-2"></i>Intelligent TTL</li>
                            <li><i class="fas fa-check text-success me-2"></i>Auto-invalidation</li>
                        </ul>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="detail-row-enhanced">
                            <div class="detail-label-enhanced">
                                <i class="fas fa-database"></i>Smart API Usage
                            </div>
                        </div>
                        <ul class="list-unstyled ms-4">
                            <li><i class="fas fa-check text-success me-2"></i>Single API call strategy</li>
                            <li><i class="fas fa-check text-success me-2"></i>Optimized timeouts</li>
                            <li><i class="fas fa-check text-success me-2"></i>Intelligent fallbacks</li>
                        </ul>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="detail-row-enhanced">
                            <div class="detail-label-enhanced">
                                <i class="fas fa-bolt"></i>Speed Optimization
                            </div>
                        </div>
                        <ul class="list-unstyled ms-4">
                            <li><i class="fas fa-check text-success me-2"></i>60-80% faster loading</li>
                            <li><i class="fas fa-check text-success me-2"></i>Reduced API calls</li>
                            <li><i class="fas fa-check text-success me-2"></i>Enhanced user experience</li>
                        </ul>
                    </div>
                </div>

                <!-- Comparison Banner -->
                <div class="alert alert-info border-0 mt-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="alert-heading mb-2">
                                <i class="fas fa-info-circle me-2"></i>Performance Comparison
                            </h6>
                            <p class="mb-0">
                                This enhanced profile service demonstrates optimized caching and reduced API calls.
                                Compare with the <a href="{{ url_for('profile') }}" class="alert-link">original profile page</a>
                                to see the performance difference.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('profile') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-user me-1"></i>Original Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Information Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="profile-info" role="tabpanel" aria-labelledby="profile-info-tab">
            <div class="detail-card-enhanced">
                <div class="detail-card-header-enhanced">
                    <div class="detail-card-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h5 class="detail-card-title">Basic Information</h5>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user"></i>First Name
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.firstName }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user"></i>Last Name
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.lastName }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-signature"></i>Display Name
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.displayName }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-hashtag"></i>Person ID
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.personid }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-at"></i>Username
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.userName }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-key"></i>Login ID
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.loginID }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user-tag"></i>Login Username
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.loginUserName }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-envelope"></i>Email
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.email }}</div>
                </div>
            </div>

            <!-- Location & Contact Information -->
            <div class="detail-card-enhanced">
                <div class="detail-card-header-enhanced">
                    <div class="detail-card-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h5 class="detail-card-title">Location & Contact</h5>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-globe"></i>Country
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.country }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-map"></i>State/Province
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.stateprovince }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-phone"></i>Phone
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.phone }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-phone-alt"></i>Primary Phone
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.primaryPhone }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-clock"></i>Time Zone
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.timezone }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-server"></i>System Time Zone
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.systimezone }}</div>
                </div>
            </div>
        </div>

        <!-- Site Access Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="site-access" role="tabpanel" aria-labelledby="site-access-tab">
            <div class="site-access-card-enhanced">
                <div class="detail-card-header-enhanced">
                    <div class="detail-card-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                        <i class="fas fa-key"></i>
                    </div>
                    <h5 class="detail-card-title">Site Access Information</h5>
                </div>

                <!-- Site Access Tabs -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <ul class="nav nav-tabs task-sub-tabs flex-grow-1" id="siteAccessTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="person-tab" data-bs-toggle="tab" data-bs-target="#person" type="button" role="tab">
                                <i class="fas fa-user me-1"></i>Person
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="maxuser-tab" data-bs-toggle="tab" data-bs-target="#maxuser" type="button" role="tab">
                                <i class="fas fa-user-cog me-1"></i>User Account
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="groups-tab" data-bs-toggle="tab" data-bs-target="#groups" type="button" role="tab">
                                <i class="fas fa-users me-1"></i>Groups
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sites-tab" data-bs-toggle="tab" data-bs-target="#sites" type="button" role="tab">
                                <i class="fas fa-building me-1"></i>Sites
                            </button>
                        </li>
                    </ul>
                    <button class="btn btn-outline-primary btn-sm ms-2" id="refreshSiteAccess" title="Refresh current tab data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>

                <!-- Site Access Tab Content -->
                <div class="tab-content" id="siteAccessTabContent">
                    <div class="tab-pane fade show active task-sub-content" id="person" role="tabpanel">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-user fa-2x mb-2"></i>
                            <p>Person data will be loaded here</p>
                        </div>
                    </div>
                    <div class="tab-pane fade task-sub-content" id="maxuser" role="tabpanel">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-user-cog fa-2x mb-2"></i>
                            <p>User account data will be loaded here</p>
                        </div>
                    </div>
                    <div class="tab-pane fade task-sub-content" id="groups" role="tabpanel">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <p>Group membership data will be loaded here</p>
                        </div>
                    </div>
                    <div class="tab-pane fade task-sub-content" id="sites" role="tabpanel">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-building fa-2x mb-2"></i>
                            <p>Site access data will be loaded here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="settings" role="tabpanel" aria-labelledby="settings-tab">
            <div class="detail-card-enhanced">
                <div class="detail-card-header-enhanced">
                    <div class="detail-card-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h5 class="detail-card-title">System Settings</h5>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-language"></i>Base Language
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.baseLang }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-dollar-sign"></i>Base Currency
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.baseCurrency }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-calendar"></i>Base Calendar
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.baseCalendar }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-calendar-alt"></i>Date Format
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.dateformat }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-toggle-on"></i>Can Use Inactive Sites
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.canUseInactiveSites }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-warehouse"></i>Default Storeroom
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.defaultStoreroom }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-tools"></i>Default Repair Site
                    </div>
                    <div class="detail-value-enhanced {{ 'empty' if not user_profile.defaultRepairSite }}">
                        {{ user_profile.defaultRepairSite or 'None' }}
                    </div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-industry"></i>Default Repair Facility
                    </div>
                    <div class="detail-value-enhanced {{ 'empty' if not user_profile.defaultRepairFacility }}">
                        {{ user_profile.defaultRepairFacility or 'None' }}
                    </div>
                </div>
            </div>

            <!-- Organization & Site Settings -->
            <div class="detail-card-enhanced">
                <div class="detail-card-header-enhanced">
                    <div class="detail-card-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                        <i class="fas fa-building"></i>
                    </div>
                    <h5 class="detail-card-title">Organization & Site Settings</h5>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-sitemap"></i>Default Organization
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.defaultOrg }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-map-marker-alt"></i>Default Site Description
                    </div>
                    <div class="detail-value-enhanced">{{ user_profile.defaultSiteDescription }}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-warehouse"></i>Default Storeroom Site
                    </div>
                    <div class="detail-value-enhanced {{ 'empty' if not user_profile.defaultStoreroomSite }}">
                        {{ user_profile.defaultStoreroomSite or 'None' }}
                    </div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-plus-circle"></i>Insert Site
                    </div>
                    <div class="detail-value-enhanced">
                        {{ user_profile.insertSite }}
                        {% if available_sites %}
                            <small class="text-muted d-block">
                                ({{ available_sites|length }} sites available)
                            </small>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

    <!-- Navigation Buttons -->
    <div class="text-center mt-4 mb-5">
        <a href="{{ url_for('welcome') }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-arrow-left me-2"></i>Back to Welcome
        </a>
        <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-user me-2"></i>Original Profile
        </a>
        <button id="refreshProfile" class="btn btn-outline-success me-2">
            <i class="fas fa-sync-alt me-2"></i>Refresh Profile
        </button>
        <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
            <i class="fas fa-sign-out-alt me-2"></i>Logout
        </a>
    </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Enhanced Profile: DOM loaded, initializing site access data...');

    // Initialize site access data loading
    loadSiteAccessData();

    // Refresh profile button functionality
    document.getElementById('refreshProfile').addEventListener('click', function() {
        window.location.reload();
    });

    console.log('✅ Enhanced Profile: Initialization complete');
});

// Function to load site access data
function loadSiteAccessData() {
    const personId = '{{ user_profile.personid }}';
    console.log('🔍 Site Access: Using personId:', personId);

    // Track current active tab
    let currentActiveTab = 'person';

    // Load data when tabs are clicked
    document.getElementById('person-tab').addEventListener('click', function() {
        currentActiveTab = 'person';
        loadPersonData(personId);
    });

    document.getElementById('maxuser-tab').addEventListener('click', function() {
        currentActiveTab = 'maxuser';
        loadMaxUserData(personId);
    });

    document.getElementById('groups-tab').addEventListener('click', function() {
        currentActiveTab = 'groups';
        loadGroupsData(personId);
    });

    document.getElementById('sites-tab').addEventListener('click', function() {
        currentActiveTab = 'sites';
        loadSitesData(personId);
    });

    // Refresh button functionality
    document.getElementById('refreshSiteAccess').addEventListener('click', function() {
        const btn = this;
        const icon = btn.querySelector('i');

        // Add spinning animation
        icon.classList.add('fa-spin');
        btn.disabled = true;

        // Refresh current tab data
        switch(currentActiveTab) {
            case 'person':
                loadPersonData(personId);
                break;
            case 'maxuser':
                loadMaxUserData(personId);
                break;
            case 'groups':
                loadGroupsData(personId);
                break;
            case 'sites':
                loadSitesData(personId);
                break;
        }

        // Remove spinning animation after 2 seconds
        setTimeout(() => {
            icon.classList.remove('fa-spin');
            btn.disabled = false;
        }, 2000);
    });

    // Load person data by default
    loadPersonData(personId);
}

// Site Access Data Loading Functions
async function loadPersonData(personId) {
    console.log('🔍 Loading person data for:', personId);
    const container = document.getElementById('person');

    // Show loading state
    showLoadingState(container, 'fas fa-user', 'Loading person data...');

    try {
        const response = await fetch(`/api/site-access/${personId}/person`);
        const result = await response.json();

        if (result.success && result.data) {
            displayPersonData(container, result.data);
            showToast('Person data loaded successfully', 'success');
        } else {
            showErrorState(container, 'fas fa-user', result.error || 'No person data found');
            showToast(result.error || 'Failed to load person data', 'error');
        }
    } catch (error) {
        console.error('Error loading person data:', error);
        showErrorState(container, 'fas fa-user', 'Failed to load person data');
        showToast('Network error while loading person data', 'error');
    }
}

async function loadMaxUserData(personId) {
    console.log('🔍 Loading maxuser data for:', personId);
    const container = document.getElementById('maxuser');

    // Show loading state
    showLoadingState(container, 'fas fa-user-cog', 'Loading user account data...');

    try {
        const response = await fetch(`/api/site-access/${personId}/maxuser`);
        const result = await response.json();

        if (result.success && result.data) {
            displayMaxUserData(container, result.data);
            showToast('User account data loaded successfully', 'success');
        } else {
            showErrorState(container, 'fas fa-user-cog', result.error || 'No user account data found');
            showToast(result.error || 'Failed to load user account data', 'error');
        }
    } catch (error) {
        console.error('Error loading maxuser data:', error);
        showErrorState(container, 'fas fa-user-cog', 'Failed to load user account data');
        showToast('Network error while loading user account data', 'error');
    }
}

async function loadGroupsData(personId) {
    console.log('🔍 Loading groups data for:', personId);
    const container = document.getElementById('groups');

    // Show loading state
    showLoadingState(container, 'fas fa-users', 'Loading group memberships...');

    try {
        const response = await fetch(`/api/site-access/${personId}/groups`);
        const result = await response.json();

        if (result.success && result.data) {
            displayGroupsData(container, result.data);
            showToast(`Loaded ${result.data.length} group memberships`, 'success');
        } else {
            showErrorState(container, 'fas fa-users', result.error || 'No group memberships found');
            showToast(result.error || 'Failed to load group memberships', 'error');
        }
    } catch (error) {
        console.error('Error loading groups data:', error);
        showErrorState(container, 'fas fa-users', 'Failed to load group memberships');
        showToast('Network error while loading group memberships', 'error');
    }
}

async function loadSitesData(personId) {
    console.log('🔍 Loading sites data for:', personId);
    const container = document.getElementById('sites');

    // Show loading state
    showLoadingState(container, 'fas fa-building', 'Loading site authorizations...');

    try {
        const response = await fetch(`/api/site-access/${personId}/sites`);
        const result = await response.json();

        if (result.success && result.data) {
            displaySitesData(container, result.data);
            showToast(`Loaded ${result.data.length} site authorizations`, 'success');
        } else {
            showErrorState(container, 'fas fa-building', result.error || 'No site authorizations found');
            showToast(result.error || 'Failed to load site authorizations', 'error');
        }
    } catch (error) {
        console.error('Error loading sites data:', error);
        showErrorState(container, 'fas fa-building', 'Failed to load site authorizations');
        showToast('Network error while loading site authorizations', 'error');
    }
}

// Helper Functions for Loading States and Data Display
function showLoadingState(container, iconClass, message) {
    container.innerHTML = `
        <div class="loading-container">
            <div class="spinner-border text-primary mb-3" role="status" aria-label="Loading">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="text-muted text-center">
                <i class="${iconClass} fa-2x mb-2 d-block opacity-50"></i>
                <p class="mb-0">${message}</p>
                <small class="text-muted">Please wait...</small>
            </div>
        </div>
    `;
}

function showErrorState(container, iconClass, message) {
    const personId = '{{ user_profile.personid }}';
    const tabId = container.id;

    container.innerHTML = `
        <div class="text-center py-4">
            <div class="text-danger mb-3">
                <i class="${iconClass} fa-2x mb-2"></i>
                <p class="mb-0">${message}</p>
            </div>
            <div class="d-flex justify-content-center gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="retryLoadData('${tabId}', '${personId}')">
                    <i class="fas fa-redo me-1"></i>Retry
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="location.reload()">
                    <i class="fas fa-refresh me-1"></i>Refresh Page
                </button>
            </div>
        </div>
    `;
}

function retryLoadData(tabId, personId) {
    switch(tabId) {
        case 'person':
            loadPersonData(personId);
            break;
        case 'maxuser':
            loadMaxUserData(personId);
            break;
        case 'groups':
            loadGroupsData(personId);
            break;
        case 'sites':
            loadSitesData(personId);
            break;
    }
}

function showEmptyState(container, iconClass, message) {
    container.innerHTML = `
        <div class="text-center py-4 text-muted">
            <i class="${iconClass} fa-2x mb-2"></i>
            <p>${message}</p>
        </div>
    `;
}

// Data Display Functions
function displayPersonData(container, data) {
    if (!data || Object.keys(data).length === 0) {
        showEmptyState(container, 'fas fa-user', 'No person data available');
        return;
    }

    const fields = [
        { key: 'Person ID', icon: 'fas fa-id-card', value: data['Person ID'] || data.personid },
        { key: 'Display Name', icon: 'fas fa-user', value: data['Display Name'] || data.displayname },
        { key: 'First Name', icon: 'fas fa-user', value: data['First Name'] || data.firstname },
        { key: 'Last Name', icon: 'fas fa-user', value: data['Last Name'] || data.lastname },
        { key: 'Status', icon: 'fas fa-info-circle', value: data['Status'] || data.status },
        { key: 'Status Description', icon: 'fas fa-info', value: data['Status_description'] || data.status_description },
        { key: 'Department', icon: 'fas fa-building', value: data['Department'] || data.department },
        { key: 'Title', icon: 'fas fa-briefcase', value: data['Title'] || data.title },
        { key: 'Phone', icon: 'fas fa-phone', value: data['Phone'] || data.phone },
        { key: 'Email', icon: 'fas fa-envelope', value: data['Email'] || data.email }
    ];

    container.innerHTML = createDataCards(fields, 'person');
}

function displayMaxUserData(container, data) {
    if (!data || Object.keys(data).length === 0) {
        showEmptyState(container, 'fas fa-user-cog', 'No user account data available');
        return;
    }

    const fields = [
        { key: 'User ID', icon: 'fas fa-id-badge', value: data['Userid'] || data.userid },
        { key: 'Login ID', icon: 'fas fa-sign-in-alt', value: data['Loginid'] || data.loginid },
        { key: 'Status', icon: 'fas fa-info-circle', value: data['Status'] || data.status },
        { key: 'Status Description', icon: 'fas fa-info', value: data['Status_description'] || data.status_description },
        { key: 'Type', icon: 'fas fa-user-tag', value: data['Type'] || data.type },
        { key: 'Type Description', icon: 'fas fa-tag', value: data['Type_description'] || data.type_description },
        { key: 'Default Site', icon: 'fas fa-map-marker-alt', value: data['Defsite'] || data.defsite },
        { key: 'User Type', icon: 'fas fa-user-shield', value: data['Ud_type'] || data.ud_type },
        { key: 'User Type Description', icon: 'fas fa-shield-alt', value: data['Ud_type_description'] || data.ud_type_description },
        { key: 'Ticket Access', icon: 'fas fa-ticket-alt', value: data['Ud_ticket'] || data.ud_ticket },
        { key: 'Memo', icon: 'fas fa-sticky-note', value: data['Memo'] || data.memo }
    ];

    container.innerHTML = createDataCards(fields, 'maxuser');
}

function displayGroupsData(container, data) {
    if (!data || !Array.isArray(data) || data.length === 0) {
        showEmptyState(container, 'fas fa-users', 'No group memberships found');
        return;
    }

    let html = '<div class="row">';

    data.forEach((group, index) => {
        const authAllSites = group['AUTHALLSITES'] === '1' || group.authallsites === '1';
        const authBadge = authAllSites
            ? '<span class="badge bg-success ms-2"><i class="fas fa-globe me-1"></i>All Sites</span>'
            : '<span class="badge bg-secondary ms-2"><i class="fas fa-map-marker-alt me-1"></i>Specific Sites</span>';

        html += `
            <div class="col-md-6 col-12 mb-3">
                <div class="detail-card-enhanced h-100">
                    <div class="d-flex align-items-center mb-2">
                        <div class="detail-card-icon me-3" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">${group['Group Name'] || group.groupname || 'Unknown Group'}</h6>
                            <small class="text-muted">${group['Description'] || group.description || 'No description'}</small>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted small">Authorization Level:</span>
                        ${authBadge}
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function displaySitesData(container, data) {
    if (!data || !Array.isArray(data) || data.length === 0) {
        showEmptyState(container, 'fas fa-building', 'No site authorizations found');
        return;
    }

    let html = '<div class="row">';

    data.forEach((site, index) => {
        const siteId = site['Site ID'] || site.siteid || 'Unknown';
        const orgId = site['Organization'] || site.orgid || site.organization || 'Unknown';
        const description = site['Description'] || site.description || '';
        const status = site['Status'] || site.status || 'Unknown';

        const statusBadge = status.toLowerCase() === 'active'
            ? '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Active</span>'
            : '<span class="badge bg-secondary"><i class="fas fa-pause-circle me-1"></i>' + status + '</span>';

        html += `
            <div class="col-lg-4 col-md-6 col-12 mb-3">
                <div class="detail-card-enhanced h-100">
                    <div class="d-flex align-items-center mb-3">
                        <div class="detail-card-icon me-3" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold text-primary">${siteId}</h6>
                            <small class="text-muted">${orgId}</small>
                        </div>
                    </div>
                    ${description ? `<p class="text-muted small mb-2">${description}</p>` : ''}
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted small">Status:</span>
                        ${statusBadge}
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';

    // Add summary info
    html += `
        <div class="mt-3 p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-6">
                    <div class="fw-bold text-primary">${data.length}</div>
                    <small class="text-muted">Total Sites</small>
                </div>
                <div class="col-6">
                    <div class="fw-bold text-success">${data.filter(s => (s.Status || s.status || '').toLowerCase() === 'active').length}</div>
                    <small class="text-muted">Active Sites</small>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function createDataCards(fields, type) {
    let html = '<div class="row">';

    fields.forEach((field, index) => {
        if (field.value && field.value !== 'N/A' && field.value !== '') {
            html += `
                <div class="col-lg-6 col-12 mb-3">
                    <div class="detail-card-enhanced h-100">
                        <div class="detail-row-enhanced">
                            <div class="detail-label-enhanced">
                                <i class="${field.icon}"></i>${field.key}
                            </div>
                            <div class="detail-value-enhanced">
                                ${field.value}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    });

    html += '</div>';

    // If no data to show
    if (html === '<div class="row"></div>') {
        return `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>No ${type} data available to display</p>
            </div>
        `;
    }

    return html;
}

// Toast notification system
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toastId = 'toast-' + Date.now();
    const iconClass = type === 'success' ? 'fas fa-check-circle' :
                     type === 'error' ? 'fas fa-exclamation-circle' :
                     'fas fa-info-circle';

    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="${iconClass} me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: type === 'error' ? 5000 : 3000
    });

    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}
</script>

{% endblock %}