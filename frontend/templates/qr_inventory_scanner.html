{% extends "base.html" %}

{% block title %}QR Code Inventory Scanner{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/inventory_management.css') }}">
<style>
    .scanner-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .qr-scanner-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .scanner-input-group {
        margin-bottom: 15px;
    }
    
    .scanner-input {
        font-family: monospace;
        font-size: 14px;
    }
    
    .inventory-info-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .inventory-operations {
        display: none;
    }
    
    .operation-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
    }
    
    .operation-card.active {
        border-color: #007bff;
        background: #f8f9ff;
    }
    
    .count-input {
        max-width: 150px;
    }
    
    .variance-positive {
        color: #28a745;
        font-weight: bold;
    }
    
    .variance-negative {
        color: #dc3545;
        font-weight: bold;
    }
    
    .variance-zero {
        color: #6c757d;
    }

    /* Hover Effects */
    .hover-field {
        transition: all 0.3s ease;
        border: 2px solid #dee2e6;
    }

    .hover-field:hover {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        transform: translateY(-1px);
    }

    .hover-field:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        transform: translateY(-1px);
    }

    .hover-label {
        transition: color 0.3s ease;
        cursor: pointer;
    }

    .hover-label:hover {
        color: #007bff;
    }

    /* Required field styling */
    .required-field {
        border-left: 4px solid #dc3545;
    }

    .required-field:focus {
        border-left: 4px solid #007bff;
    }

    .required-field.is-valid {
        border-left: 4px solid #28a745;
    }

    /* Enhanced form styling */
    .form-label {
        font-weight: 600;
        color: #495057;
    }

    .form-text {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .bg-light {
        background-color: #f8f9fa !important;
        border: 1px solid #e9ecef;
    }

    /* Icon styling */
    .fas {
        color: #6c757d;
    }

    .text-warning .fas {
        color: #ffc107 !important;
    }

    .text-danger .fas {
        color: #dc3545 !important;
    }

    .text-success .fas {
        color: #28a745 !important;
    }

    .text-info .fas {
        color: #17a2b8 !important;
    }

    /* Section headers */
    h6 {
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    h6.text-primary {
        border-bottom-color: #007bff;
    }

    h6.text-muted {
        border-bottom-color: #6c757d;
    }

    /* Compact checkbox styling */
    .compact-checkbox {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 12px 16px;
        transition: all 0.3s ease;
    }

    .compact-checkbox:hover {
        background-color: #e9ecef;
        border-color: #007bff;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
    }

    .compact-checkbox .form-check-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 2px;
    }

    .compact-checkbox .compact-help {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
        margin-bottom: 0;
    }

    .compact-checkbox .form-check-input {
        margin-top: 0.2rem;
    }

    .compact-checkbox .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .loading-text {
        font-weight: 500;
        color: #495057;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .qr-scanner-section {
            padding: 15px;
            margin-bottom: 15px;
        }

        .inventory-info-card {
            padding: 15px;
        }

        .btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .row .col-md-6 {
            margin-bottom: 15px;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .form-text {
            font-size: 0.85rem;
        }
    }

    @media (max-width: 576px) {
        .container-fluid {
            padding: 10px;
        }

        h5 {
            font-size: 1.1rem;
        }

        .btn-sm {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }

        .operation-card {
            padding: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="loading-text mt-3">Processing...</div>
    </div>
</div>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-qrcode me-2"></i>QR Code Inventory Scanner
                        </h1>
                        <p class="page-subtitle text-muted">
                            Scan QR codes for cycle counting, adjustments, and transfers
                        </p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-outline-primary" onclick="clearScanner()" title="Clear Scanner">
                            <i class="fas fa-broom me-1"></i>
                            <span class="d-none d-md-inline">Clear</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="scanner-container">
        <!-- QR Code Scanner Section -->
        <div class="qr-scanner-section">
            <h5 class="mb-3">
                <i class="fas fa-camera me-2"></i>Scan QR Code
            </h5>
            
            <!-- Manual QR Code Input -->
            <div class="scanner-input-group">
                <label for="qrCodeInput" class="form-label">QR Code Data (Manual Entry)</label>
                <div class="input-group">
                    <input type="text"
                           class="form-control scanner-input"
                           id="qrCodeInput"
                           placeholder="Paste QR code data here or scan with camera..."
                           autocomplete="off">
                    <button class="btn btn-primary" type="button" onclick="processQRCode()">
                        <i class="fas fa-search me-1"></i>Process
                    </button>
                </div>
                <div class="form-text">
                    Paste the QR code content or use the camera scanner below
                </div>

            </div>

            <!-- QR Code Upload Section -->
            <div class="scanner-input-group">
                <label for="qrImageUpload" class="form-label">Upload QR Code Image</label>
                <div class="input-group">
                    <input type="file"
                           class="form-control"
                           id="qrImageUpload"
                           accept="image/*"
                           onchange="handleQRImageUpload(event)">
                    <button class="btn btn-success" type="button" onclick="triggerFileUpload()">
                        <i class="fas fa-upload me-1"></i>Browse
                    </button>
                </div>
                <div class="form-text">
                    Upload an image containing a QR code to scan
                </div>
            </div>

            <!-- Camera Scanner -->
            <div class="camera-scanner-section">
                <div class="text-center mb-3">
                    <button class="btn btn-outline-secondary" onclick="toggleCameraScanner()" id="cameraToggleBtn" disabled>
                        <i class="fas fa-camera me-1"></i>Loading Camera...
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="switchCamera()" id="cameraSwitchBtn" style="display: none;">
                        <i class="fas fa-sync-alt me-1"></i>Switch Camera
                    </button>
                    <button class="btn btn-outline-warning ms-2" onclick="forceEnableCamera()" id="forceEnableBtn" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-1"></i>Force Enable
                    </button>
                </div>

                <!-- Camera Video Element -->
                <div id="cameraContainer" class="text-center" style="display: none;">
                    <div class="position-relative d-inline-block">
                        <video id="qrVideo" autoplay playsinline style="width: 100%; max-width: 400px; border: 2px solid #007bff; border-radius: 8px; background: #000;"></video>
                        <canvas id="qrCanvas" style="display: none;"></canvas>
                        <div id="scanRegion" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; border: 2px solid #00ff00; border-radius: 8px; pointer-events: none;"></div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">Position QR code within the green scanning area</small>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-warning btn-sm" onclick="captureQRManually()" id="captureBtn">
                            <i class="fas fa-camera-retro me-1"></i>Capture QR Now
                        </button>
                    </div>
                    <div class="mt-2">
                        <div id="cameraInfo" class="small text-info"></div>
                        <div id="scanStatus" class="small text-success mt-1"></div>
                        <div id="debugInfo" class="small text-muted mt-1" style="font-family: monospace;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Information Display -->
        <div id="inventoryInfoSection" class="qr-scanner-section" style="display: none;">
            <h5 class="mb-3">
                <i class="fas fa-box me-2"></i>Inventory Information
            </h5>
            <div id="inventoryInfoCard" class="inventory-info-card">
                <!-- Inventory details will be populated here -->
            </div>
        </div>

        <!-- Inventory Operations Section -->
        <div id="inventoryOperations" class="inventory-operations">
            <div class="qr-scanner-section">
                <h5 class="mb-3">
                    <i class="fas fa-tasks me-2"></i>Inventory Operations
                </h5>

                <!-- Cycle Count Operation -->
                <div class="operation-card" id="cycleCountCard">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>Cycle Count
                        </h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectOperation('cycleCount')">
                            Select
                        </button>
                    </div>
                    <div id="cycleCountForm" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Physical Count</label>
                                <input type="number" class="form-control count-input" id="cyclePhysicalCount"
                                       placeholder="0" step="0.01" onchange="calculateVariance()">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">System Count</label>
                                <input type="number" class="form-control count-input" id="systemCount" 
                                       readonly>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Variance</label>
                                <div id="varianceDisplay" class="form-control-plaintext">-</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">Count Notes</label>
                            <textarea class="form-control" id="countNotes" rows="2" 
                                      placeholder="Optional notes about the count..."></textarea>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="submitCycleCount()">
                                <i class="fas fa-check me-1"></i>Submit Count
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="cancelOperation()">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Inventory Adjustment Operation -->
                <div class="operation-card" id="adjustmentCard">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-edit me-2"></i>Inventory Adjustment
                        </h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectOperation('adjustment')">
                            Select
                        </button>
                    </div>
                    <div id="adjustmentForm" style="display: none;">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Adjustment Method</label>
                                <select class="form-select" id="adjustmentMethod" onchange="toggleAdjustmentFields()">
                                    <option value="PHYSICAL_COUNT">Physical Count Adjustment</option>
                                    <option value="CURRENT_BALANCE">Current Balance Adjustment</option>
                                </select>
                                <div class="form-text">Choose how you want to adjust the inventory</div>
                            </div>
                        </div>

                        <!-- Physical Count Fields -->
                        <div id="physicalCountFields" class="row">
                            <div class="col-md-6">
                                <label class="form-label">Physical Count</label>
                                <input type="number" class="form-control" id="physicalCount"
                                       placeholder="Enter actual count" step="0.01" min="0">
                                <div class="form-text">Enter the actual physical count</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Current System Balance</label>
                                <input type="number" class="form-control" id="systemBalance"
                                       placeholder="0" readonly>
                                <div class="form-text">Current balance in system (read-only)</div>
                            </div>
                        </div>

                        <!-- Current Balance Adjustment Fields -->
                        <div id="balanceAdjustmentFields" class="row" style="display: none;">
                            <div class="col-md-6">
                                <label class="form-label">Adjustment Type</label>
                                <select class="form-select" id="adjustmentType">
                                    <option value="POSITIVE">Positive Adjustment (+)</option>
                                    <option value="NEGATIVE">Negative Adjustment (-)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Adjustment Quantity</label>
                                <input type="number" class="form-control" id="adjustmentQuantity"
                                       placeholder="0" step="0.01" min="0">
                                <div class="form-text">Amount to add or subtract</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">Reason Code</label>
                            <select class="form-select" id="reasonCode">
                                <option value="DAMAGE">Damage</option>
                                <option value="LOSS">Loss/Theft</option>
                                <option value="FOUND">Found Item</option>
                                <option value="RECOUNT">Recount Correction</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">Adjustment Notes</label>
                            <textarea class="form-control" id="adjustmentNotes" rows="2"
                                      placeholder="Reason for adjustment..."></textarea>
                        </div>

                        <!-- Item Details Section -->
                        <div class="mt-4">
                            <h6 class="text-primary"><i class="fas fa-info-circle me-2"></i>Item Details</h6>
                            <div class="row">
                                <div class="col-md-6 col-sm-12">
                                    <label class="form-label">
                                        <i class="fas fa-cube me-1"></i>Bin Number
                                        <small class="text-muted">(optional)</small>
                                    </label>
                                    <input type="text" class="form-control hover-field" id="binNumber"
                                           placeholder="Enter bin number (if different from QR)">
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb me-1 text-warning"></i>
                                        Auto-filled from QR code if available
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-12">
                                    <label class="form-label">
                                        <i class="fas fa-check-circle me-1"></i>Condition Code
                                        <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select hover-field required-field" id="conditionCode" required>
                                        <option value="">Select condition...</option>
                                        <option value="A1">A1 - Good Condition</option>
                                        <option value="A2">A2 - Fair Condition</option>
                                        <option value="A3">A3 - Poor Condition</option>
                                        <option value="B1">B1 - Damaged</option>
                                        <option value="C1">C1 - Obsolete</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-exclamation-triangle me-1 text-danger"></i>
                                        Required - Item condition status
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Optional Fields Section -->
                        <div class="mt-4">
                            <h6 class="text-muted">
                                <i class="fas fa-plus-circle me-2"></i>Additional Information
                                <small class="text-secondary">(optional)</small>
                            </h6>
                            <div class="row">
                                <div class="col-md-6 col-sm-12">
                                    <label class="form-label">
                                        <i class="fas fa-barcode me-1"></i>Lot Number
                                    </label>
                                    <input type="text" class="form-control hover-field" id="lotNumber"
                                           placeholder="Lot number (if applicable)">
                                    <div class="form-text">
                                        <i class="fas fa-info me-1 text-info"></i>
                                        Manufacturing lot identifier
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-12">
                                    <label class="form-label">
                                        <i class="fas fa-tags me-1"></i>Lot Type
                                    </label>
                                    <input type="text" class="form-control hover-field" id="lotType"
                                           placeholder="Lot type (if applicable)">
                                    <div class="form-text">
                                        <i class="fas fa-info me-1 text-info"></i>
                                        Type of lot classification
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6 col-sm-12">
                                    <label class="form-label">
                                        <i class="fas fa-calculator me-1"></i>Control Account
                                    </label>
                                    <input type="text" class="form-control hover-field" id="controlAccount"
                                           placeholder="Control account (if available)">
                                    <div class="form-text">
                                        <i class="fas fa-info me-1 text-info"></i>
                                        Financial control account
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-12">
                                    <label class="form-label">
                                        <i class="fas fa-chart-line me-1"></i>Shrinkage Account
                                    </label>
                                    <input type="text" class="form-control hover-field" id="shrinkageAccount"
                                           placeholder="Shrinkage account (if available)">
                                    <div class="form-text">
                                        <i class="fas fa-info me-1 text-info"></i>
                                        Account for inventory shrinkage
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6 col-sm-12">
                                    <div class="form-check compact-checkbox">
                                        <input class="form-check-input" type="checkbox" id="reconciled" checked>
                                        <label class="form-check-label hover-label" for="reconciled">
                                            <i class="fas fa-check-double me-1 text-success"></i>
                                            Mark as Reconciled
                                        </label>
                                        <div class="form-text compact-help">
                                            Verified and reconciled adjustment
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-warning" onclick="submitAdjustment()">
                                <i class="fas fa-edit me-1"></i>Submit Adjustment
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="cancelOperation()">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Inventory Transfer Operation -->
                <div class="operation-card" id="transferCard">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-exchange-alt me-2"></i>Inventory Transfer
                        </h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectOperation('transfer')">
                            Select
                        </button>
                    </div>
                    <div id="transferForm" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Transfer Quantity</label>
                                <input type="number" class="form-control" id="transferQuantity" 
                                       placeholder="0" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Available Balance</label>
                                <input type="number" class="form-control" id="availableBalance" readonly>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">To Location</label>
                                <input type="text" class="form-control" id="toLocation" 
                                       placeholder="Destination location">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">To Bin</label>
                                <input type="text" class="form-control" id="toBin" 
                                       placeholder="Destination bin (optional)">
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">Transfer Notes</label>
                            <textarea class="form-control" id="transferNotes" rows="2" 
                                      placeholder="Reason for transfer..."></textarea>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-info" onclick="submitTransfer()">
                                <i class="fas fa-exchange-alt me-1"></i>Submit Transfer
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="cancelOperation()">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="operationResults" class="qr-scanner-section" style="display: none;">
            <h5 class="mb-3">
                <i class="fas fa-check-circle me-2"></i>Operation Results
            </h5>
            <div id="resultsContent">
                <!-- Results will be displayed here -->
            </div>
        </div>
    </div>
</div>

<!-- QR Scanner Library - Load before our scanner script -->
<script src="/static/js/jsqr.min.js"></script>
<script>
console.log('🔍 JSQR LOADED: typeof jsQR =', typeof jsQR);
if (typeof jsQR !== 'undefined') {
    console.log('✅ SUCCESS: jsQR is available');
    window.dispatchEvent(new Event('jsQRLoaded'));
} else {
    console.error('❌ ERROR: jsQR not available');
}
</script>
<script src="{{ url_for('static', filename='js/qr_inventory_scanner.js') }}"></script>
{% endblock %}
