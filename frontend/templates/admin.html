{% extends 'base.html' %}

{% block title %}Admin Configuration - Maximo Mobile{% endblock %}

{% block extra_css %}
<style>
/* Admin page specific styles */
.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

.admin-header {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
}

.admin-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.admin-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

/* Tab navigation */
.admin-nav-tabs {
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 2rem;
}

.admin-nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text-color);
    font-weight: 500;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
}

.admin-nav-tabs .nav-link:hover {
    border-bottom-color: var(--secondary-color);
    background-color: rgba(var(--secondary-color-rgb), 0.1);
}

.admin-nav-tabs .nav-link.active {
    border-bottom-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
}

/* Tab content */
.tab-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
}

/* Signature config specific styles */
.signature-config-section {
    margin-bottom: 2rem;
}

.signature-config-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.status-item {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    transition: all 0.3s ease;
}

.status-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-item.enabled {
    border-color: var(--success-color);
    background: rgba(var(--success-color-rgb), 0.05);
}

.status-checkbox {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

.status-label {
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.status-code {
    font-family: 'Courier New', monospace;
    background: rgba(var(--primary-color-rgb), 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-left: auto;
    font-size: 0.85rem;
    color: var(--primary-color);
}

/* Scope selection */
.scope-selection {
    background: rgba(var(--secondary-color-rgb), 0.05);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.scope-selection h4 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.scope-options {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.scope-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.scope-option input[type="checkbox"] {
    transform: scale(1.2);
}

.scope-option label {
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
}

/* Action buttons */
.admin-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.btn-admin {
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-admin-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-admin-secondary {
    background: var(--light-color);
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

.btn-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* AI Insights section */
.ai-insights-admin {
    background: linear-gradient(135deg, rgba(var(--secondary-color-rgb), 0.1), rgba(var(--primary-color-rgb), 0.05));
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 2rem;
}

.ai-insights-admin h4 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-insight-admin {
    background: white;
    border-left: 4px solid var(--secondary-color);
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-insight-admin:last-child {
    margin-bottom: 0;
}

.ai-insight-admin strong {
    color: var(--primary-color);
}

.ai-insight-admin.border-danger {
    border-left-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.05);
}

.ai-insight-admin.border-warning {
    border-left-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.05);
}

.ai-insight-admin.border-info {
    border-left-color: #17a2b8 !important;
    background: rgba(23, 162, 184, 0.05);
}

.text-purple {
    color: #6f42c1 !important;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .admin-container {
        padding: 0.5rem;
    }
    
    .admin-header {
        padding: 1.5rem;
    }
    
    .admin-header h1 {
        font-size: 1.5rem;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .scope-options {
        flex-direction: column;
        gap: 1rem;
    }
    
    .admin-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- Admin Header -->
    <div class="admin-header">
        <h1>
            <i class="fas fa-cogs me-2"></i>Admin Configuration
        </h1>
        <p>Configure system settings and signature requirements</p>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs admin-nav-tabs" id="adminTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="signature-config-tab" data-bs-toggle="tab" 
                    data-bs-target="#signature-config" type="button" role="tab" 
                    aria-controls="signature-config" aria-selected="true">
                <i class="fas fa-signature me-2"></i>Signature Config
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="issue-material-config-tab" data-bs-toggle="tab"
                    data-bs-target="#issue-material-config" type="button" role="tab"
                    aria-controls="issue-material-config" aria-selected="false">
                <i class="fas fa-boxes me-2"></i>Issue Material Config
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="system-settings-tab" data-bs-toggle="tab"
                    data-bs-target="#system-settings" type="button" role="tab"
                    aria-controls="system-settings" aria-selected="false">
                <i class="fas fa-sliders-h me-2"></i>System Settings
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="adminTabContent">
        <!-- Signature Configuration Tab -->
        <div class="tab-pane fade show active" id="signature-config" role="tabpanel" 
             aria-labelledby="signature-config-tab">
            
            <div class="signature-config-section">
                <h3><i class="fas fa-pen-fancy me-2"></i>Status-Based Signature Requirements</h3>
                <p class="text-muted">Select which work order statuses should require digital signatures. 
                   When users change to these statuses, they will be prompted to provide a signature.</p>
            </div>

            <!-- Scope Selection -->
            <div class="scope-selection">
                <h4><i class="fas fa-crosshairs me-2"></i>Signature Scope</h4>
                <div class="scope-options">
                    <div class="scope-option">
                        <input type="checkbox" id="scope-parent" name="scope" value="parent" checked>
                        <label for="scope-parent">Parent Work Orders</label>
                    </div>
                    <div class="scope-option">
                        <input type="checkbox" id="scope-task" name="scope" value="task" checked>
                        <label for="scope-task">Task Work Orders</label>
                    </div>
                </div>
                <small class="text-muted">Choose whether signatures apply to parent work orders, task work orders, or both.</small>
            </div>

            <!-- Status Grid -->
            <div class="status-grid" id="statusGrid">
                <!-- Status items will be populated by JavaScript -->
            </div>

            <!-- AI Insights -->
            <div class="ai-insights-admin">
                <h4><i class="fas fa-robot"></i>AI Insights - Signature Trends</h4>
                <div id="signatureInsights">
                    <!-- AI insights will be populated by JavaScript -->
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="admin-actions">
                <button type="button" class="btn-admin btn-admin-secondary" onclick="resetSignatureConfig()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
                <button type="button" class="btn-admin btn-admin-primary" onclick="saveSignatureConfig()">
                    <i class="fas fa-save me-2"></i>Save Configuration
                </button>
            </div>
        </div>

        <!-- Issue Material Configuration Tab -->
        <div class="tab-pane fade" id="issue-material-config" role="tabpanel"
             aria-labelledby="issue-material-config-tab">

            <div class="signature-config-section">
                <h3><i class="fas fa-boxes me-2"></i>Issue Material Status Configuration</h3>
                <p class="text-muted">Configure which work order and task statuses should be available for material issue operations.
                   Only work orders and tasks with these statuses will appear in the issue material dropdowns.</p>
            </div>

            <!-- Work Order Status Configuration -->
            <div class="signature-config-section">
                <h4><i class="fas fa-clipboard-list me-2"></i>Work Order Statuses</h4>
                <p class="text-muted">Select which work order statuses should be available for material issue:</p>

                <div class="status-grid" id="workOrderStatusGrid">
                    <!-- Work Order Status checkboxes - All available statuses like signature config -->
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-APPR" value="APPR">
                        <label for="wo-status-APPR">
                            <strong>APPR</strong> - Approved
                            <div class="text-muted small">Approved work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-ASSIGN" value="ASSIGN">
                        <label for="wo-status-ASSIGN">
                            <strong>ASSIGN</strong> - Assigned
                            <div class="text-muted small">Assigned work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-READY" value="READY">
                        <label for="wo-status-READY">
                            <strong>READY</strong> - Ready
                            <div class="text-muted small">Ready to start work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-INPRG" value="INPRG">
                        <label for="wo-status-INPRG">
                            <strong>INPRG</strong> - In Progress
                            <div class="text-muted small">Work orders currently in progress</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-PACK" value="PACK">
                        <label for="wo-status-PACK">
                            <strong>PACK</strong> - Pack
                            <div class="text-muted small">Packed work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-DEFER" value="DEFER">
                        <label for="wo-status-DEFER">
                            <strong>DEFER</strong> - Deferred
                            <div class="text-muted small">Deferred work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-WAPPR" value="WAPPR">
                        <label for="wo-status-WAPPR">
                            <strong>WAPPR</strong> - Waiting Approval
                            <div class="text-muted small">Waiting for approval</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-WGOVT" value="WGOVT">
                        <label for="wo-status-WGOVT">
                            <strong>WGOVT</strong> - Waiting Government
                            <div class="text-muted small">Waiting for government approval</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-AWARD" value="AWARD">
                        <label for="wo-status-AWARD">
                            <strong>AWARD</strong> - Awarded
                            <div class="text-muted small">Awarded work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-MTLCXD" value="MTLCXD">
                        <label for="wo-status-MTLCXD">
                            <strong>MTLCXD</strong> - Material Cancelled
                            <div class="text-muted small">Material cancelled</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-MTLISD" value="MTLISD">
                        <label for="wo-status-MTLISD">
                            <strong>MTLISD</strong> - Material Issued
                            <div class="text-muted small">Material issued</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-PISSUE" value="PISSUE">
                        <label for="wo-status-PISSUE">
                            <strong>PISSUE</strong> - Partial Issue
                            <div class="text-muted small">Work orders with partial material issues</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-RTI" value="RTI">
                        <label for="wo-status-RTI">
                            <strong>RTI</strong> - Return to Inventory
                            <div class="text-muted small">Return to inventory</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-WMATL" value="WMATL">
                        <label for="wo-status-WMATL">
                            <strong>WMATL</strong> - Waiting Material
                            <div class="text-muted small">Work orders waiting for materials</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-WSERV" value="WSERV">
                        <label for="wo-status-WSERV">
                            <strong>WSERV</strong> - Waiting Service
                            <div class="text-muted small">Waiting for service</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-WSCH" value="WSCH">
                        <label for="wo-status-WSCH">
                            <strong>WSCH</strong> - Waiting Schedule
                            <div class="text-muted small">Waiting to be scheduled</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-COMP" value="COMP">
                        <label for="wo-status-COMP">
                            <strong>COMP</strong> - Complete
                            <div class="text-muted small">Completed work orders</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox work-order-status-checkbox" id="wo-status-SET" value="SET">
                        <label for="wo-status-SET">
                            <strong>SET</strong> - Set
                            <div class="text-muted small">Status set</div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Task Status Configuration -->
            <div class="signature-config-section">
                <h4><i class="fas fa-tasks me-2"></i>Task Statuses</h4>
                <p class="text-muted">Select which task statuses should be available for material issue:</p>

                <div class="status-grid" id="taskStatusGrid">
                    <!-- Task Status checkboxes - All available statuses like signature config -->
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-APPR" value="APPR">
                        <label for="task-status-APPR">
                            <strong>APPR</strong> - Approved
                            <div class="text-muted small">Approved tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-ASSIGN" value="ASSIGN">
                        <label for="task-status-ASSIGN">
                            <strong>ASSIGN</strong> - Assigned
                            <div class="text-muted small">Assigned tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-READY" value="READY">
                        <label for="task-status-READY">
                            <strong>READY</strong> - Ready
                            <div class="text-muted small">Ready to start tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-INPRG" value="INPRG">
                        <label for="task-status-INPRG">
                            <strong>INPRG</strong> - In Progress
                            <div class="text-muted small">Tasks currently in progress</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-PACK" value="PACK">
                        <label for="task-status-PACK">
                            <strong>PACK</strong> - Pack
                            <div class="text-muted small">Packed tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-DEFER" value="DEFER">
                        <label for="task-status-DEFER">
                            <strong>DEFER</strong> - Deferred
                            <div class="text-muted small">Deferred tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-WAPPR" value="WAPPR">
                        <label for="task-status-WAPPR">
                            <strong>WAPPR</strong> - Waiting Approval
                            <div class="text-muted small">Waiting for approval</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-WGOVT" value="WGOVT">
                        <label for="task-status-WGOVT">
                            <strong>WGOVT</strong> - Waiting Government
                            <div class="text-muted small">Waiting for government approval</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-AWARD" value="AWARD">
                        <label for="task-status-AWARD">
                            <strong>AWARD</strong> - Awarded
                            <div class="text-muted small">Awarded tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-MTLCXD" value="MTLCXD">
                        <label for="task-status-MTLCXD">
                            <strong>MTLCXD</strong> - Material Cancelled
                            <div class="text-muted small">Material cancelled</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-MTLISD" value="MTLISD">
                        <label for="task-status-MTLISD">
                            <strong>MTLISD</strong> - Material Issued
                            <div class="text-muted small">Material issued</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-PISSUE" value="PISSUE">
                        <label for="task-status-PISSUE">
                            <strong>PISSUE</strong> - Partial Issue
                            <div class="text-muted small">Tasks with partial material issues</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-RTI" value="RTI">
                        <label for="task-status-RTI">
                            <strong>RTI</strong> - Return to Inventory
                            <div class="text-muted small">Return to inventory</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-WMATL" value="WMATL">
                        <label for="task-status-WMATL">
                            <strong>WMATL</strong> - Waiting Material
                            <div class="text-muted small">Tasks waiting for materials</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-WSERV" value="WSERV">
                        <label for="task-status-WSERV">
                            <strong>WSERV</strong> - Waiting Service
                            <div class="text-muted small">Waiting for service</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-WSCH" value="WSCH">
                        <label for="task-status-WSCH">
                            <strong>WSCH</strong> - Waiting Schedule
                            <div class="text-muted small">Waiting to be scheduled</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-COMP" value="COMP">
                        <label for="task-status-COMP">
                            <strong>COMP</strong> - Complete
                            <div class="text-muted small">Completed tasks</div>
                        </label>
                    </div>
                    <div class="status-item">
                        <input type="checkbox" class="status-checkbox task-status-checkbox" id="task-status-SET" value="SET">
                        <label for="task-status-SET">
                            <strong>SET</strong> - Set
                            <div class="text-muted small">Status set</div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-3 mt-4">
                <button type="button" class="btn-admin btn-admin-secondary" onclick="resetIssueMaterialConfig()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
                <button type="button" class="btn-admin btn-admin-primary" onclick="saveIssueMaterialConfig()">
                    <i class="fas fa-save me-2"></i>Save Configuration
                </button>
            </div>
        </div>

        <!-- System Settings Tab -->
        <div class="tab-pane fade" id="system-settings" role="tabpanel" 
             aria-labelledby="system-settings-tab">
            <div class="signature-config-section">
                <h3><i class="fas fa-sliders-h me-2"></i>System Settings</h3>
                <p class="text-muted">Additional system configuration options will be available here.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Admin page JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Admin Configuration Page Loaded');
    
    // Initialize signature configuration
    initializeSignatureConfig();

    // Load existing configuration
    loadSignatureConfig();

    // Initialize AI insights
    initializeSignatureInsights();

    // Initialize issue material configuration
    initializeIssueMaterialConfig();

    // Load existing issue material configuration
    loadIssueMaterialConfig();
});

// Status definitions with descriptions
const workOrderStatuses = [
    { code: 'APPR', name: 'Approved', description: 'Work order has been approved' },
    { code: 'ASSIGN', name: 'Assigned', description: 'Work order has been assigned' },
    { code: 'READY', name: 'Ready', description: 'Work order is ready to start' },
    { code: 'INPRG', name: 'In Progress', description: 'Work order is in progress' },
    { code: 'PACK', name: 'Packed', description: 'Work order materials are packed' },
    { code: 'DEFER', name: 'Deferred', description: 'Work order has been deferred' },
    { code: 'WAPPR', name: 'Waiting Approval', description: 'Waiting for approval' },
    { code: 'WGOVT', name: 'Waiting Government', description: 'Waiting for government approval' },
    { code: 'AWARD', name: 'Awarded', description: 'Work order has been awarded' },
    { code: 'MTLCXD', name: 'Material Cancelled', description: 'Material has been cancelled' },
    { code: 'MTLISD', name: 'Material Issued', description: 'Material has been issued' },
    { code: 'PISSUE', name: 'Partial Issue', description: 'Partial material issue' },
    { code: 'RTI', name: 'Ready to Issue', description: 'Ready to issue materials' },
    { code: 'WMATL', name: 'Waiting Material', description: 'Waiting for materials' },
    { code: 'WSERV', name: 'Waiting Service', description: 'Waiting for service' },
    { code: 'WSCH', name: 'Waiting Schedule', description: 'Waiting to be scheduled' },
    { code: 'COMP', name: 'Complete', description: 'Work order is complete' },
    { code: 'SET', name: 'Set', description: 'Work order status is set' }
];

function initializeSignatureConfig() {
    const statusGrid = document.getElementById('statusGrid');
    
    workOrderStatuses.forEach(status => {
        const statusItem = document.createElement('div');
        statusItem.className = 'status-item';
        statusItem.innerHTML = `
            <label class="status-label" for="status-${status.code}">
                <input type="checkbox" class="status-checkbox" id="status-${status.code}" 
                       value="${status.code}" onchange="updateStatusSelection(this)">
                <div>
                    <div>${status.name}</div>
                    <small class="text-muted">${status.description}</small>
                </div>
                <span class="status-code">${status.code}</span>
            </label>
        `;
        statusGrid.appendChild(statusItem);
    });
}

function updateStatusSelection(checkbox) {
    const statusItem = checkbox.closest('.status-item');
    if (checkbox.checked) {
        statusItem.classList.add('enabled');
    } else {
        statusItem.classList.remove('enabled');
    }
}

function loadSignatureConfig() {
    // Load existing configuration from server
    fetch('/api/admin/signature-config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Apply loaded configuration
                const config = data.config;
                
                // Set scope checkboxes
                document.getElementById('scope-parent').checked = config.scope.includes('parent');
                document.getElementById('scope-task').checked = config.scope.includes('task');
                
                // Set status checkboxes
                config.statuses.forEach(status => {
                    const checkbox = document.getElementById(`status-${status}`);
                    if (checkbox) {
                        checkbox.checked = true;
                        updateStatusSelection(checkbox);
                    }
                });
            }
        })
        .catch(error => {
            console.log('No existing configuration found, using defaults');
        });
}

function saveSignatureConfig() {
    // Collect selected statuses
    const selectedStatuses = [];
    document.querySelectorAll('.status-checkbox:checked').forEach(checkbox => {
        selectedStatuses.push(checkbox.value);
    });
    
    // Collect scope selection
    const scope = [];
    if (document.getElementById('scope-parent').checked) scope.push('parent');
    if (document.getElementById('scope-task').checked) scope.push('task');
    
    const config = {
        statuses: selectedStatuses,
        scope: scope,
        enabled: selectedStatuses.length > 0 && scope.length > 0
    };
    
    // Save configuration
    fetch('/api/admin/signature-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('success', 'Signature configuration saved successfully!');
            // Refresh AI insights
            initializeSignatureInsights();
        } else {
            showNotification('error', 'Failed to save configuration: ' + data.error);
        }
    })
    .catch(error => {
        showNotification('error', 'Error saving configuration: ' + error.message);
    });
}

function resetSignatureConfig() {
    if (confirm('Are you sure you want to reset the signature configuration?')) {
        // Uncheck all checkboxes
        document.querySelectorAll('.status-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            updateStatusSelection(checkbox);
        });
        
        // Reset scope to default (both checked)
        document.getElementById('scope-parent').checked = true;
        document.getElementById('scope-task').checked = true;
        
        showNotification('info', 'Configuration reset. Remember to save your changes.');
    }
}

function initializeSignatureInsights() {
    const insightsContainer = document.getElementById('signatureInsights');

    // Show loading state
    insightsContainer.innerHTML = `
        <div class="ai-insight-admin">
            <i class="fas fa-spinner fa-spin me-2"></i>
            <strong>Loading AI Insights...</strong> Analyzing signature configuration patterns...
        </div>
    `;

    // Load AI insights from server
    fetch('/api/admin/signature-analytics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAIInsights(data.analytics);
            } else {
                // Fallback to local insights
                const insights = generateSignatureInsights();
                displayLocalInsights(insights);
            }
        })
        .catch(error => {
            console.error('Error loading AI insights:', error);
            // Fallback to local insights
            const insights = generateSignatureInsights();
            displayLocalInsights(insights);
        });
}

function displayAIInsights(analytics) {
    const insightsContainer = document.getElementById('signatureInsights');

    if (!analytics.insights || analytics.insights.length === 0) {
        insightsContainer.innerHTML = `
            <div class="ai-insight-admin">
                <strong>No Insights Available:</strong> Configure signature requirements to see AI-powered analytics.
            </div>
        `;
        return;
    }

    // Display configuration summary
    const summary = analytics.configuration_summary;
    let summaryHtml = `
        <div class="ai-insight-admin" style="background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1)); border-left-color: #3498db;">
            <strong>Configuration Summary:</strong>
            ${summary.configured_statuses}/${summary.total_statuses} statuses configured (${summary.coverage_percentage}% coverage)
            <br><small>Scope: ${summary.scope.join(', ') || 'None'} | Status: ${summary.enabled ? 'Enabled' : 'Disabled'}</small>
        </div>
    `;

    // Display insights with priority-based styling
    const insightsHtml = analytics.insights.map(insight => {
        const priorityClass = insight.priority === 'high' ? 'border-danger' :
                            insight.priority === 'medium' ? 'border-warning' : 'border-info';
        const iconClass = insight.type === 'warning' ? 'fa-exclamation-triangle text-warning' :
                         insight.type === 'success' ? 'fa-check-circle text-success' :
                         insight.type === 'recommendation' ? 'fa-lightbulb text-primary' :
                         insight.type === 'trend' ? 'fa-chart-line text-info' :
                         insight.type === 'prediction' ? 'fa-crystal-ball text-purple' :
                         insight.type === 'security' ? 'fa-shield-alt text-success' :
                         'fa-info-circle text-info';

        return `
            <div class="ai-insight-admin ${priorityClass}">
                <i class="fas ${iconClass} me-2"></i>
                <strong>${insight.title}:</strong> ${insight.content}
            </div>
        `;
    }).join('');

    // Display recommendations
    const recommendations = analytics.recommendations;
    const recommendationsHtml = `
        <div class="ai-insight-admin" style="background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1)); border-left-color: #27ae60;">
            <strong>Best Practices:</strong>
            Optimal coverage: ${recommendations.optimal_coverage} |
            Critical statuses: ${recommendations.critical_statuses.join(', ')} |
            Review: ${recommendations.review_frequency}
        </div>
    `;

    insightsContainer.innerHTML = summaryHtml + insightsHtml + recommendationsHtml;
}

function displayLocalInsights(insights) {
    const insightsContainer = document.getElementById('signatureInsights');

    insightsContainer.innerHTML = insights.map(insight => `
        <div class="ai-insight-admin">
            <strong>${insight.title}:</strong> ${insight.content}
        </div>
    `).join('');
}

function generateSignatureInsights() {
    const selectedStatuses = document.querySelectorAll('.status-checkbox:checked').length;
    const totalStatuses = workOrderStatuses.length;
    const coverage = (selectedStatuses / totalStatuses * 100).toFixed(1);
    
    const insights = [
        {
            title: 'Configuration Coverage',
            content: `You have configured signatures for ${selectedStatuses} out of ${totalStatuses} statuses (${coverage}% coverage).`
        },
        {
            title: 'Recommended Statuses',
            content: 'Consider requiring signatures for critical statuses like COMP (Complete), APPR (Approved), and INPRG (In Progress) for better audit trails.'
        },
        {
            title: 'Future Trend Prediction',
            content: 'Based on industry patterns, signature requirements are typically most effective when applied to 20-30% of status transitions to balance security with usability.'
        }
    ];
    
    // Add specific insights based on selection
    if (selectedStatuses === 0) {
        insights.push({
            title: 'Getting Started',
            content: 'Start by selecting the most critical statuses for your workflow. COMP (Complete) is often a good starting point.'
        });
    } else if (selectedStatuses > 10) {
        insights.push({
            title: 'Optimization Suggestion',
            content: 'You have selected many statuses. Consider focusing on the most critical ones to avoid signature fatigue among users.'
        });
    }
    
    return insights;
}

function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Issue Material Configuration Functions
function initializeIssueMaterialConfig() {
    console.log('🔧 Initializing Issue Material Configuration');

    // Add event listeners for status checkboxes
    document.querySelectorAll('.work-order-status-checkbox, .task-status-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateIssueMaterialStatusSelection(this);
        });
    });
}

function updateIssueMaterialStatusSelection(checkbox) {
    const statusItem = checkbox.closest('.status-item');
    if (checkbox.checked) {
        statusItem.classList.add('enabled');
    } else {
        statusItem.classList.remove('enabled');
    }
}

function loadIssueMaterialConfig() {
    // Load existing configuration from server
    fetch('/api/admin/issue-material-config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Apply loaded configuration
                const config = data.config;

                // Set work order status checkboxes
                config.work_order_statuses.forEach(status => {
                    const checkbox = document.getElementById(`wo-status-${status}`);
                    if (checkbox) {
                        checkbox.checked = true;
                        updateIssueMaterialStatusSelection(checkbox);
                    }
                });

                // Set task status checkboxes
                config.task_statuses.forEach(status => {
                    const checkbox = document.getElementById(`task-status-${status}`);
                    if (checkbox) {
                        checkbox.checked = true;
                        updateIssueMaterialStatusSelection(checkbox);
                    }
                });

                console.log('✅ Issue Material Configuration loaded successfully');
            }
        })
        .catch(error => {
            console.log('No existing issue material configuration found, using defaults');
        });
}

function saveIssueMaterialConfig() {
    // Collect selected work order statuses
    const selectedWorkOrderStatuses = [];
    document.querySelectorAll('.work-order-status-checkbox:checked').forEach(checkbox => {
        selectedWorkOrderStatuses.push(checkbox.value);
    });

    // Collect selected task statuses
    const selectedTaskStatuses = [];
    document.querySelectorAll('.task-status-checkbox:checked').forEach(checkbox => {
        selectedTaskStatuses.push(checkbox.value);
    });

    // Validate that at least one status is selected for each type
    if (selectedWorkOrderStatuses.length === 0) {
        showNotification('Please select at least one work order status', 'warning');
        return;
    }

    if (selectedTaskStatuses.length === 0) {
        showNotification('Please select at least one task status', 'warning');
        return;
    }

    const config = {
        work_order_statuses: selectedWorkOrderStatuses,
        task_statuses: selectedTaskStatuses,
        enabled: true
    };

    // Save configuration
    fetch('/api/admin/issue-material-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Issue Material Configuration saved successfully!', 'success');
            console.log('✅ Issue Material Configuration saved:', data.config);
        } else {
            showNotification('Error saving configuration: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('Network error: ' + error.message, 'danger');
    });
}

function resetIssueMaterialConfig() {
    // Reset to default configuration
    const defaultConfig = {
        work_order_statuses: ['WMATL', 'PISSUE'],
        task_statuses: ['APPR', 'INPRG', 'WMATL', 'PISSUE']
    };

    // Clear all checkboxes first
    document.querySelectorAll('.work-order-status-checkbox, .task-status-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        updateIssueMaterialStatusSelection(checkbox);
    });

    // Set default work order statuses
    defaultConfig.work_order_statuses.forEach(status => {
        const checkbox = document.getElementById(`wo-status-${status}`);
        if (checkbox) {
            checkbox.checked = true;
            updateIssueMaterialStatusSelection(checkbox);
        }
    });

    // Set default task statuses
    defaultConfig.task_statuses.forEach(status => {
        const checkbox = document.getElementById(`task-status-${status}`);
        if (checkbox) {
            checkbox.checked = true;
            updateIssueMaterialStatusSelection(checkbox);
        }
    });

    showNotification('Configuration reset to defaults', 'info');
}
</script>
{% endblock %}
