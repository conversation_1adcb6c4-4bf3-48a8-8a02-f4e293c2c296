{% extends "base.html" %}

{% block title %}Work Order {{ workorder.wonum }} - Details{% endblock %}

{% block extra_css %}
<style>
    .workorder-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .back-button {
        margin-bottom: 20px;
    }

    .workorder-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--header-text-color);
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px var(--shadow-color);
    }

    .workorder-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 300;
    }

    .workorder-header .subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        margin-top: 10px;
    }

    .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    /* Enhanced Tab Navigation */
    .nav-tabs-enhanced {
        border: none;
        background: var(--card-bg);
        border-radius: 12px;
        padding: 8px;
        margin-bottom: 2rem;
        box-shadow: var(--box-shadow);
    }

    .nav-tabs-enhanced .nav-link {
        border: none;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin: 0 4px;
        color: var(--text-color);
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        background: transparent;
    }

    .nav-tabs-enhanced .nav-link:hover {
        background: rgba(var(--primary-color-rgb), 0.1);
        color: var(--text-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--shadow-color);
    }

    .nav-tabs-enhanced .nav-link.active {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--header-text-color);
        box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);
        transform: translateY(-1px);
    }

    .nav-tabs-enhanced .nav-link i {
        font-size: 1.2rem;
        margin-right: 0.75rem;
        transition: transform 0.3s ease;
    }

    .nav-tabs-enhanced .nav-link:hover i {
        transform: scale(1.1);
    }

    .nav-tabs-enhanced .nav-link.active i {
        transform: scale(1.15);
    }

    /* Enhanced Tab Content */
    .tab-content-enhanced {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: var(--box-shadow);
        overflow: hidden;
    }

    .tab-pane-enhanced {
        padding: 2rem;
        min-height: 400px;
    }

    /* Enhanced Detail Rows */
    .detail-row-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
    }

    .detail-row-enhanced:hover {
        background: rgba(var(--primary-color-rgb), 0.05);
        padding-left: 1rem;
        padding-right: 1rem;
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 8px;
    }

    .detail-row-enhanced:last-child {
        border-bottom: none;
    }

    .detail-label-enhanced {
        font-weight: 600;
        color: var(--text-color);
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        min-width: 200px;
        margin-right: 1rem;
    }

    .detail-label-enhanced i {
        font-size: 1.3rem;
        margin-right: 0.75rem;
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }

    .detail-value-enhanced {
        font-size: 1rem;
        color: var(--text-color);
        font-weight: 500;
        flex: 1;
        text-align: right;
        word-wrap: break-word;
    }

    .detail-value-enhanced.empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Task Pagination - Sticky */
    .task-pagination {
        background: var(--card-bg);
        padding: 1.5rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        box-shadow: var(--box-shadow);
        position: sticky;
        top: 0;
        z-index: 1020;
        border: 1px solid var(--border-color);
    }

    /* Sticky Task Header */
    .sticky-task-header {
        position: sticky;
        top: 80px; /* Below the pagination controls */
        z-index: 1019;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--header-text-color);
        padding: 1rem 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 12px rgba(var(--primary-color-rgb), 0.2);
        border-radius: 12px;
        border: 1px solid var(--border-light);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .sticky-task-header:hover {
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    .sticky-task-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .sticky-task-header .task-description {
        margin: 0.5rem 0 0 0;
        font-size: 0.9rem;
        opacity: 0.9;
        line-height: 1.4;
    }

    /* Parent Work Order Section */
    .parent-wo-section {
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        padding-bottom: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .parent-wo-title {
        margin: 0;
        font-weight: 600;
        font-size: 1rem;
        opacity: 0.95;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .parent-wo-description {
        font-size: 0.85rem;
        opacity: 0.85;
        line-height: 1.3;
        margin-top: 0.25rem;
        font-style: italic;
    }

    .task-section h5 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    /* Desktop sticky header adjustments */
    @media (min-width: 769px) {
        .sticky-task-header {
            top: 90px; /* Optimized space for desktop pagination */
        }

        .task-pagination {
            top: 0;
            margin-bottom: 1rem;
        }

        .task-card-enhanced {
            margin-top: 1rem;
        }
    }

    /* Tablet adjustments */
    @media (max-width: 768px) and (min-width: 576px) {
        .sticky-task-header {
            top: 75px;
        }

        .sticky-task-header h5 {
            font-size: 1.05rem;
        }
    }

    .task-card-enhanced {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
    }

    .task-card-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #007bff;
    }

    .task-header-enhanced {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 1.5rem;
        position: relative;
    }

    .task-header-enhanced::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
    }

    .task-body-enhanced {
        padding: 1.5rem;
    }

    .task-actions-enhanced {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .task-action-btn {
        flex: 1;
        min-width: 120px;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .task-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .pagination-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 0.6rem 1.2rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
        display: flex;
        align-items: center;
        gap: 0.4rem;
        position: relative;
        overflow: hidden;
        min-width: 100px;
        justify-content: center;
    }

    .pagination-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .pagination-btn:hover:not(:disabled)::before {
        left: 100%;
    }

    .pagination-btn:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.35);
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }

    .pagination-btn:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
    }

    .pagination-btn:disabled {
        background: linear-gradient(135deg, #e9ecef, #dee2e6);
        color: #6c757d;
        cursor: not-allowed;
        opacity: 0.7;
        transform: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .pagination-btn:disabled::before {
        display: none;
    }

    .pagination-info {
        background: rgba(255, 255, 255, 0.95);
        padding: 0.6rem 1.2rem;
        border-radius: 16px;
        font-weight: 600;
        font-size: 0.85rem;
        color: #495057;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
        white-space: nowrap;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        /* Ensure single scrollbar for entire page */
        body {
            overflow-x: hidden;
            overflow-y: auto;
        }

        .workorder-detail-container {
            padding: 15px;
            /* Ensure container doesn't create its own scroll context */
            overflow: visible;
        }

        .workorder-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .workorder-header h1 {
            font-size: 1.8rem;
        }

        .nav-tabs-enhanced {
            padding: 6px;
            margin-bottom: 1rem;
        }

        .nav-tabs-enhanced .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            margin: 0 2px;
        }

        .nav-tabs-enhanced .nav-link i {
            font-size: 1.1rem;
            margin-right: 0.5rem;
        }

        .tab-pane-enhanced {
            padding: 1.5rem;
            min-height: 300px;
        }

        .detail-row-enhanced {
            flex-direction: column;
            align-items: flex-start;
            padding: 1.25rem 0;
        }

        .detail-label-enhanced {
            font-size: 1.2rem;
            min-width: auto;
            margin-right: 0;
            margin-bottom: 0.5rem;
        }

        .detail-label-enhanced i {
            font-size: 1.4rem;
            margin-right: 0.75rem;
        }

        .detail-value-enhanced {
            font-size: 1.1rem;
            text-align: left;
            padding-left: 2.5rem;
        }

        .task-header-enhanced {
            padding: 1.25rem;
        }

        .task-body-enhanced {
            padding: 1.25rem;
        }

        .task-actions-enhanced {
            flex-direction: column;
            padding: 1rem;
        }

        .task-action-btn {
            min-width: 100%;
            padding: 1rem;
            font-size: 1rem;
        }

        .pagination-controls {
            flex-direction: row;
            gap: 0.5rem;
            justify-content: space-between;
            margin-top: 0.75rem;
        }

        .pagination-btn {
            padding: 0.4rem 0.6rem;
            font-size: 0.75rem;
            flex: 1;
            max-width: 85px;
            min-width: 70px;
            border-radius: 12px;
        }

        .pagination-btn i {
            font-size: 0.65rem;
        }

        .pagination-info {
            padding: 0.4rem 0.6rem;
            font-size: 0.7rem;
            text-align: center;
            flex: 0 0 auto;
            white-space: nowrap;
            border-radius: 10px;
        }

        /* Mobile sticky header adjustments - Optimized for single scroll */
        .task-pagination {
            padding: 0.5rem;
            top: 0;
            margin-bottom: 0.25rem;
        }

        .sticky-task-header {
            top: 50px; /* Reduced for more compact layout */
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.25rem;
        }

        .sticky-task-header h5 {
            font-size: 1rem;
        }

        .sticky-task-header .task-description {
            font-size: 0.85rem;
        }

        /* Ensure content doesn't hide behind sticky headers - Compact spacing */
        .task-card-enhanced {
            margin-top: 0.25rem;
            margin-bottom: 1rem; /* Reduced from default 1.5rem */
        }

        /* Hide parent work order section on mobile to save space */
        .parent-wo-section {
            display: none !important;
        }

        /* Mobile scroll optimization - Remove inner scrollbars */
        .tab-content-enhanced {
            /* Removed max-height and overflow-y to eliminate inner scrollbar */
            -webkit-overflow-scrolling: touch;
        }

        .task-card-enhanced {
            margin-bottom: 2rem;
        }
    }

    /* Enhanced Status Badge Styles with Better Contrast */
    .status-badge {
        font-weight: 600;
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-ASSIGN {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    }
    .status-APPR {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }
    .status-READY {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        color: white;
        box-shadow: 0 2px 4px rgba(32, 201, 151, 0.3);
    }
    .status-INPRG {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
    }
    .status-WMATL {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WAPPR {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WGOVT {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WSERV {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WSCH {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-COMP {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }
    .status-CLOSE {
        background: linear-gradient(135deg, #343a40, #23272b);
        color: white;
        box-shadow: 0 2px 4px rgba(52, 58, 64, 0.3);
    }
    .status-PACK {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }

    /* Elegant Status Indicator Styles */
    .status-indicator-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.25rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .status-indicator-container:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .status-indicator-icon {
        font-size: 1.2rem !important;
        transition: all 0.3s ease;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    /* Status-specific colors matching labor card scheme exactly */
    .status-indicator-icon.status-ASSIGN {
        color: #007bff;
        text-shadow: 0 0 10px rgba(0, 123, 255, 0.8);
    }
    .status-indicator-icon.status-APPR {
        color: #28a745;
        text-shadow: 0 0 10px rgba(40, 167, 69, 0.8);
    }
    .status-indicator-icon.status-READY {
        color: #20c997;
        text-shadow: 0 0 10px rgba(32, 201, 151, 0.8);
    }
    .status-indicator-icon.status-INPRG {
        color: #fd7e14;
        text-shadow: 0 0 10px rgba(253, 126, 20, 0.8);
        animation: pulse 2s infinite;
    }
    .status-indicator-icon.status-WMATL {
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
    .status-indicator-icon.status-WAPPR {
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
    .status-indicator-icon.status-WGOVT {
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
    .status-indicator-icon.status-WSERV {
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
    .status-indicator-icon.status-WSCH {
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
    .status-indicator-icon.status-COMP {
        color: #28a745;
        text-shadow: 0 0 10px rgba(40, 167, 69, 0.8);
    }
    .status-indicator-icon.status-CLOSE {
        color: #6c757d;
        text-shadow: 0 0 10px rgba(108, 117, 125, 0.8);
    }
    .status-indicator-icon.status-PACK {
        color: #6c757d;
        text-shadow: 0 0 10px rgba(108, 117, 125, 0.8);
    }

    /* Pulse animation for in-progress tasks */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
    }
    .status-DEFER {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }



    .task-action-section {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 2px solid #e9ecef;
    }

    /* Enhanced Material Card Styles */
    .material-card-enhanced {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--box-shadow);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .material-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #28a745, #17a2b8);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .material-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #007bff;
    }

    .material-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .material-card-header-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .material-item-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .material-icon {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .material-card-enhanced:hover .material-icon {
        transform: rotate(10deg) scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .material-itemnum-enhanced {
        font-weight: 700;
        color: #007bff;
        font-size: 1.1rem;
        margin: 0;
    }

    .material-description-enhanced {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
        line-height: 1.4;
    }

    .material-qty-badge {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        flex-wrap: wrap;
    }

    .material-qty-badge strong {
        color: white;
        font-weight: 600;
    }

    .material-qty-badge .text-muted {
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 0.85rem;
    }

    .material-card-enhanced:hover .material-qty-badge {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .material-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .material-detail-item-enhanced {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .material-detail-item-enhanced:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .material-detail-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        transition: all 0.3s ease;
    }

    .material-detail-item-enhanced:hover .material-detail-icon {
        transform: scale(1.1);
    }

    .material-detail-content {
        flex: 1;
    }

    .material-detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .material-detail-value-enhanced {
        color: #212529;
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    /* Enhanced Labor Card Styles */
    .labor-card-enhanced {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--box-shadow);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .labor-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #fd7e14, #ffc107, #20c997);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .labor-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #fd7e14;
    }

    .labor-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .labor-card-header-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .labor-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .labor-icon {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .labor-card-enhanced:hover .labor-icon {
        transform: rotate(-10deg) scale(1.1);
        box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
    }

    .labor-code-enhanced {
        font-weight: 700;
        color: #fd7e14;
        font-size: 1.1rem;
        margin: 0;
    }

    .labor-craft-enhanced {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    .labor-hours-badge {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .labor-card-enhanced:hover .labor-hours-badge {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    }

    .labor-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .labor-detail-item-enhanced {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .labor-detail-item-enhanced:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .labor-detail-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        transition: all 0.3s ease;
    }

    .labor-detail-item-enhanced:hover .labor-detail-icon {
        transform: scale(1.1);
    }

    .labor-detail-content {
        flex: 1;
    }

    .labor-detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .labor-detail-value-enhanced {
        color: #212529;
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    .labor-actions-enhanced {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .labor-action-btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .labor-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .priority-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .priority-1 { background-color: #e74c3c; }
    .priority-2 { background-color: #f39c12; }
    .priority-3 { background-color: #f1c40f; }
    .priority-4 { background-color: #2ecc71; }
    .priority-5 { background-color: #95a5a6; }

    /* Status Change Icon Styles */
    .status-change-icon {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        width: 28px;
        height: 28px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .status-change-icon:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .status-change-icon i {
        font-size: 0.8rem;
    }

    /* Status Change Modal Styles */
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-bottom: none;
        border-radius: 12px 12px 0 0;
        padding: 1rem 1.25rem;
    }

    .modal-title {
        font-weight: 600;
        font-size: 1rem;
        margin: 0;
    }

    .modal-body {
        padding: 1.25rem;
        background: var(--card-bg);
    }

    .modal-footer {
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 12px 12px;
        padding: 1rem 1.25rem;
    }

    .btn-close {
        filter: brightness(0) invert(1);
        opacity: 0.8;
    }

    .btn-close:hover {
        opacity: 1;
    }

    #currentStatusDisplay {
        display: inline-block;
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }

    /* Sticky Resource Tabs Styles */
    .sticky-resource-tabs {
        position: sticky;
        top: 140px; /* Below the sticky task header */
        z-index: 1018;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 1rem;
    }

    .resource-tabs {
        border: none;
        background: transparent;
        margin-bottom: 0;
    }

    .resource-tabs .nav-link {
        border: none;
        border-radius: 6px 6px 0 0;
        padding: 0.75rem 1rem;
        color: #6c757d;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: transparent;
        margin: 0 2px;
    }

    .resource-tabs .nav-link:hover {
        background: rgba(255, 255, 255, 0.7);
        color: #495057;
        transform: translateY(-1px);
    }

    /* Search buttons in tabs - Desktop */
    .resource-tabs .search-inventory-btn,
    .resource-tabs .search-labor-btn {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.75rem !important;
        border: 2px solid #ffffff !important;
        background: rgba(0, 123, 255, 0.8) !important;
        color: white !important;
        transition: all 0.2s ease;
        min-width: 32px;
        height: 28px;
        display: flex !important;
        align-items: center;
        justify-content: center;
        border-radius: 4px !important;
    }

    .resource-tabs .search-inventory-btn:hover,
    .resource-tabs .search-labor-btn:hover {
        background: #0056b3 !important;
        border-color: #ffffff !important;
        transform: scale(1.1);
        color: white !important;
    }

    /* Refresh buttons styling */
    .refresh-materials-btn,
    .refresh-labor-btn {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.8rem !important;
        border: 1px solid #6c757d !important;
        background: #f8f9fa !important;
        color: #495057 !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
    }

    .refresh-materials-btn:hover,
    .refresh-labor-btn:hover {
        background: #e9ecef !important;
        border-color: #495057 !important;
        color: #212529 !important;
        transform: translateY(-1px);
    }

    .resource-tabs .nav-link.active {
        background: var(--card-bg);
        color: var(--primary-color);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid #007bff;
    }

    .resource-tab-content {
        background: var(--card-bg);
        border-radius: 0 0 8px 8px;
        min-height: 200px;
    }

    .resource-content-area {
        padding: 1rem;
    }

    .resource-content-area h6 {
        color: #495057;
        font-weight: 600;
    }

    .load-materials-btn, .load-labor-btn {
        transition: all 0.3s ease;
    }

    .load-materials-btn:hover, .load-labor-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* Resource Pagination Controls */
    .resource-pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .pagination-nav-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border: 1px solid #007bff;
        background: var(--card-bg);
        color: #007bff;
    }

    .pagination-nav-btn:hover:not(:disabled) {
        background: #007bff;
        color: white;
        transform: scale(1.05);
    }

    .pagination-nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        border-color: #dee2e6;
        color: #6c757d;
    }

    .pagination-info-small {
        font-size: 0.85rem;
        font-weight: 600;
        color: #495057;
        min-width: 60px;
        text-align: center;
    }

    .detail-card {
        background: var(--card-bg);
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }


    .detail-card-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    .detail-card-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
        align-items: center;
    }

    .detail-label {
        font-weight: 600;
        color: #6c757d;
        min-width: 150px;
        margin-right: 15px;
    }

    .detail-value {
        color: #495057;
        flex: 1;
    }

    .detail-value.empty {
        color: #adb5bd;
        font-style: italic;
    }

    .performance-info {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 5px;
        padding: 10px;
        margin-top: 20px;
        font-size: 0.9rem;
        color: #155724;
    }

    .task-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        margin-bottom: 15px;
        overflow: hidden;
        transition: box-shadow 0.2s ease;
    }

    .task-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .task-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .task-header .d-flex {
        min-height: 40px;
    }

    .task-body {
        padding: 15px 20px;
    }

    .task-title {
        font-weight: 600;
        color: #495057;
        margin: 0;
        flex: 1;
    }

    .task-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }





    .task-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }

    .task-info-item {
        display: flex;
        flex-direction: column;
    }

    .task-info-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 2px;
    }

    .task-info-value {
        font-weight: 500;
        color: #495057;
    }

    .no-tasks {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
        font-style: italic;
    }

    .icon {
        margin-right: 8px;
        color: #6c757d;
    }

    .planned-materials-section {
        border-top: 1px solid #dee2e6;
        padding-top: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }

    .planned-materials-section h6 {
        color: #495057;
        font-weight: 600;
    }

    .btn-group .btn {
        font-size: 0.875rem;
    }

    .materials-content {
        /* Removed max-height and overflow-y for mobile single-scroll optimization */
    }

    /* Desktop List View */
    .materials-desktop-view {
        display: block;
    }

    .materials-mobile-view {
        display: none;
    }

    .material-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.85rem;
    }

    .material-table th,
    .material-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
        vertical-align: top;
    }

    .material-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .material-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .material-itemnum {
        font-weight: 600;
        color: #0d6efd;
    }

    .material-itemnum strong {
        color: #495057;
        font-weight: 500;
        margin-right: 0.5rem;
    }

    .material-description {
        max-width: 200px;
        word-wrap: break-word;
    }

    .material-description strong {
        color: #495057;
        font-weight: 500;
        margin-right: 0.5rem;
    }

    .material-qty strong {
        color: #495057;
        font-weight: 500;
        margin-right: 0.25rem;
    }

    .material-qty .text-muted {
        font-size: 0.9rem;
        margin-left: 0.5rem;
    }

    .material-cost {
        font-weight: 500;
        color: #198754;
    }

    .material-qty {
        font-weight: 500;
        color: #fd7e14;
    }

    /* Mobile Card View */
    @media (max-width: 768px) {
        .materials-desktop-view {
            display: none;
        }

        .materials-mobile-view {
            display: block;
        }

        /* Ensure no inner scrollbars on mobile */
        .materials-content {
            max-height: none !important;
            overflow-y: visible !important;
        }

        .material-card {
            background: var(--card-bg);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .material-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .material-card-itemnum {
            font-weight: 600;
            color: #0d6efd;
            font-size: 1rem;
        }

        .material-card-qty {
            font-weight: 500;
            color: #fd7e14;
            font-size: 0.9rem;
        }

        .material-card-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .material-card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.8rem;
        }

        .material-card-detail {
            display: flex;
            flex-direction: column;
        }

        .material-card-detail-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 2px;
        }

        .material-card-detail-value {
            color: #6c757d;
        }
    }

    .material-detail-item {
        display: flex;
        flex-direction: column;
    }

    .material-detail-label {
        color: #6c757d;
        font-size: 0.75rem;
        margin-bottom: 1px;
    }

    .material-detail-value {
        color: #495057;
        font-weight: 500;
    }

    .load-materials-btn {
        transition: all 0.2s ease;
    }

    .load-materials-btn:hover {
        transform: translateY(-1px);
    }

    .materials-loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    .materials-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        text-align: center;
    }

    .materials-empty {
        text-align: center;
        padding: 15px;
        color: #6c757d;
        font-style: italic;
    }

    /* Material Request Modal Styles */
    .item-result-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 8px;
        transition: background-color 0.2s ease;
    }

    .item-result-card:hover {
        background: #e9ecef;
    }

    .item-results-container {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        background: var(--card-bg);
    }

    .request-material-btn {
        transition: all 0.2s ease;
    }

    .request-material-btn:hover {
        transform: translateY(-1px);
    }

    /* ===== COMPREHENSIVE RESPONSIVE DESIGN ===== */

    /* Extra Small Devices (phones, 576px and down) */
    @media (max-width: 575.98px) {
        .workorder-detail-container {
            padding: 8px;
            margin: 0;
        }

        .workorder-header {
            padding: 15px;
            margin-bottom: 15px;
        }

        .workorder-header .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 10px;
        }

        .workorder-header h1 {
            font-size: 1.5rem;
            line-height: 1.3;
        }

        .workorder-header .subtitle {
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .status-badge {
            padding: 6px 12px;
            font-size: 0.75rem;
        }

        .detail-card {
            margin-bottom: 15px;
        }

        .detail-card-header {
            padding: 12px 15px;
            font-size: 0.9rem;
        }

        .detail-card-body {
            padding: 15px;
        }

        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .detail-label {
            min-width: auto;
            margin-bottom: 4px;
            font-size: 0.85rem;
            font-weight: 700;
        }

        .detail-value {
            font-size: 0.9rem;
        }

        /* Task Cards Mobile */
        .task-card {
            margin-bottom: 12px;
        }

        .task-header {
            padding: 12px 15px;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .task-title {
            font-size: 0.9rem;
            line-height: 1.4;
            margin: 0;
            order: 2;
        }

        .toggle-task-btn {
            order: 1;
            align-self: flex-end;
            margin-bottom: 5px;
        }

        .task-actions {
            order: 3;
            width: 100%;
            flex-direction: column;
            gap: 8px;
        }



        .task-body {
            padding: 12px 15px;
        }

        .task-info {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .task-info-item {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .task-info-label {
            font-size: 0.75rem;
        }

        .task-info-value {
            font-size: 0.85rem;
        }

        /* Materials Section Mobile */
        .planned-materials-section {
            padding: 12px;
            margin-top: 12px;
        }

        .planned-materials-section .d-flex {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .planned-materials-section h6 {
            font-size: 0.9rem;
        }

        .btn-group {
            width: 100%;
            flex-direction: column;
        }

        .btn-group .btn {
            width: 100%;
            margin-bottom: 5px;
            font-size: 0.8rem;
        }

        /* Back Button Mobile */
        .back-button {
            margin-bottom: 15px;
        }

        .back-button .btn {
            font-size: 0.85rem;
            padding: 8px 12px;
        }

        /* Extra small device sticky header - Ultra compact */
        .task-pagination {
            padding: 0.4rem;
            margin-bottom: 0.2rem;
        }

        .sticky-task-header {
            top: 45px; /* More compact for extra small screens */
            padding: 0.4rem 0.6rem;
            margin-bottom: 0.2rem;
        }

        .sticky-task-header h5 {
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .sticky-task-header .task-description {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        /* Keep the main header row horizontal on mobile like desktop */
        .sticky-task-header .d-flex.justify-content-between {
            flex-direction: row !important;
            align-items: center;
            gap: 0.5rem;
        }

        /* Only apply column layout to other flex containers if needed */
        .sticky-task-header .d-flex:not(.justify-content-between) {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .sticky-task-header .status-badge {
            font-size: 0.7rem;
            padding: 4px 8px;
        }

        /* Mobile status change icon - compact design */
        .status-change-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
        }

        .status-change-icon i {
            font-size: 0.7rem;
        }

        /* Mobile sticky resource tabs */
        .sticky-resource-tabs {
            top: 100px; /* Adjusted for mobile sticky header */
            margin-bottom: 0.5rem;
            border-radius: 6px;
        }

        .resource-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }

        /* Search buttons in tabs - Mobile */
        .resource-tabs .search-inventory-btn,
        .resource-tabs .search-labor-btn {
            padding: 0.2rem 0.4rem !important;
            font-size: 0.7rem !important;
            border: 2px solid #ffffff !important;
            background: rgba(0, 123, 255, 0.8) !important;
            color: white !important;
            min-width: 28px;
            height: 24px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            border-radius: 4px !important;
        }

        .resource-tabs .search-inventory-btn:hover,
        .resource-tabs .search-labor-btn:hover {
            background: #0056b3 !important;
            border-color: #ffffff !important;
            color: white !important;
        }

        /* Refresh buttons - Mobile */
        .refresh-materials-btn,
        .refresh-labor-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.7rem !important;
            border: 1px solid #6c757d !important;
            background: #f8f9fa !important;
            color: #495057 !important;
            border-radius: 4px !important;
        }

        .refresh-materials-btn:hover,
        .refresh-labor-btn:hover {
            background: #e9ecef !important;
            border-color: #495057 !important;
            color: #212529 !important;
        }

        .resource-content-area {
            padding: 0.75rem;
        }

        .task-card-enhanced {
            margin-top: 0.2rem;
            margin-bottom: 0.8rem; /* Ultra compact spacing */
        }
    }

    /* Small Devices (landscape phones, 576px and up) */
    @media (min-width: 576px) and (max-width: 767.98px) {
        .workorder-detail-container {
            padding: 12px;
        }

        .workorder-header {
            padding: 20px;
        }

        .workorder-header h1 {
            font-size: 1.8rem;
        }

        .task-header {
            flex-wrap: wrap;
            gap: 10px;
        }

        .task-title {
            flex: 1;
            min-width: 200px;
        }

        .task-actions {
            flex-wrap: wrap;
            gap: 8px;
        }



        .task-info {
            grid-template-columns: repeat(2, 1fr);
        }

        .planned-materials-section .d-flex {
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn-group {
            flex-wrap: wrap;
        }
    }

    /* Medium Devices (tablets, 768px and up) */
    @media (min-width: 768px) and (max-width: 991.98px) {
        .workorder-detail-container {
            padding: 15px;
        }

        .workorder-header h1 {
            font-size: 2.2rem;
        }

        .task-info {
            grid-template-columns: repeat(2, 1fr);
        }

        .task-actions {
            flex-wrap: wrap;
        }
    }

    /* Large Devices (desktops, 992px and up) */
    @media (min-width: 992px) and (max-width: 1199.98px) {
        .workorder-detail-container {
            max-width: 1000px;
        }

        .task-info {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* Extra Large Devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .workorder-detail-container {
            max-width: 1200px;
        }

        .task-info {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    /* Modal Responsive */
    @media (max-width: 575.98px) {
        .modal-dialog {
            margin: 10px;
            max-width: calc(100% - 20px);
        }

        .modal-body {
            padding: 15px;
        }

        .modal-header {
            padding: 15px;
        }

        .modal-footer {
            padding: 15px;
            flex-direction: column;
            gap: 10px;
        }

        .modal-footer .btn {
            width: 100%;
        }

        .row .col-md-8,
        .row .col-md-4,
        .row .col-md-6 {
            margin-bottom: 15px;
        }
    }

    /* Attachment Styles */
    .attachments-container {
        padding: 1.5rem;
    }

    .attachment-card {
        border: 1px solid var(--border-color);
        border-radius: 12px;
        transition: all 0.3s ease;
        background: var(--card-bg);
    }

    .attachment-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px var(--shadow-color);
        border-color: var(--primary-color);
    }

    .attachment-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background: rgba(var(--primary-color-rgb), 0.1);
        border-radius: 8px;
        flex-shrink: 0;
    }

    .attachment-meta {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .attachment-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .attachment-card .card-footer {
        border-top: 1px solid var(--border-color);
        padding: 0.75rem 1rem;
    }

    .attachment-card .btn-group .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    /* Attachment Modal Styles */
    .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    }

    .modal-header {
        border-bottom: 1px solid var(--border-color);
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 12px 12px 0 0;
    }

    .modal-header .btn-close {
        filter: invert(1);
    }

    .modal-footer {
        border-top: 1px solid var(--border-color);
        background: var(--card-bg);
        border-radius: 0 0 12px 12px;
    }

    /* File input styling */
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }

    /* Camera Capture Styles */
    .attachment-source-buttons {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .attachment-source-btn {
        flex: 1;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .attachment-source-btn.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .attachment-source-btn:hover:not(.active) {
        background-color: rgba(var(--primary-color-rgb), 0.1);
        border-color: var(--primary-color);
    }

    .attachment-section {
        transition: all 0.3s ease;
    }

    .camera-container {
        text-align: center;
    }

    .camera-preview-wrapper {
        position: relative;
        display: inline-block;
        border-radius: 12px;
        overflow: hidden;
        background: #000;
        margin-bottom: 1rem;
        max-width: 100%;
    }

    .camera-preview {
        width: 100%;
        max-width: 400px;
        height: 300px;
        object-fit: cover;
        display: block;
    }

    .captured-image-preview {
        position: relative;
        display: inline-block;
    }

    .captured-image-preview img {
        width: 100%;
        max-width: 400px;
        height: 300px;
        object-fit: cover;
        border-radius: 12px;
    }

    .captured-image-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .camera-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .camera-controls .btn {
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .camera-controls .btn-lg {
        width: 60px;
        height: 60px;
        font-size: 1.2rem;
    }

    .camera-status {
        padding: 0.5rem 1rem;
        background: rgba(var(--primary-color-rgb), 0.1);
        border-radius: 8px;
        color: var(--primary-color);
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    /* Responsive attachment grid */
    @media (max-width: 768px) {
        .attachments-container {
            padding: 1rem;
        }

        .attachment-actions {
            flex-direction: column;
            gap: 0.25rem;
        }

        .attachment-actions .btn {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
        }

        /* Mobile attachment navigation */
        .mobile-attachment-nav {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
        }

        .mobile-attachment-nav .btn {
            min-width: 80px;
        }

        .mobile-attachments-container .attachment-card {
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        /* Ensure single attachment takes full width on mobile */
        .mobile-attachments-container > div {
            width: 100%;
        }

        /* Mobile camera styles */
        .attachment-source-buttons {
            flex-direction: column;
            gap: 0.75rem;
        }

        .camera-preview {
            max-width: 100%;
            height: 250px;
        }

        .captured-image-preview img {
            max-width: 100%;
            height: 250px;
        }

        .camera-controls {
            gap: 0.75rem;
        }

        .camera-controls .btn {
            width: 45px;
            height: 45px;
        }

        .camera-controls .btn-lg {
            width: 55px;
            height: 55px;
        }

        /* Touch-friendly camera controls on mobile */
        .camera-controls {
            padding: 1rem 0;
        }

        .camera-status {
            font-size: 0.8rem;
            padding: 0.75rem;
        }

        /* Ensure modal is properly sized on mobile */
        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-content {
            max-height: 90vh;
            overflow-y: auto;
        }
    }

    /* Signature Pad Styles */
    .signature-pad-container {
        margin-bottom: 1rem;
    }

    .signature-pad-wrapper {
        position: relative;
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        background: #fafafa;
        overflow: hidden;
        transition: border-color 0.3s ease;
    }

    .signature-pad-wrapper:hover {
        border-color: var(--primary-color);
    }

    .signature-pad-wrapper.active {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }

    #signaturePad {
        display: block;
        cursor: crosshair;
        background: white;
        width: 100%;
        height: 200px;
    }

    .signature-pad-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .signature-pad-overlay.hidden {
        opacity: 0;
    }

    .signature-pad-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .signature-info {
        background: rgba(var(--info-color-rgb), 0.1);
        border: 1px solid rgba(var(--info-color-rgb), 0.2);
        border-radius: var(--border-radius);
        padding: 1rem;
    }

    /* Mobile signature pad adjustments */
    @media (max-width: 768px) {
        #signaturePad {
            height: 150px;
        }

        .signature-pad-controls {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }

    /* Loading Overlay Styles */
    .workorder-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(248, 249, 250, 0.95);
        z-index: 1018;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
    }

    .workorder-loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    [data-bs-theme="dark"] .workorder-loading-overlay {
        background: rgba(33, 37, 41, 0.95);
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div id="workorderLoadingOverlay" class="workorder-loading-overlay" style="display: none;">
    <div class="workorder-loading-spinner"></div>
    <div class="h5 text-primary mb-2">Loading Work Order Details</div>
    <div class="text-muted">Please wait while we fetch the information...</div>
</div>

<div class="workorder-detail-container" data-site-id="{{ user_site_id if user_site_id else 'UNKNOWN' }}">
    <!-- Back Button -->
    <div class="back-button">
        <a href="{{ url_for('enhanced_workorders') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Work Orders
        </a>
    </div>

    <!-- Work Order Header with Navigation -->
    <div class="workorder-header">
        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h1 class="mb-2">
                    <span class="priority-indicator priority-{{ workorder.priority or 3 }}"></span>
                    <span class="d-inline d-sm-inline">Work Order {{ workorder.wonum }}</span>
                </h1>
                <div class="subtitle">{{ workorder.description or 'No description available' }}</div>
            </div>
            <div class="mt-2 mt-sm-0">
                <span class="status-badge status-{{ workorder.status }}">{{ workorder.status or 'Unknown' }}</span>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Header for Work Order Records -->
    <div class="mobile-workorder-nav-header d-md-none" id="workorderNavHeader" style="display: none;">
        <div class="mobile-nav-counter">
            <span class="current-position" id="currentWOPosition">1</span>
            <span class="separator">of</span>
            <span class="total-workorders" id="totalWorkOrders">1</span>
        </div>
        <div class="mobile-nav-buttons">
            <button class="btn btn-outline-secondary btn-sm" onclick="navigateWorkOrderDetail(-1)" id="prevWOBtn" disabled>
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="navigateWorkOrderDetail(1)" id="nextWOBtn" disabled>
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <!-- Mobile-Optimized Diary-Style Navigation -->
    <div class="workorder-diary-view">
        <div class="diary-sidebar">
            <div class="diary-tabs" role="tablist">
                <button class="diary-tab active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab"
                        style="--tab-bg: #e3f2fd; --tab-border: #2196f3; --tab-text: #1976d2;">
                    <div class="diary-tab-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="diary-tab-label">Basic</div>
                </button>

                <button class="diary-tab" id="assignment-tab" data-bs-toggle="tab" data-bs-target="#assignment" type="button" role="tab"
                        style="--tab-bg: #e8f5e8; --tab-border: #4caf50; --tab-text: #388e3c;">
                    <div class="diary-tab-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="diary-tab-label">Assignment</div>
                </button>

                <button class="diary-tab" id="location-tab" data-bs-toggle="tab" data-bs-target="#location" type="button" role="tab"
                        style="--tab-bg: #fff3e0; --tab-border: #ff9800; --tab-text: #f57c00;">
                    <div class="diary-tab-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="diary-tab-label">Location</div>
                </button>

                <button class="diary-tab" id="technical-tab" data-bs-toggle="tab" data-bs-target="#technical" type="button" role="tab"
                        style="--tab-bg: #fce4ec; --tab-border: #e91e63; --tab-text: #c2185b;">
                    <div class="diary-tab-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="diary-tab-label">Technical</div>
                </button>

                <button class="diary-tab" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab"
                        style="--tab-bg: #f3e5f5; --tab-border: #9c27b0; --tab-text: #7b1fa2;">
                    <div class="diary-tab-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="diary-tab-label">Tasks</div>
                    {% if tasks %}
                        <div class="diary-tab-badge">{{ tasks|length }}</div>
                    {% endif %}
                </button>

                <button class="diary-tab" id="attachments-tab" data-bs-toggle="tab" data-bs-target="#attachments" type="button" role="tab"
                        style="--tab-bg: #e0f2f1; --tab-border: #009688; --tab-text: #00695c;">
                    <div class="diary-tab-icon">
                        <i class="fas fa-paperclip"></i>
                    </div>
                    <div class="diary-tab-label">Files</div>
                    <div class="diary-tab-badge" id="attachments-count">0</div>
                </button>
            </div>
        </div>

        <div class="diary-content">
            <div class="tab-content" id="workorderTabContent">
                <!-- Basic Information Tab -->
                <div class="tab-pane fade show active mobile-tab-panel" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                    <div class="mobile-tab-header">
                        <div class="mobile-tab-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <h6 class="mobile-tab-title">Basic Information</h6>
                    </div>
                    <div class="mobile-detail-fields">
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Work Order Number</div>
                                <div class="mobile-field-value">{{ workorder.wonum }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Description</div>
                                <div class="mobile-field-value">{{ workorder.description or 'No description available' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Status</div>
                                <div class="mobile-field-value">
                                    <span class="status-badge status-{{ workorder.status }}">{{ workorder.status or 'Unknown' }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Priority</div>
                                <div class="mobile-field-value">
                                    <span class="priority-indicator priority-{{ workorder.priority or 3 }}"></span>
                                    {{ workorder.priority or 'Not set' }}
                                </div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Site</div>
                                <div class="mobile-field-value">{{ workorder.siteid or user_site_id }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assignment & Scheduling Tab -->
                <div class="tab-pane fade mobile-tab-panel" id="assignment" role="tabpanel" aria-labelledby="assignment-tab">
                    <div class="mobile-tab-header">
                        <div class="mobile-tab-icon">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <h6 class="mobile-tab-title">Assignment & Scheduling</h6>
                    </div>
                    <div class="mobile-detail-fields">
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Assigned To</div>
                                <div class="mobile-field-value">{{ workorder.assignedto or 'Not assigned' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Target Start</div>
                                <div class="mobile-field-value">{{ workorder.targetstart or 'Not scheduled' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-stop-circle"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Target Finish</div>
                                <div class="mobile-field-value">{{ workorder.targetfinish or 'Not scheduled' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Report Date</div>
                                <div class="mobile-field-value">{{ workorder.reportdate or 'Not reported' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Location & Asset Tab -->
                <div class="tab-pane fade mobile-tab-panel" id="location" role="tabpanel" aria-labelledby="location-tab">
                    <div class="mobile-tab-header">
                        <div class="mobile-tab-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h6 class="mobile-tab-title">Location & Asset</h6>
                    </div>
                    <div class="mobile-detail-fields">
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-map-pin"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Location</div>
                                <div class="mobile-field-value">{{ workorder.location or 'No location specified' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Asset Number</div>
                                <div class="mobile-field-value">{{ workorder.assetnum or 'No asset assigned' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical Details Tab -->
                <div class="tab-pane fade mobile-tab-panel" id="technical" role="tabpanel" aria-labelledby="technical-tab">
                    <div class="mobile-tab-header">
                        <div class="mobile-tab-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h6 class="mobile-tab-title">Technical Details</h6>
                    </div>
                    <div class="mobile-detail-fields">
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Work Type</div>
                                <div class="mobile-field-value">{{ workorder.worktype or 'Not specified' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Classification</div>
                                <div class="mobile-field-value">{{ workorder.woclass or 'Not specified' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Reported By</div>
                                <div class="mobile-field-value">{{ workorder.reportedby or 'Unknown' }}</div>
                            </div>
                        </div>
                        <div class="mobile-detail-field">
                            <div class="mobile-field-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="mobile-field-content">
                                <div class="mobile-field-label">Long Description</div>
                                <div class="mobile-field-value">{{ workorder.description_longdescription or 'No detailed description available' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

        <!-- Tasks Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
            {% if tasks %}
                <!-- Task Pagination Controls -->
                <div class="task-pagination">
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevTaskBtn" onclick="previousTask()" disabled>
                            <i class="fas fa-chevron-left"></i>Previous
                        </button>
                        <div class="pagination-info">
                            <span id="currentTaskInfo">Task 1 of {{ tasks|length }}</span>
                        </div>
                        <button class="pagination-btn" id="nextTaskBtn" onclick="nextTask()" {% if tasks|length <= 1 %}disabled{% endif %}>
                            Next<i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Task Container -->
                <div id="taskContainer">
                    <!-- Tasks will be dynamically loaded here by JavaScript -->
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Tasks Found</h5>
                    <p class="text-muted">This work order doesn't have any associated tasks.</p>
                </div>
            {% endif %}
        </div>

        <!-- Attachments Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="attachments" role="tabpanel" aria-labelledby="attachments-tab">
            <div class="attachments-container">
                <!-- Attachments Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">
                        <i class="fas fa-paperclip me-2"></i>Work Order Attachments
                    </h5>
                    <div class="attachment-actions">
                        <button class="btn btn-primary btn-sm" onclick="openAddAttachmentModal()">
                            <i class="fas fa-plus me-1"></i>Add Attachment
                        </button>
                    </div>
                </div>

                <!-- Attachments List -->
                <div id="attachments-list">
                    <div class="text-center py-5">
                        <i class="fas fa-paperclip fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Work Order Attachments</h5>
                        <p class="text-muted">Click "Load Attachments" to view all attachments for this work order.</p>
                        <button class="btn btn-outline-primary load-attachments-btn" onclick="loadAttachments()">
                            <i class="fas fa-download me-1"></i>Load Attachments
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Information -->
    <div class="performance-info mt-4 p-3 bg-light rounded">
        <strong>Performance:</strong>
        Loaded in {{ "%.3f"|format(load_time) }}s using {{ auth_method }}
                        {% if tasks %}
                        <br><strong>Tasks:</strong> {{ tasks|length }} tasks loaded
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Status Change Modal -->
<div class="modal fade" id="statusChangeModal" tabindex="-1" aria-labelledby="statusChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="statusChangeModalLabel">
                    <i class="fas fa-edit me-2"></i>Change Task Status
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label small text-muted">Current Status:</label>
                    <div id="currentStatusDisplay" class="status-badge mb-3">UNKNOWN</div>
                </div>
                <div class="mb-3">
                    <label for="newStatusSelect" class="form-label small text-muted">New Status:</label>
                    <select class="form-select" id="newStatusSelect">
                        <option value="">Select new status...</option>
                        <option value="ASSIGN">Assign</option>
                        <option value="APPR">Approve</option>
                        <option value="INPRG">In Progress</option>
                        <option value="WMATL">Waiting for Material</option>
                        <option value="COMP">Complete</option>
                        <option value="CLOSE">Close</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="confirmStatusChange" disabled>
                    <i class="fas fa-save me-1"></i>Update Status
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Attachment Modal -->
<div class="modal fade" id="addAttachmentModal" tabindex="-1" aria-labelledby="addAttachmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAttachmentModalLabel">
                    <i class="fas fa-paperclip me-2"></i>Add Attachment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addAttachmentForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <!-- Attachment Source Selection -->
                    <div class="mb-4">
                        <label class="form-label">Choose Attachment Source</label>
                        <div class="attachment-source-buttons">
                            <button type="button" class="btn btn-outline-primary attachment-source-btn active"
                                    data-source="file" id="fileSourceBtn">
                                <i class="fas fa-folder-open me-2"></i>Choose File
                            </button>
                            <button type="button" class="btn btn-outline-primary attachment-source-btn"
                                    data-source="camera" id="cameraSourceBtn">
                                <i class="fas fa-camera me-2"></i>Take Photo
                            </button>
                        </div>
                    </div>

                    <!-- File Upload Section -->
                    <div id="fileUploadSection" class="attachment-section">
                        <div class="mb-3">
                            <label for="attachmentFile" class="form-label">Select File</label>
                            <input type="file" class="form-control" id="attachmentFile" name="file">
                            <div class="form-text">
                                Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, PNG, ZIP, etc. (Max: 50MB)
                            </div>
                        </div>
                    </div>

                    <!-- Camera Capture Section -->
                    <div id="cameraSection" class="attachment-section" style="display: none;">
                        <div class="camera-container">
                            <div class="camera-preview-wrapper">
                                <video id="cameraPreview" autoplay playsinline muted class="camera-preview"></video>
                                <canvas id="captureCanvas" style="display: none;"></canvas>
                                <div id="capturedImagePreview" class="captured-image-preview" style="display: none;">
                                    <img id="capturedImage" alt="Captured photo">
                                    <div class="captured-image-overlay">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="retakePhoto()">
                                            <i class="fas fa-redo me-1"></i>Retake
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="camera-controls">
                                <button type="button" class="btn btn-outline-secondary" id="switchCameraBtn" title="Switch Camera" style="display: none;">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-lg" id="captureBtn" title="Start Camera">
                                    <i class="fas fa-camera"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="stopCameraBtn" title="Stop Camera" style="display: none;">
                                    <i class="fas fa-stop"></i>
                                </button>
                            </div>

                            <div class="camera-status" id="cameraStatus">
                                <i class="fas fa-info-circle me-1"></i>
                                <span>Click "Take Photo" to start camera</span>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="attachmentDescription" class="form-label">Description</label>
                        <input type="text" class="form-control" id="attachmentDescription" name="description"
                               placeholder="Enter description (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="attachmentDoctype" class="form-label">Document Type</label>
                        <select class="form-select" id="attachmentDoctype" name="doctype">
                            <!-- Primary Maximo Categories -->
                            <option value="Attachments" selected>📎 Attachments</option>
                            <option value="Diagrams">📊 Diagrams</option>
                            <option value="Images">🖼️ Images</option>
                            <!-- Secondary Categories -->
                            <optgroup label="Additional Categories">
                                <option value="DRAWING">Drawing</option>
                                <option value="MANUAL">Manual</option>
                                <option value="PHOTO">Photo</option>
                                <option value="SPECIFICATION">Specification</option>
                                <option value="REPORT">Report</option>
                                <option value="OTHER">Other</option>
                            </optgroup>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="uploadAttachmentBtn">
                        <i class="fas fa-upload me-1"></i>Upload Attachment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Attachment Details Modal -->
<div class="modal fade" id="attachmentDetailsModal" tabindex="-1" aria-labelledby="attachmentDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="attachmentDetailsModalLabel">
                    <i class="fas fa-file me-2"></i>Attachment Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="attachmentDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Close
                </button>
                <button type="button" class="btn btn-success" id="viewAttachmentBtn">
                    <i class="fas fa-eye me-1"></i>View
                </button>
                <button type="button" class="btn btn-primary" id="downloadAttachmentBtn">
                    <i class="fas fa-download me-1"></i>Download
                </button>
                <button type="button" class="btn btn-danger" id="deleteAttachmentBtn">
                    <i class="fas fa-trash me-1"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Signature Capture Modal -->
<div class="modal fade" id="signatureModal" tabindex="-1" aria-labelledby="signatureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signatureModalLabel">
                    <i class="fas fa-signature me-2"></i>Digital Signature Required
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="signature-info mb-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Signature Required:</strong> This status change requires a digital signature for audit compliance.
                        <br><small>Status: <span id="signatureStatusInfo">-</span> | Work Order: <span id="signatureWonumInfo">-</span></small>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="signatureCustomerName" class="form-label">Customer Name *</label>
                        <input type="text" class="form-control" id="signatureCustomerName"
                               placeholder="Enter customer name" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Date & Time</label>
                        <input type="text" class="form-control" id="signatureDateTime" readonly>
                    </div>
                </div>

                <!-- Comments -->
                <div class="mb-3">
                    <label for="signatureComments" class="form-label">Additional Comments</label>
                    <textarea class="form-control" id="signatureComments" rows="3"
                              placeholder="Enter any additional comments (optional)"></textarea>
                </div>

                <!-- Signature Pad -->
                <div class="signature-pad-container">
                    <label class="form-label">Digital Signature *</label>
                    <div class="signature-pad-wrapper">
                        <canvas id="signaturePad" width="600" height="200"></canvas>
                        <div class="signature-pad-overlay" id="signaturePadOverlay">
                            <i class="fas fa-pen-fancy fa-2x text-muted"></i>
                            <p class="text-muted mt-2">Click and drag to sign</p>
                        </div>
                    </div>
                    <div class="signature-pad-controls mt-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSignature()">
                            <i class="fas fa-eraser me-1"></i>Clear
                        </button>
                        <small class="text-muted ms-3">Use mouse or touch to create your signature</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="submitSignatureBtn" onclick="submitSignature()" disabled>
                    <i class="fas fa-check me-1"></i>Submit Signature & Continue
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Tasks Data for JavaScript -->
<script type="application/json" id="tasksData">
{{ tasks | tojson | safe }}
</script>

<!-- Hidden Work Order Data for JavaScript -->
<script type="application/json" id="workorderData">
{{ workorder | tojson | safe }}
</script>





<script>
// Global variables
let currentSiteId = null;
let tasksData = [];
let workorderData = null;
let currentTaskIndex = 0;




// Status Change Modal Functions
let currentTaskWonum = null;
let statusChangeModal = null;

function openStatusChangeModal(taskWonum, currentStatus) {
    currentTaskWonum = taskWonum;

    // Update modal content
    const currentStatusDisplay = document.getElementById('currentStatusDisplay');
    currentStatusDisplay.className = `status-badge status-${currentStatus}`;
    currentStatusDisplay.textContent = currentStatus || 'Unknown';

    // Reset and populate the select dropdown
    const newStatusSelect = document.getElementById('newStatusSelect');
    newStatusSelect.value = '';

    // Disable current status option
    const options = newStatusSelect.querySelectorAll('option');
    options.forEach(option => {
        option.disabled = option.value === currentStatus;
    });

    // Reset confirm button
    document.getElementById('confirmStatusChange').disabled = true;

    // Show modal using the global instance
    if (statusChangeModal) {
        statusChangeModal.show();
    }
}

function updateTaskStatusFromModal(taskWonum, newStatus) {
    const confirmBtn = document.getElementById('confirmStatusChange');
    const originalText = confirmBtn.innerHTML;

    // Show loading state
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    confirmBtn.disabled = true;

    // Make API call
    fetch(`/api/task/${taskWonum}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => {
        console.log('📝 Task status response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📝 Task status response data:', data);
        if (data.success) {
            console.log('✅ Status change successful, updating UI elements');

            // Update the status badge in the sticky header
            const statusBadge = document.querySelector('.sticky-task-header .status-badge');
            if (statusBadge) {
                statusBadge.className = `status-badge status-${newStatus}`;
                statusBadge.textContent = newStatus;
                console.log('✅ Updated status badge');
            } else {
                console.log('⚠️ Status badge not found');
            }

            // Update the status change icon data
            const statusIcon = document.querySelector(`[data-task-wonum="${taskWonum}"]`);
            if (statusIcon) {
                statusIcon.setAttribute('data-current-status', newStatus);
                console.log('✅ Updated status icon data');
            } else {
                console.log('⚠️ Status icon not found');
            }

            // Update the status indicator icon color and icon
            const statusIndicatorIcon = document.querySelector(`[data-task-status="${taskWonum}"] .status-indicator-icon`);
            if (statusIndicatorIcon) {
                // Remove all status classes and icon classes
                statusIndicatorIcon.className = statusIndicatorIcon.className.replace(/status-\w+/g, '').replace(/fa-[\w-]+/g, '');
                // Add new status class and icon
                statusIndicatorIcon.classList.add(`fas`, getStatusIcon(newStatus), `status-${newStatus}`);
                console.log('✅ Updated status indicator icon');
            } else {
                console.log('⚠️ Status indicator icon not found');
            }

            // Also update the container's data attribute and title
            const statusIndicatorContainer = document.querySelector(`[data-task-status="${taskWonum}"]`);
            if (statusIndicatorContainer) {
                statusIndicatorContainer.setAttribute('data-task-status', newStatus);
                statusIndicatorContainer.setAttribute('title', `Task Status: ${newStatus}`);
                console.log('✅ Updated status indicator container');
            } else {
                console.log('⚠️ Status indicator container not found');
            }

            // Update task data
            const currentTask = tasksData[currentTaskIndex];
            if (currentTask && currentTask.wonum === taskWonum) {
                currentTask.status = newStatus;
            }

            // Close modal
            if (statusChangeModal) {
                statusChangeModal.hide();
            }

            // Show success message
            showNotification('success', data.message);
        } else if (data.signature_required) {
            // Signature is required - show signature modal
            if (statusChangeModal) {
                statusChangeModal.hide();
            }
            showSignatureModal(taskWonum, newStatus, 'task');
        } else {
            showNotification('error', data.error || 'Failed to update task status');
        }
    })
    .catch(error => {
        console.error('❌ Task status error:', error);

        // Provide more specific error messages
        if (error.message.includes('Failed to fetch')) {
            showNotification('error', 'Network connection error. Please check your connection and try again.');
        } else if (error.message.includes('HTTP 500')) {
            showNotification('error', 'Server error during status update. Please try again.');
        } else if (error.message.includes('HTTP 401')) {
            showNotification('error', 'Authentication error. Please refresh the page and login again.');
        } else {
            showNotification('error', `Error updating task status: ${error.message}`);
        }
    })
    .finally(() => {
        // Restore button state
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}

// Move navigation functions outside DOMContentLoaded to make them immediately available
// Pagination variables for materials and labor
var materialsPagination = {};
var laborPagination = {};

// Materials pagination navigation - GLOBAL FUNCTION
function navigateMaterials(taskWonum, direction) {
    // Check if materials are loaded for this task first
    const materialsContent = document.getElementById(`materials-content-${taskWonum}`);
    if (!materialsContent || materialsContent.innerHTML.includes('Load Materials')) {
        return;
    }

    // Show loading on navigation buttons
    const navButtons = document.querySelectorAll(`.materials-prev-btn[data-task-wonum="${taskWonum}"], .materials-next-btn[data-task-wonum="${taskWonum}"]`);
    navButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
    });

    const pagination = materialsPagination[taskWonum];
    if (!pagination) {
        restoreNavigationButtons(taskWonum, 'materials');
        return;
    }

    const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage);

    if (direction === 'prev' && pagination.currentPage > 0) {
        pagination.currentPage--;
    } else if (direction === 'next' && pagination.currentPage < totalPages - 1) {
        pagination.currentPage++;
    }

    // Update the display with cached data
    if (pagination.cachedMaterials && pagination.cachedMaterials.length > 0) {
        const materialsContent = document.getElementById(`materials-content-${taskWonum}`);
        if (materialsContent) {
            // Regenerate the materials display with new pagination
            const materialsHtml = generateMaterialsDisplay(pagination.cachedMaterials, taskWonum);
            materialsContent.innerHTML = materialsHtml;

            // Restore navigation buttons
            restoreNavigationButtons(taskWonum, 'materials');

            // Re-add event listeners for the new navigation buttons
            setTimeout(() => {
                const materialsNavButtons = document.querySelectorAll(`.materials-prev-btn[data-task-wonum="${taskWonum}"], .materials-next-btn[data-task-wonum="${taskWonum}"]`);
                materialsNavButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const clickTaskWonum = this.getAttribute('data-task-wonum');
                        const direction = this.getAttribute('data-direction');
                        navigateMaterials(clickTaskWonum, direction);
                    });
                });
            }, 100);
        }
    } else {
        // Fallback: reload from server
        const loadBtn = document.querySelector(`[data-task-wonum="${taskWonum}"].load-materials-btn`);
        if (loadBtn) {
            loadPlannedMaterials(taskWonum, loadBtn.getAttribute('data-task-status'), loadBtn);
        } else {
            restoreNavigationButtons(taskWonum, 'materials');
        }
    }
}

// Labor pagination navigation - GLOBAL FUNCTION
function navigateLabor(taskWonum, direction) {
    // Check if labor is loaded for this task first
    const laborContent = document.getElementById(`labor-content-${taskWonum}`);
    if (!laborContent || laborContent.innerHTML.includes('Load Labor')) {
        return;
    }

    // Show loading on navigation buttons
    const navButtons = document.querySelectorAll(`.labor-prev-btn[data-task-wonum="${taskWonum}"], .labor-next-btn[data-task-wonum="${taskWonum}"]`);
    navButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
    });

    const pagination = laborPagination[taskWonum];
    if (!pagination) {
        restoreNavigationButtons(taskWonum, 'labor');
        return;
    }

    const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage);

    if (direction === 'prev' && pagination.currentPage > 0) {
        pagination.currentPage--;
    } else if (direction === 'next' && pagination.currentPage < totalPages - 1) {
        pagination.currentPage++;
    }

    // Update the display with cached data
    if (pagination.cachedLabor && pagination.cachedLabor.length > 0) {
        const laborContent = document.getElementById(`labor-content-${taskWonum}`);
        if (laborContent) {
            // Store the data attributes before replacing content
            const taskId = laborContent.getAttribute('data-task-id');
            const siteId = laborContent.getAttribute('data-site-id');
            const taskWonumAttr = laborContent.getAttribute('data-task-wonum');
            const parentWonum = laborContent.getAttribute('data-parent-wonum');

            // Regenerate the labor display with new pagination
            const laborHtml = generateLaborDisplay(pagination.cachedLabor, taskWonum);
            laborContent.innerHTML = laborHtml;

            // Restore the data attributes after replacing content
            restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

            // Ensure all critical attributes are set
            ensureDataAttributes(laborContent, taskWonum);

            // Restore navigation buttons
            restoreNavigationButtons(taskWonum, 'labor');

            // Re-add event listeners for the new navigation buttons
            setTimeout(() => {
                const laborNavButtons = document.querySelectorAll(`.labor-prev-btn[data-task-wonum="${taskWonum}"], .labor-next-btn[data-task-wonum="${taskWonum}"]`);
                laborNavButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const clickTaskWonum = this.getAttribute('data-task-wonum');
                        const direction = this.getAttribute('data-direction');
                        navigateLabor(clickTaskWonum, direction);
                    });
                });
            }, 100);
        }
    } else {
        // Fallback: reload from server
        const loadBtn = document.querySelector(`[data-task-wonum="${taskWonum}"].load-labor-btn`);
        if (loadBtn) {
            loadTaskLabor(taskWonum, loadBtn.getAttribute('data-task-status'), loadBtn);
        } else {
            restoreNavigationButtons(taskWonum, 'labor');
        }
    }
}

// Function to restore navigation buttons after loading
function restoreNavigationButtons(taskWonum, type) {
    const prevClass = type === 'materials' ? '.materials-prev-btn' : '.labor-prev-btn';
    const nextClass = type === 'materials' ? '.materials-next-btn' : '.labor-next-btn';

    const prevButtons = document.querySelectorAll(`${prevClass}[data-task-wonum="${taskWonum}"]`);
    const nextButtons = document.querySelectorAll(`${nextClass}[data-task-wonum="${taskWonum}"]`);

    // Restore button content and enable them
    prevButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        btn.disabled = false;
    });

    nextButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        btn.disabled = false;
    });

    // Re-enable based on pagination state
    const pagination = type === 'materials' ? materialsPagination[taskWonum] : laborPagination[taskWonum];
    if (pagination) {
        const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage);

        prevButtons.forEach(btn => {
            btn.disabled = pagination.currentPage === 0;
        });

        nextButtons.forEach(btn => {
            btn.disabled = pagination.currentPage >= totalPages - 1;
        });
    }
}

// Function to get status-specific icons
function getStatusIcon(status) {
    const iconMap = {
        'ASSIGN': 'fa-user-check',      // Assigned - user with checkmark
        'APPR': 'fa-thumbs-up',         // Approved - thumbs up
        'READY': 'fa-play-circle',      // Ready - play button
        'INPRG': 'fa-cog fa-spin',      // In Progress - spinning gear
        'WMATL': 'fa-boxes',            // Waiting for Material - boxes
        'WAPPR': 'fa-hourglass-half',   // Waiting for Approval - hourglass
        'WGOVT': 'fa-landmark',         // Waiting for Government - government building
        'WSERV': 'fa-tools',            // Waiting for Service - tools
        'WSCH': 'fa-calendar-clock',    // Waiting for Schedule - calendar with clock
        'COMP': 'fa-check-circle',      // Complete - check circle
        'CLOSE': 'fa-times-circle',     // Closed - X circle
        'PACK': 'fa-archive'            // Packed - archive box
    };
    return iconMap[status] || 'fa-question-circle'; // Default icon for unknown status
}

// Function to convert decimal hours to H:MM format (like Maximo)
function formatHoursAsTime(decimalHours) {
    if (!decimalHours || decimalHours === 0) return '0:00';

    const hours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - hours) * 60);

    // Handle case where rounding gives us 60 minutes
    if (minutes === 60) {
        return `${hours + 1}:00`;
    }

    return `${hours}:${minutes.toString().padStart(2, '0')}`;
}

// Make functions globally accessible immediately
window.navigateMaterials = navigateMaterials;
window.navigateLabor = navigateLabor;
window.restoreNavigationButtons = restoreNavigationButtons;
window.getStatusIcon = getStatusIcon;
window.formatHoursAsTime = formatHoursAsTime;



// Enhanced task pagination and display functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get site ID from data attribute
    const container = document.querySelector('.workorder-detail-container');
    currentSiteId = container ? container.getAttribute('data-site-id') : 'UNKNOWN';
    console.log('Current Site ID:', currentSiteId);

    // Load tasks data from JSON
    const tasksDataElement = document.getElementById('tasksData');
    if (tasksDataElement) {
        try {
            tasksData = JSON.parse(tasksDataElement.textContent);
            console.log('Loaded tasks data:', tasksData);

            // Initialize task display if tasks exist
            if (tasksData && tasksData.length > 0) {
                displayCurrentTask();
            }
        } catch (error) {
            console.error('Error parsing tasks data:', error);
        }
    }

    // Load work order data from JSON
    const workorderDataElement = document.getElementById('workorderData');
    if (workorderDataElement) {
        try {
            workorderData = JSON.parse(workorderDataElement.textContent);
            console.log('Loaded work order data:', workorderData);
        } catch (error) {
            console.error('Error parsing work order data:', error);
        }
    }

    // Initialize event handlers for dynamic content
    initializeEventHandlers();

    // Initialize status change modal
    statusChangeModal = new bootstrap.Modal(document.getElementById('statusChangeModal'), {
        backdrop: true,
        keyboard: true,
        focus: true
    });

    // Initialize modal event listeners
    document.getElementById('newStatusSelect').addEventListener('change', function() {
        const confirmBtn = document.getElementById('confirmStatusChange');
        confirmBtn.disabled = !this.value;
    });

    document.getElementById('confirmStatusChange').addEventListener('click', function() {
        const newStatus = document.getElementById('newStatusSelect').value;
        if (newStatus && currentTaskWonum) {
            updateTaskStatusFromModal(currentTaskWonum, newStatus);
        }
    });

    // Handle modal hidden event to reset state
    document.getElementById('statusChangeModal').addEventListener('hidden.bs.modal', function() {
        currentTaskWonum = null;
        document.getElementById('newStatusSelect').value = '';
        document.getElementById('confirmStatusChange').disabled = true;
    });
});

// Task pagination functions
function nextTask() {
    if (currentTaskIndex < tasksData.length - 1) {
        currentTaskIndex++;
        displayCurrentTask();
    }
}

function previousTask() {
    if (currentTaskIndex > 0) {
        currentTaskIndex--;
        displayCurrentTask();
    }
}



function displayCurrentTask() {
    if (!tasksData || tasksData.length === 0) return;

    const task = tasksData[currentTaskIndex];
    const container = document.getElementById('taskContainer');

    if (!container) return;

    // Update pagination info
    document.getElementById('currentTaskInfo').textContent = `Task ${currentTaskIndex + 1} of ${tasksData.length}`;

    // Update pagination buttons
    document.getElementById('prevTaskBtn').disabled = currentTaskIndex === 0;
    document.getElementById('nextTaskBtn').disabled = currentTaskIndex === tasksData.length - 1;

    // Generate sticky task header and task card HTML
    container.innerHTML = generateStickyTaskHeader(task) + generateTaskCardHTML(task);

    // Reinitialize event handlers for the new content
    initializeEventHandlers();
}

function generateStickyTaskHeader(task) {
    const parentWO = workorderData || {};

    // Only show parent WO section if we have valid parent work order data
    const hasValidParentWO = parentWO.wonum && parentWO.wonum !== 'N/A' && parentWO.wonum !== '';

    let parentWOSection = '';
    if (hasValidParentWO) {
        parentWOSection = `
            <!-- Parent Work Order Section (Desktop Only) -->
            <div class="parent-wo-section d-none d-md-block mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-1 parent-wo-title">
                            <i class="fas fa-folder-open me-2"></i>
                            Parent WO: ${parentWO.wonum}
                            <span class="status-badge status-${parentWO.status} ms-2">${parentWO.status || 'Unknown'}</span>
                        </h6>
                        <div class="parent-wo-description">
                            ${parentWO.description || 'No description available'}
                        </div>
                    </div>
                </div>
                <hr class="my-2 border-light opacity-50">
            </div>`;
    }

    return `
        <div class="sticky-task-header">
            ${parentWOSection}

            <!-- Task Section -->
            <div class="task-section">
                <!-- Header Row with Status Left and Change Icon Right -->
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center gap-2">
                        <span class="status-badge status-${task.status}">${task.status || 'Unknown'}</span>
                        <div class="priority-indicator priority-${task.priority || 3}"></div>
                    </div>
                    <button class="btn btn-sm status-change-icon" data-task-wonum="${task.wonum}" data-current-status="${task.status}" title="Change Status">
                        <i class="fas fa-edit"></i>
                    </button>
                    <div class="status-indicator-container ms-2" data-task-status="${task.status}" title="Task Status: ${task.status}">
                        <i class="fas ${getStatusIcon(task.status)} status-indicator-icon status-${task.status}"></i>
                    </div>
                </div>

                <!-- Task Info Row -->
                <div class="task-info-row">
                    <h5 class="mb-1">
                        <i class="fas fa-clipboard-list me-2"></i>
                        Task WO: ${task.wonum}
                        <span class="badge bg-light text-dark ms-2">
                            <i class="fas fa-hashtag me-1"></i>ID: ${task.taskid}
                        </span>
                    </h5>
                    <div class="task-description">
                        ${task.description || 'No description available'}
                    </div>
                </div>


            </div>

            <!-- Sticky Materials and Labor Tabs -->
            <div class="sticky-resource-tabs mt-3">
                <ul class="nav nav-tabs resource-tabs" id="resourceTabs-${task.wonum}" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active d-flex align-items-center justify-content-between" id="materials-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#materials-content-${task.wonum}" type="button" role="tab">
                            <span><i class="fas fa-boxes me-2"></i>Materials</span>
                            ${['APPR', 'ASSIGN', 'WMATL', 'INPRG'].includes(task.status) ? `
                            <button type="button" class="btn btn-sm btn-outline-light ms-2 search-inventory-btn" onclick="event.stopPropagation(); window.openInventorySearchForTask('${task.siteid || 'UNKNOWN'}', '${task.parent || task.wonum}', '${task.wonum}', ${task.taskid})" title="Search Inventory">
                                <i class="fas fa-search me-1"></i>
                                <span class="d-none d-md-inline">Add Material</span>
                                <span class="d-md-none">Add</span>
                            </button>
                            ` : ''}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link d-flex align-items-center justify-content-between" id="labor-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#labor-content-${task.wonum}" type="button" role="tab">
                            <span><i class="fas fa-users me-2"></i>Labor</span>
                            ${['APPR', 'ASSIGN', 'WMATL', 'INPRG', 'READY', 'COMP'].includes(task.status) ? `
                            <button type="button" class="btn btn-sm btn-outline-light ms-2 search-labor-btn" onclick="event.stopPropagation(); window.openLaborSearchForTask('${task.siteid || 'UNKNOWN'}', '${task.parent || task.wonum}', '${task.wonum}', ${task.taskid})" title="Search Labor">
                                <i class="fas fa-search me-1"></i>
                                <span class="d-none d-md-inline">Add Labor</span>
                                <span class="d-md-none">Add</span>
                            </button>
                            ` : ''}
                        </button>
                    </li>
                </ul>
                <div class="tab-content resource-tab-content" id="resourceTabContent-${task.wonum}">
                    <div class="tab-pane fade show active" id="materials-content-${task.wonum}" role="tabpanel">
                        <div class="resource-content-area">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>Planned Materials
                                </h6>
                                <button class="btn btn-sm btn-outline-primary load-materials-btn" data-task-wonum="${task.wonum}" data-task-status="${task.status}">
                                    <i class="fas fa-download me-1"></i>Load Materials
                                </button>
                            </div>
                            <div class="materials-content" id="materials-content-${task.wonum}">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-box-open fa-2x mb-2"></i>
                                    <div>Click "Load Materials" to view planned materials</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="labor-content-${task.wonum}" role="tabpanel">
                        <div class="resource-content-area">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2"></i>Labor Resources
                                </h6>
                                <button class="btn btn-sm btn-outline-primary load-labor-btn" data-task-wonum="${task.wonum}" data-task-status="${task.status}">
                                    <i class="fas fa-download me-1"></i>Load Labor
                                </button>
                            </div>
                            <div class="labor-content" id="labor-content-${task.wonum}"
                                 data-task-id="${task.taskid || ''}"
                                 data-site-id="${task.siteid || ''}"
                                 data-task-wonum="${task.wonum || ''}"
                                 data-parent-wonum="${task.parent || task.wonum || ''}">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-user-hard-hat fa-2x mb-2"></i>
                                    <div>Click "Load Labor" to view labor resources</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateTaskCardHTML(task) {
    return `
        <div class="task-card-enhanced">

            <div class="task-body-enhanced">
                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-tools"></i>Work Type
                    </div>
                    <div class="detail-value-enhanced">${task.worktype || 'Not specified'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user"></i>Owner
                    </div>
                    <div class="detail-value-enhanced">${task.owner || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-users"></i>Owner Group
                    </div>
                    <div class="detail-value-enhanced">${task.owner_group || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user-tie"></i>Lead
                    </div>
                    <div class="detail-value-enhanced">${task.lead || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user-check"></i>Supervisor
                    </div>
                    <div class="detail-value-enhanced">${task.supervisor || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-users-cog"></i>Crew
                    </div>
                    <div class="detail-value-enhanced">${task.crew || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-map-pin"></i>Location
                    </div>
                    <div class="detail-value-enhanced">${task.location || 'Not specified'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-cog"></i>Asset
                    </div>
                    <div class="detail-value-enhanced">${task.assetnum || 'No asset'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-calendar-check"></i>Scheduled Start
                    </div>
                    <div class="detail-value-enhanced">${task.schedstart || 'Not scheduled'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-clock"></i>Estimated Duration
                    </div>
                    <div class="detail-value-enhanced">${task.estdur || 'Not estimated'}</div>
                </div>
            </div>


        </div>
    `;
}

function initializeEventHandlers() {
    // Handle status change icon clicks (new popup functionality)
    document.querySelectorAll('.status-change-icon').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const currentStatus = this.getAttribute('data-current-status');
            openStatusChangeModal(taskWonum, currentStatus);
        });
    });



    // Handle planned materials loading
    document.querySelectorAll('.load-materials-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const taskStatus = this.getAttribute('data-task-status');
            loadPlannedMaterials(taskWonum, taskStatus, this);
        });
    });

    // Handle labor loading
    document.querySelectorAll('.load-labor-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const taskStatus = this.getAttribute('data-task-status');
            loadTaskLabor(taskWonum, taskStatus, this);
        });
    });

    // Handle materials refresh (using event delegation for dynamically added buttons)
    document.addEventListener('click', function(e) {
        if (e.target.closest('.refresh-materials-btn')) {
            const button = e.target.closest('.refresh-materials-btn');
            const taskWonum = button.getAttribute('data-task-wonum');
            const taskStatus = button.getAttribute('data-task-status');
            refreshTaskMaterials(taskWonum, taskStatus, button);
        }
    });

    // Handle labor refresh (using event delegation for dynamically added buttons)
    document.addEventListener('click', function(e) {
        if (e.target.closest('.refresh-labor-btn')) {
            const button = e.target.closest('.refresh-labor-btn');
            const taskWonum = button.getAttribute('data-task-wonum');
            const taskStatus = button.getAttribute('data-task-status');
            refreshTaskLabor(taskWonum, taskStatus, button);
        }
    });

}

// Global function to open inventory search for a specific task
function openInventorySearchForTask(siteId, parentWonum, taskWonum, taskId) {
    // Store task context for material requests
    // parentWonum = parent work order (e.g. 2021-1744762)
    // taskWonum = task work order number (e.g. 2021-1835482)
    // taskId = actual numeric task ID from the task (e.g. 10, 20, 30, etc.)
    if (typeof materialRequestManager !== 'undefined') {
        materialRequestManager.setTaskContext(parentWonum, taskWonum, taskId);
    }

    // Call the regular inventory search
    openInventorySearch(siteId);
}

// Global function to open labor search for a specific task
function openLaborSearchForTask(siteId, parentWonum, taskWonum, taskId) {
    // Store task context for labor requests
    // parentWonum = parent work order (e.g. 2021-1744762)
    // taskWonum = task work order number (e.g. 2021-1835482)
    // taskId = actual numeric task ID from the task (e.g. 10, 20, 30, etc.)
    if (typeof laborSearchManager !== 'undefined') {
        laborSearchManager.setTaskContext(parentWonum, taskWonum, taskId);
    }

    // Call the regular labor search
    openLaborSearch(siteId);
}

// Utility function to format currency properly - USE ONLY REAL MAXIMO DATA
function formatCurrency(amount, currencyCode) {
    // If no real data from Maximo, return "No cost data"
    if (!currencyCode || amount === null || amount === undefined) {
        return 'No cost data';
    }

    const value = parseFloat(amount);
    if (isNaN(value)) {
        return 'No cost data';
    }

    if (currencyCode === 'USD') {
        return `$${value.toFixed(2)}`;
    } else if (currencyCode === 'EUR') {
        return `€${value.toFixed(2)}`;
    } else if (currencyCode === 'GBP') {
        return `£${value.toFixed(2)}`;
    } else {
        return `${currencyCode} ${value.toFixed(2)}`;
    }
}

// Function to format date/time values
function formatDateTime(dateString) {
    if (!dateString) return 'Not set';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateString; // Return as-is if parsing fails
    }
}

// Function to generate comprehensive materials display with pagination
function generateMaterialsDisplay(materials, taskWonum = null) {
    // Initialize pagination for this task if not exists
    if (taskWonum && !materialsPagination[taskWonum]) {
        materialsPagination[taskWonum] = {
            currentPage: 0,
            itemsPerPage: window.innerWidth <= 768 ? 1 : materials.length, // 1 item per page on mobile
            totalItems: materials.length
        };
    }

    const pagination = taskWonum ? materialsPagination[taskWonum] : null;
    const isMobile = window.innerWidth <= 768;

    // Get materials for current page
    let displayMaterials = materials;
    if (pagination && isMobile) {
        const startIndex = pagination.currentPage * pagination.itemsPerPage;
        const endIndex = startIndex + pagination.itemsPerPage;
        displayMaterials = materials.slice(startIndex, endIndex);
    }
    // Desktop table view
    let desktopHtml = `
        <div class="materials-desktop-view">
            <table class="material-table">
                <thead>
                    <tr>
                        <th>Item Number</th>
                        <th>Description</th>
                        <th>Qty</th>
                        <th>Unit Cost</th>
                        <th>Line Cost</th>
                        <th>Vendor</th>
                        <th>Store Location</th>
                        <th>Direct Request</th>
                        <th>Requested By</th>
                        <th>Required Date</th>
                    </tr>
                </thead>
                <tbody>
    `;

    displayMaterials.forEach(material => {
        desktopHtml += `
            <tr>
                <td class="material-itemnum"><strong>Item:</strong> ${material.itemnum || 'N/A'}</td>
                <td class="material-description" title="${material.description_longdescription || material.description || 'No description'}"><strong>Description:</strong> ${material.description || 'No description'}</td>
                <td class="material-qty"><strong>Qty:</strong> ${material.itemqty || 0} <span class="text-muted">| Unit:</span> ${material.unit || 'EA'}</td>
                <td class="material-cost">${formatCurrency(material.unitcost || 0, 'USD')}</td>
                <td class="material-cost">${formatCurrency(material.linecost || 0, 'USD')}</td>
                <td>${material.vendor || 'N/A'}</td>
                <td>${material.storeloc || 'N/A'}</td>
                <td>${material.directreq ? '<span class="badge bg-warning">Yes</span>' : '<span class="badge bg-success">No</span>'}</td>
                <td>${material.requestby || 'N/A'}</td>
                <td>${formatDateTime(material.requiredate)}</td>
            </tr>
        `;
    });

    desktopHtml += `
                </tbody>
            </table>
        </div>
    `;

    // Mobile card view with pagination
    let mobileHtml = `<div class="materials-mobile-view">`;

    // Add pagination controls for mobile
    if (pagination && isMobile && materials.length > 1) {
        const totalPages = Math.ceil(materials.length / pagination.itemsPerPage);
        const prevDisabled = pagination.currentPage === 0 ? 'disabled' : '';
        const nextDisabled = pagination.currentPage >= totalPages - 1 ? 'disabled' : '';

        mobileHtml += `
            <div class="resource-pagination-controls mb-3">
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn materials-prev-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="prev"
                        ${prevDisabled}>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="pagination-info-small">
                    ${pagination.currentPage + 1} of ${totalPages}
                </span>
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn materials-next-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="next"
                        ${nextDisabled}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    displayMaterials.forEach(material => {
        mobileHtml += `
            <div class="material-card-enhanced">
                <div class="material-card-header-enhanced">
                    <div class="material-item-info">
                        <div class="material-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div>
                            <h6 class="material-itemnum-enhanced">${material.itemnum || 'N/A'}</h6>
                            <p class="material-description-enhanced">${material.description || 'No description available'}</p>
                        </div>
                    </div>
                    <div class="material-qty-badge">
                        <i class="fas fa-boxes"></i>
                        <span><strong>Qty:</strong> ${material.itemqty || 0}</span>
                        <span class="text-muted ms-2">Unit: ${material.unit || 'EA'}</span>
                    </div>
                </div>

                <div class="material-details-grid">
                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Unit Cost</p>
                            <p class="material-detail-value-enhanced">${formatCurrency(material.unitcost || 0, 'USD')}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Line Cost</p>
                            <p class="material-detail-value-enhanced">${formatCurrency(material.linecost || 0, 'USD')}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Vendor</p>
                            <p class="material-detail-value-enhanced">${material.vendor || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Store Location</p>
                            <p class="material-detail-value-enhanced">${material.storeloc || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, ${material.directreq ? '#dc3545, #c82333' : '#28a745, #1e7e34'});">
                            <i class="fas ${material.directreq ? 'fa-exclamation-triangle' : 'fa-check-circle'}"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Direct Request</p>
                            <p class="material-detail-value-enhanced">${material.directreq ? 'Yes' : 'No'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #6c757d, #495057);">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Requested By</p>
                            <p class="material-detail-value-enhanced">${material.requestby || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #e83e8c, #d91a72);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Required Date</p>
                            <p class="material-detail-value-enhanced">${formatDateTime(material.requiredate)}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #20c997, #17a2b8);">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Line Type</p>
                            <p class="material-detail-value-enhanced">${material.linetype_description || material.linetype || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Condition Code</p>
                            <p class="material-detail-value-enhanced">${material.conditioncode || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-bookmark"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Reservation Type</p>
                            <p class="material-detail-value-enhanced">${material.restype_description || material.restype || 'N/A'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    mobileHtml += `</div>`;

    return desktopHtml + mobileHtml;
}



function loadPlannedMaterials(taskWonum, taskStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

    // Show enhanced loading in content area
    materialsContent.innerHTML = `
        <div class="materials-loading text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="h5 text-primary mb-2">Loading Planned Materials</div>
            <div class="text-muted">Please wait while we fetch the materials data...</div>
            <div class="progress mt-3" style="height: 6px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>
    `;

    // Make API call
    fetch(`/api/task/${taskWonum}/planned-materials?status=${taskStatus}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.show_materials) {
            if (data.materials && data.materials.length > 0) {
                // Cache materials data for pagination
                if (!materialsPagination[taskWonum]) {
                    materialsPagination[taskWonum] = {
                        currentPage: 0,
                        itemsPerPage: window.innerWidth <= 768 ? 1 : data.materials.length,
                        totalItems: data.materials.length
                    };
                }
                materialsPagination[taskWonum].cachedMaterials = data.materials;

                // Display materials with comprehensive data and pagination
                const materialsHtml = generateMaterialsDisplay(data.materials, taskWonum);
                materialsContent.innerHTML = materialsHtml;

                // Add refresh button after the materials content
                const refreshButtonHtml = `
                    <div class="d-flex justify-content-end mt-3">
                        <button class="btn btn-sm btn-outline-secondary refresh-materials-btn" data-task-wonum="${taskWonum}" data-task-status="${taskStatus}" title="Refresh Materials">
                            <i class="fas fa-sync-alt me-1"></i>
                            <span class="d-none d-md-inline">Refresh</span>
                        </button>
                    </div>
                `;
                materialsContent.insertAdjacentHTML('beforeend', refreshButtonHtml);

                // Update load button to show it's loaded
                button.innerHTML = '<i class="fas fa-check me-1"></i>Loaded';
                button.disabled = true;
                button.classList.remove('btn-outline-primary');
                button.classList.add('btn-outline-success');

                // Restore navigation buttons
                restoreNavigationButtons(taskWonum, 'materials');

                // Add event listeners for materials navigation buttons
                setTimeout(() => {
                    const materialsNavButtons = document.querySelectorAll('.materials-prev-btn, .materials-next-btn');
                    materialsNavButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const clickTaskWonum = this.getAttribute('data-task-wonum');
                            const direction = this.getAttribute('data-direction');
                            navigateMaterials(clickTaskWonum, direction);
                        });
                    });
                }, 100);

                showNotification('success', `Loaded ${data.materials.length} planned materials for task ${taskWonum}`);
            } else {
                // No materials found
                materialsContent.innerHTML = `
                    <div class="materials-empty">
                        <i class="fas fa-box-open fa-2x mb-2"></i>
                        <div>No planned materials found for this task</div>
                    </div>
                `;
                button.innerHTML = originalText;
                button.disabled = false;
            }
        } else {
            // Materials not available for this status
            materialsContent.innerHTML = `
                <div class="materials-empty">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <div>${data.message || 'Planned materials not available for this task status'}</div>
                </div>
            `;
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error loading planned materials:', error);
        materialsContent.innerHTML = `
            <div class="materials-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error loading planned materials: ${error.message || 'Unknown error'}
            </div>
        `;
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to load planned materials');
    });
}

// Helper function to restore data attributes safely
function restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum) {
    console.log('🔧 RESTORE ATTRIBUTES: Restoring data attributes for labor content');
    console.log('  - taskId:', taskId, '(type:', typeof taskId, ')');
    console.log('  - siteId:', siteId, '(type:', typeof siteId, ')');
    console.log('  - taskWonumAttr:', taskWonumAttr, '(type:', typeof taskWonumAttr, ')');
    console.log('  - parentWonum:', parentWonum, '(type:', typeof parentWonum, ')');

    if (taskId && taskId !== 'null' && taskId !== 'undefined' && taskId !== '') {
        laborContent.setAttribute('data-task-id', taskId);
        console.log('  ✅ Set data-task-id:', taskId);
    } else {
        console.log('  ❌ Skipped data-task-id (invalid value):', taskId);
    }

    if (siteId && siteId !== 'null' && siteId !== 'undefined' && siteId !== '') {
        laborContent.setAttribute('data-site-id', siteId);
        console.log('  ✅ Set data-site-id:', siteId);
    } else {
        console.log('  ❌ Skipped data-site-id (invalid value):', siteId);
    }

    if (taskWonumAttr && taskWonumAttr !== 'null' && taskWonumAttr !== 'undefined' && taskWonumAttr !== '') {
        laborContent.setAttribute('data-task-wonum', taskWonumAttr);
        console.log('  ✅ Set data-task-wonum:', taskWonumAttr);
    } else {
        console.log('  ❌ Skipped data-task-wonum (invalid value):', taskWonumAttr);
    }

    if (parentWonum && parentWonum !== 'null' && parentWonum !== 'undefined' && parentWonum !== '') {
        laborContent.setAttribute('data-parent-wonum', parentWonum);
        console.log('  ✅ Set data-parent-wonum:', parentWonum);
    } else {
        console.log('  ❌ Skipped data-parent-wonum (invalid value):', parentWonum);
    }
}

// Helper function to ensure data attributes are set from tasksData if missing
function ensureDataAttributes(laborContent, taskWonum) {
    console.log('🔧 ENSURE ATTRIBUTES: Checking data attributes for task', taskWonum);

    const currentTaskId = laborContent.getAttribute('data-task-id');
    const currentSiteId = laborContent.getAttribute('data-site-id');
    const currentParentWonum = laborContent.getAttribute('data-parent-wonum');

    // Check if any are missing or null
    if (!currentTaskId || !currentSiteId || !currentParentWonum ||
        currentTaskId === 'null' || currentSiteId === 'null' || currentParentWonum === 'null') {

        console.log('🔧 ENSURE ATTRIBUTES: Some attributes missing, trying to get from tasksData');

        const tasksDataElement = document.getElementById('tasksData');
        if (tasksDataElement) {
            try {
                const tasksData = JSON.parse(tasksDataElement.textContent);
                const task = tasksData.find(t => t.wonum === taskWonum);

                if (task) {
                    console.log('🔧 ENSURE ATTRIBUTES: Found task data:', task);

                    if (!currentTaskId || currentTaskId === 'null') {
                        laborContent.setAttribute('data-task-id', task.taskid || '');
                        console.log('🔧 ENSURE ATTRIBUTES: Set data-task-id to', task.taskid);
                    }

                    if (!currentSiteId || currentSiteId === 'null') {
                        laborContent.setAttribute('data-site-id', task.siteid || '');
                        console.log('🔧 ENSURE ATTRIBUTES: Set data-site-id to', task.siteid);
                    }

                    if (!currentParentWonum || currentParentWonum === 'null') {
                        const parentWonum = task.parent || task.wonum || '';
                        laborContent.setAttribute('data-parent-wonum', parentWonum);
                        console.log('🔧 ENSURE ATTRIBUTES: Set data-parent-wonum to', parentWonum);
                    }
                } else {
                    console.error('❌ ENSURE ATTRIBUTES: Task not found in tasksData for wonum:', taskWonum);
                }
            } catch (e) {
                console.error('❌ ENSURE ATTRIBUTES: Error parsing tasksData:', e);
            }
        } else {
            console.error('❌ ENSURE ATTRIBUTES: No tasksData element found');
        }
    } else {
        console.log('✅ ENSURE ATTRIBUTES: All attributes present');
    }
}

function loadTaskLabor(taskWonum, taskStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    const laborContent = document.getElementById(`labor-content-${taskWonum}`);

    // Store the data attributes before replacing content
    const taskId = laborContent.getAttribute('data-task-id');
    const siteId = laborContent.getAttribute('data-site-id');
    const taskWonumAttr = laborContent.getAttribute('data-task-wonum');
    const parentWonum = laborContent.getAttribute('data-parent-wonum');

    // Show enhanced loading in content area
    laborContent.innerHTML = `
        <div class="labor-loading text-center py-4">
            <div class="spinner-border text-success mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="h5 text-success mb-2">Loading Labor Resources</div>
            <div class="text-muted">Please wait while we fetch the labor data...</div>
            <div class="progress mt-3" style="height: 6px;">
                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>
    `;

    // Restore the data attributes after replacing content
    restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

    // Ensure all critical attributes are set
    ensureDataAttributes(laborContent, taskWonum);

    // Make API call to get labor data
    fetch(`/api/task/${taskWonum}/labor?status=${taskStatus}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log(`👷 LABOR LOAD: Response for task ${taskWonum}:`, data);

        if (data.success && data.show_labor) {
            if (data.labor && data.labor.length > 0) {
                // Cache labor data for pagination
                if (!laborPagination[taskWonum]) {
                    laborPagination[taskWonum] = {
                        currentPage: 0,
                        itemsPerPage: window.innerWidth <= 768 ? 1 : data.labor.length,
                        totalItems: data.labor.length
                    };
                }
                laborPagination[taskWonum].cachedLabor = data.labor;

                // Display labor with comprehensive data and pagination
                const laborHtml = generateLaborDisplay(data.labor, taskWonum);
                laborContent.innerHTML = laborHtml;

                // Restore the data attributes after replacing content
                restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

                // Add refresh button after the labor content
                const refreshButtonHtml = `
                    <div class="d-flex justify-content-end mt-3">
                        <button class="btn btn-sm btn-outline-secondary refresh-labor-btn" data-task-wonum="${taskWonum}" data-task-status="${taskStatus}" title="Refresh Labor">
                            <i class="fas fa-sync-alt me-1"></i>
                            <span class="d-none d-md-inline">Refresh</span>
                        </button>
                    </div>
                `;
                laborContent.insertAdjacentHTML('beforeend', refreshButtonHtml);

                // Update load button to show it's loaded
                button.innerHTML = '<i class="fas fa-check me-1"></i>Loaded';
                button.disabled = true;
                button.classList.remove('btn-outline-primary');
                button.classList.add('btn-outline-success');

                // Restore navigation buttons
                restoreNavigationButtons(taskWonum, 'labor');

                // Add event listeners for labor navigation buttons
                setTimeout(() => {
                    const laborNavButtons = document.querySelectorAll('.labor-prev-btn, .labor-next-btn');
                    laborNavButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const taskWonum = this.getAttribute('data-task-wonum');
                            const direction = this.getAttribute('data-direction');
                            console.log('🔄 LABOR NAV CLICK:', direction, 'for task', taskWonum);
                            navigateLabor(taskWonum, direction);
                        });
                    });
                }, 100);

                showNotification('success', `Loaded ${data.labor.length} labor assignments for task ${taskWonum}`);
                console.log(`✅ LABOR LOAD: Successfully loaded ${data.labor.length} labor records for task ${taskWonum}`);
            } else {
                // No labor found
                laborContent.innerHTML = `
                    <div class="labor-empty">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div>No labor assignments found for this task</div>
                        <small class="text-muted">Task ${taskWonum} has no labor records</small>
                    </div>
                `;

                // Restore the data attributes after replacing content
                restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

                button.innerHTML = originalText;
                button.disabled = false;
                console.log(`ℹ️ LABOR LOAD: No labor records found for task ${taskWonum}`);
            }
        } else {
            // Labor not available or error occurred
            laborContent.innerHTML = `
                <div class="labor-empty">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <div>${data.message || 'Labor assignments not available for this task'}</div>
                    <small class="text-muted">Error: ${data.error || 'Unknown error'}</small>
                </div>
            `;

            // Restore the data attributes after replacing content
            restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

            button.innerHTML = originalText;
            button.disabled = false;
            console.log(`❌ LABOR LOAD: Failed to load labor for task ${taskWonum}:`, data.error);
        }
    })
    .catch(error => {
        console.error(`❌ LABOR LOAD: Network error loading labor for task ${taskWonum}:`, error);
        laborContent.innerHTML = `
            <div class="labor-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error loading labor assignments: ${error.message || 'Network error'}
            </div>
        `;

        // Restore the data attributes after replacing content
        restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to load labor assignments');
    });
}

// Function to refresh materials for a specific task
function refreshTaskMaterials(taskWonum, taskStatus, button) {
    console.log(`🔄 Refreshing materials for task ${taskWonum}`);

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
    button.disabled = true;

    const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

    // Show loading in content area
    materialsContent.innerHTML = `
        <div class="materials-loading text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 2rem; height: 2rem;">
                <span class="visually-hidden">Refreshing...</span>
            </div>
            <div class="h6 text-primary mb-2">Refreshing Materials</div>
            <div class="text-muted">Fetching latest materials data...</div>
        </div>
    `;

    // Clear materials cache first
    fetch('/api/task/planned-materials/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(cacheResult => {
        if (cacheResult.success) {
            console.log('✅ Materials cache cleared, fetching fresh data...');

            // Fetch fresh materials data
            return fetch(`/api/task/${taskWonum}/planned-materials?status=${taskStatus}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        } else {
            throw new Error('Failed to clear materials cache');
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.show_materials && data.materials && data.materials.length > 0) {
            // Update cached materials
            if (!materialsPagination[taskWonum]) {
                materialsPagination[taskWonum] = {
                    currentPage: 0,
                    itemsPerPage: window.innerWidth <= 768 ? 1 : data.materials.length,
                    totalItems: data.materials.length
                };
            }
            materialsPagination[taskWonum].cachedMaterials = data.materials;

            // Display refreshed materials
            const materialsHtml = generateMaterialsDisplay(data.materials, taskWonum);
            materialsContent.innerHTML = materialsHtml;

            // Re-add refresh button after the refreshed content
            const refreshButtonHtml = `
                <div class="d-flex justify-content-end mt-3">
                    <button class="btn btn-sm btn-outline-secondary refresh-materials-btn" data-task-wonum="${taskWonum}" data-task-status="${taskStatus}" title="Refresh Materials">
                        <i class="fas fa-sync-alt me-1"></i>
                        <span class="d-none d-md-inline">Refresh</span>
                    </button>
                </div>
            `;
            materialsContent.insertAdjacentHTML('beforeend', refreshButtonHtml);

            // Restore navigation buttons
            restoreNavigationButtons(taskWonum, 'materials');

            showNotification('success', `Refreshed ${data.materials.length} materials for task ${taskWonum}`);
        } else {
            materialsContent.innerHTML = `
                <div class="materials-empty">
                    <i class="fas fa-box-open fa-2x mb-2"></i>
                    <div>No planned materials found for this task</div>
                </div>
            `;
        }

        // Restore button
        button.innerHTML = originalText;
        button.disabled = false;
    })
    .catch(error => {
        console.error('❌ Error refreshing materials:', error);
        materialsContent.innerHTML = `
            <div class="materials-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error refreshing materials: ${error.message || 'Unknown error'}
            </div>
        `;
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to refresh materials');
    });
}

// Function to refresh labor for a specific task
function refreshTaskLabor(taskWonum, taskStatus, button) {
    console.log(`🔄 Refreshing labor for task ${taskWonum}`);

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
    button.disabled = true;

    const laborContent = document.getElementById(`labor-content-${taskWonum}`);

    // Store the data attributes before replacing content
    const taskId = laborContent.getAttribute('data-task-id');
    const siteId = laborContent.getAttribute('data-site-id');
    const taskWonumAttr = laborContent.getAttribute('data-task-wonum');
    const parentWonum = laborContent.getAttribute('data-parent-wonum');

    // Show loading in content area
    laborContent.innerHTML = `
        <div class="labor-loading text-center py-4">
            <div class="spinner-border text-success mb-3" role="status" style="width: 2rem; height: 2rem;">
                <span class="visually-hidden">Refreshing...</span>
            </div>
            <div class="h6 text-success mb-2">Refreshing Labor</div>
            <div class="text-muted">Fetching latest labor data...</div>
        </div>
    `;

    // Restore the data attributes after replacing content
    restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

    // Fetch fresh labor data
    fetch(`/api/task/${taskWonum}/labor?status=${taskStatus}&refresh=true`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.show_labor && data.labor && data.labor.length > 0) {
            // Update cached labor
            if (!laborPagination[taskWonum]) {
                laborPagination[taskWonum] = {
                    currentPage: 0,
                    itemsPerPage: window.innerWidth <= 768 ? 1 : data.labor.length,
                    totalItems: data.labor.length
                };
            }
            laborPagination[taskWonum].cachedLabor = data.labor;

            // Display refreshed labor
            const laborHtml = generateLaborDisplay(data.labor, taskWonum);
            laborContent.innerHTML = laborHtml;

            // Restore the data attributes after replacing content
            restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

            // Re-add refresh button after the refreshed content
            const refreshButtonHtml = `
                <div class="d-flex justify-content-end mt-3">
                    <button class="btn btn-sm btn-outline-secondary refresh-labor-btn" data-task-wonum="${taskWonum}" data-task-status="${taskStatus}" title="Refresh Labor">
                        <i class="fas fa-sync-alt me-1"></i>
                        <span class="d-none d-md-inline">Refresh</span>
                    </button>
                </div>
            `;
            laborContent.insertAdjacentHTML('beforeend', refreshButtonHtml);

            // Restore navigation buttons
            restoreNavigationButtons(taskWonum, 'labor');

            showNotification('success', `Refreshed ${data.labor.length} labor assignments for task ${taskWonum}`);
        } else {
            laborContent.innerHTML = `
                <div class="labor-empty">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <div>No labor assignments found for this task</div>
                </div>
            `;

            // Restore the data attributes after replacing content
            restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);
        }

        // Restore button
        button.innerHTML = originalText;
        button.disabled = false;
    })
    .catch(error => {
        console.error('❌ Error refreshing labor:', error);
        laborContent.innerHTML = `
            <div class="labor-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error refreshing labor: ${error.message || 'Unknown error'}
            </div>
        `;

        // Restore the data attributes after replacing content
        restoreDataAttributes(laborContent, taskId, siteId, taskWonumAttr, parentWonum);

        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to refresh labor');
    });
}

// Function to generate comprehensive labor display with pagination
function generateLaborDisplay(laborRecords, taskWonum = null) {
    // Initialize pagination for this task if not exists
    if (taskWonum && !laborPagination[taskWonum]) {
        laborPagination[taskWonum] = {
            currentPage: 0,
            itemsPerPage: window.innerWidth <= 768 ? 1 : laborRecords.length, // 1 item per page on mobile
            totalItems: laborRecords.length
        };
    }

    const pagination = taskWonum ? laborPagination[taskWonum] : null;
    const isMobile = window.innerWidth <= 768;

    // Get labor records for current page
    let displayLabor = laborRecords;
    if (pagination && isMobile) {
        const startIndex = pagination.currentPage * pagination.itemsPerPage;
        const endIndex = startIndex + pagination.itemsPerPage;
        displayLabor = laborRecords.slice(startIndex, endIndex);
    }
    // Desktop table view
    let desktopHtml = `
        <div class="labor-desktop-view d-none d-lg-block">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Labor Code</th>
                        <th>Hours</th>
                        <th>Craft</th>
                        <th>Start Date</th>
                        <th>Finish Date</th>
                        <th>Regular Hrs</th>
                        <th>Premium Hrs</th>
                        <th>Transaction Type</th>
                        <th>Trans ID</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    displayLabor.forEach(labor => {
        desktopHtml += `
            <tr>
                <td class="labor-code"><strong>${labor.laborcode || 'N/A'}</strong></td>
                <td class="labor-hours">${labor.laborhrs || 0} hrs</td>
                <td class="labor-craft">${labor.craft || 'N/A'}</td>
                <td class="labor-start">${formatDateTime(labor.startdate)}</td>
                <td class="labor-finish">${formatDateTime(labor.finishdate)}</td>
                <td class="labor-regular">${labor.regularhrs || 0} hrs</td>
                <td class="labor-premium">${labor.premiumpayhours || 0} hrs</td>
                <td class="labor-transtype">${labor.transtype || 'N/A'}</td>
                <td class="labor-transid">${labor.labtransid || 'N/A'}</td>
                <td class="labor-actions">
                    ${(labor.regularhrs || 0) > 0 ? `
                    <button class="btn btn-sm btn-outline-warning delete-labor-btn"
                            data-labor-id="${labor.labtransid || ''}"
                            data-labor-code="${labor.laborcode || ''}"
                            data-labor-hours="${labor.regularhrs || 0}"
                            data-craft="${labor.craft || ''}"
                            onclick="deleteLaborEntry(this)"
                            title="Add negative hours to offset this entry">
                        <i class="fas fa-minus me-1"></i>Subtract Hours
                    </button>
                    ` : `
                    <span class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>Negative Entry
                    </span>
                    `}
                </td>
            </tr>
        `;
    });

    desktopHtml += `
                </tbody>
            </table>
        </div>
    `;

    // Mobile card view with pagination
    let mobileHtml = `<div class="labor-mobile-view d-lg-none">`;

    // Add pagination controls for mobile
    if (pagination && isMobile && laborRecords.length > 1) {
        const totalPages = Math.ceil(laborRecords.length / pagination.itemsPerPage);
        const prevDisabled = pagination.currentPage === 0 ? 'disabled' : '';
        const nextDisabled = pagination.currentPage >= totalPages - 1 ? 'disabled' : '';

        mobileHtml += `
            <div class="resource-pagination-controls mb-3">
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn labor-prev-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="prev"
                        ${prevDisabled}>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="pagination-info-small">
                    ${pagination.currentPage + 1} of ${totalPages}
                </span>
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn labor-next-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="next"
                        ${nextDisabled}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    displayLabor.forEach(labor => {
        mobileHtml += `
            <div class="labor-card-enhanced">
                <div class="labor-card-header-enhanced">
                    <div class="labor-info">
                        <div class="labor-icon">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <div>
                            <h6 class="labor-code-enhanced">${labor.laborcode || 'N/A'}</h6>
                            <p class="labor-craft-enhanced">${labor.craft || 'No craft specified'}</p>
                        </div>
                    </div>
                    <div class="labor-hours-badge">
                        <i class="fas fa-clock"></i>
                        ${labor.laborhrs || 0} hrs
                    </div>
                </div>

                <div class="labor-details-grid">
                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Regular Hours</p>
                            <p class="labor-detail-value-enhanced">${formatHoursAsTime(labor.regularhrs)} (${labor.regularhrs || 0} hrs)</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Premium Hours</p>
                            <p class="labor-detail-value-enhanced">${labor.premiumpayhours || 0} hrs</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Trans ID</p>
                            <p class="labor-detail-value-enhanced">${labor.labtransid || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Transaction Type</p>
                            <p class="labor-detail-value-enhanced">${labor.transtype || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Start Date</p>
                            <p class="labor-detail-value-enhanced">${formatDateTime(labor.startdate)}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                            <i class="fas fa-calendar-minus"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Finish Date</p>
                            <p class="labor-detail-value-enhanced">${formatDateTime(labor.finishdate)}</p>
                        </div>
                    </div>
                </div>

                <div class="labor-actions-enhanced">
                    ${(labor.regularhrs || 0) > 0 ? `
                    <button class="labor-action-btn btn-outline-warning delete-labor-btn"
                            data-labor-id="${labor.labtransid || ''}"
                            data-labor-code="${labor.laborcode || ''}"
                            data-labor-hours="${labor.regularhrs || 0}"
                            data-craft="${labor.craft || ''}"
                            onclick="deleteLaborEntry(this)"
                            title="Add negative hours to offset this entry">
                        <i class="fas fa-minus"></i>Subtract Hours
                    </button>
                    ` : `
                    <span class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>Negative Entry
                    </span>
                    `}
                </div>
            </div>
        `;
    });

    mobileHtml += `</div>`;

    return desktopHtml + mobileHtml;
}














function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    let alertClass = 'alert-danger'; // default to error
    if (type === 'success') alertClass = 'alert-success';
    else if (type === 'info') alertClass = 'alert-info';
    else if (type === 'warning') alertClass = 'alert-warning';

    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1019; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Global function to refresh materials after material addition
function refreshMaterials() {
    console.log('🔄 Refreshing materials after material addition...');

    // Clear materials cache first
    fetch('/api/task/planned-materials/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ Materials cache cleared successfully');

            // Refresh all loaded materials sections
            const loadedMaterialsButtons = document.querySelectorAll('.load-materials-btn');
            loadedMaterialsButtons.forEach(button => {
                const taskWonum = button.getAttribute('data-task-wonum');
                const taskStatus = button.getAttribute('data-task-status');
                const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

                // Only refresh if materials were already loaded (not showing the initial load button)
                if (materialsContent && !materialsContent.querySelector('.materials-loading') &&
                    materialsContent.innerHTML.trim() !== '' &&
                    !materialsContent.innerHTML.includes('Load Materials')) {

                    console.log(`🔄 Refreshing materials for task ${taskWonum}`);
                    loadPlannedMaterials(taskWonum, taskStatus, button);
                }
            });

            showNotification('success', 'Materials refreshed successfully');
        } else {
            console.error('❌ Failed to clear materials cache:', data.error);
            showNotification('error', 'Failed to refresh materials cache');
        }
    })
    .catch(error => {
        console.error('❌ Error clearing materials cache:', error);
        showNotification('error', 'Network error while refreshing materials');
    });
}

// Attachment Management Functions
let attachmentModal = null;
let attachmentDetailsModal = null;

// Mobile pagination variables
let currentAttachmentPage = 1;
let attachmentsPerPage = 1; // Mobile: 1 per page
let totalAttachments = 0;
let allAttachments = [];

// Camera variables
let cameraStream = null;
let currentFacingMode = 'environment'; // 'user' for front, 'environment' for back
let capturedImageBlob = null;

// Initialize attachment functionality when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modals
    attachmentModal = new bootstrap.Modal(document.getElementById('addAttachmentModal'));
    attachmentDetailsModal = new bootstrap.Modal(document.getElementById('attachmentDetailsModal'));

    // Add modal event listeners for camera cleanup
    const addAttachmentModalElement = document.getElementById('addAttachmentModal');
    if (addAttachmentModalElement) {
        addAttachmentModalElement.addEventListener('hidden.bs.modal', function() {
            // Clean up camera resources when modal is closed
            resetCameraCapture();
        });
    }

    // Setup attachment form submission
    const attachmentForm = document.getElementById('addAttachmentForm');
    if (attachmentForm) {
        attachmentForm.addEventListener('submit', handleAttachmentUpload);
    }

    // Setup file input change handler
    const fileInput = document.getElementById('attachmentFile');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Auto-fill description with filename if empty
                const descInput = document.getElementById('attachmentDescription');
                if (!descInput.value) {
                    descInput.value = file.name;
                }

                // Auto-select appropriate document type based on file extension
                const doctypeSelect = document.getElementById('attachmentDoctype');
                if (doctypeSelect) {
                    const fileName = file.name.toLowerCase();
                    const extension = fileName.split('.').pop();

                    // Image files -> Images category
                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg', 'webp'].includes(extension)) {
                        doctypeSelect.value = 'Images';
                    }
                    // Diagram/Drawing files -> Diagrams category
                    else if (['dwg', 'dxf', 'pdf', 'vsd', 'vsdx'].includes(extension) &&
                             (fileName.includes('diagram') || fileName.includes('drawing') || fileName.includes('plan') || fileName.includes('blueprint'))) {
                        doctypeSelect.value = 'Diagrams';
                    }
                    // Default to Attachments for all other files
                    else {
                        doctypeSelect.value = 'Attachments';
                    }
                }
            }
        });
    }

    // Setup camera source buttons
    const fileSourceBtn = document.getElementById('fileSourceBtn');
    const cameraSourceBtn = document.getElementById('cameraSourceBtn');

    if (fileSourceBtn && cameraSourceBtn) {
        fileSourceBtn.addEventListener('click', function() {
            switchAttachmentSource('file');
        });

        cameraSourceBtn.addEventListener('click', function() {
            switchAttachmentSource('camera');
        });
    }

    // Setup camera controls
    const captureBtn = document.getElementById('captureBtn');
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    const stopCameraBtn = document.getElementById('stopCameraBtn');

    if (captureBtn) {
        captureBtn.addEventListener('click', function() {
            if (cameraStream) {
                capturePhoto();
            } else {
                startCamera();
            }
        });
    }

    if (switchCameraBtn) {
        switchCameraBtn.addEventListener('click', switchCamera);
    }

    if (stopCameraBtn) {
        stopCameraBtn.addEventListener('click', stopCamera);
    }

    // Handle window resize to switch between mobile and desktop views
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (allAttachments.length > 0) {
                // Reset to first page when switching views
                currentAttachmentPage = 1;
                displayAttachments(allAttachments, workorderData?.wonum);
            }
        }, 250);
    });
});

function openAddAttachmentModal() {
    if (attachmentModal) {
        // Reset form
        document.getElementById('addAttachmentForm').reset();

        // Reset camera state and switch to file upload mode
        resetCameraCapture();
        switchAttachmentSource('file');

        attachmentModal.show();
    }
}

function loadAttachments() {
    const wonum = workorderData?.wonum;
    if (!wonum) {
        console.error('No work order number available');
        showNotification('error', 'No work order number available');
        return;
    }

    const attachmentsList = document.getElementById('attachments-list');
    if (!attachmentsList) return;

    console.log(`📎 Loading attachments for work order ${wonum}`);

    // Show enhanced loading state
    attachmentsList.innerHTML = `
        <div class="attachments-loading text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="h5 text-primary mb-2">Loading Attachments</div>
            <div class="text-muted">Please wait while we fetch the attachment data...</div>
            <div class="progress mt-3" style="height: 6px;">
                <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>
    `;

    // Fetch attachments
    fetch(`/api/workorder/${wonum}/attachments`)
        .then(response => {
            console.log(`📎 Attachments API response status: ${response.status}`);
            return response.json();
        })
        .then(data => {
            console.log('📎 Attachments API response:', data);

            if (data.success) {
                displayAttachments(data.attachments, wonum);
                updateAttachmentsCount(data.count);
                showNotification('success', `Loaded ${data.count} attachment(s) successfully`);
            } else {
                console.error('📎 Attachments API error:', data.error);
                attachmentsList.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Unable to load attachments:</strong> ${data.error}
                        <br><small class="text-muted">This may be normal if the work order has no attachments or if there are permission restrictions.</small>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="loadAttachments()">
                                <i class="fas fa-retry me-1"></i>Try Again
                            </button>
                            <button class="btn btn-primary btn-sm ms-2" onclick="openAddAttachmentModal()">
                                <i class="fas fa-plus me-1"></i>Add First Attachment
                            </button>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('📎 Error loading attachments:', error);
            attachmentsList.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Network error loading attachments</strong>
                    <br><small class="text-muted">Please check your connection and try again.</small>
                    <div class="mt-3">
                        <button class="btn btn-outline-danger btn-sm" onclick="loadAttachments()">
                            <i class="fas fa-retry me-1"></i>Retry
                        </button>
                    </div>
                </div>
            `;
            showNotification('error', 'Network error loading attachments');
        });
}

function displayAttachments(attachments, wonum) {
    const attachmentsList = document.getElementById('attachments-list');
    if (!attachmentsList) return;

    if (!attachments || attachments.length === 0) {
        attachmentsList.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-paperclip fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Attachments Found</h5>
                <p class="text-muted">This work order doesn't have any attachments yet.</p>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="openAddAttachmentModal()">
                        <i class="fas fa-plus me-1"></i>Add First Attachment
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="loadAttachments()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>
        `;
        return;
    }

    // Store attachments for pagination
    allAttachments = attachments;
    totalAttachments = attachments.length;

    // Check if mobile view
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        displayMobileAttachments(attachments, wonum);
    } else {
        displayDesktopAttachments(attachments, wonum);
    }
}

function displayDesktopAttachments(attachments, wonum) {
    const attachmentsList = document.getElementById('attachments-list');

    // Add header with refresh button
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
                <i class="fas fa-paperclip me-2"></i>Found ${attachments.length} attachment(s)
            </h6>
            <button class="btn btn-outline-secondary btn-sm" onclick="loadAttachments()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
        <div class="row">
    `;

    attachments.forEach(attachment => {
        html += generateAttachmentCard(attachment, false);
    });

    html += '</div>';
    attachmentsList.innerHTML = html;
}

function displayMobileAttachments(attachments, wonum) {
    const attachmentsList = document.getElementById('attachments-list');

    // Calculate pagination
    const totalPages = Math.ceil(totalAttachments / attachmentsPerPage);
    const startIndex = (currentAttachmentPage - 1) * attachmentsPerPage;
    const endIndex = startIndex + attachmentsPerPage;
    const currentAttachments = attachments.slice(startIndex, endIndex);

    // Add header with pagination info
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
                <i class="fas fa-paperclip me-2"></i>Attachment ${currentAttachmentPage} of ${totalAttachments}
            </h6>
            <button class="btn btn-outline-secondary btn-sm" onclick="loadAttachments()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    `;

    // Add pagination navigation
    if (totalPages > 1) {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-3 mobile-attachment-nav">
                <button class="btn btn-outline-primary btn-sm" onclick="previousAttachment()" ${currentAttachmentPage <= 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left me-1"></i>Previous
                </button>
                <span class="badge bg-primary">Page ${currentAttachmentPage} of ${totalPages}</span>
                <button class="btn btn-outline-primary btn-sm" onclick="nextAttachment()" ${currentAttachmentPage >= totalPages ? 'disabled' : ''}>
                    Next<i class="fas fa-chevron-right ms-1"></i>
                </button>
            </div>
        `;
    }

    // Display current attachment(s)
    html += '<div class="mobile-attachments-container">';
    currentAttachments.forEach(attachment => {
        html += generateAttachmentCard(attachment, true);
    });
    html += '</div>';

    attachmentsList.innerHTML = html;
}

function generateAttachmentCard(attachment, isMobile) {
    const fileIcon = getFileIcon(attachment.file_extension);
    const filename = attachment.filename || attachment.document || attachment.urlname || 'Unknown File';
    const description = attachment.description || 'No description';

    // Show what Maximo actually provides
    const maximoFields = [];
    if (attachment.docinfoid) maximoFields.push(`ID: ${attachment.docinfoid}`);
    if (attachment.urltype) maximoFields.push(`Type: ${attachment.urltype}`);

    // Get category badge for primary Maximo categories
    const getCategoryBadge = (doctype) => {
        switch(doctype) {
            case 'Attachments': return '<span class="badge bg-primary me-1">📎 Attachments</span>';
            case 'Diagrams': return '<span class="badge bg-info me-1">📊 Diagrams</span>';
            case 'Images': return '<span class="badge bg-success me-1">🖼️ Images</span>';
            default: return doctype ? `<span class="badge bg-secondary me-1">${doctype}</span>` : '';
        }
    };

    const categoryBadge = getCategoryBadge(attachment.doctype);
    const cardClass = isMobile ? 'mb-3' : 'col-md-6 col-lg-4 mb-3';
    const titleLength = isMobile ? 30 : 25;

    return `
        <div class="${cardClass}">
            <div class="card attachment-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-start">
                        <div class="attachment-icon me-3">
                            <i class="fas ${fileIcon} fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <h6 class="card-title mb-0 me-2" title="${filename}">${filename.length > titleLength ? filename.substring(0, titleLength) + '...' : filename}</h6>
                                ${categoryBadge}
                            </div>
                            <p class="card-text text-muted small mb-2">${description}</p>
                            <div class="attachment-meta">
                                ${attachment.changeby ? `<small class="text-muted"><i class="fas fa-user me-1"></i>${attachment.changeby}</small><br>` : ''}
                                ${attachment.createdate ? `<small class="text-muted"><i class="fas fa-calendar me-1"></i>${formatDateTime(attachment.createdate)}</small><br>` : ''}
                                ${maximoFields.length > 0 ? `<small class="text-muted">${maximoFields.join(' • ')}</small>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-info btn-sm" onclick="viewAttachmentDetails('${attachment.docinfoid}', ${JSON.stringify(attachment).replace(/"/g, '&quot;')})">
                            <i class="fas fa-info me-1"></i>Info
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="viewAttachmentFile('${attachment.docinfoid}')">
                            <i class="fas fa-eye me-1"></i>View
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="downloadAttachment('${attachment.docinfoid}')">
                            <i class="fas fa-download me-1"></i>Download
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteAttachment('${attachment.docinfoid}')">
                            <i class="fas fa-trash me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Mobile pagination functions
function nextAttachment() {
    const totalPages = Math.ceil(totalAttachments / attachmentsPerPage);
    if (currentAttachmentPage < totalPages) {
        currentAttachmentPage++;
        displayMobileAttachments(allAttachments, workorderData?.wonum);
    }
}

function previousAttachment() {
    if (currentAttachmentPage > 1) {
        currentAttachmentPage--;
        displayMobileAttachments(allAttachments, workorderData?.wonum);
    }
}

function updateAttachmentsCount(count) {
    const countBadge = document.getElementById('attachments-count');
    if (countBadge) {
        countBadge.textContent = count || 0;
    }
}

function getFileIcon(extension) {
    const iconMap = {
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word',
        'docx': 'fa-file-word',
        'xls': 'fa-file-excel',
        'xlsx': 'fa-file-excel',
        'ppt': 'fa-file-powerpoint',
        'pptx': 'fa-file-powerpoint',
        'txt': 'fa-file-alt',
        'jpg': 'fa-file-image',
        'jpeg': 'fa-file-image',
        'png': 'fa-file-image',
        'gif': 'fa-file-image',
        'zip': 'fa-file-archive',
        'rar': 'fa-file-archive',
        '7z': 'fa-file-archive',
        'mp4': 'fa-file-video',
        'avi': 'fa-file-video',
        'mov': 'fa-file-video',
        'mp3': 'fa-file-audio',
        'wav': 'fa-file-audio'
    };

    return iconMap[extension?.toLowerCase()] || 'fa-file';
}

function formatFileSize(bytes) {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function handleAttachmentUpload(event) {
    event.preventDefault();

    const wonum = workorderData?.wonum;
    if (!wonum) {
        showNotification('error', 'No work order number available');
        return;
    }

    // Check if we're using camera capture or file upload
    const cameraSourceBtn = document.getElementById('cameraSourceBtn');
    const isUsingCamera = cameraSourceBtn && cameraSourceBtn.classList.contains('active');

    let formData;

    if (isUsingCamera) {
        // Handle camera capture
        if (!capturedImageBlob) {
            showNotification('error', 'Please capture a photo first');
            return;
        }

        formData = new FormData();

        // Create a filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `camera-capture-${timestamp}.jpg`;

        // Add the captured image as a file
        formData.append('file', capturedImageBlob, filename);

        // Add description and document type from form
        const description = document.getElementById('attachmentDescription').value || `Camera capture - ${new Date().toLocaleString()}`;
        const doctype = document.getElementById('attachmentDoctype').value || 'Images';

        formData.append('description', description);
        formData.append('doctype', doctype);
    } else {
        // Handle regular file upload
        const fileInput = document.getElementById('attachmentFile');
        if (!fileInput.files[0]) {
            showNotification('error', 'Please select a file');
            return;
        }

        formData = new FormData(event.target);
    }

    const uploadBtn = document.getElementById('uploadAttachmentBtn');
    const originalText = uploadBtn.innerHTML;

    // Show loading state
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
    uploadBtn.disabled = true;

    // Upload attachment
    fetch(`/api/workorder/${wonum}/attachments`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('success', 'Attachment uploaded successfully');

            // Clean up camera resources if used
            if (isUsingCamera) {
                resetCameraCapture();
                // Switch back to file upload mode
                switchAttachmentSource('file');
            }

            attachmentModal.hide();
            loadAttachments(); // Refresh the attachments list
        } else {
            showNotification('error', `Upload failed: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('Error uploading attachment:', error);
        showNotification('error', 'Network error during upload');
    })
    .finally(() => {
        // Restore button state
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
    });
}

function viewAttachmentDetails(docinfoid, attachmentData) {
    try {
        // Parse the attachment data if it's a string
        const attachment = typeof attachmentData === 'string' ? JSON.parse(attachmentData) : attachmentData;
        showAttachmentDetails(attachment);
    } catch (error) {
        console.error('Error parsing attachment data:', error);
        showNotification('error', 'Error displaying attachment details');
    }
}

function viewAttachment(docinfoid) {
    // Fallback function for compatibility
    viewAttachmentDetails(docinfoid, null);
}

function showAttachmentDetails(attachment) {
    const content = document.getElementById('attachmentDetailsContent');
    const fileIcon = getFileIcon(attachment.file_extension);
    const filename = attachment.filename || attachment.document || attachment.urlname || 'Unknown File';

    // Build the details view showing all Maximo data
    let detailsHtml = `
        <div class="text-center mb-4">
            <i class="fas ${fileIcon} fa-4x text-primary mb-3"></i>
            <h5>${filename}</h5>
        </div>

        <div class="row">
            <div class="col-sm-4"><strong>Description:</strong></div>
            <div class="col-sm-8">${attachment.description || 'No description'}</div>
        </div>
    `;

    // Show all available Maximo fields
    const maximoFields = [
        { key: 'docinfoid', label: 'Document ID' },
        { key: 'doctype', label: 'Document Type' },
        { key: 'urltype', label: 'URL Type' },
        { key: 'urlname', label: 'URL Name' },
        { key: 'document', label: 'Document Path' },
        { key: 'changeby', label: 'Created By' },
        { key: 'createdate', label: 'Created Date' },
        { key: 'href', label: 'Maximo Reference' }
    ];

    maximoFields.forEach(field => {
        const value = attachment[field.key];
        if (value) {
            let displayValue = value;
            if (field.key === 'createdate') {
                displayValue = formatDateTime(value);
            } else if (field.key === 'href') {
                displayValue = `<a href="${value}" target="_blank" class="text-primary">${value}</a>`;
            }

            detailsHtml += `
                <div class="row mt-2">
                    <div class="col-sm-4"><strong>${field.label}:</strong></div>
                    <div class="col-sm-8">${displayValue}</div>
                </div>
            `;
        }
    });

    // Show raw Maximo data if available
    if (attachment.original_data) {
        detailsHtml += `
            <div class="mt-4">
                <h6><strong>Raw Maximo Data:</strong></h6>
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0" style="font-size: 0.8rem; max-height: 200px; overflow-y: auto;">${JSON.stringify(attachment.original_data, null, 2)}</pre>
                </div>
            </div>
        `;
    }

    content.innerHTML = detailsHtml;

    // Setup modal buttons
    const viewBtn = document.getElementById('viewAttachmentBtn');
    const downloadBtn = document.getElementById('downloadAttachmentBtn');
    const deleteBtn = document.getElementById('deleteAttachmentBtn');

    viewBtn.onclick = () => viewAttachmentFile(attachment.docinfoid);
    downloadBtn.onclick = () => downloadAttachment(attachment.docinfoid);
    deleteBtn.onclick = () => {
        attachmentDetailsModal.hide();
        deleteAttachment(attachment.docinfoid);
    };

    // Show the modal
    attachmentDetailsModal.show();
}

function downloadAttachment(docinfoid) {
    const wonum = workorderData?.wonum;
    if (!wonum) {
        showNotification('error', 'No work order number available');
        return;
    }

    // Show loading notification
    showNotification('info', 'Downloading attachment...');

    // Create a temporary link to trigger download
    const downloadUrl = `/api/workorder/${wonum}/attachments/${docinfoid}/download`;

    // Use fetch to handle the download
    fetch(downloadUrl)
        .then(response => {
            if (response.ok) {
                // Get filename from Content-Disposition header or use default
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `attachment_${docinfoid}`;

                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Convert response to blob and create download link
                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showNotification('success', `Downloaded: ${filename}`);
                });
            } else {
                // Handle error response
                return response.json().then(errorData => {
                    throw new Error(errorData.error || 'Download failed');
                });
            }
        })
        .catch(error => {
            console.error('Error downloading attachment:', error);
            showNotification('error', `Download failed: ${error.message}`);
        });
}

function viewAttachmentFile(docinfoid) {
    const wonum = workorderData?.wonum;
    if (!wonum) {
        showNotification('error', 'No work order number available');
        return;
    }

    // Show loading notification
    showNotification('info', 'Loading attachment for viewing...');

    // Try to view the attachment inline
    const viewUrl = `/api/workorder/${wonum}/attachments/${docinfoid}/view`;

    fetch(viewUrl)
        .then(response => {
            if (response.ok) {
                // Get content type to determine how to display
                const contentType = response.headers.get('Content-Type');

                // Get filename from Content-Disposition header
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `attachment_${docinfoid}`;

                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Create blob URL and open in new tab
                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const newWindow = window.open(url, '_blank');

                    if (newWindow) {
                        newWindow.document.title = filename;
                        showNotification('success', `Opened: ${filename}`);

                        // Clean up the blob URL after a delay
                        setTimeout(() => {
                            window.URL.revokeObjectURL(url);
                        }, 10000);
                    } else {
                        showNotification('error', 'Popup blocked. Please allow popups and try again.');
                        window.URL.revokeObjectURL(url);
                    }
                });
            } else {
                // Handle error response
                return response.json().then(errorData => {
                    if (errorData.downloadable) {
                        showNotification('warning', `${errorData.error} Downloading instead...`);
                        downloadAttachment(docinfoid);
                    } else {
                        throw new Error(errorData.error || 'View failed');
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error viewing attachment:', error);
            showNotification('error', `View failed: ${error.message}`);
        });
}

function deleteAttachment(docinfoid) {
    const wonum = workorderData?.wonum;
    if (!wonum) {
        showNotification('error', 'No work order number available');
        return;
    }

    if (!confirm('Are you sure you want to delete this attachment? This action cannot be undone.')) {
        return;
    }

    // Delete attachment
    fetch(`/api/workorder/${wonum}/attachments/${docinfoid}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('success', 'Attachment deleted successfully');
            loadAttachments(); // Refresh the attachments list
        } else {
            showNotification('error', `Delete failed: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('Error deleting attachment:', error);
        showNotification('error', 'Network error during deletion');
    });
}

function refreshAttachments() {
    loadAttachments();
}

// Camera functionality
function switchAttachmentSource(source) {
    const fileSourceBtn = document.getElementById('fileSourceBtn');
    const cameraSourceBtn = document.getElementById('cameraSourceBtn');
    const fileSection = document.getElementById('fileUploadSection');
    const cameraSection = document.getElementById('cameraSection');
    const fileInput = document.getElementById('attachmentFile');

    // Update button states
    fileSourceBtn.classList.toggle('active', source === 'file');
    cameraSourceBtn.classList.toggle('active', source === 'camera');

    // Show/hide sections
    if (source === 'file') {
        fileSection.style.display = 'block';
        cameraSection.style.display = 'none';
        fileInput.required = true;
        stopCamera(); // Stop camera if switching away
        resetCameraCapture();
    } else {
        fileSection.style.display = 'none';
        cameraSection.style.display = 'block';
        fileInput.required = false;
        updateCameraStatus('Click "Capture Photo" to start camera');
    }
}

async function startCamera() {
    try {
        updateCameraStatus('Starting camera...');

        // Check if device has camera support
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Camera not supported in this browser');
        }

        // Mobile-optimized constraints
        const isMobile = window.innerWidth <= 768;
        const constraints = {
            video: {
                facingMode: currentFacingMode,
                width: { ideal: isMobile ? 640 : 1280 },
                height: { ideal: isMobile ? 480 : 720 }
            }
        };

        cameraStream = await navigator.mediaDevices.getUserMedia(constraints);
        const video = document.getElementById('cameraPreview');
        video.srcObject = cameraStream;

        // Wait for video to be ready
        video.onloadedmetadata = function() {
            updateCameraStatus('Camera ready - Click capture button to take photo');
        };

        // Update capture button
        const captureBtn = document.getElementById('captureBtn');
        captureBtn.innerHTML = '<i class="fas fa-camera"></i>';
        captureBtn.title = 'Capture Photo';

        // Show camera controls and check for multiple cameras
        const stopCameraBtn = document.getElementById('stopCameraBtn');
        if (stopCameraBtn) stopCameraBtn.style.display = 'flex';

        // Check if multiple cameras are available for switch button
        checkCameraAvailability();

    } catch (error) {
        console.error('Error accessing camera:', error);
        let errorMessage = 'Unable to access camera. ';

        if (error.name === 'NotAllowedError') {
            errorMessage += 'Please allow camera permissions and try again.';
        } else if (error.name === 'NotFoundError') {
            errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage += 'Camera not supported in this browser.';
        } else {
            errorMessage += error.message;
        }

        updateCameraStatus(errorMessage, 'error');
        showNotification('error', errorMessage);
    }
}

function capturePhoto() {
    const video = document.getElementById('cameraPreview');
    const canvas = document.getElementById('captureCanvas');
    const context = canvas.getContext('2d');

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0);

    // Convert to blob
    canvas.toBlob(function(blob) {
        capturedImageBlob = blob;

        // Show captured image preview
        const capturedImagePreview = document.getElementById('capturedImagePreview');
        const capturedImage = document.getElementById('capturedImage');
        const cameraPreview = document.getElementById('cameraPreview');

        capturedImage.src = URL.createObjectURL(blob);
        cameraPreview.style.display = 'none';
        capturedImagePreview.style.display = 'block';

        // Auto-fill description and document type
        const descInput = document.getElementById('attachmentDescription');
        const doctypeSelect = document.getElementById('attachmentDoctype');

        if (!descInput.value) {
            const timestamp = new Date().toLocaleString();
            descInput.value = `Camera capture - ${timestamp}`;
        }

        if (doctypeSelect) {
            doctypeSelect.value = 'Images'; // Auto-select Images for camera captures
        }

        updateCameraStatus('Photo captured! You can retake or upload this photo.');

    }, 'image/jpeg', 0.8);
}

async function switchCamera() {
    if (!cameraStream) {
        showNotification('warning', 'Please start the camera first');
        return;
    }

    try {
        // Check if device has multiple cameras
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        if (videoDevices.length < 2) {
            showNotification('info', 'Only one camera available on this device');
            return;
        }

        // Toggle facing mode
        currentFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';

        updateCameraStatus('Switching camera...');

        // Stop current stream
        stopCamera();

        // Start with new facing mode
        await startCamera();

    } catch (error) {
        console.error('Error switching camera:', error);
        showNotification('error', 'Unable to switch camera');
    }
}

function stopCamera() {
    if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop());
        cameraStream = null;

        const video = document.getElementById('cameraPreview');
        video.srcObject = null;

        updateCameraStatus('Camera stopped. Click "Capture Photo" to restart.');

        // Reset capture button
        const captureBtn = document.getElementById('captureBtn');
        captureBtn.innerHTML = '<i class="fas fa-camera"></i>';
        captureBtn.title = 'Start Camera';

        // Hide camera controls
        const switchCameraBtn = document.getElementById('switchCameraBtn');
        const stopCameraBtn = document.getElementById('stopCameraBtn');
        if (switchCameraBtn) switchCameraBtn.style.display = 'none';
        if (stopCameraBtn) stopCameraBtn.style.display = 'none';
    }
}

function retakePhoto() {
    const capturedImagePreview = document.getElementById('capturedImagePreview');
    const cameraPreview = document.getElementById('cameraPreview');

    // Hide captured image and show camera preview
    capturedImagePreview.style.display = 'none';
    cameraPreview.style.display = 'block';

    // Clear captured image data
    capturedImageBlob = null;

    // Clear the image src to free memory
    const capturedImage = document.getElementById('capturedImage');
    if (capturedImage.src) {
        URL.revokeObjectURL(capturedImage.src);
        capturedImage.src = '';
    }

    updateCameraStatus('Camera ready - Click capture button to take photo');
}

function resetCameraCapture() {
    // Stop camera
    stopCamera();

    // Reset UI
    const capturedImagePreview = document.getElementById('capturedImagePreview');
    const cameraPreview = document.getElementById('cameraPreview');

    capturedImagePreview.style.display = 'none';
    cameraPreview.style.display = 'block';

    // Clear captured image data
    capturedImageBlob = null;

    // Clear the image src to free memory
    const capturedImage = document.getElementById('capturedImage');
    if (capturedImage.src) {
        URL.revokeObjectURL(capturedImage.src);
        capturedImage.src = '';
    }
}

function updateCameraStatus(message, type = 'info') {
    const statusElement = document.getElementById('cameraStatus');
    if (statusElement) {
        const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
        statusElement.innerHTML = `<i class="${icon} me-1"></i><span>${message}</span>`;

        // Update styling based on type
        statusElement.className = 'camera-status';
        if (type === 'error') {
            statusElement.style.background = 'rgba(220, 53, 69, 0.1)';
            statusElement.style.color = '#dc3545';
        } else {
            statusElement.style.background = 'rgba(var(--primary-color-rgb), 0.1)';
            statusElement.style.color = 'var(--primary-color)';
        }
    }
}

async function checkCameraAvailability() {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        const switchCameraBtn = document.getElementById('switchCameraBtn');
        if (switchCameraBtn) {
            if (videoDevices.length > 1) {
                switchCameraBtn.style.display = 'flex';
                switchCameraBtn.title = `Switch Camera (${videoDevices.length} available)`;
            } else {
                switchCameraBtn.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error checking camera availability:', error);
        // Hide switch button if we can't determine camera availability
        const switchCameraBtn = document.getElementById('switchCameraBtn');
        if (switchCameraBtn) {
            switchCameraBtn.style.display = 'none';
        }
    }
}


function deleteAttachment(docinfoid) {
    const wonum = workorderData?.wonum;
    if (!wonum) {
        showNotification('error', 'No work order number available');
        return;
    }

    if (!confirm('Are you sure you want to delete this attachment? This action cannot be undone.')) {
        return;
    }

    // Delete attachment
    fetch(`/api/workorder/${wonum}/attachments/${docinfoid}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('success', 'Attachment deleted successfully');
            loadAttachments(); // Refresh the attachments list
        } else {
            showNotification('error', `Delete failed: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('Error deleting attachment:', error);
        showNotification('error', 'Network error during deletion');
    });
}

// Signature Capture Functionality
let signaturePad = null;
let signatureModal = null;
let pendingStatusChange = null;

// Initialize signature pad when modal is shown
document.addEventListener('DOMContentLoaded', function() {
    const signatureModalElement = document.getElementById('signatureModal');

    if (signatureModalElement) {
        signatureModal = new bootstrap.Modal(signatureModalElement);

        // Initialize signature pad when modal is shown
        signatureModalElement.addEventListener('shown.bs.modal', function() {
            initializeSignaturePad();
            updateSignatureDateTime();
        });

        // Clean up when modal is hidden
        signatureModalElement.addEventListener('hidden.bs.modal', function() {
            clearSignature();
            pendingStatusChange = null;
        });

        console.log('✅ Signature modal initialized successfully');
    } else {
        console.error('❌ Signature modal element not found during initialization');
    }
});

function initializeSignaturePad() {
    const canvas = document.getElementById('signaturePad');
    const overlay = document.getElementById('signaturePadOverlay');

    if (!canvas) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    const ctx = canvas.getContext('2d');
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    let isDrawing = false;
    let hasSignature = false;

    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);

    function startDrawing(e) {
        isDrawing = true;
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        ctx.beginPath();
        ctx.moveTo(x, y);

        // Hide overlay on first draw
        if (!hasSignature) {
            if (overlay) {
                overlay.classList.add('hidden');
            }
            canvas.parentElement.classList.add('active');
            hasSignature = true;
            validateSignatureForm();
        }
    }

    function draw(e) {
        if (!isDrawing) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        ctx.lineTo(x, y);
        ctx.stroke();
    }

    function stopDrawing() {
        isDrawing = false;
        ctx.beginPath();
    }

    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                        e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    }

    // Store reference for clearing
    signaturePad = {
        canvas: canvas,
        context: ctx,
        overlay: overlay,
        hasSignature: () => hasSignature,
        clear: () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (overlay) {
                overlay.classList.remove('hidden');
            }
            canvas.parentElement.classList.remove('active');
            hasSignature = false;
            validateSignatureForm();
        }
    };
}

function clearSignature() {
    if (signaturePad) {
        signaturePad.clear();
    }
}

function updateSignatureDateTime() {
    const now = new Date();
    const dateTimeString = now.toLocaleString();
    const dateTimeElement = document.getElementById('signatureDateTime');
    if (dateTimeElement) {
        dateTimeElement.value = dateTimeString;
    } else {
        console.error('❌ signatureDateTime element not found');
    }
}

function validateSignatureForm() {
    const customerNameElement = document.getElementById('signatureCustomerName');
    const submitBtn = document.getElementById('submitSignatureBtn');

    if (!customerNameElement || !submitBtn) {
        console.error('❌ Required signature form elements not found');
        return;
    }

    const customerName = customerNameElement.value.trim();
    const hasSignature = signaturePad && signaturePad.hasSignature();

    submitBtn.disabled = !customerName || !hasSignature;
}

// Add event listeners for form validation
const signatureCustomerNameElement = document.getElementById('signatureCustomerName');
if (signatureCustomerNameElement) {
    signatureCustomerNameElement.addEventListener('input', validateSignatureForm);
} else {
    console.error('❌ signatureCustomerName element not found for event listener');
}

function showSignatureModal(wonum, newStatus, woType = 'parent') {
    // Store pending status change
    pendingStatusChange = { wonum, newStatus, woType };

    // Update modal information with null checks
    const statusInfoElement = document.getElementById('signatureStatusInfo');
    const wonumInfoElement = document.getElementById('signatureWonumInfo');
    const customerNameElement = document.getElementById('signatureCustomerName');
    const commentsElement = document.getElementById('signatureComments');

    if (statusInfoElement) {
        statusInfoElement.textContent = newStatus;
    } else {
        console.error('❌ signatureStatusInfo element not found');
    }

    if (wonumInfoElement) {
        wonumInfoElement.textContent = wonum;
    } else {
        console.error('❌ signatureWonumInfo element not found');
    }

    // Clear form with null checks
    if (customerNameElement) {
        customerNameElement.value = '';
    } else {
        console.error('❌ signatureCustomerName element not found');
    }

    if (commentsElement) {
        commentsElement.value = '';
    } else {
        console.error('❌ signatureComments element not found');
    }

    // Show modal with null check
    if (signatureModal) {
        signatureModal.show();
    } else {
        console.error('❌ signatureModal not initialized');
        showNotification('error', 'Signature modal not available. Please refresh the page.');
    }
}

function submitSignature() {
    if (!pendingStatusChange) {
        showNotification('error', 'No pending status change');
        return;
    }

    const customerNameElement = document.getElementById('signatureCustomerName');
    const commentsElement = document.getElementById('signatureComments');
    const dateTimeElement = document.getElementById('signatureDateTime');

    if (!customerNameElement || !commentsElement || !dateTimeElement) {
        showNotification('error', 'Signature form elements not found. Please refresh the page.');
        return;
    }

    const customerName = customerNameElement.value.trim();
    const comments = commentsElement.value.trim();
    const dateTime = dateTimeElement.value;

    if (!customerName) {
        showNotification('error', 'Customer name is required');
        return;
    }

    if (!signaturePad || !signaturePad.hasSignature()) {
        showNotification('error', 'Signature is required');
        return;
    }

    // Get signature as base64
    const signatureData = signaturePad.canvas.toDataURL('image/png');

    const submitBtn = document.getElementById('submitSignatureBtn');
    if (!submitBtn) {
        showNotification('error', 'Submit button not found. Please refresh the page.');
        return;
    }

    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    submitBtn.disabled = true;

    // Submit signature and process status change
    const signaturePayload = {
        wonum: pendingStatusChange.wonum,
        status: pendingStatusChange.newStatus,
        wo_type: pendingStatusChange.woType,
        signature_data: signatureData,
        customer_name: customerName,
        comments: comments,
        date_time: dateTime
    };

    console.log('📝 Submitting signature payload:', {
        wonum: signaturePayload.wonum,
        status: signaturePayload.status,
        wo_type: signaturePayload.wo_type,
        customer_name: signaturePayload.customer_name,
        has_signature: !!signaturePayload.signature_data
    });

    fetch('/api/signature/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(signaturePayload)
    })
    .then(response => {
        console.log('📝 Signature submission response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📝 Signature submission result:', data);
        if (data.success) {
            showNotification('success', 'Signature captured and status updated successfully!');
            signatureModal.hide();

            // Refresh the page or update the UI as needed
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            console.error('❌ Signature submission failed:', data);
            showNotification('error', `Failed to process signature: ${data.error}`);

            // Show additional details if available
            if (data.pdf_generated && !data.pdf_attached) {
                showNotification('warning', 'PDF generated but attachment to Maximo failed');
            }
        }
    })
    .catch(error => {
        console.error('❌ Error submitting signature:', error);

        // Provide more specific error messages
        if (error.message.includes('Failed to fetch')) {
            showNotification('error', 'Network connection error. Please check your connection and try again.');
        } else if (error.message.includes('HTTP 500')) {
            showNotification('error', 'Server error during signature processing. Please try again.');
        } else if (error.message.includes('HTTP 401')) {
            showNotification('error', 'Authentication error. Please refresh the page and login again.');
        } else {
            showNotification('error', `Network error: ${error.message}`);
        }
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}
</script>

<!-- Include Inventory Search Modal -->
{% include 'components/inventory_search_modal.html' %}

<!-- Include Labor Search Modal -->
{% include 'components/labor_search_modal.html' %}

<!-- Include Inventory Search JavaScript -->
<script src="{{ url_for('static', filename='js/inventory_search.js') }}"></script>

<!-- Include Labor Search JavaScript -->
<script src="{{ url_for('static', filename='js/labor_search.js') }}"></script>

<!-- Work Order Navigation JavaScript -->
<script>
// Work Order Navigation Variables
let workOrderList = [];
let currentWorkOrderIndex = -1;
let currentActiveTab = 'basic-tab';

// Initialize work order navigation if coming from work order list
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have work order list data from session storage
    const storedWorkOrders = sessionStorage.getItem('workOrderList');
    const currentWONum = '{{ workorder.wonum }}';

    if (storedWorkOrders) {
        try {
            workOrderList = JSON.parse(storedWorkOrders);
            currentWorkOrderIndex = workOrderList.findIndex(wo => wo.wonum === currentWONum);

            if (currentWorkOrderIndex >= 0) {
                setupWorkOrderNavigation();
            }
        } catch (error) {
            console.error('Error parsing work order list:', error);
        }
    }

    // Store current active tab when switching
    document.querySelectorAll('.diary-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            currentActiveTab = this.id;
        });
    });
});

function setupWorkOrderNavigation() {
    const navHeader = document.getElementById('workorderNavHeader');
    const currentPos = document.getElementById('currentWOPosition');
    const totalWOs = document.getElementById('totalWorkOrders');
    const prevBtn = document.getElementById('prevWOBtn');
    const nextBtn = document.getElementById('nextWOBtn');

    if (navHeader && workOrderList.length > 1) {
        navHeader.style.display = 'flex';
        currentPos.textContent = currentWorkOrderIndex + 1;
        totalWOs.textContent = workOrderList.length;

        // Update button states
        prevBtn.disabled = currentWorkOrderIndex <= 0;
        nextBtn.disabled = currentWorkOrderIndex >= workOrderList.length - 1;
    }
}

async function navigateWorkOrderDetail(direction) {
    if (!workOrderList.length) return;

    const newIndex = currentWorkOrderIndex + direction;
    if (newIndex < 0 || newIndex >= workOrderList.length) return;

    const newWorkOrder = workOrderList[newIndex];

    // Show loading state
    showWorkOrderNavigationLoading();

    try {
        // Navigate to new work order with smooth transition
        await transitionToWorkOrder(newWorkOrder.wonum);
    } catch (error) {
        console.error('Error navigating to work order:', error);
        hideWorkOrderNavigationLoading();
    }
}

function showWorkOrderNavigationLoading() {
    const content = document.querySelector('.workorder-diary-view');
    if (content) {
        content.classList.add('workorder-transition-loading');

        // Disable navigation buttons
        const navButtons = document.querySelectorAll('#prevWOBtn, #nextWOBtn');
        navButtons.forEach(btn => btn.disabled = true);
    }
}

function hideWorkOrderNavigationLoading() {
    const content = document.querySelector('.workorder-diary-view');
    if (content) {
        content.classList.remove('workorder-transition-loading');
    }
}

async function transitionToWorkOrder(wonum) {
    const content = document.querySelector('.workorder-diary-view');

    // Start fade out transition
    if (content) {
        content.classList.add('workorder-transition-fade-out');
    }

    // Wait for fade out to complete
    await new Promise(resolve => setTimeout(resolve, 200));

    // Navigate to new work order
    const newUrl = `/workorder/${wonum}`;
    window.location.href = newUrl;
}

// Store work order list when navigating from work order management page
function storeWorkOrderList(workOrders) {
    sessionStorage.setItem('workOrderList', JSON.stringify(workOrders));
}
</script>

{% endblock %}
