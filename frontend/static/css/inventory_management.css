/* Inventory Management Specific Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Cards */
.search-card, .results-card {
    border: none;
    box-shadow: 0 2px 10px var(--shadow-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-card:hover, .results-card:hover {
    box-shadow: 0 4px 20px var(--shadow-color);
}

/* Inventory Item Cards */
.inventory-item-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: var(--card-bg);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.inventory-item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition);
}

.inventory-item-card:hover {
    box-shadow: 0 8px 25px var(--shadow-color);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

.inventory-item-card:hover::before {
    opacity: 1;
}

/* Item Header */
.inventory-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.inventory-item-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.inventory-item-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.3);
}

.inventory-item-details h6 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.inventory-item-details p {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 0;
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Status Badge */
.inventory-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Item Fields Grid */
.inventory-item-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.inventory-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.inventory-field:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    border-radius: 4px;
    border-bottom-color: var(--primary-color);
}

.inventory-field-label {
    font-weight: 500;
    color: var(--text-color);
    opacity: 0.8;
    display: flex;
    align-items: center;
}

.inventory-field-label i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.inventory-field-value {
    font-weight: 600;
    color: var(--text-color);
    text-align: right;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(3px);
}

.loading-content {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    color: var(--text-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
    background-color: var(--card-bg);
}

.pagination .page-link:hover {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Results Info */
.results-info {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
}

/* Header Actions */
.header-actions .btn {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Inventory Action Buttons Responsive Styles */
.inventory-item-actions {
    margin-top: 1rem;
}

.inventory-item-actions .btn {
    transition: all 0.2s ease;
    font-weight: 500;
    min-height: 38px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.inventory-item-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Desktop specific button styling */
@media (min-width: 992px) {
    .inventory-item-actions .btn-group .btn {
        min-width: 90px;
    }

    .inventory-item-actions .details-btn {
        min-width: 80px;
    }

    .inventory-item-actions .availability-btn {
        min-width: 120px;
    }

    .inventory-item-actions .transfer-btn {
        min-width: 90px;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
        text-align: center;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .inventory-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .inventory-status-badge {
        margin-top: 0.5rem;
        align-self: flex-start;
    }

    .inventory-item-fields {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .inventory-field {
        padding: 0.5rem 0;
    }

    .header-actions {
        margin-top: 1rem;
        width: 100%;
    }

    .header-actions .btn {
        width: 100%;
    }

    .inventory-item-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .inventory-item-details h6 {
        font-size: 1.1rem;
    }

    .inventory-item-details p {
        font-size: 0.9rem;
    }

    /* Mobile button adjustments */
    .inventory-item-actions .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    .inventory-item-actions .btn i {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    /* Extra small screen button adjustments */
    .inventory-item-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        border-radius: 0.375rem;
    }

    .inventory-item-actions .btn i {
        font-size: 0.8rem;
        margin-right: 0.25rem;
    }

    /* Ensure proper spacing between button rows on mobile */
    .inventory-item-actions .mb-2 {
        margin-bottom: 0.75rem !important;
    }

    /* Adjust gap between buttons */
    .inventory-item-actions .row.g-2 {
        --bs-gutter-x: 0.5rem;
        --bs-gutter-y: 0.5rem;
    }
}

/* Dark Theme Support */
[data-bs-theme="dark"] .page-header {
    background: linear-gradient(135deg, var(--bs-dark), var(--bs-secondary));
}

[data-bs-theme="dark"] .inventory-item-card {
    background: var(--bs-dark);
    border-color: var(--bs-secondary);
    color: var(--bs-light);
}

[data-bs-theme="dark"] .inventory-item-card:hover {
    border-color: var(--bs-primary);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .inventory-field {
    border-bottom-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .inventory-field:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-bottom-color: var(--bs-primary);
}

[data-bs-theme="dark"] .inventory-field-label,
[data-bs-theme="dark"] .inventory-field-value {
    color: var(--bs-light);
}

[data-bs-theme="dark"] .loading-content {
    background: var(--bs-dark);
    color: var(--bs-light);
    border-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .search-card,
[data-bs-theme="dark"] .results-card {
    background: var(--bs-dark);
    border-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .pagination .page-link {
    background-color: var(--bs-dark);
    border-color: var(--bs-secondary);
    color: var(--bs-light);
}

[data-bs-theme="dark"] .pagination .page-link:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.inventory-item-card {
    animation: fadeInUp 0.3s ease-out;
}

/* Focus states for accessibility */
.inventory-item-card:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.inventory-field:focus-within {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-radius: 4px;
}

/* Modal and Tabs Styling */
.modal-xl {
    max-width: 95%;
}

.nav-tabs .nav-link {
    color: var(--text-color);
    border-color: var(--border-color);
    background-color: var(--card-bg);
}

.nav-tabs .nav-link:hover {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: var(--card-bg);
    border-color: var(--primary-color) var(--primary-color) var(--card-bg);
    border-bottom-color: var(--card-bg);
}

.tab-content {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    padding: 1.5rem;
}

.tab-pane h6 {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.tab-pane .table {
    margin-bottom: 0;
}

.tab-pane .table td {
    border-color: var(--border-color);
    padding: 0.5rem;
}

.tab-pane .table td:first-child {
    width: 40%;
    font-weight: 500;
}

.tab-pane .table td:last-child {
    width: 60%;
}

/* Dark theme support for tabs */
[data-bs-theme="dark"] .nav-tabs .nav-link {
    color: var(--bs-light);
    border-color: var(--bs-secondary);
    background-color: var(--bs-dark);
}

[data-bs-theme="dark"] .nav-tabs .nav-link:hover {
    border-color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active {
    color: var(--bs-primary);
    background-color: var(--bs-dark);
    border-color: var(--bs-primary) var(--bs-primary) var(--bs-dark);
    border-bottom-color: var(--bs-dark);
}

[data-bs-theme="dark"] .tab-content {
    background-color: var(--bs-dark);
    border-color: var(--bs-secondary);
}

[data-bs-theme="dark"] .tab-pane .table td {
    border-color: var(--bs-secondary);
    color: var(--bs-light);
}

/* Print styles for modal content */
@media print {
    .modal-header,
    .modal-footer {
        display: none !important;
    }

    .modal-body {
        max-height: none !important;
        overflow: visible !important;
    }

    .nav-tabs {
        display: none !important;
    }

    .tab-content {
        border: none !important;
        padding: 0 !important;
    }

    .tab-pane {
        display: block !important;
        opacity: 1 !important;
    }
}
