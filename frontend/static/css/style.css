/* Base styles for <PERSON><PERSON>gin - Mobile First Design */

:root {
    /* Light theme colors */
    --primary-color: #2c3e50;
    --primary-color-rgb: 44, 62, 80;
    --secondary-color: #3498db;
    --secondary-color-rgb: 52, 152, 219;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #ecf0f1;
    --light-color-rgb: 236, 240, 241;
    --dark-color: #34495e;
    --dark-color-rgb: 52, 73, 94;
    --background-color: #f5f7fa;
    --text-color: #333333;
    --border-color: #dfe6e9;
    --card-bg: #ffffff;
    --header-bg: #2c3e50;
    --footer-bg: #2c3e50;
    --mobile-nav-bg: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    /* Additional theme variables for better theming */
    --header-text-color: #ffffff;
    --header-text-color-rgb: 255, 255, 255;
    --nav-text-color: rgba(255, 255, 255, 0.7);
    --nav-text-active-color: #ffffff;
    --nav-hover-bg: rgba(255, 255, 255, 0.1);
    --border-light: rgba(255, 255, 255, 0.1);
    --footer-text-color: rgba(255, 255, 255, 0.8);
}

/* Dark theme colors */
[data-bs-theme="dark"] {
    --primary-color: #3498db;
    --primary-color-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-color-rgb: 44, 62, 80;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #34495e;
    --light-color-rgb: 52, 73, 94;
    --dark-color: #ecf0f1;
    --dark-color-rgb: 236, 240, 241;
    --background-color: #1a1a2e;
    --text-color: #ecf0f1;
    --border-color: #34495e;
    --card-bg: #16213e;
    --header-bg: #0f3460;
    --footer-bg: #0f3460;
    --mobile-nav-bg: #0f3460;
    --shadow-color: rgba(0, 0, 0, 0.3);
    /* Dark theme specific overrides */
    --header-text-color: #ecf0f1;
    --header-text-color-rgb: 236, 240, 241;
    --nav-text-color: rgba(236, 240, 241, 0.7);
    --nav-text-active-color: #ecf0f1;
    --nav-hover-bg: rgba(236, 240, 241, 0.1);
    --border-light: rgba(236, 240, 241, 0.1);
    --footer-text-color: rgba(236, 240, 241, 0.8);
}

/* Base styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
    padding-top: 60px; /* Space for fixed header */
    padding-bottom: 75px; /* Space for enhanced mobile nav */
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header styles */
.app-header {
    background-color: var(--header-bg);
    box-shadow: var(--box-shadow);
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 1030;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.app-title a {
    color: white;
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
}

.user-info {
    color: var(--header-text-color);
    margin-right: 15px;
    opacity: 0.9;
}

/* Theme switch styling */
.app-header .form-check-input {
    background-color: rgba(var(--header-text-color-rgb), 0.2);
    border-color: rgba(var(--header-text-color-rgb), 0.3);
}

.app-header .form-check-input:checked {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.app-header .form-check-label {
    color: var(--header-text-color);
    opacity: 0.9;
}

/* Dark mode specific improvements for better contrast */
[data-bs-theme="dark"] .table-light {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .table-light th {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .badge.bg-light {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .card-header.bg-dark {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

[data-bs-theme="dark"] .btn-outline-light {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .btn-outline-light:hover {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

/* CRITICAL: Force override all Bootstrap card defaults in dark mode */
[data-bs-theme="dark"] .card {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .card-header {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-bottom-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .card-body {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .card-footer {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    border-top-color: var(--border-color) !important;
}

/* NUCLEAR OPTION: Kill the table-light white background completely */
[data-bs-theme="dark"] .table-light,
[data-bs-theme="dark"] thead.table-light,
[data-bs-theme="dark"] .table-light th,
[data-bs-theme="dark"] .table-light td,
[data-bs-theme="dark"] thead.table-light th,
[data-bs-theme="dark"] thead.table-light td {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-color: var(--border-color) !important;
}

/* Force override Bootstrap's table headers */
[data-bs-theme="dark"] .table thead th {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-bottom: 2px solid var(--border-color) !important;
}

/* Target any table with table-light class */
[data-bs-theme="dark"] table thead.table-light,
[data-bs-theme="dark"] table .table-light {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

/* Mobile bottom navigation */
.mobile-nav {
    background-color: var(--mobile-nav-bg);
    box-shadow: 0 -2px 10px var(--shadow-color);
    height: 65px;
    z-index: 1020;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    backdrop-filter: blur(10px);
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.5rem 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    flex: 1;
    text-decoration: none;
    position: relative;
    margin: 0.25rem;
    min-height: 44px;
    font-size: 0.75rem;
    font-weight: 500;
}

.mobile-nav .nav-link i {
    font-size: 1.2rem;
    margin-bottom: 0.2rem;
    transition: all 0.3s ease;
}

.mobile-nav .nav-link span {
    font-size: 0.7rem;
    line-height: 1;
    transition: all 0.3s ease;
}

/* Enhanced Active State */
.mobile-nav .nav-link.active {
    color: white;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.mobile-nav .nav-link.active::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 0 0 3px 3px;
}

.mobile-nav .nav-link.active i {
    transform: scale(1.1);
    color: #ffffff;
}

/* Hover Effects */
.mobile-nav .nav-link:hover:not(.active) {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.mobile-nav .nav-link:active {
    transform: translateY(0);
}

/* Dark Theme Support for Mobile Navigation */
[data-bs-theme="dark"] .mobile-nav {
    background-color: var(--mobile-nav-bg);
    border-top-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .mobile-nav .nav-link {
    color: rgba(255, 255, 255, 0.5);
}

[data-bs-theme="dark"] .mobile-nav .nav-link.active {
    color: white;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.03));
    border-color: rgba(255, 255, 255, 0.15);
}

[data-bs-theme="dark"] .mobile-nav .nav-link:hover:not(.active) {
    color: rgba(255, 255, 255, 0.8);
    background-color: rgba(255, 255, 255, 0.05);
}

/* Mobile More Menu */
.mobile-more-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: flex;
    align-items: flex-end;
}

.more-menu-content {
    background-color: var(--card-bg);
    width: 100%;
    border-radius: 20px 20px 0 0;
    max-height: 70vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;
}

.more-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--header-bg);
    color: var(--header-text-color);
}

.more-menu-header h6 {
    margin: 0;
    font-weight: 600;
}

.more-menu-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.more-menu-items {
    padding: 1rem 0;
}

.more-menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

.more-menu-item:last-child {
    border-bottom: none;
}

.more-menu-item:hover {
    background-color: var(--nav-hover-bg);
    color: var(--primary-color);
}

.more-menu-item i {
    font-size: 1.2rem;
    margin-right: 1rem;
    width: 20px;
    text-align: center;
}

.more-menu-item span {
    font-weight: 500;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

/* Desktop navigation */
.app-header .nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
}

.app-header .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Main content area */
.app-content {
    flex: 1;
    padding: 1.75rem 0;
    margin-top: 0.5rem;
}

/* Footer styles */
.app-footer {
    background-color: var(--footer-bg);
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Card styles */
.card {
    background-color: var(--card-bg);
    border: none;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Alert customization */
.alert {
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

/* Form controls */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.input-group-text {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

/* Badge styling */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 50px;
}

/* Theme toggle */
.theme-toggle {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    cursor: pointer;
}

/* Welcome page specific styles */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}



/* Helper classes for colors */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Responsive adjustments */
@media (min-width: 992px) {
    .app-content {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}

/* ===== INVENTORY COST DATA SECTION STYLES ===== */

/* Cost Data Section Container */
.cost-data-section {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    padding: 0.75rem;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.cost-data-section:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Cost Data Section Header */
.cost-data-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0;
}

.cost-data-section .badge {
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

/* Cost Toggle Button */
.cost-toggle-btn {
    border: 2px solid var(--secondary-color) !important;
    color: var(--secondary-color) !important;
    background-color: var(--card-bg) !important;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
    position: relative;
    z-index: 10;
}

.cost-toggle-btn:hover {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

.cost-toggle-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
    outline: none;
}

.cost-toggle-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}

/* Balance Toggle Button */
.balance-toggle-btn {
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    background-color: var(--card-bg) !important;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
    position: relative;
    z-index: 10;
}

.balance-toggle-btn:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
}

.balance-toggle-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
    outline: none;
}

.balance-toggle-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
}

/* Cost Data Table */
.cost-data-table {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.cost-data-table .table {
    margin-bottom: 0;
    background-color: var(--card-bg);
}

.cost-data-table .table th {
    background-color: var(--primary-color);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.5rem;
    color: white;
}

.cost-data-table .table td {
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.85rem;
    vertical-align: middle;
    background-color: var(--card-bg);
    color: var(--text-color);
}

.cost-data-table .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.cost-data-table .table tbody tr:hover td {
    background-color: rgba(52, 152, 219, 0.1);
}

/* Cost Value Styling */
.cost-value {
    font-weight: 500;
}

.cost-value .text-success {
    font-weight: 600;
}

.cost-value .text-info {
    font-weight: 500;
}

.cost-value .text-primary {
    font-weight: 500;
}

.cost-value .badge {
    font-size: 0.75rem;
}

/* Mobile Cost Data View */
.mobile-cost-view {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.mobile-cost-item {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: calc(var(--border-radius) - 2px);
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-cost-item:last-child {
    margin-bottom: 0;
}

.mobile-cost-item:hover {
    background-color: rgba(52, 152, 219, 0.1);
    border-color: var(--secondary-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.mobile-cost-label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-color);
}

.mobile-cost-value {
    font-size: 0.85rem;
    text-align: right;
    color: var(--text-color);
}

/* Responsive Design for Cost Data */
@media (max-width: 767.98px) {
    .cost-data-section {
        padding: 0.5rem;
        margin-top: 0.75rem;
    }

    .cost-data-section h6 {
        font-size: 0.9rem;
    }

    .cost-toggle-btn,
    .balance-toggle-btn {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
    }

    .mobile-cost-item {
        padding: 0.4rem;
    }

    .mobile-cost-label,
    .mobile-cost-value {
        font-size: 0.8rem;
    }
}

/* Dark Theme Support for Cost Data */
[data-bs-theme="dark"] .cost-data-section {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .cost-data-table {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .cost-data-table .table th {
    background-color: var(--primary-color);
    color: white;
    border-bottom-color: var(--border-color);
}

[data-bs-theme="dark"] .cost-data-table .table td {
    border-bottom-color: var(--border-color);
    color: var(--text-color);
    background-color: var(--card-bg);
}

[data-bs-theme="dark"] .cost-data-table .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.2);
}

[data-bs-theme="dark"] .cost-data-table .table tbody tr:hover td {
    background-color: rgba(52, 152, 219, 0.2);
}

[data-bs-theme="dark"] .mobile-cost-view {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .mobile-cost-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .mobile-cost-item:hover {
    background-color: rgba(52, 152, 219, 0.2);
    border-color: var(--secondary-color);
}

[data-bs-theme="dark"] .mobile-cost-label {
    color: var(--text-color);
}

[data-bs-theme="dark"] .mobile-cost-value {
    color: var(--text-color);
}

/* Animation for Cost Section Toggle */
.cost-data-section .collapse {
    transition: height 0.35s ease;
}

.cost-data-section .collapsing {
    transition: height 0.35s ease;
}

/* Icon Styling in Cost Data */
.cost-data-section .fas {
    transition: transform 0.2s ease;
}

.cost-toggle-btn[aria-expanded="true"] .fas {
    transform: rotate(180deg);
}

/* ===== ELEGANT HOVER ANIMATIONS ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Section Headers and Toggle Buttons */
.cost-data-section,
.balance-records-section {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cost-data-section:hover,
.balance-records-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Toggle Button Animations */
.balance-toggle-btn,
.cost-toggle-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.balance-toggle-btn::before,
.cost-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.balance-toggle-btn:hover::before,
.cost-toggle-btn:hover::before {
    left: 100%;
}

.balance-toggle-btn:hover,
.cost-toggle-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.balance-toggle-btn:active,
.cost-toggle-btn:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
}

/* Icon Animations */
.balance-toggle-btn .fas,
.cost-toggle-btn .fas {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.balance-toggle-btn:hover .fas,
.cost-toggle-btn:hover .fas {
    transform: scale(1.1);
}

/* Inventory Item Cards */
.inventory-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.inventory-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

/* Individual Data Fields */
.inventory-field {
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 0.25rem;
    margin: 0.1rem 0;
}

.inventory-field:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: translateX(3px);
}

/* Cost Data Table Rows (Desktop) */
.cost-data-table .table tbody tr {
    transition: all 0.2s ease;
}

.cost-data-table .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1) !important;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.15);
}

/* Mobile Cost Items */
.mobile-cost-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-cost-item:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
    border-color: var(--secondary-color) !important;
}

/* Balance Records Table Rows */
.balance-records-table .table tbody tr {
    transition: all 0.2s ease;
}

.balance-records-table .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.08);
    transform: translateX(3px);
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.12);
}

/* Action Buttons */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    transition: all 0.1s ease;
}

/* Ripple Effect for Buttons */
.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:active::after {
    width: 300px;
    height: 300px;
}

/* Badge Animations */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Collapse Animations */
.collapse {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapsing {
    transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pulse Animation for Important Elements */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.cost-data-section .badge,
.balance-records-section .badge {
    animation: pulse 2s infinite;
}

/* Fade In Animation for New Content */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cost-data-table,
.balance-records-table,
.mobile-cost-view {
    animation: fadeInUp 0.5s ease-out;
}

/* Smooth Focus Transitions */
.form-control:focus,
.form-select:focus {
    transition: all 0.3s ease;
    transform: scale(1.02);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Dark Theme Hover Adjustments */
[data-bs-theme="dark"] .inventory-field:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

[data-bs-theme="dark"] .cost-data-table .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.15) !important;
}

[data-bs-theme="dark"] .balance-records-table .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.12);
}

/* Purchase Order and Purchase Requisition Card Hover Animations */
.po-card, .pr-card {
    transition: var(--transition);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    margin-bottom: 1rem;
    padding: 1rem;
    cursor: pointer;
}

.po-card:hover, .pr-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.15);
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(var(--primary-color-rgb), 0.02) 100%);
}

.po-card:hover .card-title, .pr-card:hover .card-title {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.po-card:hover .badge, .pr-card:hover .badge {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Tab Content Hover Effects */
.nav-tabs .nav-link {
    transition: var(--transition);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.nav-tabs .nav-link:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-tabs .nav-link.active:hover {
    transform: none;
}

/* Status Badge Hover Effects */
.badge {
    transition: var(--transition);
}

.badge:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Table Row Hover Effects for PO/PR Tables */
.po-pr-table tbody tr {
    transition: var(--transition);
    cursor: pointer;
}

.po-pr-table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.08) !important;
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
}

/* Dark Theme Adjustments for PO/PR Cards */
[data-bs-theme="dark"] .po-card:hover,
[data-bs-theme="dark"] .pr-card:hover {
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.25);
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(var(--primary-color-rgb), 0.05) 100%);
}

[data-bs-theme="dark"] .po-pr-table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.15) !important;
}

/* Responsive Design for Inventory Management */
@media (max-width: 768px) {
    /* Mobile-first responsive design */

    /* Tab navigation improvements */
    .nav-tabs {
        flex-wrap: wrap;
        border-bottom: 1px solid var(--border-color);
    }

    .nav-tabs .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        margin-bottom: 2px;
    }

    /* Table responsiveness */
    .table-responsive {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
    }

    .po-pr-table {
        font-size: 0.875rem;
    }

    .po-pr-table th,
    .po-pr-table td {
        padding: 0.5rem 0.25rem;
        white-space: nowrap;
    }

    /* Reservations table specific */
    .reservations-table {
        min-width: 1200px; /* Force horizontal scroll for wide table */
    }

    /* Badge adjustments for mobile */
    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    /* Card improvements */
    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Alert improvements */
    .alert {
        font-size: 0.875rem;
        padding: 0.75rem;
    }

    /* Search and filter improvements */
    .search-container {
        margin-bottom: 1rem;
    }

    .search-container .form-control {
        font-size: 1rem; /* Prevent zoom on iOS */
    }
}

@media (max-width: 576px) {
    /* Extra small devices */

    .nav-tabs .nav-link {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }

    .po-pr-table {
        font-size: 0.75rem;
    }

    .po-pr-table th,
    .po-pr-table td {
        padding: 0.375rem 0.25rem;
    }

    .badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    .alert {
        font-size: 0.75rem;
        padding: 0.5rem;
    }
}

@media (min-width: 769px) {
    /* Desktop improvements */

    .po-pr-table {
        font-size: 0.9rem;
    }

    /* Better spacing for larger screens */
    .tab-content {
        padding: 1.5rem 0;
    }

    /* Enhanced hover effects for desktop */
    .po-pr-table tbody tr:hover {
        transform: scale(1.005);
    }
}

/* Horizontal scroll indicators */
.table-responsive::before,
.table-responsive::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
    transition: opacity 0.3s ease;
}

.table-responsive::before {
    left: 0;
    background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
    opacity: 0;
}

.table-responsive::after {
    right: 0;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
    opacity: 0;
}

.table-responsive.scrolled-left::before {
    opacity: 1;
}

.table-responsive.scrolled-right::after {
    opacity: 1;
}

/* Dark theme adjustments for responsive design */
[data-bs-theme="dark"] .table-responsive::before {
    background: linear-gradient(to right, rgba(255,255,255,0.1), transparent);
}

[data-bs-theme="dark"] .table-responsive::after {
    background: linear-gradient(to left, rgba(255,255,255,0.1), transparent);
}

/* ===== STREAMLINED ASSET DETAILS MODAL - MOBILE-FIRST DESIGN ===== */

/* Asset Details Modal Container */
.asset-detail-modal {
    border: none;
    border-radius: 0;
}

.asset-detail-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
    padding: 1rem 1.5rem;
    border-radius: 0;
}

.asset-detail-header .modal-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.asset-detail-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

/* Modal Title with Navigation */
.modal-title-with-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.modal-nav-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.modal-nav-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.8rem;
    border-width: 1.5px;
}

.modal-nav-counter {
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.modal-nav-counter .current-asset {
    font-size: 1.1rem;
    font-weight: 700;
}

.modal-nav-counter .separator {
    opacity: 0.8;
}

.modal-asset-title {
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
}

/* Asset Navigation Transition Animations */
.asset-transition-loading {
    position: relative;
    pointer-events: none;
}

.asset-transition-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.asset-transition-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1001;
}

.asset-transition-fade-out {
    opacity: 0;
    transform: translateX(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.asset-transition-fade-in {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced Modal Navigation Button States */
.modal-nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
}

.modal-nav-btn:not(:disabled):hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

.asset-detail-body {
    padding: 0;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    background: #f8f9fa;
}

/* Asset Details Grid Layout */
.asset-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

/* Asset Detail Cards */
.asset-detail-card {
    background: white;
    border-radius: 12px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.asset-detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* Card Headers */
.asset-detail-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f1f3f4;
}

.asset-detail-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 1.1rem;
    color: white;
}

.asset-detail-card-title {
    font-weight: 600;
    font-size: 1rem;
    color: #212529;
    margin: 0;
}

/* Field Layout */
.asset-detail-fields {
    display: grid;
    gap: 0.75rem;
}

.asset-detail-field {
    display: flex;
    align-items: flex-start;
    padding: 0.5rem 0;
}

.asset-detail-field-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: #6c757d;
    font-size: 0.85rem;
    flex-shrink: 0;
}

.asset-detail-field-content {
    flex: 1;
    min-width: 0;
}

.asset-detail-field-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.asset-detail-field-value {
    font-size: 0.95rem;
    color: #212529;
    font-weight: 500;
    word-break: break-word;
}

/* Category-specific icon colors */
.asset-detail-card[data-category="basic_information"] .asset-detail-card-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.asset-detail-card[data-category="location_information"] .asset-detail-card-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.asset-detail-card[data-category="technical_information"] .asset-detail-card-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.asset-detail-card[data-category="financial_information"] .asset-detail-card-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.asset-detail-card[data-category="dates_and_status"] .asset-detail-card-icon {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
}

/* Mobile optimizations - No scrolling whatsoever */
@media (max-width: 768px) {
    .asset-detail-modal .modal-dialog {
        margin: 0;
        height: 100vh;
        max-height: 100vh;
        width: 100vw;
        max-width: 100vw;
    }

    .asset-detail-modal .modal-content {
        height: 100vh;
        border-radius: 0;
        display: flex;
        flex-direction: column;
    }

    .asset-detail-body {
        flex: 1;
        overflow: hidden;
        background: #f8f9fa;
        padding: 0;
    }

    .asset-detail-header {
        padding: 0.75rem 1rem;
        flex-shrink: 0;
    }

    .asset-detail-header .modal-title {
        font-size: 1rem;
    }

    /* Hide desktop grid on mobile */
    .asset-detail-grid {
        display: none;
    }

    /* Show mobile diary view */
    .mobile-diary-view {
        display: flex;
        height: 100%;
    }

    /* Hide old mobile tabbed view */
    .mobile-tabbed-view {
        display: none;
    }
}

/* Desktop view - hide mobile elements */
@media (min-width: 769px) {
    .mobile-tabbed-view,
    .mobile-diary-view {
        display: none;
    }

    .asset-detail-grid {
        display: grid;
    }
}

/* Tablet optimizations */
@media (min-width: 577px) and (max-width: 992px) {
    .asset-detail-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.875rem;
        padding: 0.875rem;
    }

    .asset-detail-card {
        padding: 1.125rem;
    }
}

/* Desktop optimizations */
@media (min-width: 993px) {
    .asset-detail-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
        margin: 0 auto;
    }

    .asset-detail-modal .modal-dialog {
        max-width: 1200px;
    }
}

/* Dark theme support */
[data-bs-theme="dark"] .asset-detail-body {
    background: var(--background-color);
}

[data-bs-theme="dark"] .asset-detail-card {
    background: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-bs-theme="dark"] .asset-detail-card-header {
    border-bottom-color: var(--border-color);
}

[data-bs-theme="dark"] .asset-detail-card-title {
    color: var(--text-color);
}

[data-bs-theme="dark"] .asset-detail-field-icon {
    background: var(--border-color);
    color: var(--text-color);
}

[data-bs-theme="dark"] .asset-detail-field-label {
    color: var(--nav-text-color);
}

[data-bs-theme="dark"] .asset-detail-field-value {
    color: var(--text-color);
}

/* ===== MOBILE SINGLE CARD VIEW STYLES ===== */

/* Mobile Single Card Container */
.mobile-single-card-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0.5rem;
}

/* Mobile Navigation Header */
.mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mobile-nav-counter {
    font-weight: 600;
    color: #495057;
    font-size: 1.1rem;
}

.mobile-nav-counter .current-position {
    color: #007bff;
    font-size: 1.3rem;
    font-weight: 700;
}

.mobile-nav-counter .separator {
    margin: 0 0.5rem;
    color: #6c757d;
}

.mobile-nav-buttons {
    display: flex;
    gap: 0.5rem;
}

.mobile-nav-buttons .btn {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.9rem;
    border-width: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mobile-nav-buttons .btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.mobile-nav-buttons .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Mobile Asset Card */
.mobile-asset-card {
    background: white;
    border-radius: 16px;
    padding: 1.25rem;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    max-width: 100%;
    margin: 0 auto;
}

.mobile-asset-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.25rem;
}

.mobile-asset-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.mobile-asset-info {
    flex: 1;
    min-width: 0;
}

/* Mobile Header Fields with Labels */
.mobile-header-field {
    margin-bottom: 0.75rem;
}

.mobile-header-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.mobile-header-label i {
    color: #007bff;
    font-size: 0.85rem;
    width: 14px;
    text-align: center;
}

.mobile-header-label span {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-asset-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #212529;
    margin: 0;
    line-height: 1.3;
}

.mobile-asset-description {
    color: #495057;
    font-size: 1.05rem;
    margin: 0;
    line-height: 1.4;
    font-weight: 500;
}

/* Mobile Header Badges with Labels */
.mobile-header-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.mobile-badge-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.mobile-badge-label {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.mobile-badge-label i {
    color: #007bff;
    font-size: 0.8rem;
    width: 12px;
    text-align: center;
}

.mobile-badge-label span {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Asset Details */
.mobile-asset-details {
    margin-bottom: 1.25rem;
}

.mobile-detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.mobile-detail-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.875rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.mobile-detail-item i {
    color: #007bff;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.mobile-detail-content {
    flex: 1;
    min-width: 0;
}

.mobile-detail-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.mobile-detail-value {
    display: block;
    font-size: 1rem;
    color: #212529;
    font-weight: 600;
    line-height: 1.3;
    word-break: break-word;
}

/* Mobile Action Buttons */
.mobile-action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.mobile-detail-btn,
.mobile-action-btn {
    padding: 0.625rem 0.875rem;
    font-weight: 600;
    font-size: 0.9rem;
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
}

.mobile-detail-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
    color: white;
}

.mobile-detail-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    color: white;
}

.mobile-action-btn {
    border-color: #6c757d;
    color: #6c757d;
    background: white;
}

.mobile-action-btn:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108,117,125,0.3);
}

.mobile-detail-btn i,
.mobile-action-btn i {
    font-size: 0.85rem;
}

/* ===== MOBILE DIARY-STYLE TABBED VIEW ===== */

/* Mobile Diary View Container */
.mobile-diary-view {
    height: 100%;
    display: flex;
    background: #f8f9fa;
}

/* Diary Sidebar */
.diary-sidebar {
    width: 68px;
    flex-shrink: 0;
    background: linear-gradient(180deg, #fafafa 0%, #f0f0f0 100%);
    border-right: 2px solid #e0e0e0;
    padding: 0.5rem 0.2rem;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.diary-tabs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Diary Tab Buttons - Vintage Style */
.diary-tab {
    width: 100%;
    padding: 0.65rem 0.4rem;
    border: none;
    border-radius: 0 10px 10px 0;
    background: var(--tab-bg);
    border-left: 3px solid var(--tab-border);
    color: var(--tab-text);
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    position: relative;
    box-shadow: 2px 2px 6px rgba(0,0,0,0.1);
    margin-right: 0.2rem;
    opacity: 0.8;
}

.diary-tab::before {
    content: '';
    position: absolute;
    top: 0;
    right: -2px;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, transparent 0%, var(--tab-border) 20%, var(--tab-border) 80%, transparent 100%);
    border-radius: 0 2px 2px 0;
}

.diary-tab.active {
    background: white;
    border-left: 5px solid var(--tab-border);
    box-shadow: 6px 6px 16px rgba(0,0,0,0.2);
    transform: translateX(4px);
    margin-right: 0;
    opacity: 1;
    z-index: 10;
    border-top: 2px solid var(--tab-border);
    border-bottom: 2px solid var(--tab-border);
}

.diary-tab.active::before {
    display: none;
}

.diary-tab.active::after {
    content: '';
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid white;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    filter: drop-shadow(2px 0 4px rgba(0,0,0,0.1));
}

.diary-tab:hover {
    transform: translateX(2px);
    box-shadow: 4px 4px 10px rgba(0,0,0,0.15);
    opacity: 0.9;
}

.diary-tab-icon {
    font-size: 1rem;
    margin-bottom: 0.1rem;
}

.diary-tab-label {
    font-size: 0.65rem;
    font-weight: 600;
    text-align: center;
    line-height: 1;
    text-transform: uppercase;
    letter-spacing: 0.2px;
}

.diary-tab-badge {
    position: absolute;
    top: -3px;
    right: -3px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.4);
    z-index: 10;
}

/* Enhanced badge visibility for mobile */
@media (max-width: 768px) {
    .diary-tab-badge {
        width: 22px;
        height: 22px;
        font-size: 0.8rem;
        top: -4px;
        right: -4px;
        border-width: 2.5px;
        box-shadow: 0 3px 8px rgba(0,0,0,0.5);
    }
}

/* Enhanced badge contrast for better readability */
.diary-tab-badge.high-count {
    background: #e74c3c;
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Diary Content Area */
.diary-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: white;
    margin: 0.5rem 0.5rem 0.5rem 0;
    border-radius: 12px;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.06);
}

.mobile-tab-panel {
    padding: 1rem;
    height: 100%;
}

.mobile-tab-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.mobile-tab-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 1rem;
    color: white;
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.mobile-tab-title {
    font-weight: 600;
    color: #212529;
    margin: 0;
    font-size: 1rem;
}

/* Mobile Detail Fields */
.mobile-detail-fields {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.mobile-detail-field {
    display: flex;
    align-items: flex-start;
    padding: 0.875rem;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin-bottom: 0.5rem;
}

.mobile-detail-field:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    background: white;
}

.mobile-field-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background: #e3f2fd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: #1976d2;
    font-size: 0.85rem;
    flex-shrink: 0;
}

.mobile-field-content {
    flex: 1;
    min-width: 0;
}

.mobile-field-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-field-value {
    font-size: 1.05rem;
    color: #212529;
    font-weight: 600;
    word-break: break-word;
    line-height: 1.4;
}

/* Dark theme support for mobile views */
@media (max-width: 768px) {
    [data-bs-theme="dark"] .mobile-nav-header {
        background: linear-gradient(135deg, var(--border-color), var(--card-bg));
    }

    [data-bs-theme="dark"] .mobile-asset-card {
        background: var(--card-bg);
        border-color: var(--border-color);
    }

    [data-bs-theme="dark"] .mobile-asset-title {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .mobile-asset-description {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .mobile-asset-details {
        background: transparent;
    }

    [data-bs-theme="dark"] .mobile-detail-item {
        background: var(--border-color);
        border-color: var(--border-color);
    }

    [data-bs-theme="dark"] .mobile-detail-label {
        color: var(--nav-text-color);
    }

    [data-bs-theme="dark"] .mobile-detail-value {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .mobile-tab-nav {
        background: var(--card-bg);
        border-bottom-color: var(--border-color);
    }

    [data-bs-theme="dark"] .mobile-tab-content {
        background: var(--background-color);
    }

    [data-bs-theme="dark"] .mobile-tab-header,
    [data-bs-theme="dark"] .mobile-detail-field {
        background: var(--card-bg);
        border-color: var(--border-color);
    }

    [data-bs-theme="dark"] .mobile-tab-title {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .mobile-field-icon {
        background: var(--border-color);
        color: var(--primary-color);
    }

    [data-bs-theme="dark"] .mobile-field-label {
        color: var(--nav-text-color);
    }

    [data-bs-theme="dark"] .mobile-field-value {
        color: var(--text-color);
    }

    /* Dark theme for diary interface */
    [data-bs-theme="dark"] .mobile-diary-view {
        background: var(--background-color);
    }

    [data-bs-theme="dark"] .diary-sidebar {
        background: linear-gradient(180deg, var(--card-bg) 0%, var(--border-color) 100%);
        border-right-color: var(--border-color);
    }

    [data-bs-theme="dark"] .diary-tab {
        background: var(--border-color);
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .diary-tab.active {
        background: var(--card-bg);
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .diary-content {
        background: var(--card-bg);
    }

    [data-bs-theme="dark"] .mobile-tab-header {
        background: linear-gradient(135deg, var(--border-color), var(--card-bg));
        border-left-color: var(--primary-color);
    }

    [data-bs-theme="dark"] .mobile-detail-field {
        background: var(--border-color);
        border-color: var(--border-color);
    }

    [data-bs-theme="dark"] .mobile-detail-field:hover {
        background: var(--card-bg);
    }
}

/* ===== COMPACT SEARCH LAYOUT ===== */

/* Compact Search Container */
.compact-search-layout {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Search Input Row */
.search-input-row {
    display: flex;
    gap: 0.75rem;
    align-items: end;
}

.search-input-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    min-width: 0;
}

.compact-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.compact-label i {
    width: 16px;
    text-align: center;
    color: #007bff;
    font-size: 0.9rem;
}

.search-input-expanded {
    width: 100%;
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-input-expanded:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.search-help-text {
    font-size: 0.8rem;
    color: #6c757d;
    line-height: 1.3;
    margin-top: 0.25rem;
}

.search-btn {
    padding: 0.5rem 0.875rem;
    font-size: 0.9rem;
    border-radius: 8px;
    white-space: nowrap;
    min-width: 44px;
    height: fit-content;
}

/* Filters Grid */
.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.75rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.filter-group .form-select {
    font-size: 0.85rem;
}

/* Mobile optimizations for search */
@media (max-width: 576px) {
    .compact-search-layout {
        gap: 1rem;
        padding: 0.25rem;
    }

    .search-input-row {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }

    .search-input-group {
        width: 100%;
    }

    .search-input-expanded {
        width: 100%;
        font-size: 1.1rem;
        padding: 0.875rem 1rem;
        min-height: 48px;
    }

    .search-help-text {
        font-size: 0.85rem;
        line-height: 1.4;
        color: #495057;
    }

    .compact-label {
        font-size: 1rem;
        gap: 0.5rem;
    }

    .compact-label i {
        font-size: 1rem;
    }

    .search-btn {
        width: 100%;
        padding: 0.875rem;
        font-size: 1rem;
        min-height: 48px;
    }

    .filters-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .filter-group .form-select {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
        min-height: 44px;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .filters-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 769px) {
    .filters-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .search-input-row {
        align-items: flex-end;
    }
}

/* Dark theme support for compact search */
[data-bs-theme="dark"] .compact-label {
    color: var(--text-color);
}

[data-bs-theme="dark"] .compact-label i {
    color: var(--primary-color);
}

/* Description Search Row */
.description-search-row {
    margin-top: 0.75rem;
}

.description-search-row .search-input-group {
    width: 100%;
}

.description-search-row .form-control {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

@media (max-width: 576px) {
    .description-search-row {
        margin-top: 0.5rem;
    }

    .description-search-row .form-control {
        font-size: 1rem;
        padding: 0.75rem 1rem;
        min-height: 44px;
    }
}

[data-bs-theme="dark"] .search-input-expanded {
    background: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-bs-theme="dark"] .search-input-expanded:focus {
    border-color: var(--primary-color);
    background: var(--card-bg);
}

[data-bs-theme="dark"] .search-help-text {
    color: var(--nav-text-color);
}

[data-bs-theme="dark"] .asset-transition-loading::before {
    background: rgba(var(--background-color-rgb, 26, 26, 46), 0.8);
}

/* ===== MOBILE LAYOUT OPTIMIZATIONS ===== */

/* Enhanced Touch Targets */
@media (max-width: 768px) {
    /* Ensure all interactive elements meet minimum touch target size */
    .btn, .form-control, .form-select, .diary-tab, .mobile-nav-btn {
        min-height: 44px;
        min-width: 44px;
    }

    /* Improved spacing for touch interaction */
    .mobile-action-buttons {
        gap: 1rem;
        margin-top: 1rem;
    }

    .mobile-detail-btn,
    .mobile-action-btn {
        min-height: 48px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* Enhanced diary tab touch targets */
    .diary-tab {
        min-height: 60px;
        padding: 0.75rem 0.5rem;
    }

    /* Better spacing in mobile asset cards */
    .mobile-asset-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    /* Improved modal header spacing */
    .asset-detail-header {
        padding: 1rem 1.25rem;
    }

    /* Enhanced navigation controls */
    .mobile-nav-header {
        padding: 1rem 1.25rem;
        margin-bottom: 1.25rem;
    }

    .mobile-nav-buttons .btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* Better content spacing */
    .mobile-detail-fields {
        gap: 1rem;
    }

    .mobile-detail-field {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    /* Improved tab content spacing */
    .mobile-tab-panel {
        padding: 1.25rem;
    }

    /* Enhanced visual consistency */
    .mobile-asset-details .mobile-detail-row {
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .mobile-detail-item {
        padding: 1rem;
        border-radius: 12px;
    }
}

/* ===== MOBILE WORK ORDER CARD STYLES ===== */

/* Mobile Work Order Card */
.mobile-work-order-card {
    background: white;
    border-radius: 16px;
    padding: 1.25rem;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    max-width: 100%;
    margin: 0 auto;
    transition: opacity 0.15s ease, transform 0.15s ease;
}

.mobile-work-order-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.25rem;
}

.mobile-work-order-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.mobile-work-order-info {
    flex: 1;
    min-width: 0;
}

.mobile-work-order-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #212529;
    margin: 0;
    line-height: 1.3;
}

.mobile-work-order-description {
    color: #495057;
    font-size: 1.05rem;
    margin: 0;
    line-height: 1.4;
    font-weight: 500;
}

/* Mobile Work Order Details */
.mobile-work-order-details {
    margin-bottom: 1.25rem;
}

/* Mobile Check Buttons */
.mobile-check-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.mobile-check-btn {
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    font-size: 0.85rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    white-space: nowrap;
}

.mobile-check-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Dark theme support for mobile work order cards */
@media (max-width: 768px) {
    [data-bs-theme="dark"] .mobile-work-order-card {
        background: var(--card-bg);
        border-color: var(--border-color);
    }

    [data-bs-theme="dark"] .mobile-work-order-title {
        color: var(--text-color);
    }

    [data-bs-theme="dark"] .mobile-work-order-description {
        color: var(--text-color);
    }
}

/* ===== WORK ORDER DETAIL PAGE DIARY VIEW ===== */

/* Work Order Diary View Container */
.workorder-diary-view {
    display: flex;
    min-height: 500px;
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Mobile-specific styling for work order detail */
@media (max-width: 768px) {
    .workorder-diary-view {
        min-height: calc(100vh - 200px);
        border-radius: 0;
        margin: -1rem;
    }

    .workorder-diary-view .diary-sidebar {
        width: 68px;
    }

    .workorder-diary-view .diary-content {
        margin: 0;
        border-radius: 0;
    }
}

/* Desktop styling for work order detail */
@media (min-width: 769px) {
    .workorder-diary-view .diary-sidebar {
        width: 85px;
    }

    .workorder-diary-view .diary-content {
        margin: 0.5rem 0.5rem 0.5rem 0;
    }
}

/* Dark theme support for work order diary view */
[data-bs-theme="dark"] .workorder-diary-view {
    background: var(--background-color);
}

[data-bs-theme="dark"] .workorder-diary-view .diary-sidebar {
    background: linear-gradient(180deg, var(--card-bg) 0%, var(--border-color) 100%);
    border-right-color: var(--border-color);
}

[data-bs-theme="dark"] .workorder-diary-view .diary-content {
    background: var(--card-bg);
}

/* Work Order Navigation Header */
.mobile-workorder-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mobile-workorder-nav-header .mobile-nav-counter {
    font-weight: 600;
    color: #495057;
    font-size: 1.1rem;
}

.mobile-workorder-nav-header .mobile-nav-counter .current-position {
    color: #007bff;
    font-size: 1.3rem;
    font-weight: 700;
}

.mobile-workorder-nav-header .mobile-nav-counter .separator {
    margin: 0 0.5rem;
    color: #6c757d;
}

.mobile-workorder-nav-header .mobile-nav-buttons {
    display: flex;
    gap: 0.5rem;
}

.mobile-workorder-nav-header .mobile-nav-buttons .btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.85rem;
    border-width: 1.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mobile-workorder-nav-header .mobile-nav-buttons .btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.mobile-workorder-nav-header .mobile-nav-buttons .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Work Order Transition Animations */
.workorder-transition-loading {
    position: relative;
    pointer-events: none;
}

.workorder-transition-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.workorder-transition-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1001;
}

.workorder-transition-fade-out {
    opacity: 0;
    transform: translateX(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Dark theme support for work order navigation */
[data-bs-theme="dark"] .mobile-workorder-nav-header {
    background: linear-gradient(135deg, var(--border-color), var(--card-bg));
}

[data-bs-theme="dark"] .workorder-transition-loading::before {
    background: rgba(var(--background-color-rgb, 26, 26, 46), 0.8);
}

/* ===== MOBILE INVENTORY SINGLE CARD NAVIGATION ===== */

/* Mobile Inventory Navigation Container */
.mobile-single-inventory-container {
    width: 100%;
    max-width: 100%;
}

/* Mobile Inventory Navigation Header */
.mobile-inventory-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mobile-inventory-nav-header .mobile-nav-counter {
    font-weight: 600;
    color: #1976d2;
    font-size: 1.1rem;
}

.mobile-inventory-nav-header .mobile-nav-counter .current-position {
    color: #0d47a1;
    font-size: 1.3rem;
    font-weight: 700;
}

.mobile-inventory-nav-header .mobile-nav-counter .separator {
    margin: 0 0.5rem;
    color: #1976d2;
}

.mobile-inventory-nav-header .mobile-nav-buttons {
    display: flex;
    gap: 0.5rem;
}

.mobile-inventory-nav-header .mobile-nav-buttons .btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.85rem;
    border-width: 1.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mobile-inventory-nav-header .mobile-nav-buttons .btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.mobile-inventory-nav-header .mobile-nav-buttons .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Mobile Inventory Card Wrapper */
.mobile-inventory-card-wrapper {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.mobile-inventory-card-wrapper.inventory-transition-fade-out {
    opacity: 0;
    transform: translateX(-10px);
}

/* Ensure inventory cards work well in mobile single view */
.mobile-single-inventory-container .inventory-item-card {
    margin-bottom: 0;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border-radius: 16px;
}

/* Dark theme support for mobile inventory navigation */
[data-bs-theme="dark"] .mobile-inventory-nav-header {
    background: linear-gradient(135deg, #1a237e, #283593);
}

[data-bs-theme="dark"] .mobile-inventory-nav-header .mobile-nav-counter {
    color: #bbdefb;
}

[data-bs-theme="dark"] .mobile-inventory-nav-header .mobile-nav-counter .current-position {
    color: #e3f2fd;
}

[data-bs-theme="dark"] .mobile-inventory-nav-header .mobile-nav-counter .separator {
    color: #90caf9;
}

/* ===== INVENTORY DETAIL MODAL DIARY VIEW ===== */

/* Inventory Diary View Container */
.inventory-diary-view {
    display: flex;
    min-height: 500px;
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Modal-specific styling for inventory detail */
.modal-body .inventory-diary-view {
    min-height: 60vh;
    border-radius: 8px;
    margin: -1rem;
}

.modal-body .inventory-diary-view .diary-sidebar {
    width: 70px;
}

.modal-body .inventory-diary-view .diary-content {
    margin: 0.5rem 0.5rem 0.5rem 0;
}

/* Mobile Balance Summary Styling */
.mobile-balance-summary {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.mobile-balance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.mobile-balance-item:last-child {
    border-bottom: none;
}

.balance-location {
    font-size: 0.9rem;
    color: #495057;
    flex: 1;
}

.balance-amount {
    font-size: 1rem;
    color: #007bff;
    font-weight: 600;
}

/* Dark theme support for inventory diary view */
[data-bs-theme="dark"] .inventory-diary-view {
    background: var(--background-color);
}

[data-bs-theme="dark"] .inventory-diary-view .diary-sidebar {
    background: linear-gradient(180deg, var(--card-bg) 0%, var(--border-color) 100%);
    border-right-color: var(--border-color);
}

[data-bs-theme="dark"] .inventory-diary-view .diary-content {
    background: var(--card-bg);
}

[data-bs-theme="dark"] .mobile-balance-summary {
    background: rgba(var(--card-bg-rgb, 40, 44, 52), 0.5);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .balance-location {
    color: var(--text-color);
}

[data-bs-theme="dark"] .balance-amount {
    color: #64b5f6;
}

/* ===== ENHANCED DIARY TAB TRANSITIONS ===== */

/* Smooth tab content transitions */
.tab-pane {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.tab-pane.fade:not(.show) {
    opacity: 0;
    transform: translateX(-10px);
}

.tab-pane.fade.show {
    opacity: 1;
    transform: translateX(0);
}

/* Enhanced diary tab hover and active transitions */
.diary-tab {
    cursor: pointer;
}

.diary-tab:hover {
    transform: translateX(2px);
    box-shadow: 4px 4px 10px rgba(0,0,0,0.15);
    opacity: 0.9;
    transition: all 0.15s ease;
}

.diary-tab.active {
    background: white;
    border-left: 5px solid var(--tab-border);
    box-shadow: 6px 6px 16px rgba(0,0,0,0.2);
    transform: translateX(4px);
    margin-right: 0;
    opacity: 1;
    z-index: 10;
    border-top: 2px solid var(--tab-border);
    border-bottom: 2px solid var(--tab-border);
    transition: all 0.2s ease;
}

/* Mobile tab panel transitions */
.mobile-tab-panel {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.mobile-tab-panel.fade:not(.show) {
    opacity: 0;
    transform: translateY(10px);
}

.mobile-tab-panel.fade.show {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth field animations */
.mobile-detail-field {
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.mobile-detail-field:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateX(2px);
}

/* Enhanced icon transitions */
.diary-tab-icon {
    transition: transform 0.2s ease, color 0.2s ease;
}

.diary-tab:hover .diary-tab-icon {
    transform: scale(1.1);
}

.diary-tab.active .diary-tab-icon {
    transform: scale(1.15);
    color: var(--tab-border);
}

/* ===== AVAILABILITY MODAL DIARY-STYLE DESIGN ===== */

/* Availability Modal Container */
.availability-modal {
    border: none;
    border-radius: 0;
}

.availability-modal .modal-dialog {
    max-width: 1200px;
}

.availability-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-bottom: none;
    padding: 1rem 1.5rem;
    border-radius: 0;
}

.availability-header .modal-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.availability-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

/* Availability Diary View Container */
.availability-diary-view {
    height: 100%;
    display: flex;
    background: #f8f9fa;
    min-height: 60vh;
}

/* Availability Diary Sidebar */
.availability-diary-sidebar {
    width: 70px;
    flex-shrink: 0;
    background: linear-gradient(180deg, #fafafa 0%, #f0f0f0 100%);
    border-right: 2px solid #e0e0e0;
    padding: 0.5rem 0.2rem;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.availability-diary-tabs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Availability Diary Tab Buttons - Vintage Style with Unique Colors */
.availability-diary-tab {
    width: 100%;
    padding: 0.65rem 0.4rem;
    border: none;
    border-radius: 0 10px 10px 0;
    background: var(--tab-bg);
    border-left: 3px solid var(--tab-border);
    color: var(--tab-text);
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    position: relative;
    box-shadow: 2px 2px 6px rgba(0,0,0,0.1);
    margin-right: 0.2rem;
    opacity: 0.8;
    cursor: pointer;
    min-height: 44px; /* Mobile touch target */
}

/* Availability Tab Color Schemes */
.availability-diary-tab.summary-tab {
    --tab-bg: linear-gradient(135deg, #f3e5f5, #e1bee7);
    --tab-border: #673ab7;
    --tab-text: #4527a0;
}

.availability-diary-tab.locations-tab {
    --tab-bg: linear-gradient(135deg, #e3f2fd, #bbdefb);
    --tab-border: #2196f3;
    --tab-text: #1565c0;
}

.availability-diary-tab.lots-tab {
    --tab-bg: linear-gradient(135deg, #f3e5f5, #e1bee7);
    --tab-border: #9c27b0;
    --tab-text: #7b1fa2;
}

.availability-diary-tab.purchase-orders-tab {
    --tab-bg: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    --tab-border: #4caf50;
    --tab-text: #388e3c;
}

.availability-diary-tab.purchase-requisitions-tab {
    --tab-bg: linear-gradient(135deg, #fff3e0, #ffcc02);
    --tab-border: #ff9800;
    --tab-text: #f57c00;
}

.availability-diary-tab.reservations-tab {
    --tab-bg: linear-gradient(135deg, #ffebee, #ffcdd2);
    --tab-border: #f44336;
    --tab-text: #d32f2f;
}

.availability-diary-tab.alternates-tab {
    --tab-bg: linear-gradient(135deg, #fce4ec, #f8bbd9);
    --tab-border: #e91e63;
    --tab-text: #c2185b;
}

.availability-diary-tab-icon {
    font-size: 1rem;
    margin-bottom: 0.1rem;
    transition: transform 0.2s ease, color 0.2s ease;
}

.availability-diary-tab-name {
    font-size: 0.65rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.1;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.availability-diary-tab-badge {
    position: absolute;
    top: -0.3rem;
    right: -0.3rem;
    background: var(--tab-border);
    color: white;
    border-radius: 50%;
    width: 1.2rem;
    height: 1.2rem;
    font-size: 0.6rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse-badge 2s infinite;
}

/* Availability Tab Hover and Active States */
.availability-diary-tab:hover {
    transform: translateX(2px);
    box-shadow: 4px 4px 10px rgba(0,0,0,0.15);
    opacity: 0.9;
    transition: all 0.15s ease;
}

.availability-diary-tab:hover .availability-diary-tab-icon {
    transform: scale(1.1);
}

.availability-diary-tab.active {
    background: white;
    border-left: 5px solid var(--tab-border);
    box-shadow: 6px 6px 16px rgba(0,0,0,0.2);
    transform: translateX(4px);
    margin-right: 0;
    opacity: 1;
    z-index: 10;
    border-top: 2px solid var(--tab-border);
    border-bottom: 2px solid var(--tab-border);
    transition: all 0.2s ease;
}

.availability-diary-tab.active .availability-diary-tab-icon {
    transform: scale(1.15);
    color: var(--tab-border);
}

/* Availability Content Area */
.availability-diary-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: white;
    margin: 0.5rem 0.5rem 0.5rem 0;
    border-radius: 12px;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.06);
}

.availability-tab-panel {
    padding: 1rem;
    height: 100%;
}

.availability-tab-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #ffffff, #f1f3f4);
    border-radius: 8px;
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.availability-tab-header h6 {
    margin: 0;
    font-weight: 600;
    color: #212529; /* Darker text for better contrast */
}

.availability-tab-header .badge {
    margin-left: auto;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Mobile optimizations for availability modal */
@media (max-width: 768px) {
    .availability-modal .modal-dialog {
        margin: 0;
        height: 100vh;
        max-height: 100vh;
        width: 100vw;
        max-width: 100vw;
    }

    .availability-modal .modal-content {
        height: 100vh;
        border-radius: 0;
        display: flex;
        flex-direction: column;
    }

    .availability-modal .modal-body {
        flex: 1;
        overflow: hidden;
        background: #f8f9fa;
        padding: 0;
    }

    .availability-header {
        padding: 0.75rem 1rem;
        flex-shrink: 0;
    }

    .availability-header .modal-title {
        font-size: 1rem;
    }

    .availability-diary-view {
        height: 100%;
        min-height: auto;
    }

    .availability-diary-sidebar {
        width: 68px;
    }

    .availability-diary-content {
        margin: 0.5rem 0.5rem 0.5rem 0;
        border-radius: 8px;
    }

    /* Hide old horizontal tabs on mobile */
    .comprehensive-availability-tabs .nav-tabs {
        display: none;
    }

    /* Show diary view on mobile */
    .availability-diary-view {
        display: flex;
    }

    /* CRITICAL FIX: Hide availability summary cards on mobile to prevent layout obstruction */
    .availability-summary {
        display: none !important;
    }

    /* Ensure no-scroll mobile experience */
    .availability-diary-view {
        height: calc(100vh - 120px); /* Account for header and footer */
        max-height: calc(100vh - 120px);
        overflow: hidden;
    }

    .availability-diary-content {
        height: 100%;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Optimize tab panel height */
    .availability-tab-panel {
        height: calc(100vh - 180px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Ensure tables don't break layout */
    .table-responsive {
        max-height: calc(100vh - 250px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Desktop view - hide diary elements if needed */
@media (min-width: 769px) {
    .availability-diary-sidebar {
        width: 75px;
    }

    .availability-diary-content {
        margin: 0.5rem 0.5rem 0.5rem 0;
    }
}

/* Dark theme support for availability diary view */
[data-bs-theme="dark"] .availability-diary-view {
    background: var(--background-color);
}

[data-bs-theme="dark"] .availability-diary-sidebar {
    background: linear-gradient(180deg, var(--card-bg) 0%, var(--border-color) 100%);
    border-right-color: var(--border-color);
}

[data-bs-theme="dark"] .availability-diary-tab {
    background: var(--border-color);
    color: var(--text-color);
}

[data-bs-theme="dark"] .availability-diary-tab.active {
    background: var(--card-bg);
    color: var(--text-color);
}

[data-bs-theme="dark"] .availability-diary-content {
    background: var(--card-bg);
}

[data-bs-theme="dark"] .availability-tab-header {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border-left-color: #48bb78;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

[data-bs-theme="dark"] .availability-tab-header h6 {
    color: #f7fafc; /* Light text for dark theme */
}

/* Additional badge colors for availability tabs */
.badge.bg-purple {
    background-color: #9c27b0 !important;
}

.badge.bg-pink {
    background-color: #e91e63 !important;
}

/* Mobile responsive adjustments for availability modal */
@media (max-width: 576px) {
    .availability-diary-sidebar {
        width: 65px;
    }

    .availability-diary-tab-name {
        font-size: 0.6rem;
    }

    .availability-diary-tab-icon {
        font-size: 0.9rem;
    }

    .availability-tab-header h6 {
        font-size: 0.9rem;
    }

    /* Optimize mobile summary grid for smaller screens */
    .mobile-summary-grid {
        gap: 0.5rem;
    }

    .summary-item {
        padding: 0.5rem;
    }

    .summary-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 0.5rem;
    }

    .summary-icon i {
        font-size: 0.85rem;
    }

    .summary-value {
        font-size: 1rem;
    }
}

/* Mobile Summary Grid - Compact layout for availability overview */
.mobile-summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.summary-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    background: white;
    border-left: 4px solid var(--item-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.summary-item.primary {
    --item-color: #007bff;
}

.summary-item.success {
    --item-color: #28a745;
}

.summary-item.warning {
    --item-color: #ffc107;
}

.summary-item.info {
    --item-color: #17a2b8;
}

.summary-item.secondary {
    --item-color: #6c757d;
}

.summary-item.danger {
    --item-color: #dc3545;
}

.summary-icon {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--item-color);
    color: white;
    border-radius: 50%;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.summary-icon i {
    font-size: 1rem;
}

.summary-content {
    flex: 1;
    min-width: 0;
}

.summary-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.2rem;
}

.summary-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #495057;
    line-height: 1.2;
}

.summary-metadata {
    padding: 0.75rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-top: 1rem;
}

/* Ensure proper scrolling on mobile */
@media (max-width: 768px) {
    .availability-diary-content {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .availability-tab-panel {
        padding: 0.75rem;
    }

    /* Hide horizontal scroll on tables */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Dark theme support for mobile summary */
[data-bs-theme="dark"] .summary-item {
    background: var(--card-bg);
    color: var(--text-color);
}

[data-bs-theme="dark"] .summary-label {
    color: #adb5bd;
}

[data-bs-theme="dark"] .summary-value {
    color: var(--text-color);
}

[data-bs-theme="dark"] .summary-metadata {
    background: rgba(var(--card-bg-rgb, 40, 44, 52), 0.8);
    border-color: var(--border-color);
}

/* ===== MOBILE CARD LAYOUTS FOR AVAILABILITY DATA ===== */

/* Mobile Card Container */
.mobile-card-container {
    display: none; /* Hidden by default, shown on mobile */
}

.mobile-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid var(--card-accent, #007bff);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.mobile-card-title {
    font-weight: 600;
    color: #212529;
    font-size: 1rem;
    margin: 0;
}

.mobile-card-badge {
    background: var(--card-accent, #007bff);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.mobile-card-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.mobile-card-field {
    display: flex;
    flex-direction: column;
}

.mobile-card-field.full-width {
    grid-column: 1 / -1;
}

.mobile-card-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.mobile-card-value {
    font-size: 0.9rem;
    color: #212529;
    font-weight: 500;
    word-break: break-word;
}

.mobile-card-value.highlight {
    color: var(--card-accent, #007bff);
    font-weight: 600;
}

.mobile-card-value.currency {
    color: #28a745;
    font-weight: 600;
}

.mobile-card-value.status {
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-card-value.status.active {
    background: #d4edda;
    color: #155724;
}

.mobile-card-value.status.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Card Accent Colors for Different Data Types */
.mobile-card.location-card {
    --card-accent: #007bff;
}

.mobile-card.lot-card {
    --card-accent: #6f42c1;
}

.mobile-card.po-card {
    --card-accent: #28a745;
}

.mobile-card.pr-card {
    --card-accent: #fd7e14;
}

.mobile-card.reservation-card {
    --card-accent: #dc3545;
}

.mobile-card.alternate-card {
    --card-accent: #e83e8c;
}

/* Mobile Card Navigation */
.mobile-card-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.mobile-card-nav-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    min-width: 44px; /* Touch target */
    min-height: 44px;
}

.mobile-card-nav-button:hover {
    background: #0056b3;
}

.mobile-card-nav-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.mobile-card-counter {
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

/* Mobile Responsive Rules */
@media (max-width: 768px) {
    /* Hide tables on mobile */
    .availability-tab-panel .table-responsive {
        display: none;
    }

    /* Show mobile cards on mobile */
    .mobile-card-container {
        display: block;
    }

    /* Adjust card layout for smaller screens */
    .mobile-card-body {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .mobile-card {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .mobile-card-header {
        margin-bottom: 0.5rem;
    }
}

/* Dark theme support for mobile cards */
[data-bs-theme="dark"] .mobile-card {
    background: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

[data-bs-theme="dark"] .mobile-card-title {
    color: var(--text-color);
}

[data-bs-theme="dark"] .mobile-card-value {
    color: var(--text-color);
}

[data-bs-theme="dark"] .mobile-card-label {
    color: #adb5bd;
}

[data-bs-theme="dark"] .mobile-card-navigation {
    background: var(--border-color);
}

[data-bs-theme="dark"] .mobile-card-counter {
    color: var(--text-color);
}

/* Smooth mobile field icon transitions */
.mobile-field-icon {
    transition: transform 0.2s ease, color 0.2s ease;
}

.mobile-detail-field:hover .mobile-field-icon {
    transform: scale(1.1);
    color: var(--primary-color, #007bff);
}

/* Related Records Modal Styles */
.related-records-container {
    min-height: 400px;
}

.related-records-container .nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.related-records-container .nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.related-records-container .nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: 600;
}

.related-records-container .nav-tabs .nav-link:hover {
    border-bottom-color: #007bff;
    color: #007bff;
}

.related-records-container .tab-content {
    min-height: 300px;
}

.related-records-container .list-group-item {
    border-left: none;
    border-right: none;
    border-radius: 0;
    padding: 1rem;
}

.related-records-container .list-group-item:first-child {
    border-top: none;
}

.related-records-container .list-group-item:last-child {
    border-bottom: none;
}

.related-records-container .list-group-item:hover {
    background-color: #f8f9fa;
}

.related-records-container .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.related-records-container .text-muted {
    font-size: 0.875rem;
}

/* Mobile optimizations for related records modal */
@media (max-width: 768px) {
    #relatedRecordsModal .modal-dialog {
        margin: 0;
        height: 100vh;
        max-width: 100%;
        width: 100%;
    }

    #relatedRecordsModal .modal-content {
        height: 100vh;
        border-radius: 0;
        border: none;
    }

    #relatedRecordsModal .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
    }

    .related-records-container .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .related-records-container .list-group-item {
        padding: 0.75rem;
    }

    .related-records-container .list-group-item h6 {
        font-size: 1rem;
    }

    .related-records-container .list-group-item p {
        font-size: 0.875rem;
    }

    .related-records-container .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Desktop optimizations for related records modal */
@media (min-width: 992px) {
    #relatedRecordsModal .modal-dialog {
        max-width: 900px;
    }

    .related-records-container .tab-content {
        min-height: 400px;
    }
}
