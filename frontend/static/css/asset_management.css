/* Asset Management Specific Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Cards */
.search-card, .results-card {
    border: none;
    box-shadow: 0 2px 10px var(--shadow-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-card:hover, .results-card:hover {
    box-shadow: 0 4px 20px var(--shadow-color);
}

/* Asset Item Cards */
.asset-item-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: var(--card-bg);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.asset-item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition);
}

.asset-item-card:hover {
    box-shadow: 0 8px 25px var(--shadow-color);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

.asset-item-card:hover::before {
    opacity: 1;
}

/* Asset Header */
.asset-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.asset-item-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.asset-item-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.3);
}

.asset-item-details h5 {
    margin-bottom: 0.25rem;
    color: var(--text-color);
    font-weight: 600;
}

.asset-item-details .text-muted {
    font-size: 0.9rem;
}

/* Asset Actions */
.asset-item-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.asset-action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.9rem;
}

.asset-action-btn:hover {
    transform: scale(1.1);
}

/* Asset Fields Grid */
.asset-item-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.asset-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.asset-field-label {
    font-weight: 500;
    color: var(--text-color);
    opacity: 0.8;
}

.asset-field-value {
    font-weight: 600;
    color: var(--text-color);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-OPERATING {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
    box-shadow: 0 2px 4px rgba(46, 204, 113, 0.3);
}

.status-ACTIVE {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.status-INACTIVE {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 2px 4px rgba(149, 165, 166, 0.3);
}

.status-BROKEN {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

.status-MISSING {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
}

/* Type Badges */
.type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--light-color);
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

/* Loading States */
.loading-container {
    padding: 3rem 1rem;
}

.loading-container .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Table Styles */
.table th {
    background-color: var(--header-bg);
    color: var(--header-text-color);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
        text-align: center;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .asset-item-card {
        padding: 1rem;
    }

    .asset-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .asset-item-actions {
        margin-top: 1rem;
        width: 100%;
        justify-content: space-around;
    }

    .asset-item-fields {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .asset-field {
        flex-direction: column;
        align-items: flex-start;
        padding: 0.75rem 0;
    }

    .asset-field-label {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .asset-field-value {
        font-size: 1rem;
    }
}

/* Dark Mode Support */
[data-bs-theme="dark"] .asset-item-card {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .table th {
    background-color: var(--header-bg);
    color: var(--header-text-color);
}

[data-bs-theme="dark"] .table td {
    border-bottom-color: var(--border-color);
}

[data-bs-theme="dark"] .table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* Asset Detail Modal Styles */
.asset-detail-category {
    margin-bottom: 2rem;
}

.asset-detail-category-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.asset-detail-category-header i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.asset-detail-category-header h6 {
    margin: 0;
    font-weight: 600;
    color: var(--text-color);
}

.asset-detail-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.asset-detail-field {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.asset-detail-field i {
    margin-right: 0.75rem;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.asset-detail-field-content {
    flex: 1;
}

.asset-detail-field-label {
    font-size: 0.8rem;
    color: var(--text-color);
    opacity: 0.7;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.asset-detail-field-value {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

/* Asset Actions Grid */
.asset-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.asset-action-card {
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--card-bg);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.asset-action-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px var(--shadow-color);
    transform: translateY(-2px);
}

.asset-action-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.asset-action-card h6 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 600;
    user-select: none;
    pointer-events: none;
}

.asset-action-card p {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.7;
    margin: 0;
    user-select: none;
    pointer-events: none;
}

.asset-action-card i {
    user-select: none;
    pointer-events: none;
}

/* Ensure the entire button is clickable and text is not selectable */
.asset-action-card * {
    user-select: none;
    pointer-events: none;
}

.asset-action-card {
    position: relative;
}

.asset-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}
