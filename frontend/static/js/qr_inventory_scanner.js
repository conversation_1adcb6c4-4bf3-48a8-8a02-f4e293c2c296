/**
 * QR Code Inventory Scanner JavaScript
 * Handles QR code scanning and inventory operations
 */

class QRInventoryScanner {
    constructor() {
        this.currentInventoryData = null;
        this.selectedOperation = null;
        this.videoElement = null;
        this.canvasElement = null;
        this.canvasContext = null;
        this.cameraActive = false;
        this.currentCameraId = null;
        this.availableCameras = [];
        this.scanningInterval = null;
        this.currentStream = null;
        this.scanCount = 0;
        this.lastScanTime = 0;
        this.init();
    }

    init() {
        console.log('🔍 QR SCANNER: Initializing QR inventory scanner');

        // Set up event listeners
        this.setupEventListeners();

        // Wait for QR scanner library to load
        this.waitForQRLibrary();

        // Focus on QR input
        document.getElementById('qrCodeInput').focus();
    }

    waitForQRLibrary() {
        console.log('🔄 QR SCANNER: Starting to wait for jsQR library...');
        console.log('🔍 QR SCANNER: Initial check - typeof jsQR =', typeof jsQR);
        console.log('🔍 QR SCANNER: Initial check - window.jsQR =', window.jsQR);

        // Check if jsQR library is loaded
        let attempts = 0;
        const maxAttempts = 40; // 20 seconds max wait

        const checkLibrary = () => {
            attempts++;
            console.log(`🔍 QR SCANNER: Check attempt ${attempts}/${maxAttempts} - typeof jsQR =`, typeof jsQR);

            if (typeof jsQR !== 'undefined') {
                console.log('✅ QR SCANNER: jsQR library loaded successfully');
                console.log('🔍 QR SCANNER: jsQR function =', jsQR);
                this.initializeCameraElements();
                // Enable camera button
                const cameraBtn = document.getElementById('cameraToggleBtn');
                if (cameraBtn) {
                    cameraBtn.disabled = false;
                    cameraBtn.innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera Scanner';
                    cameraBtn.classList.remove('btn-outline-secondary', 'btn-outline-warning');
                    cameraBtn.classList.add('btn-outline-primary');
                }

                // Hide force enable button since library loaded successfully
                const forceBtn = document.getElementById('forceEnableBtn');
                if (forceBtn) {
                    forceBtn.style.display = 'none';
                }

            } else if (attempts < maxAttempts) {
                console.log(`⏳ QR SCANNER: Waiting for jsQR library... (${attempts}/${maxAttempts})`);
                setTimeout(checkLibrary, 500);
            } else {
                console.error('❌ QR SCANNER: jsQR library failed to load after 20 seconds');
                console.error('❌ QR SCANNER: Final check - typeof jsQR =', typeof jsQR);
                console.error('❌ QR SCANNER: Final check - window.jsQR =', window.jsQR);
                console.error('❌ QR SCANNER: Available globals:', Object.keys(window).filter(k => k.toLowerCase().includes('qr')));

                // Enable button anyway for manual testing
                const cameraBtn = document.getElementById('cameraToggleBtn');
                const forceBtn = document.getElementById('forceEnableBtn');
                if (cameraBtn) {
                    cameraBtn.disabled = false;
                    cameraBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Library Failed - Try Anyway';
                    cameraBtn.classList.remove('btn-outline-primary');
                    cameraBtn.classList.add('btn-outline-warning');
                }
                if (forceBtn) {
                    forceBtn.style.display = 'inline-block';
                }

                // Show error message to user
                this.showError('QR scanning library failed to load. Please refresh the page. If the problem persists, check your internet connection.');
            }
        };

        // Also listen for the jsQRLoaded event
        window.addEventListener('jsQRLoaded', () => {
            console.log('✅ QR SCANNER: jsQR library loaded via event');
            console.log('🔍 QR SCANNER: Event check - typeof jsQR =', typeof jsQR);

            if (typeof jsQR !== 'undefined') {
                this.initializeCameraElements();
                // Enable camera button
                const cameraBtn = document.getElementById('cameraToggleBtn');
                if (cameraBtn) {
                    cameraBtn.disabled = false;
                    cameraBtn.innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera Scanner';
                    cameraBtn.classList.remove('btn-outline-secondary', 'btn-outline-warning');
                    cameraBtn.classList.add('btn-outline-primary');
                }

                // Hide force enable button
                const forceBtn = document.getElementById('forceEnableBtn');
                if (forceBtn) {
                    forceBtn.style.display = 'none';
                }
            } else {
                console.error('❌ QR SCANNER: Event fired but jsQR still not available');
            }
        });

        checkLibrary();
    }

    initializeCameraElements() {
        this.videoElement = document.getElementById('qrVideo');
        this.canvasElement = document.getElementById('qrCanvas');
        if (this.canvasElement) {
            this.canvasContext = this.canvasElement.getContext('2d');
        }
        console.log('✅ QR SCANNER: Camera elements initialized');
    }

    setupEventListeners() {
        // Enter key processing for QR input
        document.getElementById('qrCodeInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.processQRCode();
            }
        });

        // Auto-process when QR data is pasted
        document.getElementById('qrCodeInput').addEventListener('paste', () => {
            setTimeout(() => {
                this.processQRCode();
            }, 100);
        });
    }

    async processQRCode() {
        const qrInput = document.getElementById('qrCodeInput');
        const qrContent = qrInput.value.trim();

        if (!qrContent) {
            return;
        }

        this.showLoading('Processing QR code...');

        try {
            // Decode QR code
            const response = await fetch('/api/inventory/decode-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ qr_content: qrContent })
            });

            const result = await response.json();

            if (result.success) {
                this.currentInventoryData = result.inventory_data;
                this.displayInventoryInfo(this.currentInventoryData);
                this.showInventoryOperations();
                this.hideLoading();

            } else {
                this.hideLoading();
            }

        } catch (error) {
            this.hideLoading();
        }
    }

    displayInventoryInfo(inventoryData) {
        const infoSection = document.getElementById('inventoryInfoSection');
        const infoCard = document.getElementById('inventoryInfoCard');

        // Format the inventory information
        const generatedDate = inventoryData.generated_timestamp ?
            new Date(inventoryData.generated_timestamp).toLocaleString() : 'Unknown';

        // Handle both currentbalance (inventory-level) and curbal (balance-specific) QR codes
        const currentBalance = inventoryData.currentbalance || inventoryData.curbal || 0;

        // Determine QR type for display
        const qrType = inventoryData.qr_type || 'inventory_level';
        const isBalanceSpecific = qrType === 'balance_specific';

        infoCard.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">Item Information</h6>
                    <table class="table table-sm table-borderless">
                        <tr><td><strong>Item Number:</strong></td><td>${inventoryData.itemnum || 'N/A'}</td></tr>
                        <tr><td><strong>Description:</strong></td><td>${inventoryData.description || 'N/A'}</td></tr>
                        <tr><td><strong>Site ID:</strong></td><td>${inventoryData.siteid || 'N/A'}</td></tr>
                        <tr><td><strong>Location:</strong></td><td>${inventoryData.storeloc || 'N/A'}</td></tr>
                        <tr><td><strong>Bin:</strong></td><td>${inventoryData.binnum || 'N/A'}</td></tr>
                        ${isBalanceSpecific ? `<tr><td><strong>QR Type:</strong></td><td><span class="badge bg-info">Balance-Specific</span></td></tr>` : ''}
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">Current Status</h6>
                    <table class="table table-sm table-borderless">
                        <tr><td><strong>Current Balance:</strong></td><td class="text-primary fw-bold">${currentBalance}</td></tr>
                        <tr><td><strong>Issue Unit:</strong></td><td>${inventoryData.issueunit || 'EA'}</td></tr>
                        <tr><td><strong>Condition:</strong></td><td>${inventoryData.conditioncode || 'N/A'}</td></tr>
                        <tr><td><strong>Lot Number:</strong></td><td>${inventoryData.lotnum || 'N/A'}</td></tr>
                        <tr><td><strong>Control Account:</strong></td><td>${inventoryData.controlacc || 'N/A'}</td></tr>
                        <tr><td><strong>Shrinkage Account:</strong></td><td>${inventoryData.shrinkageacc || 'N/A'}</td></tr>
                        <tr><td><strong>QR Generated:</strong></td><td>${generatedDate}</td></tr>
                    </table>
                </div>
            </div>
            ${isBalanceSpecific ? this.renderBalanceSpecificInfo(inventoryData) : ''}
        `;

        // Show the inventory info section
        infoSection.style.display = 'block';
        infoSection.scrollIntoView({ behavior: 'smooth' });
    }

    renderBalanceSpecificInfo(inventoryData) {
        // Additional information for balance-specific QR codes
        const shelfLifeDays = inventoryData.shelf_life_days;
        const expirationDate = inventoryData.expiration_date;
        const stagedBalance = inventoryData.stagedcurbal || 0;
        const physCount = inventoryData.physcnt || 0;
        const physCountDate = inventoryData.physcntdate;
        const reconciled = inventoryData.reconciled;

        return `
            <div class="mt-3">
                <h6 class="text-info">Balance-Specific Details</h6>
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr><td><strong>Staged Balance:</strong></td><td>${stagedBalance}</td></tr>
                            <tr><td><strong>Physical Count:</strong></td><td>${physCount}</td></tr>
                            <tr><td><strong>Reconciled:</strong></td><td>${reconciled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-warning">No</span>'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            ${shelfLifeDays ? `<tr><td><strong>Shelf Life:</strong></td><td>${shelfLifeDays} days</td></tr>` : ''}
                            ${expirationDate ? `<tr><td><strong>Expiration:</strong></td><td>${new Date(expirationDate).toLocaleDateString()}</td></tr>` : ''}
                            ${physCountDate ? `<tr><td><strong>Count Date:</strong></td><td>${new Date(physCountDate).toLocaleDateString()}</td></tr>` : ''}
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    showInventoryOperations() {
        const operationsSection = document.getElementById('inventoryOperations');
        operationsSection.style.display = 'block';

        // Handle both currentbalance (inventory-level) and curbal (balance-specific) QR codes
        const currentBalance = this.currentInventoryData.currentbalance || this.currentInventoryData.curbal || 0;

        // Pre-populate system count for cycle counting
        document.getElementById('systemCount').value = currentBalance;

        // Pre-populate system balance for adjustment
        document.getElementById('systemBalance').value = currentBalance;

        // Pre-populate fields from QR data
        document.getElementById('binNumber').value = this.currentInventoryData.binnum || '';
        document.getElementById('conditionCode').value = this.currentInventoryData.conditioncode || '';

        // Update condition code field based on QR data
        this.updateConditionCodeRequirement();

        // Pre-populate optional fields from QR data if available
        document.getElementById('lotNumber').value = this.currentInventoryData.lotnum || '';
        document.getElementById('lotType').value = this.currentInventoryData.lottype || '';
        document.getElementById('controlAccount').value = this.currentInventoryData.controlacc || '';
        document.getElementById('shrinkageAccount').value = this.currentInventoryData.shrinkageacc || '';
        document.getElementById('availableBalance').value = currentBalance;

        // Reset forms
        this.resetOperationForms();
    }

    updateConditionCodeRequirement() {
        const conditionCodeField = document.getElementById('conditionCode');
        const conditionCodeLabel = document.querySelector('label[for="conditionCode"]');
        const conditionCodeHelp = conditionCodeField.parentElement.querySelector('.form-text');

        // Check if we have inventory data and if QR code has condition code
        const hasConditionCodeInQR = this.currentInventoryData &&
                                     this.currentInventoryData.conditioncode &&
                                     this.currentInventoryData.conditioncode.trim() !== '';

        if (hasConditionCodeInQR) {
            // Make it required
            conditionCodeField.setAttribute('required', 'required');
            conditionCodeField.classList.add('required-field');
            conditionCodeLabel.innerHTML = `
                <i class="fas fa-check-circle me-1"></i>Condition Code
                <span class="text-danger">*</span>
            `;
            conditionCodeHelp.innerHTML = `
                <i class="fas fa-exclamation-triangle me-1 text-danger"></i>
                Required - Item condition from QR code
            `;
        } else {
            // Make it optional
            conditionCodeField.removeAttribute('required');
            conditionCodeField.classList.remove('required-field');
            conditionCodeLabel.innerHTML = `
                <i class="fas fa-check-circle me-1"></i>Condition Code
                <small class="text-muted">(optional)</small>
            `;
            conditionCodeHelp.innerHTML = `
                <i class="fas fa-info me-1 text-info"></i>
                Optional - Select if different from default (A1)
            `;
            // Set default value if empty
            if (!conditionCodeField.value) {
                conditionCodeField.value = 'A1';
            }
        }
    }

    selectOperation(operationType) {
        // Hide all operation forms
        document.querySelectorAll('.operation-card').forEach(card => {
            card.classList.remove('active');
            card.querySelector('div[id$="Form"]').style.display = 'none';
        });

        // Show selected operation form
        const selectedCard = document.getElementById(`${operationType}Card`);
        const selectedForm = document.getElementById(`${operationType}Form`);
        
        selectedCard.classList.add('active');
        selectedForm.style.display = 'block';
        
        this.selectedOperation = operationType;
        
        console.log(`🔍 QR SCANNER: Selected operation: ${operationType}`);
        
        // Focus on first input of selected operation
        setTimeout(() => {
            const firstInput = selectedForm.querySelector('input, select, textarea');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    calculateVariance() {
        const physicalCount = parseFloat(document.getElementById('cyclePhysicalCount').value) || 0;
        const systemCount = parseFloat(document.getElementById('systemCount').value) || 0;
        const variance = physicalCount - systemCount;
        
        const varianceDisplay = document.getElementById('varianceDisplay');
        
        if (variance > 0) {
            varianceDisplay.textContent = `+${variance.toFixed(2)}`;
            varianceDisplay.className = 'form-control-plaintext variance-positive';
        } else if (variance < 0) {
            varianceDisplay.textContent = variance.toFixed(2);
            varianceDisplay.className = 'form-control-plaintext variance-negative';
        } else {
            varianceDisplay.textContent = '0.00';
            varianceDisplay.className = 'form-control-plaintext variance-zero';
        }
    }

    async submitCycleCount() {
        const physicalCount = parseFloat(document.getElementById('cyclePhysicalCount').value);
        const systemCount = parseFloat(document.getElementById('systemCount').value) || 0;
        const notes = document.getElementById('countNotes').value.trim();

        if (isNaN(physicalCount)) {
            return;
        }

        const variance = physicalCount - systemCount;

        this.showLoading('Submitting cycle count...');

        try {
            // Submit cycle count to API
            const countData = {
                inventory_data: this.currentInventoryData,
                physical_count: physicalCount,
                system_count: systemCount,
                notes: notes
            };

            const response = await fetch('/api/inventory/cycle-count', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(countData)
            });

            const result = await response.json();

            if (result.success) {
                this.showOperationResult('Cycle Count', 'success', result.message);
            } else {
                this.hideLoading();
            }

        } catch (error) {
            this.hideLoading();
        }
    }

    async submitAdjustment() {
        console.log('🔍 QR SCANNER: submitAdjustment() called');

        const adjustmentMethod = document.getElementById('adjustmentMethod').value;
        const reasonCode = document.getElementById('reasonCode').value;
        const notes = document.getElementById('adjustmentNotes').value.trim();

        // Get required fields
        const binNumber = document.getElementById('binNumber').value.trim();
        let conditionCode = document.getElementById('conditionCode').value;

        // Validate condition code if required
        const conditionCodeField = document.getElementById('conditionCode');
        const isConditionCodeRequired = conditionCodeField.hasAttribute('required');

        if (isConditionCodeRequired && (!conditionCode || conditionCode.trim() === '')) {
            this.showError('Condition Code is required. Please enter a valid condition code.');
            conditionCodeField.focus();
            return;
        }

        // Set default condition code if not provided and not required
        if (!conditionCode || conditionCode.trim() === '') {
            conditionCode = 'A1';
        }

        // Get optional fields
        const lotNumber = document.getElementById('lotNumber').value.trim();
        const lotType = document.getElementById('lotType').value.trim();
        const controlAccount = document.getElementById('controlAccount').value.trim();
        const shrinkageAccount = document.getElementById('shrinkageAccount').value.trim();
        const reconciled = document.getElementById('reconciled').checked;

        let adjustmentData;

        if (adjustmentMethod === 'PHYSICAL_COUNT') {
            // Physical Count Adjustment
            const physicalCountValue = document.getElementById('physicalCount').value.trim();
            const physicalCount = parseFloat(physicalCountValue);

            if (!physicalCountValue || isNaN(physicalCount) || physicalCount < 0) {
                this.showError('Please enter a valid physical count (must be 0 or greater).');
                document.getElementById('physicalCount').focus();
                return;
            }

            adjustmentData = {
                inventory_data: {
                    ...this.currentInventoryData,
                    binnum: binNumber,
                    conditioncode: conditionCode,
                    lotnum: lotNumber,
                    lottype: lotType,
                    controlacc: controlAccount,
                    shrinkageacc: shrinkageAccount
                },
                adjustment_method: 'PHYSICAL_COUNT',
                adjustment_type: 'PHYSICAL_COUNT',
                physical_count: physicalCount,
                reason_code: reasonCode,
                notes: notes,
                reconciled: reconciled
            };

        } else {
            // Current Balance Adjustment
            const adjustmentType = document.getElementById('adjustmentType').value;
            const adjustmentQuantityValue = document.getElementById('adjustmentQuantity').value.trim();
            const adjustmentQuantity = parseFloat(adjustmentQuantityValue);

            if (!adjustmentQuantityValue || isNaN(adjustmentQuantity) || adjustmentQuantity <= 0) {
                this.showError('Please enter a valid adjustment quantity (must be greater than 0).');
                document.getElementById('adjustmentQuantity').focus();
                return;
            }

            adjustmentData = {
                inventory_data: {
                    ...this.currentInventoryData,
                    binnum: binNumber,
                    conditioncode: conditionCode,
                    lotnum: lotNumber,
                    lottype: lotType,
                    controlacc: controlAccount,
                    shrinkageacc: shrinkageAccount
                },
                adjustment_method: 'CURRENT_BALANCE',
                adjustment_type: adjustmentType,
                quantity: adjustmentQuantity,
                reason_code: reasonCode,
                notes: notes,
                reconciled: reconciled
            };
        }

        console.log('🔍 QR SCANNER: Submitting adjustment data:', adjustmentData);
        this.showLoading('Submitting adjustment...');

        try {
            const response = await fetch('/api/inventory/adjustment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();
            console.log('🔍 QR SCANNER: Adjustment response:', result);

            if (result.success) {
                this.showOperationResult('Inventory Adjustment', 'success', result.message);
            } else {
                this.hideLoading();
                this.showError(result.message || 'Failed to submit adjustment. Please try again.');
            }

        } catch (error) {
            console.error('❌ QR SCANNER: Error submitting adjustment:', error);
            this.hideLoading();
            this.showError('Network error occurred while submitting adjustment. Please check your connection and try again.');
        }
    }

    async submitTransfer() {
        const transferQuantity = parseFloat(document.getElementById('transferQuantity').value);
        const availableBalance = parseFloat(document.getElementById('availableBalance').value) || 0;
        const toLocation = document.getElementById('toLocation').value.trim();
        const toBin = document.getElementById('toBin').value.trim();
        const notes = document.getElementById('transferNotes').value.trim();

        if (isNaN(transferQuantity) || transferQuantity <= 0) {
            return;
        }

        if (transferQuantity > availableBalance) {
            return;
        }

        if (!toLocation) {
            return;
        }

        this.showLoading('Submitting transfer...');

        try {
            // Submit transfer to API
            const transferData = {
                inventory_data: this.currentInventoryData,
                quantity: transferQuantity,
                to_location: toLocation,
                to_bin: toBin,
                notes: notes
            };

            const response = await fetch('/api/inventory/transfer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(transferData)
            });

            const result = await response.json();

            if (result.success) {
                this.showOperationResult('Inventory Transfer', 'success', result.message);
            } else {
                this.hideLoading();
            }

        } catch (error) {
            this.hideLoading();
        }
    }

    showOperationResult(operationType, status, message) {
        const resultsSection = document.getElementById('operationResults');
        const resultsContent = document.getElementById('resultsContent');

        const statusClass = status === 'success' ? 'alert-success' : 'alert-danger';
        const statusIcon = status === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

        resultsContent.innerHTML = `
            <div class="alert ${statusClass}">
                <i class="fas ${statusIcon} me-2"></i>
                <strong>${operationType}:</strong> ${message}
            </div>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="qrScanner.startNewScan()">
                    <i class="fas fa-qrcode me-1"></i>Scan Another Item
                </button>
                <button class="btn btn-outline-secondary ms-2" onclick="qrScanner.clearScanner()">
                    <i class="fas fa-broom me-1"></i>Clear All
                </button>
            </div>
        `;

        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    cancelOperation() {
        this.resetOperationForms();
        this.selectedOperation = null;
        
        // Remove active state from all cards
        document.querySelectorAll('.operation-card').forEach(card => {
            card.classList.remove('active');
            card.querySelector('div[id$="Form"]').style.display = 'none';
        });
    }

    resetOperationForms() {
        // Reset all form inputs
        document.getElementById('physicalCount').value = '';
        document.getElementById('countNotes').value = '';
        document.getElementById('adjustmentQuantity').value = '';
        document.getElementById('adjustmentNotes').value = '';
        document.getElementById('transferQuantity').value = '';
        document.getElementById('toLocation').value = '';
        document.getElementById('toBin').value = '';
        document.getElementById('transferNotes').value = '';
        
        // Reset variance display
        document.getElementById('varianceDisplay').textContent = '-';
        document.getElementById('varianceDisplay').className = 'form-control-plaintext';
    }

    startNewScan() {
        // Clear QR input and focus
        document.getElementById('qrCodeInput').value = '';
        document.getElementById('qrCodeInput').focus();
        
        // Hide results
        document.getElementById('operationResults').style.display = 'none';
        
        // Reset operation selection
        this.cancelOperation();
    }

    clearScanner() {
        // Clear all data and reset to initial state
        this.currentInventoryData = null;
        this.selectedOperation = null;
        
        // Clear QR input
        document.getElementById('qrCodeInput').value = '';
        
        // Hide all sections
        document.getElementById('inventoryInfoSection').style.display = 'none';
        document.getElementById('inventoryOperations').style.display = 'none';
        document.getElementById('operationResults').style.display = 'none';
        
        // Reset forms
        this.resetOperationForms();
        
        // Focus on QR input
        document.getElementById('qrCodeInput').focus();
        
        console.log('🔍 QR SCANNER: Scanner cleared');
    }

    showLoading(message) {
        // Simple loading indication (could be enhanced with a proper loading modal)
        console.log(`⏳ QR SCANNER: ${message}`);

        // Show loading overlay if it exists
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
            const loadingText = loadingOverlay.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = message;
            }
        }
    }

    hideLoading() {
        console.log('✅ QR SCANNER: Loading hidden');

        // Hide loading overlay if it exists
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    showError(message) {
        console.error('❌ QR SCANNER: Error:', message);

        // Show error in a user-friendly way
        const resultsSection = document.getElementById('operationResults');
        const resultsContent = document.getElementById('resultsContent');

        resultsContent.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error:</strong> ${message}
            </div>
            <div class="mt-3">
                <button class="btn btn-outline-primary" onclick="qrScanner.hideError()">
                    <i class="fas fa-times me-1"></i>Dismiss
                </button>
            </div>
        `;

        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    hideError() {
        const resultsSection = document.getElementById('operationResults');
        resultsSection.style.display = 'none';
    }

    async toggleCameraScanner() {
        if (this.cameraActive) {
            this.stopCameraScanner();
        } else {
            await this.startCameraScanner();
        }
    }

    async startCameraScanner() {
        try {
            console.log('🔍 QR SCANNER: Starting camera scanner');

            // Initialize camera elements if not done
            if (!this.videoElement) {
                this.initializeCameraElements();
            }

            // Get available cameras
            await this.getAvailableCameras();

            if (this.availableCameras.length === 0) {
                this.showError('No cameras found. Please check camera permissions.');
                return;
            }

            // Use the first available camera (usually back camera on mobile, any camera on desktop)
            this.currentCameraId = this.availableCameras[0].deviceId;

            // Start camera stream
            await this.startCameraStream();

            this.cameraActive = true;

            // Start QR code scanning
            this.startQRScanning();

            // Update UI
            document.getElementById('cameraContainer').style.display = 'block';
            document.getElementById('cameraToggleBtn').innerHTML = '<i class="fas fa-stop me-1"></i>Stop Camera';

            // Show camera switch button if multiple cameras available
            const switchBtn = document.getElementById('cameraSwitchBtn');
            if (this.availableCameras.length > 1) {
                switchBtn.style.display = 'inline-block';
                const nextIndex = 1 % this.availableCameras.length;
                const nextCamera = this.availableCameras[nextIndex];
                const nextCameraLabel = nextCamera.label || `Camera ${nextIndex + 1}`;
                switchBtn.innerHTML = `<i class="fas fa-sync-alt me-1"></i>Switch to ${nextCameraLabel}`;
            } else {
                switchBtn.style.display = 'none';
            }

            // Update camera info
            const currentCamera = this.availableCameras.find(cam => cam.deviceId === this.currentCameraId);
            const currentIndex = this.availableCameras.indexOf(currentCamera);
            const cameraLabel = currentCamera ? (currentCamera.label || `Camera ${currentIndex + 1}`) : 'Unknown Camera';
            document.getElementById('cameraInfo').textContent = `Using: ${cameraLabel}`;



        } catch (error) {
            console.error('❌ QR SCANNER: Failed to start camera:', error);
            this.showError(`Failed to start camera: ${error.message}. Please ensure camera permissions are granted.`);
        }
    }

    async getAvailableCameras() {
        try {
            // Request permissions first to get proper device labels
            await navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
                stream.getTracks().forEach(track => track.stop());
            });

            const devices = await navigator.mediaDevices.enumerateDevices();
            this.availableCameras = devices.filter(device => device.kind === 'videoinput');

            // Sort cameras to prioritize back camera first, then front camera
            this.availableCameras.sort((a, b) => {
                const aLabel = (a.label || '').toLowerCase();
                const bLabel = (b.label || '').toLowerCase();

                // Prioritize back/rear cameras
                if (aLabel.includes('back') || aLabel.includes('rear') || aLabel.includes('environment')) return -1;
                if (bLabel.includes('back') || bLabel.includes('rear') || bLabel.includes('environment')) return 1;

                // Then front cameras
                if (aLabel.includes('front') || aLabel.includes('user')) return -1;
                if (bLabel.includes('front') || bLabel.includes('user')) return 1;

                return 0;
            });

            console.log('📷 Available cameras (sorted):', this.availableCameras.map((cam, idx) => ({
                index: idx,
                deviceId: cam.deviceId,
                label: cam.label || `Camera ${idx + 1}`,
                kind: cam.kind
            })));

        } catch (error) {
            console.error('❌ QR SCANNER: Failed to get cameras:', error);
            this.availableCameras = [];
        }
    }

    async startCameraStream() {
        try {
            // Stop existing stream if any
            if (this.currentStream) {
                this.currentStream.getTracks().forEach(track => track.stop());
                this.currentStream = null;
            }

            // Build camera constraints with better settings for QR scanning
            const constraints = {
                video: {
                    deviceId: this.currentCameraId ? { exact: this.currentCameraId } : undefined,
                    width: { ideal: 1280, min: 640 },
                    height: { ideal: 720, min: 480 },
                    facingMode: this.currentCameraId ? undefined : 'environment', // Prefer back camera
                    focusMode: 'continuous',
                    exposureMode: 'continuous',
                    whiteBalanceMode: 'continuous'
                }
            };

            console.log('📷 QR SCANNER: Requesting camera with constraints:', constraints);

            this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.videoElement.srcObject = this.currentStream;

            // Wait for video to be ready with timeout
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Camera stream timeout'));
                }, 10000); // 10 second timeout

                this.videoElement.onloadedmetadata = () => {
                    clearTimeout(timeout);
                    this.videoElement.play().then(() => {

                        resolve();
                    }).catch(reject);
                };

                this.videoElement.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(error);
                };
            });

        } catch (error) {
            console.error('❌ QR SCANNER: Failed to start camera stream:', error);

            // Try fallback constraints if the initial request fails
            if (this.currentCameraId) {
                console.log('🔄 QR SCANNER: Trying fallback constraints without specific device ID');
                try {
                    const fallbackConstraints = {
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'environment'
                        }
                    };

                    this.currentStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
                    this.videoElement.srcObject = this.currentStream;

                    return new Promise((resolve, reject) => {
                        this.videoElement.onloadedmetadata = () => {
                            this.videoElement.play().then(() => {
                                console.log('✅ QR SCANNER: Fallback camera stream started');
                                resolve();
                            }).catch(reject);
                        };
                        this.videoElement.onerror = reject;
                    });
                } catch (fallbackError) {
                    console.error('❌ QR SCANNER: Fallback camera also failed:', fallbackError);
                    throw fallbackError;
                }
            }

            throw error;
        }
    }

    startQRScanning() {
        if (this.scanningInterval) {
            clearInterval(this.scanningInterval);
        }

        // Reset scan count
        this.scanCount = 0;

        // Use requestAnimationFrame for better performance and smoother scanning
        const scanLoop = () => {
            if (this.cameraActive && this.scanningInterval !== null) {
                this.scanForQRCode();
                this.scanningInterval = requestAnimationFrame(scanLoop);
            }
        };

        this.scanningInterval = requestAnimationFrame(scanLoop);
        console.log('✅ QR SCANNER: QR scanning started with optimized frame rate');
    }

    scanForQRCode() {
        if (!this.videoElement || !this.canvasElement || !this.canvasContext || typeof jsQR === 'undefined') {
            return;
        }

        if (this.videoElement.readyState === this.videoElement.HAVE_ENOUGH_DATA) {
            try {
                this.scanCount++;
                this.lastScanTime = Date.now();

                // Ensure video dimensions are available
                const videoWidth = this.videoElement.videoWidth;
                const videoHeight = this.videoElement.videoHeight;

                if (videoWidth === 0 || videoHeight === 0) {
                    return; // Video not ready yet
                }

                // Set canvas size to match video
                this.canvasElement.width = videoWidth;
                this.canvasElement.height = videoHeight;

                // Draw video frame to canvas
                this.canvasContext.drawImage(this.videoElement, 0, 0, videoWidth, videoHeight);

                // Get image data
                const imageData = this.canvasContext.getImageData(0, 0, videoWidth, videoHeight);

                // Update debug info less frequently for performance
                if (this.scanCount % 30 === 0) { // Update every 30 frames (~0.5 seconds at 60fps)
                    this.updateDebugInfo();
                }

                // Try multiple scanning approaches for better detection
                let code = null;

                // Attempt 1: Full frame scanning with high quality settings
                code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "attemptBoth",
                });

                // Attempt 2: If no code found, try with inverted colors first
                if (!code) {
                    code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "invertFirst",
                    });
                }

                // Attempt 3: Try with only inverted colors
                if (!code) {
                    code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "onlyInvert",
                    });
                }

                // Attempt 4: Try with center region for better focus (larger region)
                if (!code && videoWidth > 300 && videoHeight > 300) {
                    const centerX = Math.floor(videoWidth * 0.2);
                    const centerY = Math.floor(videoHeight * 0.2);
                    const centerWidth = Math.floor(videoWidth * 0.6);
                    const centerHeight = Math.floor(videoHeight * 0.6);

                    const centerImageData = this.canvasContext.getImageData(centerX, centerY, centerWidth, centerHeight);
                    code = jsQR(centerImageData.data, centerImageData.width, centerImageData.height, {
                        inversionAttempts: "attemptBoth",
                    });
                }

                // Attempt 5: Try with smaller center region for very close QR codes
                if (!code && videoWidth > 200 && videoHeight > 200) {
                    const centerX = Math.floor(videoWidth * 0.3);
                    const centerY = Math.floor(videoHeight * 0.3);
                    const centerWidth = Math.floor(videoWidth * 0.4);
                    const centerHeight = Math.floor(videoHeight * 0.4);

                    const centerImageData = this.canvasContext.getImageData(centerX, centerY, centerWidth, centerHeight);
                    code = jsQR(centerImageData.data, centerImageData.width, centerImageData.height, {
                        inversionAttempts: "attemptBoth",
                    });
                }

                if (code && code.data && code.data.trim().length > 0) {
                    console.log('🔍 QR SCANNER: QR code detected:', code.data);
                    this.updateScanStatus('QR Code Detected!', 'success');
                    this.onQRCodeDetected({ data: code.data });
                } else {
                    // Update scan status to show it's actively scanning (less frequently for performance)
                    if (this.scanCount % 60 === 0) { // Update every 60 frames (~1 second at 60fps)
                        this.updateScanStatus(`Scanning... (${this.scanCount} attempts)`, 'info');
                    }
                }

            } catch (error) {
                console.error('❌ QR SCANNER: Error during QR scanning:', error);
                this.updateScanStatus('Scanning error: ' + error.message, 'danger');
            }
        } else {
            // Video not ready, show status
            if (this.scanCount % 120 === 0) { // Update every 2 seconds at 60fps
                this.updateScanStatus('Waiting for camera to be ready...', 'warning');
            }
        }
    }

    updateDebugInfo() {
        const debugElement = document.getElementById('debugInfo');
        if (debugElement) {
            debugElement.textContent = `Scans: ${this.scanCount} | Video: ${this.videoElement.videoWidth}x${this.videoElement.videoHeight} | Canvas: ${this.canvasElement.width}x${this.canvasElement.height}`;
        }
    }

    updateScanStatus(message, type = 'info') {
        const statusElement = document.getElementById('scanStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `small text-${type} mt-1`;
        }
    }

    stopCameraScanner() {
        try {
            console.log('🔍 QR SCANNER: Stopping camera scanner');

            // Stop scanning interval/animation frame
            if (this.scanningInterval) {
                if (typeof this.scanningInterval === 'number') {
                    cancelAnimationFrame(this.scanningInterval);
                } else {
                    clearInterval(this.scanningInterval);
                }
                this.scanningInterval = null;
            }

            // Stop camera stream
            if (this.currentStream) {
                this.currentStream.getTracks().forEach(track => track.stop());
                this.currentStream = null;
            }

            // Clear video element
            if (this.videoElement) {
                this.videoElement.srcObject = null;
            }

            this.cameraActive = false;

            // Update UI
            document.getElementById('cameraContainer').style.display = 'none';
            document.getElementById('cameraToggleBtn').innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera Scanner';
            document.getElementById('cameraSwitchBtn').style.display = 'none';
            document.getElementById('cameraInfo').textContent = '';

            console.log('✅ QR SCANNER: Camera scanner stopped');

        } catch (error) {
            console.error('❌ QR SCANNER: Error stopping camera:', error);
        }
    }

    async switchCamera() {
        if (!this.cameraActive || this.availableCameras.length <= 1) {
            this.showError('No additional cameras available to switch to.');
            return;
        }

        try {
            console.log('🔍 QR SCANNER: Switching camera');
            this.updateScanStatus('Switching camera...', 'warning');

            // Find current camera index
            const currentIndex = this.availableCameras.findIndex(camera => camera.deviceId === this.currentCameraId);

            // Switch to next camera (cycle through available cameras)
            const nextIndex = (currentIndex + 1) % this.availableCameras.length;
            const nextCamera = this.availableCameras[nextIndex];

            console.log(`📷 Switching from camera ${currentIndex} to camera ${nextIndex}`);
            console.log(`📷 New camera: ${nextCamera.label || 'Camera ' + (nextIndex + 1)} (${nextCamera.deviceId})`);

            // Update camera ID
            this.currentCameraId = nextCamera.deviceId;

            // Stop current scanning
            if (this.scanningInterval) {
                if (typeof this.scanningInterval === 'number') {
                    cancelAnimationFrame(this.scanningInterval);
                } else {
                    clearInterval(this.scanningInterval);
                }
                this.scanningInterval = null;
            }

            // Reset scan count for new camera
            this.scanCount = 0;

            // Start new camera stream
            await this.startCameraStream();

            // Restart scanning
            this.startQRScanning();

            // Update camera info and switch button
            const cameraLabel = nextCamera.label || `Camera ${nextIndex + 1}`;
            document.getElementById('cameraInfo').textContent = `Using: ${cameraLabel}`;

            // Update switch button to show which camera is next
            const nextNextIndex = (nextIndex + 1) % this.availableCameras.length;
            const nextNextCamera = this.availableCameras[nextNextIndex];
            const switchBtn = document.getElementById('cameraSwitchBtn');
            if (switchBtn && this.availableCameras.length > 1) {
                const nextCameraLabel = nextNextCamera.label || `Camera ${nextNextIndex + 1}`;
                switchBtn.innerHTML = `<i class="fas fa-sync-alt me-1"></i>Switch to ${nextCameraLabel}`;
            }

            this.updateScanStatus('Camera switched successfully!', 'success');


        } catch (error) {
            console.error('❌ QR SCANNER: Failed to switch camera:', error);
            this.updateScanStatus('Failed to switch camera', 'danger');
            this.showError(`Failed to switch camera: ${error.message}`);
        }
    }

    onQRCodeDetected(result) {
        console.log('🔍 QR SCANNER: QR code detected via camera:', result.data);

        // Stop camera scanner
        this.stopCameraScanner();

        // Set the QR content in the input field
        document.getElementById('qrCodeInput').value = result.data;

        // Show success feedback
        this.showSuccessMessage('QR Code scanned successfully! Processing...');

        // Process the QR code automatically
        setTimeout(() => {
            this.processQRCode();
        }, 500);
    }

    captureQRManually() {
        console.log('🔍 QR SCANNER: Manual capture triggered');

        // Check if jsQR library is available
        if (typeof jsQR === 'undefined') {
            this.showError('QR scanning library not loaded. Please refresh the page and try again.');
            return;
        }

        if (!this.videoElement || !this.canvasElement || !this.canvasContext) {
            this.showError('Camera not properly initialized');
            return;
        }

        if (this.videoElement.readyState !== this.videoElement.HAVE_ENOUGH_DATA) {
            this.showError('Camera not ready. Please wait for the video to load.');
            return;
        }

        try {
            // Ensure video dimensions are available
            const videoWidth = this.videoElement.videoWidth;
            const videoHeight = this.videoElement.videoHeight;

            if (videoWidth === 0 || videoHeight === 0) {
                this.showError('Video dimensions not available. Please restart the camera.');
                return;
            }

            // Set canvas size to match video
            this.canvasElement.width = videoWidth;
            this.canvasElement.height = videoHeight;

            // Draw current video frame to canvas
            this.canvasContext.drawImage(this.videoElement, 0, 0, videoWidth, videoHeight);

            // Get image data
            const imageData = this.canvasContext.getImageData(0, 0, videoWidth, videoHeight);

            // Try to detect QR code with multiple attempts
            let code = null;

            // Attempt 1: Full frame
            code = jsQR(imageData.data, imageData.width, imageData.height, {
                inversionAttempts: "attemptBoth",
            });

            // Attempt 2: Center region
            if (!code && videoWidth > 200 && videoHeight > 200) {
                const centerX = Math.floor(videoWidth * 0.25);
                const centerY = Math.floor(videoHeight * 0.25);
                const centerWidth = Math.floor(videoWidth * 0.5);
                const centerHeight = Math.floor(videoHeight * 0.5);

                const centerImageData = this.canvasContext.getImageData(centerX, centerY, centerWidth, centerHeight);
                code = jsQR(centerImageData.data, centerImageData.width, centerImageData.height, {
                    inversionAttempts: "attemptBoth",
                });
            }

            if (code && code.data && code.data.trim().length > 0) {
                console.log('🔍 QR SCANNER: Manual capture successful:', code.data);
                this.updateScanStatus('QR Code captured successfully!', 'success');
                this.onQRCodeDetected({ data: code.data });
            } else {
                this.showError('No QR code detected in the current frame. Please position the QR code clearly in view and try again.');
                this.updateScanStatus('No QR code found in capture', 'warning');
            }

        } catch (error) {
            console.error('❌ QR SCANNER: Manual capture failed:', error);
            this.showError('Failed to capture QR code: ' + error.message);
        }
    }

    processQRImageUpload(imageFile) {
        console.log('🔍 QR SCANNER: Processing uploaded QR image');

        // Check if jsQR library is available
        if (typeof jsQR === 'undefined') {
            this.showError('QR scanning library not loaded. Please refresh the page and try again.');
            return;
        }

        if (!imageFile) {
            this.showError('No image file provided');
            return;
        }

        // Check file type
        if (!imageFile.type.startsWith('image/')) {
            this.showError('Please upload a valid image file');
            return;
        }

        // Check file size (limit to 10MB)
        if (imageFile.size > 10 * 1024 * 1024) {
            this.showError('Image file is too large. Please upload an image smaller than 10MB.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                try {
                    // Create a canvas to draw the image
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // Set canvas size to image size
                    canvas.width = img.width;
                    canvas.height = img.height;

                    // Draw image to canvas
                    ctx.drawImage(img, 0, 0);

                    // Get image data
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    // Try to detect QR code
                    let code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "attemptBoth",
                    });

                    if (code && code.data && code.data.trim().length > 0) {
                        console.log('🔍 QR SCANNER: QR code found in uploaded image:', code.data);

                        // Set the QR content in the input field
                        document.getElementById('qrCodeInput').value = code.data;

                        // Show success feedback
                        this.showSuccessMessage('QR Code found in uploaded image! Processing...');

                        // Process the QR code
                        setTimeout(() => {
                            this.processQRCode();
                        }, 500);

                    } else {
                        this.showError('No QR code detected in the uploaded image. Please ensure the image contains a clear, readable QR code.');
                    }

                } catch (error) {
                    console.error('❌ QR SCANNER: Error processing uploaded image:', error);
                    this.showError('Failed to process uploaded image: ' + error.message);
                }
            };

            img.onerror = () => {
                this.showError('Failed to load the uploaded image. Please try a different image.');
            };

            img.src = e.target.result;
        };

        reader.onerror = () => {
            this.showError('Failed to read the uploaded file. Please try again.');
        };

        reader.readAsDataURL(imageFile);
    }

    showSuccessMessage(message) {
        // Create a temporary success message
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(successDiv);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }
}

// Global functions for HTML onclick handlers
function processQRCode() {
    qrScanner.processQRCode();
}

function selectOperation(operationType) {
    qrScanner.selectOperation(operationType);
}

function calculateVariance() {
    qrScanner.calculateVariance();
}

function submitCycleCount() {
    qrScanner.submitCycleCount();
}

function submitAdjustment() {
    qrScanner.submitAdjustment();
}

function submitTransfer() {
    qrScanner.submitTransfer();
}

function cancelOperation() {
    qrScanner.cancelOperation();
}

function clearScanner() {
    qrScanner.clearScanner();
}

function hideError() {
    qrScanner.hideError();
}

function toggleCameraScanner() {
    qrScanner.toggleCameraScanner();
}

function switchCamera() {
    qrScanner.switchCamera();
}

function forceEnableCamera() {
    // Force enable camera scanner even if library didn't load properly
    const cameraBtn = document.getElementById('cameraToggleBtn');
    const forceBtn = document.getElementById('forceEnableBtn');

    if (cameraBtn) {
        cameraBtn.disabled = false;
        cameraBtn.innerHTML = '<i class="fas fa-camera me-1"></i>Start Camera Scanner';
        cameraBtn.classList.remove('btn-outline-danger', 'btn-outline-warning');
        cameraBtn.classList.add('btn-outline-primary');
    }

    if (forceBtn) {
        forceBtn.style.display = 'none';
    }

    // Initialize camera elements manually
    if (window.qrScanner) {
        window.qrScanner.initializeCameraElements();
    }

    console.log('🔄 QR SCANNER: Force enabled camera scanner');
}

function captureQRManually() {
    if (qrScanner) {
        qrScanner.captureQRManually();
    } else {
        console.error('QR Scanner not initialized');
    }
}

function handleQRImageUpload(event) {
    const file = event.target.files[0];
    if (file && qrScanner) {
        qrScanner.processQRImageUpload(file);
    } else if (!qrScanner) {
        console.error('QR Scanner not initialized');
    }

    // Clear the file input so the same file can be uploaded again if needed
    event.target.value = '';
}

function triggerFileUpload() {
    const fileInput = document.getElementById('qrImageUpload');
    if (fileInput) {
        fileInput.click();
    }
}



function toggleAdjustmentFields() {
    const adjustmentMethod = document.getElementById('adjustmentMethod').value;
    const physicalCountFields = document.getElementById('physicalCountFields');
    const balanceAdjustmentFields = document.getElementById('balanceAdjustmentFields');

    if (adjustmentMethod === 'PHYSICAL_COUNT') {
        physicalCountFields.style.display = 'block';
        balanceAdjustmentFields.style.display = 'none';
    } else {
        physicalCountFields.style.display = 'none';
        balanceAdjustmentFields.style.display = 'block';
    }
}



// Initialize scanner when page loads
let qrScanner;
document.addEventListener('DOMContentLoaded', function() {
    qrScanner = new QRInventoryScanner();
    console.log('✅ QR SCANNER: Page loaded and scanner initialized');
});
