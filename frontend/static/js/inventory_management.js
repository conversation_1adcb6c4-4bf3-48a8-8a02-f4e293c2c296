/**
 * Inventory Management JavaScript
 * 
 * Handles search, pagination, and display of inventory items
 * with responsive design and theme support.
 */

class InventoryManagement {
    constructor() {
        this.currentPage = 0;
        this.totalPages = 0;
        this.currentSearchTerm = '';
        this.currentLocation = '';
        this.currentLimit = 10;
        this.isSearching = false;
        this.searchTimeout = null;
        this.currentSiteId = null;
        this.sortColumn = null;
        this.sortDirection = 'asc';

        this.init();
        this.initializeResponsiveHandling();
    }

    init() {
        console.log('🔧 Initializing Inventory Management');

        // Load available sites first
        this.loadAvailableSites();

        // Initialize event listeners
        this.initializeEventListeners();

        // Set initial limit based on screen size
        this.setInitialLimit();

        console.log('✅ Inventory Management initialized');
    }

    async loadAvailableSites() {
        try {
            const response = await fetch('/api/enhanced-workorders/available-sites');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateSiteDropdown(result.sites, result.default_site);
                this.currentSiteId = result.default_site;
                console.log(`🏢 INVENTORY: Loaded ${result.sites.length} sites, default: ${result.default_site}`);
            } else {
                console.error('Failed to load sites:', result.error);
                this.showSiteLoadError();
            }
        } catch (error) {
            console.error('Error loading available sites:', error);
            this.showSiteLoadError();
        }
    }

    populateSiteDropdown(sites, defaultSite) {
        const siteFilter = document.getElementById('inventorySiteFilter');

        // Clear existing options
        siteFilter.innerHTML = '';

        if (sites.length === 0) {
            siteFilter.innerHTML = '<option value="">No sites available</option>';
            return;
        }

        // Add sites as options
        sites.forEach(site => {
            const option = document.createElement('option');
            option.value = site.siteid;
            option.textContent = `${site.siteid}${site.description !== site.siteid ? ' - ' + site.description : ''}`;

            // Pre-select the user's default site
            if (site.siteid === defaultSite) {
                option.selected = true;
            }

            siteFilter.appendChild(option);
        });

        console.log(`✅ Loaded ${sites.length} available sites for inventory, default: ${defaultSite}`);
    }

    showSiteLoadError() {
        const siteFilter = document.getElementById('inventorySiteFilter');
        siteFilter.innerHTML = '<option value="">Error loading sites</option>';
    }

    setInitialLimit() {
        const isMobile = window.innerWidth <= 768;
        const limitSelect = document.getElementById('inventorySearchLimit');
        
        if (isMobile) {
            limitSelect.value = '1';
            this.currentLimit = 1;
        } else {
            limitSelect.value = '10';
            this.currentLimit = 10;
        }
        
        console.log(`📱 Initial limit set to: ${this.currentLimit} (mobile: ${isMobile})`);
    }

    initializeEventListeners() {
        // Search form submission
        const searchForm = document.getElementById('inventoryManagementSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Real-time search with debouncing
        const searchInput = document.getElementById('inventorySearchTerm');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    const searchTerm = e.target.value.trim();
                    if (searchTerm.length >= 2 && searchTerm !== this.currentSearchTerm) {
                        this.performSearch();
                    } else if (searchTerm.length === 0) {
                        this.showInitialState();
                    }
                }, 500);
            });
        }

        // Limit change handler
        const limitSelect = document.getElementById('inventorySearchLimit');
        if (limitSelect) {
            limitSelect.addEventListener('change', (e) => {
                this.currentLimit = parseInt(e.target.value);
                if (this.currentSearchTerm) {
                    this.performSearch(0); // Reset to first page
                }
            });
        }

        // Site selection change
        const siteSelect = document.getElementById('inventorySiteFilter');
        if (siteSelect) {
            siteSelect.addEventListener('change', (e) => {
                this.currentSiteId = e.target.value;
                console.log(`🏢 Site changed to: ${this.currentSiteId}`);
                if (this.currentSearchTerm) {
                    this.performSearch(0); // Reset to first page
                }
            });
        }

        // Location filter change
        const locationInput = document.getElementById('inventorySearchLocation');
        if (locationInput) {
            locationInput.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    if (this.currentSearchTerm) {
                        this.performSearch(0); // Reset to first page
                    }
                }, 500);
            });
        }

        // Window resize handler for responsive behavior
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    handleResize() {
        const isMobile = window.innerWidth <= 768;
        const limitSelect = document.getElementById('inventorySearchLimit');
        
        // Auto-adjust limit for mobile if not manually changed
        if (isMobile && this.currentLimit > 1) {
            limitSelect.value = '1';
            this.currentLimit = 1;
            if (this.currentSearchTerm) {
                this.performSearch(0);
            }
        }
    }

    async performSearch(page = 0) {
        console.log('🚀 SEARCH: performSearch called with page:', page);
        const searchTerm = document.getElementById('inventorySearchTerm').value.trim();
        const siteId = document.getElementById('inventorySiteFilter').value.trim();
        const statusFilter = document.getElementById('inventoryStatusFilter').value.trim();
        console.log('🚀 SEARCH: Search parameters:', { searchTerm, siteId, statusFilter });

        if (!siteId) {
            this.showError('Please select a site first');
            return;
        }

        if (!searchTerm) {
            this.showInitialState();
            return;
        }

        if (searchTerm.length < 2) {
            this.showError('Search term must be at least 2 characters long');
            return;
        }

        if (this.isSearching) {
            return; // Prevent multiple simultaneous searches
        }

        this.isSearching = true;
        this.currentPage = page;
        this.currentSearchTerm = searchTerm;
        this.currentStatusFilter = statusFilter;
        this.currentSiteId = siteId; // Ensure site ID is always updated

        this.showLoading();

        try {
            let url = '/api/inventory/management/search';
            const params = new URLSearchParams({
                siteid: siteId,
                q: searchTerm,
                limit: this.currentLimit.toString(),
                page: page.toString()
            });

            if (statusFilter) {
                params.append('status', statusFilter);
            }

            console.log('🔍 SEARCH: Making API request to:', `${url}?${params}`);
            console.log('🔍 SEARCH: Search parameters:', { siteId, searchTerm, limit: this.currentLimit, page, statusFilter });

            const response = await fetch(`${url}?${params}`);
            console.log('🔍 SEARCH: Response status:', response.status);
            console.log('🔍 SEARCH: Response ok:', response.ok);

            const data = await response.json();
            console.log('🔍 SEARCH: Response data:', data);

            if (data.success) {
                console.log('🔍 SEARCH: Calling displayResults with', data.items?.length, 'items');
                this.displayResults(data.items);
                console.log('🔍 SEARCH: displayResults completed');

                console.log('🔍 SEARCH: Calling updatePagination');
                this.updatePagination(data.metadata);
                console.log('🔍 SEARCH: updatePagination completed');

                console.log('🔍 SEARCH: Calling updateResultsInfo');
                this.updateResultsInfo(data.metadata);
                console.log('🔍 SEARCH: updateResultsInfo completed');
            } else {
                console.error('🔍 SEARCH: API returned error:', data.error);
                this.showError(data.error || 'Search failed');
            }
        } catch (error) {
            console.error('🔍 SEARCH: Caught error:', error);
            console.error('🔍 SEARCH: Error stack:', error.stack);
            console.error('🔍 SEARCH: Error name:', error.name);
            console.error('🔍 SEARCH: Error message:', error.message);
            this.showError('Network error occurred during search');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            // Also hide the refresh loading indicator if it exists
            this.hideRefreshLoadingIndicator();
        }
    }

    displayResults(items) {
        console.log('🔍 DISPLAY: Starting displayResults with items:', items);

        try {
            const resultsContainer = document.getElementById('inventorySearchResults');
            console.log('🔍 DISPLAY: Found results container:', resultsContainer);

            if (!items || items.length === 0) {
                console.log('🔍 DISPLAY: No items to display');
                resultsContainer.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p class="mb-0">No inventory items found</p>
                        <small class="text-muted">Try adjusting your search criteria</small>
                    </div>
                `;
                return;
            }

            console.log('🔍 DISPLAY: Processing', items.length, 'items');

            // Check if mobile view should use single card navigation
            const isMobile = window.innerWidth <= 768;

            if (isMobile && items.length > 1) {
                // Store items for mobile navigation
                this.mobileInventoryItems = items;
                this.currentMobileInventoryIndex = 0;

                // Render single card view with navigation
                this.renderMobileSingleInventoryCard();
            } else {
                // Render all cards for desktop or single item
                let html = '';
                items.forEach((item, index) => {
                    try {
                        const cardHtml = this.generateItemCard(item);
                        html += cardHtml;
                        console.log(`🔍 DISPLAY: Generated card for item ${item.itemnum} successfully`);
                    } catch (cardError) {
                        console.error(`🔍 DISPLAY: Error generating card for item ${item.itemnum}:`, cardError);
                        throw cardError;
                    }
                });

                console.log('🔍 DISPLAY: Setting innerHTML with', html.length, 'characters');
                resultsContainer.innerHTML = html;
            }

            console.log('🔍 DISPLAY: displayResults completed successfully');
        } catch (error) {
            console.error('🔍 DISPLAY: Error in displayResults:', error);
            console.error('🔍 DISPLAY: Error stack:', error.stack);
            throw error;
        }
    }

    renderMobileSingleInventoryCard() {
        const resultsContainer = document.getElementById('inventorySearchResults');
        if (!resultsContainer || !this.mobileInventoryItems || this.mobileInventoryItems.length === 0) return;

        const item = this.mobileInventoryItems[this.currentMobileInventoryIndex];
        const totalItems = this.mobileInventoryItems.length;
        const currentPosition = this.currentMobileInventoryIndex + 1;

        resultsContainer.innerHTML = `
            <div class="mobile-single-inventory-container">
                <!-- Navigation Header -->
                <div class="mobile-inventory-nav-header">
                    <div class="mobile-nav-counter">
                        <span class="current-position">${currentPosition}</span>
                        <span class="separator">of</span>
                        <span class="total-items">${totalItems}</span>
                    </div>
                    <div class="mobile-nav-buttons">
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.inventoryManager.navigateMobileInventory(-1)" ${this.currentMobileInventoryIndex === 0 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.inventoryManager.navigateMobileInventory(1)" ${this.currentMobileInventoryIndex === totalItems - 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Single Inventory Card -->
                <div class="mobile-inventory-card-wrapper">
                    ${this.generateItemCard(item)}
                </div>
            </div>
        `;
    }

    navigateMobileInventory(direction) {
        if (!this.mobileInventoryItems) return;

        const newIndex = this.currentMobileInventoryIndex + direction;
        if (newIndex >= 0 && newIndex < this.mobileInventoryItems.length) {
            // Add fade transition
            const cardWrapper = document.querySelector('.mobile-inventory-card-wrapper');
            if (cardWrapper) {
                cardWrapper.classList.add('inventory-transition-fade-out');

                setTimeout(() => {
                    this.currentMobileInventoryIndex = newIndex;
                    this.renderMobileSingleInventoryCard();
                }, 200);
            } else {
                this.currentMobileInventoryIndex = newIndex;
                this.renderMobileSingleInventoryCard();
            }
        }
    }

    generateItemCard(item) {
        console.log(`🔍 CARD: Generating card for item:`, item);

        try {
            const statusClass = this.getStatusClass(item.status);
            const sourceIcon = item.source === 'direct_issue' ? 'fas fa-direct-hit' : 'fas fa-cube';
            const balanceRecords = item.invbalances_records || [];
            const hasBalanceRecords = balanceRecords.length > 0;
            // Generate unique card ID using inventoryid, itemnum, siteid, and a timestamp to ensure uniqueness
            const timestamp = Date.now();
            const inventoryId = item.inventoryid || 'no-id';
            const itemnum = (item.itemnum || 'unknown').replace(/[^a-zA-Z0-9]/g, '-');
            const siteid = (item.siteid || 'unknown').replace(/[^a-zA-Z0-9]/g, '-');
            const cardId = `inventory-card-${inventoryId}-${itemnum}-${siteid}-${timestamp}`;

            console.log(`🔍 CARD: Card variables:`, {
                statusClass,
                sourceIcon,
                hasBalanceRecords,
                cardId,
                balanceRecordsCount: balanceRecords.length
            });

            // Build the card HTML step by step to avoid template string issues
            let cardHtml = '<div class="inventory-item-card"';
            cardHtml += ` data-itemnum="${item.itemnum || ''}"`;
            cardHtml += ` data-siteid="${item.siteid || ''}"`;
            cardHtml += ` data-inventoryid="${item.inventoryid || ''}"`;
            cardHtml += '>';

            // Header section
            cardHtml += '<div class="inventory-item-header">';
            cardHtml += '<div class="inventory-item-info">';
            cardHtml += '<div class="inventory-item-icon">';
            cardHtml += `<i class="${sourceIcon}"></i>`;
            cardHtml += '</div>';
            cardHtml += '<div class="inventory-item-details">';
            cardHtml += `<h6>${item.itemnum || 'Unknown Item'}</h6>`;
            cardHtml += `<p>${item.description || 'No description'}</p>`;
            cardHtml += '</div>';
            cardHtml += '</div>';
            cardHtml += `<div class="inventory-status-badge ${statusClass}">${item.status || 'Unknown'}</div>`;
            cardHtml += '</div>';

            // Fields section
            cardHtml += '<div class="inventory-item-fields">';

            // Primary location and balance information
            if (item.location) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-map-marker-alt me-1"></i>Location';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.location}</span>`;
                cardHtml += '</div>';
            }

            if (item.curbaltotal !== undefined) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-boxes me-1"></i>Current Balance';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${this.formatNumber(item.curbaltotal)}</span>`;
                cardHtml += '</div>';
            }

            if (item.avblbalance !== undefined) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-check-circle me-1"></i>Available';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${this.formatNumber(item.avblbalance)}</span>`;
                cardHtml += '</div>';
            }

            // Additional meaningful inventory fields
            if (item.siteid) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-building me-1"></i>Site ID';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.siteid}</span>`;
                cardHtml += '</div>';
            }

            if (item.itemsetid) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-layer-group me-1"></i>Item Set';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.itemsetid}</span>`;
                cardHtml += '</div>';
            }

            if (item.issueunit) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-ruler me-1"></i>Issue Unit';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.issueunit}</span>`;
                cardHtml += '</div>';
            }

            if (item.orderunit) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-shopping-cart me-1"></i>Order Unit';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.orderunit}</span>`;
                cardHtml += '</div>';
            }

            if (item.abctype) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-sort-alpha-down me-1"></i>ABC Type';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value"><span class="badge bg-info">${item.abctype}</span></span>`;
                cardHtml += '</div>';
            }

            if (item.glaccount) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-calculator me-1"></i>GL Account';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.glaccount}</span>`;
                cardHtml += '</div>';
            }

            if (item.vendor) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-truck me-1"></i>Vendor';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.vendor}</span>`;
                cardHtml += '</div>';
            }

            if (item.manufacturer) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-industry me-1"></i>Manufacturer';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.manufacturer}</span>`;
                cardHtml += '</div>';
            }

            if (item.modelnum) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-hashtag me-1"></i>Model Number';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value">${item.modelnum}</span>`;
                cardHtml += '</div>';
            }

            if (item.lottype) {
                cardHtml += '<div class="inventory-field">';
                cardHtml += '<span class="inventory-field-label">';
                cardHtml += '<i class="fas fa-tags me-1"></i>Lot Type';
                cardHtml += '</span>';
                cardHtml += `<span class="inventory-field-value"><span class="badge bg-secondary">${item.lottype}</span></span>`;
                cardHtml += '</div>';
            }

            cardHtml += '</div>'; // Close inventory-item-fields

            // Inventory Cost Data section
            cardHtml += this.generateCostDataSection(item, cardId);

            // Balance records section
            if (hasBalanceRecords) {
                cardHtml += '<div class="balance-records-section mt-3">';
                cardHtml += '<div class="d-flex justify-content-between align-items-center">';
                cardHtml += '<h6 class="mb-0">';
                cardHtml += '<i class="fas fa-chart-bar me-1"></i>Balance Details';
                cardHtml += `<span class="badge bg-secondary">${balanceRecords.length}</span>`;
                cardHtml += '</h6>';
                cardHtml += '<button type="button" class="btn btn-outline-primary btn-sm balance-toggle-btn"';
                cardHtml += ` data-bs-toggle="collapse" data-bs-target="#${cardId}-balances"`;
                cardHtml += ` aria-expanded="false" aria-controls="${cardId}-balances"`;
                cardHtml += ` data-card-id="${cardId}" data-item-num="${item.itemnum}"`;
                cardHtml += ` onclick="event.stopPropagation(); window.inventoryManager.handleBalanceToggle(this, '${cardId}', '${item.itemnum}');">`;
                cardHtml += '<i class="fas fa-chevron-down me-1"></i>Show Balances';
                cardHtml += '</button>';
                cardHtml += '</div>';
                cardHtml += `<div class="collapse mt-2" id="${cardId}-balances">`;
                cardHtml += '<div class="balance-records-table">';
                cardHtml += this.generateBalanceRecordsTable(balanceRecords, item);
                cardHtml += '</div>';
                cardHtml += '</div>';
                cardHtml += '</div>';
            }

            // Actions section
            cardHtml += '<div class="inventory-item-actions mt-3">';

            // Desktop Layout
            cardHtml += '<div class="d-none d-lg-block">';
            cardHtml += '<div class="d-flex justify-content-between align-items-center flex-wrap gap-2">';

            if (!hasBalanceRecords) {
                // Show action buttons for items with no balance records
                cardHtml += '<div class="no-balance-actions">';
                cardHtml += '<small class="text-warning d-block mb-2">';
                cardHtml += '<i class="fas fa-exclamation-triangle me-1"></i>No inventory balances - Create initial records:';
                cardHtml += '</small>';
                cardHtml += '<div class="btn-group me-2" role="group">';

                // Physical Count button
                cardHtml += '<button type="button" class="btn btn-primary btn-sm"';
                cardHtml += ` onclick="window.inventoryManager.openNoBalancePhysicalCountModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"`;
                cardHtml += ' title="Create new inventory record with physical count">';
                cardHtml += '<i class="fas fa-clipboard-check me-1"></i>Physical Count';
                cardHtml += '</button>';

                // Current Balance button
                cardHtml += '<button type="button" class="btn btn-warning btn-sm"';
                cardHtml += ` onclick="window.inventoryManager.openNoBalanceCurrentBalanceModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"`;
                cardHtml += ' title="Create new inventory record with current balance">';
                cardHtml += '<i class="fas fa-balance-scale me-1"></i>Current Balance';
                cardHtml += '</button>';

                // QR Code button
                cardHtml += '<button type="button" class="btn btn-outline-secondary btn-sm"';
                cardHtml += ` onclick="generateQRCodeForItem('${item.itemnum}', '${item.inventoryid || ''}')"`;
                cardHtml += ` title="Generate QR Code for ${item.itemnum}">`;
                cardHtml += '<i class="fas fa-qrcode me-1"></i>QR Code';
                cardHtml += '</button>';
                cardHtml += '</div>';
                cardHtml += '</div>';
            } else {
                cardHtml += '<small class="text-muted">';
                cardHtml += '<i class="fas fa-info-circle me-1"></i>QR codes available in balance details above';
                cardHtml += '</small>';
            }

            // Cost Adjustment Buttons Section
            cardHtml += '<div class="btn-group me-2" role="group">';
            cardHtml += '<button type="button" class="btn btn-outline-warning btn-sm cost-adjustment-btn"';
            cardHtml += ` onclick="window.inventoryManager.openAvgCostModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
            cardHtml += ' title="Adjust Average Cost">';
            cardHtml += '<i class="fas fa-dollar-sign me-1"></i>Avg Cost';
            cardHtml += '</button>';
            cardHtml += '<button type="button" class="btn btn-outline-success btn-sm cost-adjustment-btn"';
            cardHtml += ` onclick="window.inventoryManager.openStdCostModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
            cardHtml += ' title="Adjust Standard Cost">';
            cardHtml += '<i class="fas fa-chart-line me-1"></i>Std Cost';
            cardHtml += '</button>';
            cardHtml += '</div>';

            // Action buttons group
            cardHtml += '<div class="btn-group" role="group">';

            // Transfer Current Item Button (only show if item has balance records)
            if (hasBalanceRecords) {
                cardHtml += '<button type="button" class="btn btn-info btn-sm transfer-btn"';
                cardHtml += ` onclick="window.inventoryManager.openTransferCurrentItemModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
                cardHtml += ' title="Transfer Current Item to Another Location">';
                cardHtml += '<i class="fas fa-exchange-alt me-1"></i>Transfer';
                cardHtml += '</button>';

                // Issue Current Item Button
                cardHtml += '<button type="button" class="btn btn-warning btn-sm issue-btn ms-1"';
                cardHtml += ` onclick="window.inventoryManager.openIssueCurrentItemModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
                cardHtml += ' title="Issue Current Item from Inventory">';
                cardHtml += '<i class="fas fa-arrow-right me-1"></i>Issue';
                cardHtml += '</button>';
            }

            // View Availability button
            cardHtml += '<button type="button" class="btn btn-success btn-sm availability-btn"';
            cardHtml += ` onclick="window.inventoryManager.openAvailabilityModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
            cardHtml += ' title="View Item Availability Data from Maximo">';
            cardHtml += '<i class="fas fa-chart-bar me-1"></i>View Availability';
            cardHtml += '</button>';

            cardHtml += '<button type="button" class="btn btn-primary btn-sm details-btn"';
            cardHtml += ` onclick="showInventoryItemDetails('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ' title="View Detailed Information">';
            cardHtml += '<i class="fas fa-info-circle me-1"></i>Details';
            cardHtml += '</button>';
            cardHtml += '</div>';
            cardHtml += '</div>';
            cardHtml += '</div>';

            // Mobile Layout
            cardHtml += '<div class="d-lg-none">';

            if (!hasBalanceRecords) {
                // Show action buttons for items with no balance records
                cardHtml += '<div class="no-balance-actions mb-3">';
                cardHtml += '<small class="text-warning d-block mb-2">';
                cardHtml += '<i class="fas fa-exclamation-triangle me-1"></i>No inventory balances - Create initial records:';
                cardHtml += '</small>';

                // Primary actions row
                cardHtml += '<div class="row g-2 mb-2">';
                cardHtml += '<div class="col-6">';
                cardHtml += '<button type="button" class="btn btn-primary btn-sm w-100"';
                cardHtml += ` onclick="window.inventoryManager.openNoBalancePhysicalCountModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"`;
                cardHtml += ' title="Create new inventory record with physical count">';
                cardHtml += '<i class="fas fa-clipboard-check me-1"></i>Physical Count';
                cardHtml += '</button>';
                cardHtml += '</div>';
                cardHtml += '<div class="col-6">';
                cardHtml += '<button type="button" class="btn btn-warning btn-sm w-100"';
                cardHtml += ` onclick="window.inventoryManager.openNoBalanceCurrentBalanceModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"`;
                cardHtml += ' title="Create new inventory record with current balance">';
                cardHtml += '<i class="fas fa-balance-scale me-1"></i>Current Balance';
                cardHtml += '</button>';
                cardHtml += '</div>';
                cardHtml += '</div>';

                // QR Code button
                cardHtml += '<div class="mb-2">';
                cardHtml += '<button type="button" class="btn btn-outline-secondary btn-sm w-100"';
                cardHtml += ` onclick="generateQRCodeForItem('${item.itemnum}', '${item.inventoryid || ''}')"`;
                cardHtml += ` title="Generate QR Code for ${item.itemnum}">`;
                cardHtml += '<i class="fas fa-qrcode me-1"></i>Generate QR Code';
                cardHtml += '</button>';
                cardHtml += '</div>';
                cardHtml += '</div>';
            } else {
                cardHtml += '<div class="mb-3">';
                cardHtml += '<small class="text-muted">';
                cardHtml += '<i class="fas fa-info-circle me-1"></i>QR codes available in balance details above';
                cardHtml += '</small>';
                cardHtml += '</div>';
            }

            // Cost Adjustment Buttons Section
            cardHtml += '<div class="row g-2 mb-2">';
            cardHtml += '<div class="col-6">';
            cardHtml += '<button type="button" class="btn btn-outline-warning btn-sm w-100 cost-adjustment-btn"';
            cardHtml += ` onclick="window.inventoryManager.openAvgCostModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
            cardHtml += ' title="Adjust Average Cost">';
            cardHtml += '<i class="fas fa-dollar-sign me-1"></i>Avg Cost';
            cardHtml += '</button>';
            cardHtml += '</div>';
            cardHtml += '<div class="col-6">';
            cardHtml += '<button type="button" class="btn btn-outline-success btn-sm w-100 cost-adjustment-btn"';
            cardHtml += ` onclick="window.inventoryManager.openStdCostModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
            cardHtml += ' title="Adjust Standard Cost">';
            cardHtml += '<i class="fas fa-chart-line me-1"></i>Std Cost';
            cardHtml += '</button>';
            cardHtml += '</div>';
            cardHtml += '</div>';

            // Main action buttons
            cardHtml += '<div class="row g-2">';

            // Transfer Current Item Button (only show if item has balance records)
            if (hasBalanceRecords) {
                cardHtml += '<div class="col-6 mb-2">';
                cardHtml += '<button type="button" class="btn btn-info btn-sm w-100 transfer-btn"';
                cardHtml += ` onclick="window.inventoryManager.openTransferCurrentItemModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
                cardHtml += ' title="Transfer Current Item to Another Location">';
                cardHtml += '<i class="fas fa-exchange-alt me-1"></i>Transfer';
                cardHtml += '</button>';
                cardHtml += '</div>';

                // Issue Current Item Button
                cardHtml += '<div class="col-6 mb-2">';
                cardHtml += '<button type="button" class="btn btn-warning btn-sm w-100 issue-btn"';
                cardHtml += ` onclick="window.inventoryManager.openIssueCurrentItemModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
                cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
                cardHtml += ' title="Issue Current Item from Inventory">';
                cardHtml += '<i class="fas fa-arrow-right me-1"></i>Issue';
                cardHtml += '</button>';
                cardHtml += '</div>';
            }

            // View Availability button
            cardHtml += '<div class="col-6">';
            cardHtml += '<button type="button" class="btn btn-success btn-sm w-100 availability-btn"';
            cardHtml += ` onclick="window.inventoryManager.openAvailabilityModal('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ` data-item-data="${this.escapeForAttribute(JSON.stringify(item))}"`;
            cardHtml += ' title="View Item Availability Data from Maximo">';
            cardHtml += '<i class="fas fa-chart-bar me-1"></i>Availability';
            cardHtml += '</button>';
            cardHtml += '</div>';

            cardHtml += '<div class="col-6">';
            cardHtml += '<button type="button" class="btn btn-primary btn-sm w-100 details-btn"';
            cardHtml += ` onclick="showInventoryItemDetails('${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"`;
            cardHtml += ' title="View Detailed Information">';
            cardHtml += '<i class="fas fa-info-circle me-1"></i>Details';
            cardHtml += '</button>';
            cardHtml += '</div>';
            cardHtml += '</div>';
            cardHtml += '</div>';

            cardHtml += '</div>';

            cardHtml += '</div>'; // Close inventory-item-card

            console.log(`🔍 CARD: Successfully generated card for item ${item.itemnum}`);
            return cardHtml;

        } catch (error) {
            console.error(`🔍 CARD: Error generating card for item ${item.itemnum || 'unknown'}:`, error);
            console.error(`🔍 CARD: Error stack:`, error.stack);
            console.error(`🔍 CARD: Item data:`, item);
            return `<div class="inventory-item-card error">
                <h6>Error loading item: ${item.itemnum || 'Unknown'}</h6>
                <p class="text-danger">Failed to generate card</p>
            </div>`;
        }
    }

    generateCostDataSection(item, cardId) {
        console.log(`🔍 COST DATA: Generating cost section for item ${item.itemnum}`);

        // Extract cost-related fields from the item data
        const costFields = this.extractCostFields(item);

        // If no cost data is available, don't show the section
        if (Object.keys(costFields).length === 0) {
            console.log(`🔍 COST DATA: No cost data available for item ${item.itemnum}`);
            return '';
        }

        let costHtml = '<div class="cost-data-section mt-3">';
        costHtml += '<div class="d-flex justify-content-between align-items-center">';
        costHtml += '<h6 class="mb-0">';
        costHtml += '<i class="fas fa-dollar-sign me-1"></i>Inventory Cost Data';
        costHtml += `<span class="badge bg-info">${Object.keys(costFields).length}</span>`;
        costHtml += '</h6>';
        costHtml += '<button type="button" class="btn btn-outline-info btn-sm cost-toggle-btn"';
        costHtml += ` data-bs-toggle="collapse" data-bs-target="#${cardId}-costs"`;
        costHtml += ` aria-expanded="false" aria-controls="${cardId}-costs"`;
        costHtml += ` data-card-id="${cardId}" data-item-num="${item.itemnum}"`;
        costHtml += ` onclick="event.stopPropagation(); window.inventoryManager.handleCostToggle(this, '${cardId}', '${item.itemnum}');">`;
        costHtml += '<i class="fas fa-chevron-down me-1"></i>Show Costs';
        costHtml += '</button>';
        costHtml += '</div>';
        costHtml += `<div class="collapse mt-2" id="${cardId}-costs">`;
        costHtml += '<div class="cost-data-table">';
        costHtml += this.generateCostDataTable(costFields);
        costHtml += '</div>';
        costHtml += '</div>';
        costHtml += '</div>';

        console.log(`🔍 COST DATA: Successfully generated cost section for item ${item.itemnum}`);
        return costHtml;
    }

    extractCostFields(item) {
        console.log(`🔍 COST DATA: Extracting cost fields from item data`);

        const costFields = {};

        // Define cost-related fields with their display names and formatting
        const costFieldDefinitions = {
            'avgcost': { label: 'Average Cost', type: 'currency', icon: 'fas fa-chart-line' },
            'stdcost': { label: 'Standard Cost', type: 'currency', icon: 'fas fa-calculator' },
            'lastcost': { label: 'Last Cost', type: 'currency', icon: 'fas fa-history' },
            'unitcost': { label: 'Unit Cost', type: 'currency', icon: 'fas fa-tag' },
            'conditioncode': { label: 'Condition Code', type: 'text', icon: 'fas fa-check-circle' },
            'orgid': { label: 'Organization ID', type: 'text', icon: 'fas fa-building' },
            'invcostid': { label: 'Cost Record ID', type: 'number', icon: 'fas fa-hashtag' },
            'condrate': { label: 'Condition Rate', type: 'percentage', icon: 'fas fa-percent' },
            'conditionrate': { label: 'Condition Rate', type: 'percentage', icon: 'fas fa-percent' }
        };

        // Extract available cost fields from the item
        for (const [fieldName, definition] of Object.entries(costFieldDefinitions)) {
            const value = item[fieldName];
            if (value !== undefined && value !== null && value !== '') {
                costFields[fieldName] = {
                    label: definition.label,
                    value: value,
                    type: definition.type,
                    icon: definition.icon
                };
            }
        }

        console.log(`🔍 COST DATA: Extracted ${Object.keys(costFields).length} cost fields:`, Object.keys(costFields));
        return costFields;
    }

    generateCostDataTable(costFields) {
        if (!costFields || Object.keys(costFields).length === 0) {
            return '<div class="text-muted small">No cost data available</div>';
        }

        // Check if mobile view
        const isMobile = window.innerWidth < 768;

        if (isMobile) {
            return this.generateMobileCostDataView(costFields);
        }

        // Desktop table view
        let tableHtml = '<div class="table-responsive">';
        tableHtml += '<table class="table table-sm table-hover mb-0">';
        tableHtml += '<thead class="table-light">';
        tableHtml += '<tr>';
        tableHtml += '<th><i class="fas fa-tag me-1"></i>Cost Field</th>';
        tableHtml += '<th class="text-end"><i class="fas fa-dollar-sign me-1"></i>Value</th>';
        tableHtml += '</tr>';
        tableHtml += '</thead>';
        tableHtml += '<tbody>';

        // Sort cost fields for consistent display
        const sortedFields = Object.entries(costFields).sort(([a], [b]) => {
            const order = ['avgcost', 'stdcost', 'lastcost', 'unitcost', 'conditioncode', 'orgid', 'invcostid', 'condrate'];
            return order.indexOf(a) - order.indexOf(b);
        });

        sortedFields.forEach(([fieldName, fieldData]) => {
            tableHtml += '<tr>';
            tableHtml += '<td>';
            tableHtml += `<i class="${fieldData.icon} me-2 text-muted"></i>`;
            tableHtml += `<strong>${fieldData.label}</strong>`;
            tableHtml += '</td>';
            tableHtml += '<td class="text-end">';
            tableHtml += `<span class="cost-value">${this.formatCostValue(fieldData.value, fieldData.type)}</span>`;
            tableHtml += '</td>';
            tableHtml += '</tr>';
        });

        tableHtml += '</tbody>';
        tableHtml += '</table>';
        tableHtml += '</div>';

        return tableHtml;
    }

    generateMobileCostDataView(costFields) {
        let mobileHtml = '<div class="mobile-cost-view">';

        // Sort cost fields for consistent display
        const sortedFields = Object.entries(costFields).sort(([a], [b]) => {
            const order = ['avgcost', 'stdcost', 'lastcost', 'unitcost', 'conditioncode', 'orgid', 'invcostid', 'condrate'];
            return order.indexOf(a) - order.indexOf(b);
        });

        sortedFields.forEach(([fieldName, fieldData]) => {
            mobileHtml += '<div class="mobile-cost-item">';
            mobileHtml += '<div class="d-flex justify-content-between align-items-center">';
            mobileHtml += '<div class="mobile-cost-label">';
            mobileHtml += `<i class="${fieldData.icon} me-2 text-muted"></i>`;
            mobileHtml += `<strong>${fieldData.label}</strong>`;
            mobileHtml += '</div>';
            mobileHtml += '<div class="mobile-cost-value">';
            mobileHtml += `<span class="cost-value">${this.formatCostValue(fieldData.value, fieldData.type)}</span>`;
            mobileHtml += '</div>';
            mobileHtml += '</div>';
            mobileHtml += '</div>';
        });

        mobileHtml += '</div>';
        return mobileHtml;
    }

    formatCostValue(value, type) {
        if (value === null || value === undefined || value === '') {
            return '<span class="text-muted">-</span>';
        }

        switch (type) {
            case 'currency':
                const num = parseFloat(value);
                if (isNaN(num)) return '<span class="text-muted">-</span>';
                return `<span class="text-success fw-bold">$${num.toFixed(2)}</span>`;

            case 'percentage':
                const percent = parseFloat(value);
                if (isNaN(percent)) return '<span class="text-muted">-</span>';
                // Format percentage without unnecessary decimals
                const formattedPercent = percent % 1 === 0 ? percent.toFixed(0) : percent.toFixed(2);
                return `<span class="text-info">${formattedPercent}%</span>`;

            case 'number':
                const numVal = parseFloat(value);
                if (isNaN(numVal)) return value;
                return `<span class="text-primary">${numVal.toLocaleString()}</span>`;

            case 'text':
            default:
                return `<span class="badge bg-secondary">${value}</span>`;
        }
    }

    handleCostToggle(button, cardId, itemnum) {
        console.log(`🔍 COST DATA: Toggling cost section for item ${itemnum}, card ${cardId}`);

        // Verify the target element exists
        const targetElement = document.getElementById(`${cardId}-costs`);
        if (!targetElement) {
            console.error(`❌ COST DATA: Target element ${cardId}-costs not found`);
            return;
        }

        // Check current state before any changes
        const isCurrentlyExpanded = targetElement.classList.contains('show');
        console.log(`🔍 COST DATA: Current state - expanded: ${isCurrentlyExpanded}`);

        // Get or create Bootstrap Collapse instance
        let bsCollapse = bootstrap.Collapse.getInstance(targetElement);
        if (!bsCollapse) {
            bsCollapse = new bootstrap.Collapse(targetElement, {
                toggle: false
            });
        }

        // Toggle the collapse state
        if (isCurrentlyExpanded) {
            console.log(`🔍 COST DATA: Hiding costs for ${itemnum}`);
            bsCollapse.hide();
        } else {
            console.log(`🔍 COST DATA: Showing costs for ${itemnum}`);
            bsCollapse.show();
        }

        // Update button appearance immediately (will be corrected by event listeners if needed)
        this.updateCostToggleButton(button, !isCurrentlyExpanded);

        console.log(`✅ COST DATA: Successfully initiated toggle to ${!isCurrentlyExpanded ? 'expanded' : 'collapsed'} state for ${itemnum}`);
    }

    updateCostToggleButton(button, isExpanded) {
        // Update chevron icon and button text
        const iconClass = isExpanded ? 'fa-chevron-up' : 'fa-chevron-down';
        const buttonText = isExpanded ? 'Hide' : 'Show';

        button.innerHTML = `<i class="fas ${iconClass} me-1"></i>${buttonText} Costs`;
        button.setAttribute('aria-expanded', isExpanded.toString());

        console.log(`🔍 COST DATA: Updated button text to "${buttonText} Costs" (expanded: ${isExpanded})`);
    }

    generateBalanceRecordsTable(balanceRecords, item) {
        if (!balanceRecords || balanceRecords.length === 0) {
            return '<div class="text-muted small">No balance records available</div>';
        }

        // Check if mobile view
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            return this.generateMobileBalanceView(balanceRecords, item);
        } else {
            return this.generateDesktopBalanceTable(balanceRecords, item);
        }
    }

    generateDesktopBalanceTable(balanceRecords, item) {
        let tableHtml = `
            <div class="table-responsive">
                <table class="table table-sm table-striped balance-records-table">
                    <thead class="table-dark">
                        <tr>
                            <th class="sortable" data-sort="binnum">
                                <i class="fas fa-archive me-1"></i>Bin
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="lotnum">
                                <i class="fas fa-barcode me-1"></i>Lot
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="conditioncode">
                                <i class="fas fa-tag me-1"></i>Condition Code
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable text-end" data-sort="curbal">
                                <i class="fas fa-boxes me-1"></i>Current Balance
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable text-end" data-sort="stagedcurbal">
                                <i class="fas fa-layer-group me-1"></i>Staged Balance
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="stagingbin">
                                <i class="fas fa-warehouse me-1"></i>Staging Bin?
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable text-end" data-sort="physcnt">
                                <i class="fas fa-clipboard-check me-1"></i>Physical Count
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="physcntdate">
                                <i class="fas fa-calendar me-1"></i>Physical Count Date
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable text-center" data-sort="reconciled">
                                <i class="fas fa-check-circle me-1"></i>Reconciled?
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable text-end" data-sort="shelflife">
                                <i class="fas fa-hourglass-half me-1"></i>Shelf Life (Days)
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="expirationdate">
                                <i class="fas fa-exclamation-triangle me-1"></i>Expiration Date
                                <i class="fas fa-sort sort-icon"></i>
                            </th>
                            <th class="text-center">
                                <i class="fas fa-qrcode me-1"></i>QR Code
                            </th>
                            <th class="text-center">
                                <i class="fas fa-edit me-1"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        balanceRecords.forEach((balance, index) => {
            const expirationDate = this.calculateExpirationDate(balance);
            const shelfLifeDays = this.calculateShelfLifeDays(balance);

            tableHtml += `
                <tr class="balance-record-row" data-index="${index}">
                    <td><strong>${balance.binnum || '-'}</strong></td>
                    <td>${balance.lotnum || '-'}</td>
                    <td>
                        ${balance.conditioncode ? `<span class="badge bg-secondary">${balance.conditioncode}</span>` : '-'}
                    </td>
                    <td class="text-end">
                        <strong class="text-primary">${this.formatNumber(balance.curbal)}</strong>
                    </td>
                    <td class="text-end">
                        ${balance.stagedcurbal ? `<span class="text-warning">${this.formatNumber(balance.stagedcurbal)}</span>` : '-'}
                    </td>
                    <td>
                        ${balance.stagingbin ? `<span class="badge bg-info">${balance.stagingbin}</span>` : '-'}
                    </td>
                    <td class="text-end">
                        ${this.formatNumber(balance.physcnt) || '-'}
                    </td>
                    <td>
                        ${this.formatDate(balance.physcntdate) || '-'}
                    </td>
                    <td class="text-center">
                        ${balance.reconciled ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}
                    </td>
                    <td class="text-end">
                        ${shelfLifeDays ? `<span class="badge ${this.getShelfLifeBadgeClass(shelfLifeDays)}">${shelfLifeDays}</span>` : '-'}
                    </td>
                    <td>
                        ${expirationDate ? `<span class="text-${this.getExpirationClass(expirationDate)}">${this.formatDate(expirationDate)}</span>` : '-'}
                    </td>
                    <td class="text-center">
                        <button type="button"
                                class="btn btn-success btn-sm balance-qr-btn"
                                style="color: white; background-color: #28a745; border-color: #28a745;"
                                onclick="window.inventoryManager.generateBalanceQRCode('${balance.invbalancesid || index}', '${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"
                                data-balance-data="${this.escapeForAttribute(JSON.stringify(balance))}"
                                data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid}))}"
                                title="Generate QR Code for Bin: ${balance.binnum || 'No Bin'}, Lot: ${balance.lotnum || 'No Lot'}">
                            <i class="fas fa-qrcode" style="color: white;"></i>
                        </button>
                    </td>
                    <td class="text-center">
                        <div class="btn-group-vertical" role="group" style="min-width: 90px;">
                            <button type="button"
                                    class="btn btn-primary btn-xs mb-1"
                                    style="font-size: 0.65rem; padding: 0.15rem 0.3rem; line-height: 1.2;"
                                    onclick="window.inventoryManager.openPhysicalCountModal('${balance.invbalancesid || index}', '${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"
                                    data-balance-data="${this.escapeForAttribute(JSON.stringify(balance))}"
                                    data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"
                                    title="Physical Count Adjustment for Bin: ${balance.binnum || 'No Bin'}">
                                <i class="fas fa-clipboard-check" style="font-size: 0.6rem;"></i><br><span style="font-size: 0.55rem;">Physical</span>
                            </button>
                            <button type="button"
                                    class="btn btn-warning btn-xs"
                                    style="font-size: 0.65rem; padding: 0.15rem 0.3rem; line-height: 1.2;"
                                    onclick="window.inventoryManager.openCurrentBalanceModal('${balance.invbalancesid || index}', '${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"
                                    data-balance-data="${this.escapeForAttribute(JSON.stringify(balance))}"
                                    data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"
                                    title="Current Balance Adjustment for Bin: ${balance.binnum || 'No Bin'}">
                                <i class="fas fa-balance-scale" style="font-size: 0.6rem;"></i><br><span style="font-size: 0.55rem;">Balance</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHtml += `
                    </tbody>
                </table>
            </div>
        `;

        return tableHtml;
    }

    generateMobileBalanceView(balanceRecords, item) {
        let mobileHtml = '<div class="mobile-balance-records">';

        balanceRecords.forEach((balance, index) => {
            const expirationDate = this.calculateExpirationDate(balance);
            const shelfLifeDays = this.calculateShelfLifeDays(balance);

            mobileHtml += `
                <div class="mobile-balance-card mb-3" data-index="${index}">
                    <div class="mobile-balance-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-archive me-1"></i>
                                Bin: <strong>${balance.binnum || 'N/A'}</strong>
                            </h6>
                            <span class="badge bg-primary">${this.formatNumber(balance.curbal)}</span>
                        </div>
                        ${balance.lotnum ? `<small class="text-muted"><i class="fas fa-barcode me-1"></i>Lot: ${balance.lotnum}</small>` : ''}
                    </div>

                    <div class="mobile-balance-details mt-2">
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-tag me-1"></i>Condition</small>
                                    <div class="field-value">
                                        ${balance.conditioncode ? `<span class="badge bg-secondary">${balance.conditioncode}</span>` : '-'}
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-layer-group me-1"></i>Staged</small>
                                    <div class="field-value">
                                        ${balance.stagedcurbal ? `<span class="text-warning">${this.formatNumber(balance.stagedcurbal)}</span>` : '-'}
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-warehouse me-1"></i>Staging Bin</small>
                                    <div class="field-value">
                                        ${balance.stagingbin ? `<span class="badge bg-info">${balance.stagingbin}</span>` : '-'}
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-clipboard-check me-1"></i>Physical Count</small>
                                    <div class="field-value">${this.formatNumber(balance.physcnt) || '-'}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-calendar me-1"></i>Count Date</small>
                                    <div class="field-value">${this.formatDate(balance.physcntdate) || '-'}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-check-circle me-1"></i>Reconciled</small>
                                    <div class="field-value">
                                        ${balance.reconciled ? '<i class="fas fa-check text-success"></i> Yes' : '<i class="fas fa-times text-danger"></i> No'}
                                    </div>
                                </div>
                            </div>
                            ${shelfLifeDays ? `
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-hourglass-half me-1"></i>Shelf Life</small>
                                    <div class="field-value">
                                        <span class="badge ${this.getShelfLifeBadgeClass(shelfLifeDays)}">${shelfLifeDays} days</span>
                                    </div>
                                </div>
                            </div>
                            ` : ''}
                            ${expirationDate ? `
                            <div class="col-6">
                                <div class="mobile-field">
                                    <small class="field-label"><i class="fas fa-exclamation-triangle me-1"></i>Expires</small>
                                    <div class="field-value">
                                        <span class="text-${this.getExpirationClass(expirationDate)}">${this.formatDate(expirationDate)}</span>
                                    </div>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        <!-- Action Buttons for Mobile -->
                        <div class="mt-3">
                            <div class="row g-2">
                                <div class="col-12">
                                    <button type="button"
                                            class="btn btn-success btn-sm w-100 balance-qr-btn"
                                            style="color: white; background-color: #28a745; border-color: #28a745;"
                                            onclick="window.inventoryManager.generateBalanceQRCode('${balance.invbalancesid || index}', '${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"
                                            data-balance-data="${this.escapeForAttribute(JSON.stringify(balance))}"
                                            data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid}))}"
                                            title="Generate QR Code for Bin: ${balance.binnum || 'No Bin'}, Lot: ${balance.lotnum || 'No Lot'}">
                                        <i class="fas fa-qrcode me-1" style="color: white;"></i>Generate QR Code
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button"
                                            class="btn btn-primary btn-sm w-100"
                                            style="font-size: 0.75rem; padding: 0.375rem 0.5rem;"
                                            onclick="window.inventoryManager.openPhysicalCountModal('${balance.invbalancesid || index}', '${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"
                                            data-balance-data="${this.escapeForAttribute(JSON.stringify(balance))}"
                                            data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"
                                            title="Physical Count Adjustment for Bin: ${balance.binnum || 'No Bin'}">
                                        <i class="fas fa-clipboard-check me-1"></i>Physical
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button"
                                            class="btn btn-warning btn-sm w-100"
                                            style="font-size: 0.75rem; padding: 0.375rem 0.5rem;"
                                            onclick="window.inventoryManager.openCurrentBalanceModal('${balance.invbalancesid || index}', '${item.itemnum}', '${item.siteid}', '${item.inventoryid || ''}')"
                                            data-balance-data="${this.escapeForAttribute(JSON.stringify(balance))}"
                                            data-item-data="${this.escapeForAttribute(JSON.stringify({itemnum: item.itemnum, siteid: item.siteid, inventoryid: item.inventoryid, itemsetid: item.itemsetid, location: item.location}))}"
                                            title="Current Balance Adjustment for Bin: ${balance.binnum || 'No Bin'}">
                                        <i class="fas fa-balance-scale me-1"></i>Balance
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        mobileHtml += '</div>';
        return mobileHtml;
    }

    escapeForAttribute(str) {
        if (!str) return '';
        return String(str)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    calculateExpirationDate(balance) {
        // This would need to be calculated based on lot creation date + shelf life
        // For now, return the expiration date if available in the data
        return balance.expirationdate || null;
    }

    calculateShelfLifeDays(balance) {
        // This would need to be calculated based on item specifications
        // For now, return shelf life if available in the data
        return balance.shelflife || balance.shelflifedays || null;
    }

    getShelfLifeBadgeClass(days) {
        if (days <= 7) return 'bg-danger';
        if (days <= 30) return 'bg-warning';
        if (days <= 90) return 'bg-info';
        return 'bg-success';
    }

    getExpirationClass(expirationDate) {
        const today = new Date();
        const expDate = new Date(expirationDate);
        const daysUntilExpiration = Math.ceil((expDate - today) / (1000 * 60 * 60 * 24));

        if (daysUntilExpiration < 0) return 'danger'; // Expired
        if (daysUntilExpiration <= 7) return 'warning'; // Expires soon
        if (daysUntilExpiration <= 30) return 'info'; // Expires within a month
        return 'success'; // Good expiration date
    }

    initializeResponsiveHandling() {
        // Handle window resize for responsive balance tables and mobile view switching
        window.addEventListener('resize', () => {
            this.handleResponsiveBalanceTables();
            this.handleMobileViewSwitch();
        });

        // Initialize sorting for balance tables
        document.addEventListener('click', (e) => {
            if (e.target.closest('.sortable')) {
                this.handleBalanceTableSort(e.target.closest('.sortable'));
            }
        });

        // Initialize balance toggle event handlers to ensure proper isolation
        this.initializeBalanceToggleHandlers();

        // Initialize cost toggle event handlers
        this.initializeCostToggleHandlers();
    }

    handleMobileViewSwitch() {
        // If we have stored mobile inventory items and the view has changed, re-render
        if (this.mobileInventoryItems && this.mobileInventoryItems.length > 0) {
            const isMobile = window.innerWidth <= 768;
            const resultsContainer = document.getElementById('inventorySearchResults');
            const hasMobileNav = resultsContainer && resultsContainer.querySelector('.mobile-inventory-nav-header');

            // If we're on mobile but don't have mobile nav, or vice versa, re-render
            if ((isMobile && !hasMobileNav) || (!isMobile && hasMobileNav)) {
                this.displayResults(this.mobileInventoryItems);
            }
        }
    }

    initializeBalanceToggleHandlers() {
        // Remove any existing event listeners and add new ones
        document.addEventListener('click', (e) => {
            const toggleBtn = e.target.closest('.balance-toggle-btn');
            if (toggleBtn) {
                e.stopPropagation();
                e.preventDefault();

                const cardId = toggleBtn.getAttribute('data-card-id');
                const itemNum = toggleBtn.getAttribute('data-item-num');

                if (cardId && itemNum) {
                    console.log(`🔍 BALANCE TOGGLE EVENT: Handling toggle for card ${cardId}, item ${itemNum}`);
                    this.handleBalanceToggle(toggleBtn, cardId, itemNum);
                } else {
                    console.error(`❌ BALANCE TOGGLE EVENT: Missing data attributes on button`);
                }
            }
        });

        // Listen for Bootstrap collapse events to keep button state in sync
        document.addEventListener('shown.bs.collapse', (e) => {
            if (e.target.id.includes('-balances')) {
                const cardId = e.target.id.replace('-balances', '');
                const button = document.querySelector(`[data-card-id="${cardId}"]`);
                if (button) {
                    this.updateBalanceToggleButton(button, true);
                }
            }
        });

        document.addEventListener('hidden.bs.collapse', (e) => {
            if (e.target.id.includes('-balances')) {
                const cardId = e.target.id.replace('-balances', '');
                const button = document.querySelector(`[data-card-id="${cardId}"]`);
                if (button) {
                    this.updateBalanceToggleButton(button, false);
                }
            }
        });
    }

    initializeCostToggleHandlers() {
        // Remove any existing event listeners and add new ones
        document.addEventListener('click', (e) => {
            const toggleBtn = e.target.closest('.cost-toggle-btn');
            if (toggleBtn) {
                e.stopPropagation();
                e.preventDefault();

                const cardId = toggleBtn.getAttribute('data-card-id');
                const itemNum = toggleBtn.getAttribute('data-item-num');

                if (cardId && itemNum) {
                    console.log(`🔍 COST TOGGLE EVENT: Handling toggle for card ${cardId}, item ${itemNum}`);
                    this.handleCostToggle(toggleBtn, cardId, itemNum);
                } else {
                    console.error(`❌ COST TOGGLE EVENT: Missing data attributes on button`);
                }
            }
        });

        // Listen for Bootstrap collapse events to keep button state in sync
        document.addEventListener('shown.bs.collapse', (e) => {
            if (e.target.id.includes('-costs')) {
                const cardId = e.target.id.replace('-costs', '');
                const button = document.querySelector(`[data-card-id="${cardId}"].cost-toggle-btn`);
                if (button) {
                    this.updateCostToggleButton(button, true);
                }
            }
        });

        document.addEventListener('hidden.bs.collapse', (e) => {
            if (e.target.id.includes('-costs')) {
                const cardId = e.target.id.replace('-costs', '');
                const button = document.querySelector(`[data-card-id="${cardId}"].cost-toggle-btn`);
                if (button) {
                    this.updateCostToggleButton(button, false);
                }
            }
        });
    }

    handleResponsiveBalanceTables() {
        // Re-render balance tables if they exist and window size changed significantly
        const balanceTables = document.querySelectorAll('.balance-records-table, .mobile-balance-records');
        if (balanceTables.length > 0) {
            // Debounce the re-render to avoid excessive calls
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                // You could re-render the tables here if needed
            }, 250);
        }
    }

    handleBalanceTableSort(headerElement) {
        const sortColumn = headerElement.dataset.sort;
        const table = headerElement.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        // Toggle sort direction
        if (this.sortColumn === sortColumn) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = sortColumn;
            this.sortDirection = 'asc';
        }

        // Update sort icons
        table.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
        });

        const currentIcon = headerElement.querySelector('.sort-icon');
        currentIcon.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'} sort-icon`;

        // Sort rows
        rows.sort((a, b) => {
            const aValue = this.getSortValue(a, sortColumn);
            const bValue = this.getSortValue(b, sortColumn);

            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;

            return this.sortDirection === 'asc' ? comparison : -comparison;
        });

        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    }

    getSortValue(row, column) {
        const index = row.dataset.index;
        const balanceRecords = this.currentBalanceRecords || [];
        const record = balanceRecords[index];

        if (!record) return '';

        switch (column) {
            case 'binnum':
                return record.binnum || '';
            case 'lotnum':
                return record.lotnum || '';
            case 'conditioncode':
                return record.conditioncode || '';
            case 'curbal':
                return parseFloat(record.curbal) || 0;
            case 'stagedcurbal':
                return parseFloat(record.stagedcurbal) || 0;
            case 'stagingbin':
                return record.stagingbin || '';
            case 'physcnt':
                return parseFloat(record.physcnt) || 0;
            case 'physcntdate':
                return new Date(record.physcntdate || 0);
            case 'reconciled':
                return record.reconciled ? 1 : 0;
            case 'shelflife':
                return parseInt(this.calculateShelfLifeDays(record)) || 0;
            case 'expirationdate':
                return new Date(this.calculateExpirationDate(record) || 0);
            default:
                return '';
        }
    }

    handleBalanceToggle(button, cardId, itemnum) {
        console.log(`🔍 BALANCE TOGGLE: Toggling balances for card ${cardId}, item ${itemnum}`);

        // Verify the target element exists
        const targetElement = document.getElementById(`${cardId}-balances`);
        if (!targetElement) {
            console.error(`❌ BALANCE TOGGLE: Target element ${cardId}-balances not found`);
            return;
        }

        // Check current state before any changes
        const isCurrentlyExpanded = targetElement.classList.contains('show');
        console.log(`🔍 BALANCE TOGGLE: Current state - expanded: ${isCurrentlyExpanded}`);

        // Get or create Bootstrap Collapse instance
        let bsCollapse = bootstrap.Collapse.getInstance(targetElement);
        if (!bsCollapse) {
            bsCollapse = new bootstrap.Collapse(targetElement, {
                toggle: false
            });
        }

        // Toggle the collapse state
        if (isCurrentlyExpanded) {
            console.log(`🔍 BALANCE TOGGLE: Hiding balances for ${itemnum}`);
            bsCollapse.hide();
        } else {
            console.log(`🔍 BALANCE TOGGLE: Showing balances for ${itemnum}`);
            bsCollapse.show();
        }

        // Update button appearance immediately (will be corrected by event listeners if needed)
        this.updateBalanceToggleButton(button, !isCurrentlyExpanded);

        console.log(`✅ BALANCE TOGGLE: Successfully initiated toggle to ${!isCurrentlyExpanded ? 'expanded' : 'collapsed'} state for ${itemnum}`);
    }

    updateBalanceToggleButton(button, isExpanded) {
        // Update chevron icon and button text
        const iconClass = isExpanded ? 'fa-chevron-up' : 'fa-chevron-down';
        const buttonText = isExpanded ? 'Hide' : 'Show';

        button.innerHTML = `<i class="fas ${iconClass} me-1"></i>${buttonText} Balances`;

        // Update aria-expanded attribute
        button.setAttribute('aria-expanded', isExpanded.toString());
    }

    generateBalanceQRCode(balanceId, itemnum, siteid, inventoryid) {
        console.log(`🔍 BALANCE QR: Generating QR code for balance ID ${balanceId}, Item ${itemnum}, Site ${siteid}, Inventory ${inventoryid}`);

        // Get the balance data from the button's data attribute
        const button = event.target.closest('.balance-qr-btn');
        if (!button) {
            console.error('❌ BALANCE QR: Could not find QR button element');
            alert('Error: Could not find QR button element');
            return;
        }

        let balanceRecord, itemData;
        try {
            const balanceDataAttr = button.getAttribute('data-balance-data');
            const itemDataAttr = button.getAttribute('data-item-data');

            if (!balanceDataAttr) {
                console.error('❌ BALANCE QR: No balance data found on button');
                alert('Error: No balance data found on button');
                return;
            }

            balanceRecord = JSON.parse(balanceDataAttr);
            itemData = itemDataAttr ? JSON.parse(itemDataAttr) : {itemnum, siteid, inventoryid};

            console.log('🔍 BALANCE QR: Balance record:', balanceRecord);
            console.log('🔍 BALANCE QR: Item data:', itemData);

            // Use item data from button if available, otherwise use parameters
            itemnum = itemData.itemnum || itemnum;
            siteid = itemData.siteid || siteid;
            inventoryid = itemData.inventoryid || inventoryid;

        } catch (error) {
            console.error('❌ BALANCE QR: Error parsing data:', error);
            alert('Error parsing QR data: ' + error.message);
            return;
        }

        // Show loading state
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Restore button after operation
        const restoreButton = () => {
            button.innerHTML = originalContent;
            button.disabled = false;
        };

        // First get the comprehensive inventory details
        this.fetchInventoryDetails(itemnum, siteid, inventoryid)
            .then(inventoryData => {
                if (inventoryData) {
                    // Generate balance-specific QR code
                    this.callBalanceQRCodeAPI(inventoryData, balanceRecord);
                } else {
                    console.error('❌ BALANCE QR: Failed to fetch inventory details');
                    alert('Failed to fetch inventory details for QR code generation');
                }
                restoreButton();
            })
            .catch(error => {
                console.error('❌ BALANCE QR: Error fetching inventory details:', error);
                alert('Error fetching inventory details: ' + error.message);
                restoreButton();
            });
    }

    async callBalanceQRCodeAPI(inventoryData, balanceRecord) {
        try {
            console.log('🔍 BALANCE QR API: Calling balance-specific QR generation');
            console.log('🔍 BALANCE QR API: Inventory data keys:', Object.keys(inventoryData));
            console.log('🔍 BALANCE QR API: Balance record:', balanceRecord);

            const response = await fetch('/api/inventory/generate-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    inventory_data: inventoryData,
                    balance_record: balanceRecord,
                    qr_type: 'balance_level'
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                console.log('✅ BALANCE QR API: Successfully generated balance-specific QR code');

                // Validate QR result before displaying
                if (result.qr_image_base64 && result.qr_data) {
                    this.displayQRCodeModal(result, balanceRecord);
                } else {
                    console.error('❌ BALANCE QR API: Invalid QR result structure');
                    alert('Error: Invalid QR code data received');
                }
            }
        } catch (error) {
        }
    }

    displayQRCodeModal(qrResult, balanceRecord) {
        // Create enhanced modal content for balance-specific QR code
        const qrData = qrResult.qr_data || {};
        const binInfo = qrData.binnum || balanceRecord.binnum || 'No Bin';
        const lotInfo = qrData.lotnum || balanceRecord.lotnum || 'No Lot';
        const balanceInfo = this.formatNumber(qrData.curbal || balanceRecord.curbal);
        const conditionCode = qrData.conditioncode || balanceRecord.conditioncode || 'N/A';
        const shelfLifeDays = qrData.shelf_life_days;
        const expirationDate = qrData.expiration_date;
        const reconciled = qrData.reconciled || balanceRecord.reconciled;
        const stagingBin = qrData.stagingbin || balanceRecord.stagingbin;
        const stagedBalance = this.formatNumber(qrData.stagedcurbal || balanceRecord.stagedcurbal);
        const physCount = this.formatNumber(qrData.physcnt || balanceRecord.physcnt);
        const physCountDate = qrData.physcntdate || balanceRecord.physcntdate;

        const modalTitle = `Enhanced Balance QR Code - Bin: ${binInfo}`;
        const modalBody = `
            <div class="enhanced-qr-modal">
                <!-- QR Code Header -->
                <div class="text-center mb-4">
                    <div class="qr-header-badge">
                        <i class="fas fa-qrcode me-2"></i>Enhanced Balance QR Code
                    </div>
                    <div class="qr-location-info mt-2">
                        <span class="badge bg-primary me-2"><i class="fas fa-cube me-1"></i>Bin: ${binInfo}</span>
                        <span class="badge bg-secondary me-2"><i class="fas fa-tags me-1"></i>Lot: ${lotInfo}</span>
                        <span class="badge bg-success"><i class="fas fa-balance-scale me-1"></i>Balance: ${balanceInfo}</span>
                    </div>
                </div>

                <!-- QR Code Image -->
                <div class="qr-code-container text-center mb-4">
                    <div class="qr-image-wrapper">
                        <img src="${qrResult.qr_image_base64}"
                             alt="Enhanced Balance QR Code"
                             class="qr-code-image">
                    </div>
                </div>

                <!-- Enhanced Balance Details -->
                <div class="balance-details-grid">
                    <div class="row g-3">
                        <!-- Core Balance Information -->
                        <div class="col-md-6">
                            <div class="detail-card">
                                <h6 class="detail-card-title"><i class="fas fa-info-circle me-2"></i>Core Details</h6>
                                <div class="detail-item">
                                    <span class="detail-label">Condition Code:</span>
                                    <span class="detail-value badge bg-info">${conditionCode}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Reconciled:</span>
                                    <span class="detail-value">
                                        ${reconciled ? '<i class="fas fa-check text-success"></i> Yes' : '<i class="fas fa-times text-danger"></i> No'}
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Staging Bin:</span>
                                    <span class="detail-value">
                                        ${stagingBin ? '<i class="fas fa-check text-success"></i> Yes' : '<i class="fas fa-times text-muted"></i> No'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Information -->
                        <div class="col-md-6">
                            <div class="detail-card">
                                <h6 class="detail-card-title"><i class="fas fa-calculator me-2"></i>Quantities</h6>
                                <div class="detail-item">
                                    <span class="detail-label">Staged Balance:</span>
                                    <span class="detail-value">${stagedBalance || '0'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Physical Count:</span>
                                    <span class="detail-value">${physCount || '0'}</span>
                                </div>
                                ${physCountDate ? `
                                <div class="detail-item">
                                    <span class="detail-label">Count Date:</span>
                                    <span class="detail-value">${this.formatDate(physCountDate)}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>

                        <!-- Shelf Life & Expiration -->
                        ${shelfLifeDays || expirationDate ? `
                        <div class="col-12">
                            <div class="detail-card shelf-life-card">
                                <h6 class="detail-card-title"><i class="fas fa-clock me-2"></i>Shelf Life & Expiration</h6>
                                <div class="row">
                                    ${shelfLifeDays ? `
                                    <div class="col-md-6">
                                        <div class="detail-item">
                                            <span class="detail-label">Shelf Life:</span>
                                            <span class="detail-value">
                                                <span class="badge ${this.getShelfLifeBadgeClass(shelfLifeDays)}">${shelfLifeDays} days</span>
                                            </span>
                                        </div>
                                    </div>
                                    ` : ''}
                                    ${expirationDate ? `
                                    <div class="col-md-6">
                                        <div class="detail-item">
                                            <span class="detail-label">Expiration Date:</span>
                                            <span class="detail-value">
                                                <span class="text-${this.getExpirationClass(expirationDate)}">${this.formatDate(expirationDate)}</span>
                                            </span>
                                        </div>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <!-- QR Metadata -->
                <div class="qr-metadata mt-4 text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>Generated: ${new Date(qrResult.generated_at).toLocaleString()}<br>
                        <i class="fas fa-tag me-1"></i>Type: Enhanced Balance-Specific QR Code v${qrData.balance_qr_version || '2.0'}<br>
                        ${qrData.includes_shelf_life ? '<i class="fas fa-check text-success me-1"></i>Includes Shelf Life ' : ''}
                        ${qrData.includes_expiration ? '<i class="fas fa-check text-success me-1"></i>Includes Expiration' : ''}
                    </small>
                </div>
            </div>
        `;

        // Show the modal with enhanced styling
        this.showModal(modalTitle, modalBody);

        // Set up download functionality for inventory-level QR code
        this.setupQRDownload(qrResult, null, 'inventory');
    }

    setupQRDownload(qrResult, balanceRecord, qrType) {
        // Set up download functionality with proper file naming
        setTimeout(() => {
            const downloadBtn = document.getElementById('downloadQRBtn');
            if (downloadBtn) {
                // Remove any existing event listeners
                const newDownloadBtn = downloadBtn.cloneNode(true);
                downloadBtn.parentNode.replaceChild(newDownloadBtn, downloadBtn);

                // Add new event listener with proper file naming
                newDownloadBtn.addEventListener('click', () => {
                    this.downloadQRCode(qrResult, balanceRecord, qrType);
                });
            }
        }, 200);
    }

    downloadQRCode(qrResult, balanceRecord, qrType) {
        try {
            const qrData = qrResult.qr_data || {};
            const itemnum = qrData.itemnum || 'UNKNOWN_ITEM';



            // Generate filename based on QR type and your specifications
            let filename;

            // Check if this is a balance-specific QR (either from parameter or QR data)
            const isBalanceQR = qrType === 'balance' || qrData.qr_type === 'balance_specific';

            if (isBalanceQR) {
                // Balance-specific QR: itemnum_binnum.png
                // Get binnum from QR data first (most reliable), then from balance record
                let binnum = qrData.binnum || (balanceRecord && balanceRecord.binnum);



                if (!binnum) {
                    const randomBin = 'BIN' + Math.random().toString(36).substr(2, 6).toUpperCase();
                    binnum = randomBin;
                }
                filename = `${itemnum}_${binnum}.png`;
            } else {
                // Inventory-level QR: itemnum.png
                filename = `${itemnum}.png`;
            }

            // Clean filename to remove invalid characters
            filename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');



            // Validate QR image data
            if (!qrResult.qr_image_base64) {
                throw new Error('No QR image data available for download');
            }

            // Create download link
            const link = document.createElement('a');
            link.href = qrResult.qr_image_base64;
            link.download = filename;

            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success message with detailed info
            const qrTypeDisplay = isBalanceQR ? 'Balance-Specific' : 'Inventory-Level';
            this.showTemporaryMessage(`${qrTypeDisplay} QR code downloaded as ${filename}`, 'success');

        } catch (error) {
        }
    }

    showTemporaryMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        messageDiv.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        messageDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }

    displayInventoryQRCodeModal(qrResult) {
        // Create enhanced modal content for inventory-level QR code
        const qrData = qrResult.qr_data || {};
        const itemnum = qrData.itemnum || 'Unknown Item';
        const description = qrData.description || 'No Description';
        const siteid = qrData.siteid || 'Unknown Site';
        const location = qrData.storeloc || 'Unknown Location';
        const currentBalance = this.formatNumber(qrData.currentbalance || 0);
        const conditionCode = qrData.conditioncode || 'N/A';
        const issueUnit = qrData.issueunit || 'EA';

        const modalTitle = `Inventory QR Code - ${itemnum}`;
        const modalBody = `
            <div class="enhanced-qr-modal">
                <!-- QR Code Header -->
                <div class="text-center mb-4">
                    <div class="qr-header-badge">
                        <i class="fas fa-qrcode me-2"></i>Inventory-Level QR Code
                    </div>
                    <div class="qr-location-info mt-2">
                        <span class="badge bg-primary me-2"><i class="fas fa-box me-1"></i>Item: ${itemnum}</span>
                        <span class="badge bg-secondary me-2"><i class="fas fa-building me-1"></i>Site: ${siteid}</span>
                        <span class="badge bg-success"><i class="fas fa-balance-scale me-1"></i>Balance: ${currentBalance}</span>
                    </div>
                </div>

                <!-- QR Code Image -->
                <div class="qr-code-container text-center mb-4">
                    <div class="qr-image-wrapper">
                        <img src="${qrResult.qr_image_base64}"
                             alt="Inventory QR Code"
                             class="qr-code-image">
                    </div>
                </div>

                <!-- Inventory Details -->
                <div class="balance-details-grid">
                    <div class="row g-3">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="detail-card">
                                <h6 class="detail-card-title"><i class="fas fa-info-circle me-2"></i>Item Details</h6>
                                <div class="detail-item">
                                    <span class="detail-label">Description:</span>
                                    <span class="detail-value">${description}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Location:</span>
                                    <span class="detail-value">${location}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Issue Unit:</span>
                                    <span class="detail-value badge bg-info">${issueUnit}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Inventory Information -->
                        <div class="col-md-6">
                            <div class="detail-card">
                                <h6 class="detail-card-title"><i class="fas fa-warehouse me-2"></i>Inventory Info</h6>
                                <div class="detail-item">
                                    <span class="detail-label">Current Balance:</span>
                                    <span class="detail-value"><strong>${currentBalance}</strong></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Condition Code:</span>
                                    <span class="detail-value badge bg-info">${conditionCode}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Inventory ID:</span>
                                    <span class="detail-value">${qrData.inventoryid || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QR Metadata -->
                <div class="qr-metadata mt-4 text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>Generated: ${new Date(qrResult.generated_at).toLocaleString()}<br>
                        <i class="fas fa-tag me-1"></i>Type: Inventory-Level QR Code<br>
                        <i class="fas fa-layer-group me-1"></i>Contains overall inventory data for item
                    </small>
                </div>
            </div>
        `;

        // Show the modal with enhanced styling
        this.showModal(modalTitle, modalBody);

        // Set up download functionality for balance-specific QR code
        this.setupQRDownload(qrResult, balanceRecord, 'balance');
    }

    async fetchInventoryDetails(itemnum, siteid, inventoryid) {
        try {
            const response = await fetch(`/api/inventory/comprehensive-details/${itemnum}?siteid=${siteid}&inventoryid=${inventoryid}`);
            if (response.ok) {
                return await response.json();
            } else {
                console.error('❌ Failed to fetch inventory details:', response.status);
                return null;
            }
        } catch (error) {
            console.error('❌ Error fetching inventory details:', error);
            return null;
        }
    }

    showModal(title, body) {
        // Create or update modal
        let modal = document.getElementById('qrCodeModal');
        if (!modal) {
            // Create modal if it doesn't exist
            modal = document.createElement('div');
            modal.id = 'qrCodeModal';
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="qrCodeModalTitle"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="qrCodeModalBody"></div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success" id="downloadQRBtn">
                                <i class="fas fa-download me-1"></i>Download QR Code
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>Print QR Code
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Wait for DOM to be ready before updating content
        setTimeout(() => {
            try {
                const titleElement = document.getElementById('qrCodeModalTitle');
                const bodyElement = document.getElementById('qrCodeModalBody');

                if (titleElement && bodyElement) {
                    titleElement.innerHTML = title;
                    bodyElement.innerHTML = body;

                    // Show modal
                    const bootstrapModal = new bootstrap.Modal(modal);
                    bootstrapModal.show();
                } else {
                    console.error('❌ MODAL: Could not find modal elements');
                    alert('Error: Could not display QR code modal');
                }
            } catch (error) {
                console.error('❌ MODAL: Error updating modal content:', error);
                alert('Error displaying QR code: ' + error.message);
            }
        }, 100);
    }

    getStatusClass(status) {
        if (!status) return 'bg-secondary text-white';

        const statusUpper = status.toUpperCase();
        switch (statusUpper) {
            case 'ACTIVE':
                return 'bg-success text-white';
            case 'INACTIVE':
                return 'bg-warning text-dark';
            case 'OBSOLETE':
                return 'bg-danger text-white';
            case 'PENDING':
                return 'bg-info text-white';
            default:
                return 'bg-secondary text-white';
        }
    }

    // QR Code and Label Generation Functions
    async generateQRCodeForItem(itemnum, inventoryid) {
        try {
            console.log(`🔍 QR CODE: Generating QR code for item ${itemnum}`);

            // Get current inventory data for this item
            const inventoryData = await this.fetchInventoryDataForQR(itemnum, inventoryid);
            if (!inventoryData) {
                alert('Could not fetch inventory data for QR code generation');
                return;
            }

            // Generate inventory-level QR code (backward compatibility)
            const response = await fetch('/api/inventory/generate-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inventory_data: inventoryData
                    // No balance_record = inventory-level QR code
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.qr_image_base64 && result.qr_data) {
                // Use the enhanced modal for inventory-level QR codes too
                this.displayInventoryQRCodeModal(result);
            } else {
                console.error('❌ INVENTORY QR: Failed to generate QR code:', result.error);
                alert(`QR Code generation failed: ${result.error || 'Unknown error'}`);
            }

        } catch (error) {
        }
    }



    async fetchInventoryDataForQR(itemnum, inventoryid) {
        try {
            // Use the comprehensive details endpoint to get current data
            let url = `/api/inventory/comprehensive-details/${itemnum}?siteid=${this.currentSiteId}`;
            if (inventoryid) {
                url += `&inventoryid=${inventoryid}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success && data.details) {
                return data.details;
            }

            return null;
        } catch (error) {
            console.error('Error fetching inventory data for QR:', error);
            return null;
        }
    }

    async showItemDetails(itemnum, siteid, inventoryid = null) {
        try {
            // Show loading state
            const modalBody = document.getElementById('inventoryItemModalBody');
            const modalTitle = document.getElementById('inventoryItemModalLabel');

            modalTitle.innerHTML = `<i class="fas fa-cube me-2"></i>Loading Details...`;
            modalBody.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading comprehensive inventory details...</p>
                </div>
            `;

            // Use the new comprehensive details endpoint
            let url = `/api/inventory/comprehensive-details/${itemnum}?siteid=${siteid}`;
            if (inventoryid) {
                url += `&inventoryid=${inventoryid}`;
            }
            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                this.displayComprehensiveItemModal(data.details, data.metadata);
            } else {
                this.showError(data.error || 'Failed to load item details');
            }
        } catch (error) {
            console.error('Error loading item details:', error);
            this.showError('Failed to load item details');
        }
    }

    displayComprehensiveItemModal(details, metadata) {
        const modalBody = document.getElementById('inventoryItemModalBody');
        const modalTitle = document.getElementById('inventoryItemModalLabel');

        // Extract basic info for title
        const basicInfo = details.basic_info || {};
        modalTitle.innerHTML = `<i class="fas fa-cube me-2"></i>${basicInfo.itemnum || 'Item Details'} - ${basicInfo.description || 'No Description'}`;

        // Create diary-style vertical sidebar interface for comprehensive data
        modalBody.innerHTML = `
            <div class="inventory-diary-view">
                <div class="diary-sidebar">
                    <div class="diary-tabs" role="tablist">
                        <button class="diary-tab active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab"
                                style="--tab-bg: #e3f2fd; --tab-border: #2196f3; --tab-text: #1976d2;">
                            <div class="diary-tab-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="diary-tab-label">Basic</div>
                        </button>

                        <button class="diary-tab" id="location-tab" data-bs-toggle="tab" data-bs-target="#location" type="button" role="tab"
                                style="--tab-bg: #fff3e0; --tab-border: #ff9800; --tab-text: #f57c00;">
                            <div class="diary-tab-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="diary-tab-label">Location</div>
                        </button>

                        <button class="diary-tab" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab"
                                style="--tab-bg: #e8f5e8; --tab-border: #4caf50; --tab-text: #388e3c;">
                            <div class="diary-tab-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="diary-tab-label">Stock</div>
                        </button>

                        <button class="diary-tab" id="costs-tab" data-bs-toggle="tab" data-bs-target="#costs" type="button" role="tab"
                                style="--tab-bg: #fce4ec; --tab-border: #e91e63; --tab-text: #c2185b;">
                            <div class="diary-tab-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="diary-tab-label">Costs</div>
                        </button>

                        <button class="diary-tab" id="balances-tab" data-bs-toggle="tab" data-bs-target="#balances" type="button" role="tab"
                                style="--tab-bg: #f3e5f5; --tab-border: #9c27b0; --tab-text: #7b1fa2;">
                            <div class="diary-tab-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="diary-tab-label">Balances</div>
                        </button>

                        <button class="diary-tab" id="technical-tab" data-bs-toggle="tab" data-bs-target="#technical" type="button" role="tab"
                                style="--tab-bg: #e0f2f1; --tab-border: #009688; --tab-text: #00695c;">
                            <div class="diary-tab-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="diary-tab-label">Technical</div>
                        </button>
                    </div>
                </div>

                <div class="diary-content">
                    <div class="tab-content" id="inventoryDetailTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active mobile-tab-panel" id="basic" role="tabpanel">
                            <div class="mobile-tab-header">
                                <div class="mobile-tab-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <h6 class="mobile-tab-title">Basic Information</h6>
                            </div>
                            <div class="mobile-detail-fields">
                                ${this.generateMobileBasicInfoFields(details)}
                            </div>
                        </div>

                        <!-- Location & Storage Tab -->
                        <div class="tab-pane fade mobile-tab-panel" id="location" role="tabpanel">
                            <div class="mobile-tab-header">
                                <div class="mobile-tab-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h6 class="mobile-tab-title">Location & Storage</h6>
                            </div>
                            <div class="mobile-detail-fields">
                                ${this.generateMobileLocationFields(details)}
                            </div>
                        </div>

                        <!-- Stock & Inventory Tab -->
                        <div class="tab-pane fade mobile-tab-panel" id="inventory" role="tabpanel">
                            <div class="mobile-tab-header">
                                <div class="mobile-tab-icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <h6 class="mobile-tab-title">Stock & Inventory</h6>
                            </div>
                            <div class="mobile-detail-fields">
                                ${this.generateMobileInventoryFields(details)}
                            </div>
                        </div>

                        <!-- Costs & Pricing Tab -->
                        <div class="tab-pane fade mobile-tab-panel" id="costs" role="tabpanel">
                            <div class="mobile-tab-header">
                                <div class="mobile-tab-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <h6 class="mobile-tab-title">Costs & Pricing</h6>
                            </div>
                            <div class="mobile-detail-fields">
                                ${this.generateMobileCostsFields(details)}
                            </div>
                        </div>

                        <!-- Balances Tab -->
                        <div class="tab-pane fade mobile-tab-panel" id="balances" role="tabpanel">
                            <div class="mobile-tab-header">
                                <div class="mobile-tab-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <h6 class="mobile-tab-title">Balance Records</h6>
                            </div>
                            <div class="mobile-detail-fields">
                                ${this.generateMobileBalancesFields(details)}
                            </div>
                        </div>

                        <!-- Technical Details Tab -->
                        <div class="tab-pane fade mobile-tab-panel" id="technical" role="tabpanel">
                            <div class="mobile-tab-header">
                                <div class="mobile-tab-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <h6 class="mobile-tab-title">Technical Details</h6>
                            </div>
                            <div class="mobile-detail-fields">
                                ${this.generateMobileTechnicalFields(details)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const modal = new bootstrap.Modal(document.getElementById('inventoryItemModal'));
        modal.show();
    }

    generateBasicInfoTab(details) {
        const basicInfo = details.basic_info || {};

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary"><i class="fas fa-tag me-2"></i>Item Information</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Item Number:</strong></td><td>${basicInfo.itemnum || ''}</td></tr>
                        <tr><td><strong>Description:</strong></td><td>${basicInfo.description || ''}</td></tr>
                        <tr><td><strong>Status:</strong></td><td><span class="badge ${this.getStatusClass(basicInfo.status)}">${basicInfo.status || ''}</span></td></tr>
                        <tr><td><strong>Item Type:</strong></td><td>${basicInfo.itemtype || ''}</td></tr>
                        <tr><td><strong>Item Set ID:</strong></td><td>${basicInfo.itemsetid || ''}</td></tr>
                        <tr><td><strong>ABC Classification:</strong></td><td>${basicInfo.abctype || ''}</td></tr>
                        <tr><td><strong>Rotating:</strong></td><td>${basicInfo.rotating ? 'Yes' : 'No'}</td></tr>
                        <tr><td><strong>Lot Type:</strong></td><td>${basicInfo.lottype || ''}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary"><i class="fas fa-building me-2"></i>Location Information</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Site ID:</strong></td><td>${basicInfo.siteid || ''}</td></tr>
                        <tr><td><strong>Location:</strong></td><td>${basicInfo.location || ''}</td></tr>
                        <tr><td><strong>Store Location:</strong></td><td>${basicInfo.store_location || ''}</td></tr>
                        <tr><td><strong>Bin Number:</strong></td><td>${basicInfo.binnum || ''}</td></tr>
                        <tr><td><strong>Condition Enabled:</strong></td><td>${basicInfo.conditionenabled ? 'Yes' : 'No'}</td></tr>
                        <tr><td><strong>Condition Code:</strong></td><td>${basicInfo.conditioncode || ''}</td></tr>
                        <tr><td><strong>Lot Number:</strong></td><td>${basicInfo.lotnum || ''}</td></tr>
                    </table>
                </div>
            </div>
        `;
    }

    generateInventoryTab(details) {
        const currentBalances = details.inventory?.current_balances || {};
        const unitsOrdering = details.inventory?.units_ordering || {};

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-success"><i class="fas fa-boxes me-2"></i>Current Balances</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Current Balance Total:</strong></td><td class="text-end">${this.formatNumber(currentBalances.curbaltotal)}</td></tr>
                        <tr><td><strong>Available Balance:</strong></td><td class="text-end">${this.formatNumber(currentBalances.avblbalance)}</td></tr>
                        <tr><td><strong>Reserved Quantity:</strong></td><td class="text-end">${this.formatNumber(currentBalances.reservedqty)}</td></tr>
                        <tr><td><strong>Hard Reserved:</strong></td><td class="text-end">${this.formatNumber(currentBalances.hardreservedqty)}</td></tr>
                        <tr><td><strong>Soft Reserved:</strong></td><td class="text-end">${this.formatNumber(currentBalances.softreservedqty)}</td></tr>
                        <tr><td><strong>Physical Count:</strong></td><td class="text-end">${this.formatNumber(currentBalances.physcnt)}</td></tr>
                        <tr><td><strong>Physical Count Date:</strong></td><td>${this.formatDate(currentBalances.physcntdate)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success"><i class="fas fa-ruler me-2"></i>Units & Ordering</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Issue Unit:</strong></td><td>${unitsOrdering.issueunit || ''}</td></tr>
                        <tr><td><strong>Order Unit:</strong></td><td>${unitsOrdering.orderunit || ''}</td></tr>
                        <tr><td><strong>Reorder Point:</strong></td><td class="text-end">${this.formatNumber(unitsOrdering.minlevel)}</td></tr>
                        <tr><td><strong>Max Level:</strong></td><td class="text-end">${this.formatNumber(unitsOrdering.maxlevel)}</td></tr>
                        <tr><td><strong>Reorder Quantity:</strong></td><td class="text-end">${this.formatNumber(unitsOrdering.reorderqty)}</td></tr>
                        <tr><td><strong>Lead Time:</strong></td><td>${this.formatNumber(unitsOrdering.deliverytime)} days</td></tr>
                    </table>
                </div>
            </div>
        `;
    }

    generateCostsTab(details) {
        const costInfo = details.cost_information || {};

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-warning"><i class="fas fa-dollar-sign me-2"></i>Cost Information</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Average Cost:</strong></td><td class="text-end">${this.formatCurrency(costInfo.avgcost)}</td></tr>
                        <tr><td><strong>Last Cost:</strong></td><td class="text-end">${this.formatCurrency(costInfo.lastcost)}</td></tr>
                        <tr><td><strong>Standard Cost:</strong></td><td class="text-end">${this.formatCurrency(costInfo.stdcost)}</td></tr>
                        <tr><td><strong>Unit Cost:</strong></td><td class="text-end">${this.formatCurrency(costInfo.unitcost)}</td></tr>
                        <tr><td><strong>Currency:</strong></td><td>${costInfo.currencycode || ''}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-warning"><i class="fas fa-industry me-2"></i>Vendor Information</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Vendor:</strong></td><td>${costInfo.vendor || ''}</td></tr>
                        <tr><td><strong>Manufacturer:</strong></td><td>${costInfo.manufacturer || ''}</td></tr>
                        <tr><td><strong>Model Number:</strong></td><td>${costInfo.modelnum || ''}</td></tr>
                        <tr><td><strong>Contract:</strong></td><td>${costInfo.contractnum || ''}</td></tr>
                    </table>
                </div>
            </div>
        `;
    }

    generateBalancesTab(details) {
        const balanceDetails = details.balance_details || {};

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-info"><i class="fas fa-chart-bar me-2"></i>Balance Details</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Store Location:</strong></td><td>${balanceDetails.store_location || ''}</td></tr>
                        <tr><td><strong>Bin Number:</strong></td><td>${balanceDetails.binnum || ''}</td></tr>
                        <tr><td><strong>Current Balance:</strong></td><td class="text-end">${this.formatNumber(balanceDetails.curbal)}</td></tr>
                        <tr><td><strong>Condition Code:</strong></td><td>${balanceDetails.conditioncode || ''}</td></tr>
                        <tr><td><strong>Lot Number:</strong></td><td>${balanceDetails.lotnum || ''}</td></tr>
                        <tr><td><strong>Physical Count:</strong></td><td class="text-end">${this.formatNumber(balanceDetails.physcnt)}</td></tr>
                        <tr><td><strong>Physical Count Date:</strong></td><td>${this.formatDate(balanceDetails.physcntdate)}</td></tr>
                        <tr><td><strong>Staging Bin:</strong></td><td>${balanceDetails.stagingbin || ''}</td></tr>
                        <tr><td><strong>Staged Current Balance:</strong></td><td class="text-end">${this.formatNumber(balanceDetails.stagedcurbal)}</td></tr>
                    </table>
                </div>
            </div>
        `;
    }

    generateTechnicalTab(details) {
        const technicalDetails = details.technical_details || {};
        const systemInfo = details.system_information || {};
        const additionalAttrs = details.additional_attributes || {};

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-secondary"><i class="fas fa-cogs me-2"></i>Technical Details</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Bench Stock:</strong></td><td>${technicalDetails.benchstock ? 'Yes' : 'No'}</td></tr>
                        <tr><td><strong>Shrinkage %:</strong></td><td>${this.formatNumber(technicalDetails.shrinkageacc)}%</td></tr>
                        <tr><td><strong>GL Account:</strong></td><td>${technicalDetails.glaccount || ''}</td></tr>
                        <tr><td><strong>Control Account:</strong></td><td>${technicalDetails.controlacc || ''}</td></tr>
                        <tr><td><strong>Manufacturer:</strong></td><td>${technicalDetails.manufacturer || ''}</td></tr>
                        <tr><td><strong>Model Number:</strong></td><td>${technicalDetails.modelnum || ''}</td></tr>
                        <tr><td><strong>Vendor:</strong></td><td>${technicalDetails.vendor || ''}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-secondary"><i class="fas fa-database me-2"></i>System Information</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Item Set ID:</strong></td><td>${systemInfo.itemsetid || ''}</td></tr>
                        <tr><td><strong>Currency Code:</strong></td><td>${systemInfo.currencycode || ''}</td></tr>
                        <tr><td><strong>Created Date:</strong></td><td>${this.formatDate(systemInfo.statusdate)}</td></tr>
                        <tr><td><strong>Modified Date:</strong></td><td>${this.formatDate(systemInfo.statusdate)}</td></tr>
                        <tr><td><strong>Last Issue Date:</strong></td><td>${this.formatDate(systemInfo.lastissuedate)}</td></tr>
                    </table>

                    <h6 class="text-secondary mt-4"><i class="fas fa-tags me-2"></i>Additional Attributes</h6>
                    <table class="table table-sm table-striped">
                        <tr><td><strong>Rotating Asset:</strong></td><td>${additionalAttrs.rotating ? 'Yes' : 'No'}</td></tr>
                        <tr><td><strong>Condition Enabled:</strong></td><td>${additionalAttrs.conditionenabled ? 'Yes' : 'No'}</td></tr>
                        <tr><td><strong>ABC Classification:</strong></td><td>${additionalAttrs.abc || ''}</td></tr>
                    </table>
                </div>
            </div>
        `;
    }

    generateAllRecordsTab(details) {
        const inventoryRecords = details.inventory_records || [];
        const totalRecords = inventoryRecords.length;

        if (totalRecords === 0) {
            return `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No inventory records found for this item in the selected site.
                </div>
            `;
        }

        let recordsHtml = `
            <div class="mb-3">
                <h6 class="text-primary">
                    <i class="fas fa-list me-2"></i>
                    All Inventory Records (${totalRecords} ${totalRecords === 1 ? 'record' : 'records'})
                </h6>
                <p class="text-muted small">
                    Showing all inventory records for this item in the selected site. Each record may represent different locations, bins, or conditions.
                </p>
            </div>
        `;

        // Add navigation controls for mobile/desktop
        if (totalRecords > 1) {
            recordsHtml += `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="prevRecord" onclick="inventoryManager.navigateRecord(-1)">
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="nextRecord" onclick="inventoryManager.navigateRecord(1)">
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <span class="badge bg-secondary" id="recordCounter">Record 1 of ${totalRecords}</span>
                </div>
            `;
        }

        recordsHtml += '<div id="inventoryRecordsContainer">';

        // Generate cards for each inventory record
        inventoryRecords.forEach((record, index) => {
            const isVisible = index === 0 ? '' : 'style="display: none;"';
            recordsHtml += `
                <div class="inventory-record-card" data-record-index="${index}" ${isVisible}>
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-warehouse me-2"></i>
                                Record ${index + 1} - ${record.location || 'Unknown Location'}
                                ${record.invbalances_binnum ? `(Bin: ${record.invbalances_binnum})` : ''}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success"><i class="fas fa-map-marker-alt me-2"></i>Location Details</h6>
                                    <table class="table table-sm table-striped">
                                        <tr><td><strong>Location:</strong></td><td>${record.location || ''}</td></tr>
                                        <tr><td><strong>Bin Number:</strong></td><td>${record.invbalances_binnum || ''}</td></tr>
                                        <tr><td><strong>ABC Classification:</strong></td><td>${record.abc || ''}</td></tr>
                                        <tr><td><strong>Status:</strong></td><td><span class="badge ${this.getStatusClass(record.status)}">${record.status || ''}</span></td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-info"><i class="fas fa-boxes me-2"></i>Balance Information</h6>
                                    <table class="table table-sm table-striped">
                                        <tr><td><strong>Current Balance:</strong></td><td class="text-end">${this.formatNumber(record.curbaltotal)}</td></tr>
                                        <tr><td><strong>Available Balance:</strong></td><td class="text-end">${this.formatNumber(record.avblbalance)}</td></tr>
                                        <tr><td><strong>Reserved Qty:</strong></td><td class="text-end">${this.formatNumber(record.reservedqty)}</td></tr>
                                        <tr><td><strong>Physical Count:</strong></td><td class="text-end">${this.formatNumber(record.invbalances_physcnt)}</td></tr>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="text-warning"><i class="fas fa-dollar-sign me-2"></i>Cost Information</h6>
                                    <table class="table table-sm table-striped">
                                        <tr><td><strong>Average Cost:</strong></td><td class="text-end">${this.formatCurrency(record.invcost_avgcost)}</td></tr>
                                        <tr><td><strong>Last Cost:</strong></td><td class="text-end">${this.formatCurrency(record.invcost_lastcost)}</td></tr>
                                        <tr><td><strong>Standard Cost:</strong></td><td class="text-end">${this.formatCurrency(record.invcost_stdcost)}</td></tr>
                                        <tr><td><strong>Unit Cost:</strong></td><td class="text-end">${this.formatCurrency(record.invbalances_unitcost)}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-secondary"><i class="fas fa-industry me-2"></i>Vendor Information</h6>
                                    <table class="table table-sm table-striped">
                                        <tr><td><strong>Vendor:</strong></td><td>${record.invvendor_vendor || ''}</td></tr>
                                        <tr><td><strong>Manufacturer:</strong></td><td>${record.invvendor_manufacturer || ''}</td></tr>
                                        <tr><td><strong>Model Number:</strong></td><td>${record.invvendor_modelnum || ''}</td></tr>
                                        <tr><td><strong>Currency:</strong></td><td>${record.invvendor_currencycode || ''}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        recordsHtml += '</div>';

        return recordsHtml;
    }

    // Navigation methods for inventory records
    navigateRecord(direction) {
        const container = document.getElementById('inventoryRecordsContainer');
        const records = container.querySelectorAll('.inventory-record-card');
        const counter = document.getElementById('recordCounter');

        let currentIndex = 0;
        records.forEach((record, index) => {
            if (record.style.display !== 'none') {
                currentIndex = index;
            }
            record.style.display = 'none';
        });

        let newIndex = currentIndex + direction;
        if (newIndex < 0) newIndex = records.length - 1;
        if (newIndex >= records.length) newIndex = 0;

        records[newIndex].style.display = 'block';
        counter.textContent = `Record ${newIndex + 1} of ${records.length}`;

        // Update navigation buttons
        const prevBtn = document.getElementById('prevRecord');
        const nextBtn = document.getElementById('nextRecord');

        if (prevBtn && nextBtn) {
            prevBtn.disabled = false;
            nextBtn.disabled = false;
        }
    }

    // Mobile field generation functions for diary-style tabs
    generateMobileBasicInfoFields(details) {
        const basicInfo = details.basic_info || {};

        return `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-hashtag"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Item Number</div>
                    <div class="mobile-field-value">${basicInfo.itemnum || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Description</div>
                    <div class="mobile-field-value">${basicInfo.description || 'No description available'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-flag"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Status</div>
                    <div class="mobile-field-value">
                        <span class="badge ${this.getStatusClass(basicInfo.status)}">${basicInfo.status || 'Unknown'}</span>
                    </div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Item Type</div>
                    <div class="mobile-field-value">${basicInfo.itemtype || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Item Set ID</div>
                    <div class="mobile-field-value">${basicInfo.itemsetid || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-sort-alpha-down"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">ABC Classification</div>
                    <div class="mobile-field-value">${basicInfo.abctype || 'Not classified'}</div>
                </div>
            </div>
        `;
    }

    generateMobileLocationFields(details) {
        const basicInfo = details.basic_info || {};

        return `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Site ID</div>
                    <div class="mobile-field-value">${basicInfo.siteid || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-map-pin"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Location</div>
                    <div class="mobile-field-value">${basicInfo.location || 'No location specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Store Location</div>
                    <div class="mobile-field-value">${basicInfo.store_location || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Bin Number</div>
                    <div class="mobile-field-value">${basicInfo.binnum || 'No bin assigned'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Condition Code</div>
                    <div class="mobile-field-value">${basicInfo.conditioncode || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-barcode"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Lot Number</div>
                    <div class="mobile-field-value">${basicInfo.lotnum || 'No lot number'}</div>
                </div>
            </div>
        `;
    }

    generateMobileInventoryFields(details) {
        const currentBalances = details.inventory?.current_balances || {};

        return `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Current Balance Total</div>
                    <div class="mobile-field-value">${this.formatNumber(currentBalances.curbaltotal) || '0'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Available Balance</div>
                    <div class="mobile-field-value">${this.formatNumber(currentBalances.avblbalance) || '0'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-lock"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Reserved Quantity</div>
                    <div class="mobile-field-value">${this.formatNumber(currentBalances.reservedqty) || '0'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">On Order</div>
                    <div class="mobile-field-value">${this.formatNumber(currentBalances.onorder) || '0'}</div>
                </div>
            </div>
        `;
    }

    generateMobileCostsFields(details) {
        const costs = details.costs || {};

        return `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Average Cost</div>
                    <div class="mobile-field-value">${this.formatCurrency(costs.avgcost) || 'Not available'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-money-bill"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Standard Cost</div>
                    <div class="mobile-field-value">${this.formatCurrency(costs.stdcost) || 'Not available'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Last Cost</div>
                    <div class="mobile-field-value">${this.formatCurrency(costs.lastcost) || 'Not available'}</div>
                </div>
            </div>
        `;
    }

    generateMobileBalancesFields(details) {
        const inventoryRecords = details.inventory?.inventory_records || [];
        const totalRecords = inventoryRecords.length;

        if (totalRecords === 0) {
            return `
                <div class="mobile-detail-field">
                    <div class="mobile-field-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="mobile-field-content">
                        <div class="mobile-field-label">Balance Records</div>
                        <div class="mobile-field-value">No balance records available</div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Total Records</div>
                    <div class="mobile-field-value">${totalRecords} balance ${totalRecords === 1 ? 'record' : 'records'}</div>
                </div>
            </div>
            <div class="mobile-balance-summary">
                ${inventoryRecords.slice(0, 3).map((record, index) => `
                    <div class="mobile-balance-item">
                        <div class="balance-location">
                            <i class="fas fa-map-pin me-1"></i>
                            ${record.location || 'Unknown Location'}
                            ${record.invbalances_binnum ? ` (${record.invbalances_binnum})` : ''}
                        </div>
                        <div class="balance-amount">
                            <strong>${this.formatNumber(record.invbalances_curbal) || '0'}</strong>
                        </div>
                    </div>
                `).join('')}
                ${totalRecords > 3 ? `<div class="text-muted small">... and ${totalRecords - 3} more records</div>` : ''}
            </div>
        `;
    }

    generateMobileTechnicalFields(details) {
        const technicalDetails = details.technical_details || {};
        const basicInfo = details.basic_info || {};

        return `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-sync"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Rotating Item</div>
                    <div class="mobile-field-value">${basicInfo.rotating ? 'Yes' : 'No'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Lot Type</div>
                    <div class="mobile-field-value">${basicInfo.lottype || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-ruler"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Issue Unit</div>
                    <div class="mobile-field-value">${technicalDetails.issueunit || 'Not specified'}</div>
                </div>
            </div>
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">Order Unit</div>
                    <div class="mobile-field-value">${technicalDetails.orderunit || 'Not specified'}</div>
                </div>
            </div>
        `;
    }

    // Utility functions for formatting - show actual values or empty for missing
    formatNumber(value) {
        if (value === null || value === undefined || value === '') return '';
        const num = parseFloat(value);
        return isNaN(num) ? '' : num.toLocaleString();
    }

    formatCurrency(value, currency = 'USD') {
        if (value === null || value === undefined || value === '') return '';
        const num = parseFloat(value);
        if (isNaN(num)) return '';

        try {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency || 'USD'
            }).format(num);
        } catch (e) {
            return `$${num.toFixed(2)}`;
        }
    }

    formatDate(dateString) {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        } catch (e) {
            return dateString;
        }
    }

    updatePagination(metadata) {
        const paginationContainer = document.getElementById('inventoryPaginationContainer');
        const pagination = document.getElementById('inventoryPagination');

        if (!metadata || metadata.total_pages <= 1) {
            paginationContainer.classList.add('d-none');
            return;
        }

        paginationContainer.classList.remove('d-none');

        let html = '';

        // Previous button
        const prevDisabled = metadata.page === 0 ? 'disabled' : '';
        html += `
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="inventoryManager.performSearch(${metadata.page - 1})" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(0, metadata.page - 2);
        const endPage = Math.min(metadata.total_pages - 1, metadata.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const active = i === metadata.page ? 'active' : '';
            html += `
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="inventoryManager.performSearch(${i})">${i + 1}</a>
                </li>
            `;
        }

        // Next button
        const nextDisabled = metadata.page >= metadata.total_pages - 1 ? 'disabled' : '';
        html += `
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="inventoryManager.performSearch(${metadata.page + 1})" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;

        pagination.innerHTML = html;
    }

    updateResultsInfo(metadata) {
        const resultsInfo = document.getElementById('inventoryResultsInfo');

        if (!metadata || metadata.count === 0) {
            resultsInfo.innerHTML = '';
            return;
        }

        const startItem = (metadata.page * this.currentLimit) + 1;
        const endItem = Math.min(startItem + this.currentLimit - 1, metadata.count);

        resultsInfo.innerHTML = `
            <small class="text-muted">
                Showing ${startItem}-${endItem} of ${metadata.count} items
                ${metadata.load_time ? `(${metadata.load_time.toFixed(2)}s)` : ''}
            </small>
        `;
    }

    showLoading() {
        const overlay = document.getElementById('inventoryLoadingOverlay');
        if (overlay) {
            overlay.classList.remove('d-none');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('inventoryLoadingOverlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }
    }

    // QR Code Modal Functions
    showQRCodeModal(qrResult) {
        // Create or update QR code modal
        let modal = document.getElementById('qrCodeModal');
        if (!modal) {
            modal = this.createQRCodeModal();
            document.body.appendChild(modal);
        }

        // Update modal content
        const qrImage = modal.querySelector('#qrCodeImage');
        const qrData = modal.querySelector('#qrCodeData');
        const itemInfo = modal.querySelector('#qrItemInfo');
        const modalTitle = modal.querySelector('.modal-title');

        qrImage.src = qrResult.qr_image_base64;
        qrData.textContent = qrResult.data_encoded;

        // Update modal title with ITEMNUM
        const item = qrResult.qr_data;
        modalTitle.innerHTML = `
            <i class="fas fa-qrcode me-2"></i>QR Code - ${item.itemnum}
        `;

        // Store item number for download function
        qrImage.setAttribute('data-itemnum', item.itemnum);

        // Display item information
        itemInfo.innerHTML = `
            <strong>Item:</strong> ${item.itemnum}<br>
            <strong>Description:</strong> ${item.description}<br>
            <strong>Site:</strong> ${item.siteid}<br>
            <strong>Location:</strong> ${item.storeloc}<br>
            <strong>Bin:</strong> ${item.binnum || 'N/A'}<br>
            <strong>Current Balance:</strong> ${item.currentbalance}<br>
            <strong>Condition:</strong> ${item.conditioncode || 'N/A'}<br>
            <strong>Issue Unit:</strong> ${item.issueunit}<br>
            <strong>Generated:</strong> ${new Date(item.generated_timestamp).toLocaleString()}
        `;

        // Show modal
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }



    createQRCodeModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'qrCodeModal';
        modal.tabIndex = -1;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-qrcode me-2"></i>Inventory QR Code
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>QR Code</h6>
                                <img id="qrCodeImage" class="img-fluid border" style="max-width: 300px;" alt="QR Code">
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary btn-sm" onclick="downloadQRCode()">
                                        <i class="fas fa-download me-1"></i>Download
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-start">
                                <h6>Item Information</h6>
                                <div id="qrItemInfo" class="small"></div>
                                <hr>
                                <h6>Encoded Data</h6>
                                <textarea id="qrCodeData" class="form-control small" rows="8" readonly></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="printQRCode()">
                            <i class="fas fa-print me-1"></i>Print
                        </button>
                    </div>
                </div>
            </div>
        `;
        return modal;
    }



    showError(message) {
        const resultsContainer = document.getElementById('inventorySearchResults');
        resultsContainer.innerHTML = `
            <div class="text-center text-danger py-5">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <p class="mb-0">${message}</p>
                <small class="text-muted">Please try again or contact support if the problem persists</small>
            </div>
        `;

        // Hide pagination and results info
        document.getElementById('inventoryPaginationContainer').classList.add('d-none');
        document.getElementById('inventoryResultsInfo').innerHTML = '';
    }

    showInitialState() {
        const resultsContainer = document.getElementById('inventorySearchResults');
        resultsContainer.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p class="mb-0">Select a site and enter a search term to find inventory items</p>
                <small class="text-muted">Search by item number or description</small>
            </div>
        `;

        // Hide pagination and results info
        document.getElementById('inventoryPaginationContainer').classList.add('d-none');
        document.getElementById('inventoryResultsInfo').innerHTML = '';

        // Reset state
        this.currentSearchTerm = '';
        this.currentLocation = '';
        this.currentPage = 0;
        this.totalPages = 0;
    }

    // Physical Count Adjustment Modal Functions
    openPhysicalCountModal(balanceId, itemnum, siteid, inventoryid) {
        console.log(`🔍 PHYSICAL COUNT: Opening modal for balance ID ${balanceId}, Item ${itemnum}, Site ${siteid}`);

        // Get the balance data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ PHYSICAL COUNT: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let balanceRecord, itemData;
        try {
            const balanceDataAttr = button.getAttribute('data-balance-data');
            const itemDataAttr = button.getAttribute('data-item-data');

            if (!balanceDataAttr) {
                console.error('❌ PHYSICAL COUNT: No balance data found on button');
                alert('Error: No balance data found on button');
                return;
            }

            balanceRecord = JSON.parse(balanceDataAttr);
            itemData = itemDataAttr ? JSON.parse(itemDataAttr) : {itemnum, siteid, inventoryid};

            console.log('🔍 PHYSICAL COUNT: Balance record:', balanceRecord);
            console.log('🔍 PHYSICAL COUNT: Item data:', itemData);

        } catch (error) {
            console.error('❌ PHYSICAL COUNT: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populatePhysicalCountModal(balanceRecord, itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('physicalCountModal'));
        modal.show();
    }

    populatePhysicalCountModal(balanceRecord, itemData) {
        // Populate readonly fields
        document.getElementById('pc_itemnum').value = itemData.itemnum || '';
        document.getElementById('pc_itemsetid').value = itemData.itemsetid || 'ITEMSET';
        document.getElementById('pc_siteid').value = itemData.siteid || '';
        document.getElementById('pc_location').value = itemData.location || balanceRecord.location || '';
        document.getElementById('pc_binnum').value = balanceRecord.binnum || '';
        document.getElementById('pc_conditioncode').value = balanceRecord.conditioncode || '';

        // Set current physical count if available
        document.getElementById('pc_physcnt').value = balanceRecord.physcnt || '';

        // Clear other fields
        document.getElementById('pc_reason_code').value = '';
        document.getElementById('pc_notes').value = '';

        // Store the data for submission
        this.currentAdjustmentData = {
            balanceRecord: balanceRecord,
            itemData: itemData,
            adjustmentType: 'PHYSICAL_COUNT'
        };
    }

    async submitPhysicalCountAdjustment() {
        try {
            // Validate form
            const form = document.getElementById('physicalCountForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form values
            const physicalCount = parseFloat(document.getElementById('pc_physcnt').value);
            const reasonCode = document.getElementById('pc_reason_code').value;
            const notes = document.getElementById('pc_notes').value;

            if (!this.currentAdjustmentData) {
                alert('Error: No adjustment data available');
                return;
            }

            // Create the specific payload structure for physical count adjustment
            const currentTimestamp = new Date().toISOString().slice(0, 19); // Format: "2021-09-24T09:16:12"

            const adjustmentData = [
                {
                    "_action": "AddChange",
                    "itemnum": this.currentAdjustmentData.itemData.itemnum,
                    "itemsetid": this.currentAdjustmentData.itemData.itemsetid || "ITEMSET",
                    "siteid": this.currentAdjustmentData.itemData.siteid,
                    "location": this.currentAdjustmentData.itemData.location,
                    "invbalances": [
                        {
                            "binnum": this.currentAdjustmentData.balanceRecord.binnum || "",
                            "physcnt": physicalCount,
                            "physcntdate": currentTimestamp,
                            "conditioncode": this.currentAdjustmentData.balanceRecord.conditioncode || "",
                            "memo": reasonCode
                        }
                    ]
                }
            ];

            console.log('🔍 PHYSICAL COUNT: Submitting adjustment:', adjustmentData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
            submitBtn.disabled = true;

            // Submit the adjustment to the new physical count endpoint
            const response = await fetch('/api/inventory/physical-count-adjustment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ PHYSICAL COUNT: Adjustment submitted successfully');
                console.log('✅ PHYSICAL COUNT: API Response:', result);
                this.showTemporaryMessage('Physical count adjustment submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('physicalCountModal'));
                modal.hide();

                // Clear the form
                document.getElementById('physicalCountForm').reset();
                this.currentAdjustmentData = null;

                // Automatically refresh the search results to show updated balances
                console.log('🔄 PHYSICAL COUNT: About to call refreshInventoryAfterAdjustment');
                this.refreshInventoryAfterAdjustment('Physical Count');
            } else {
                console.error('❌ PHYSICAL COUNT: Adjustment failed:', result.error);
                // Show detailed error in modal without closing it
                this.showModalError('physicalCountModal', result.error, result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ PHYSICAL COUNT: Error submitting adjustment:', error);
            // Show network error in modal without closing it
            this.showModalError('physicalCountModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Submit Adjustment';
            submitBtn.disabled = false;
        }
    }

    // Current Balance Adjustment Modal Functions
    openCurrentBalanceModal(balanceId, itemnum, siteid, inventoryid) {
        console.log(`🔍 CURRENT BALANCE: Opening modal for balance ID ${balanceId}, Item ${itemnum}, Site ${siteid}`);

        // Get the balance data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ CURRENT BALANCE: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let balanceRecord, itemData;
        try {
            const balanceDataAttr = button.getAttribute('data-balance-data');
            const itemDataAttr = button.getAttribute('data-item-data');

            if (!balanceDataAttr) {
                console.error('❌ CURRENT BALANCE: No balance data found on button');
                alert('Error: No balance data found on button');
                return;
            }

            balanceRecord = JSON.parse(balanceDataAttr);
            itemData = itemDataAttr ? JSON.parse(itemDataAttr) : {itemnum, siteid, inventoryid};

            console.log('🔍 CURRENT BALANCE: Balance record:', balanceRecord);
            console.log('🔍 CURRENT BALANCE: Item data:', itemData);

        } catch (error) {
            console.error('❌ CURRENT BALANCE: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populateCurrentBalanceModal(balanceRecord, itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('currentBalanceModal'));
        modal.show();
    }

    populateCurrentBalanceModal(balanceRecord, itemData) {
        // Populate readonly fields
        document.getElementById('cb_itemnum').value = itemData.itemnum || '';
        document.getElementById('cb_itemsetid').value = itemData.itemsetid || 'ITEMSET';
        document.getElementById('cb_siteid').value = itemData.siteid || '';
        document.getElementById('cb_location').value = itemData.location || balanceRecord.location || '';
        document.getElementById('cb_binnum').value = balanceRecord.binnum || '';
        document.getElementById('cb_conditioncode').value = balanceRecord.conditioncode || '';

        // Set current balance (readonly)
        const currentBalance = balanceRecord.curbal || 0;
        document.getElementById('cb_current_balance').value = this.formatNumber(currentBalance);

        // Clear editable fields
        document.getElementById('cb_new_balance').value = '';
        document.getElementById('cb_reason_code').value = '';
        document.getElementById('cb_adjustment_type').value = '';
        document.getElementById('cb_notes').value = '';

        // Store the data for submission
        this.currentAdjustmentData = {
            balanceRecord: balanceRecord,
            itemData: itemData,
            adjustmentType: 'CURRENT_BALANCE'
        };
    }

    async submitCurrentBalanceAdjustment() {
        try {
            // Validate form
            const form = document.getElementById('currentBalanceForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form values
            const newBalance = parseFloat(document.getElementById('cb_new_balance').value);
            const currentBalance = this.currentAdjustmentData.balanceRecord.curbal || 0;
            const reasonCode = document.getElementById('cb_reason_code').value;
            const adjustmentType = document.getElementById('cb_adjustment_type').value;
            const notes = document.getElementById('cb_notes').value;

            if (!this.currentAdjustmentData) {
                alert('Error: No adjustment data available');
                return;
            }

            // Create the specific payload structure for current balance adjustment
            const adjustmentData = [
                {
                    "_action": "AddChange",
                    "itemnum": this.currentAdjustmentData.itemData.itemnum,
                    "itemsetid": this.currentAdjustmentData.itemData.itemsetid || "ITEMSET",
                    "siteid": this.currentAdjustmentData.itemData.siteid,
                    "location": this.currentAdjustmentData.itemData.location,
                    "invbalances": [
                        {
                            "binnum": this.currentAdjustmentData.balanceRecord.binnum || "",
                            "curbal": newBalance,
                            "conditioncode": this.currentAdjustmentData.balanceRecord.conditioncode || "",
                            "memo": reasonCode
                        }
                    ]
                }
            ];

            console.log('🔍 CURRENT BALANCE: Submitting adjustment:', adjustmentData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
            submitBtn.disabled = true;

            // Submit the adjustment to the new current balance endpoint
            const response = await fetch('/api/inventory/current-balance-adjustment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ CURRENT BALANCE: Adjustment submitted successfully');
                console.log('✅ CURRENT BALANCE: API Response:', result);
                this.showTemporaryMessage('Current balance adjustment submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('currentBalanceModal'));
                modal.hide();

                // Clear the form
                document.getElementById('currentBalanceForm').reset();
                this.currentAdjustmentData = null;

                // Automatically refresh the search results to show updated balances
                console.log('🔄 CURRENT BALANCE: About to call refreshInventoryAfterAdjustment');
                this.refreshInventoryAfterAdjustment('Current Balance');
            } else {
                console.error('❌ CURRENT BALANCE: Adjustment failed:', result.error);
                // Show detailed error in modal without closing it
                this.showModalError('currentBalanceModal', result.error, result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ CURRENT BALANCE: Error submitting adjustment:', error);
            // Show network error in modal without closing it
            this.showModalError('currentBalanceModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Submit Adjustment';
            submitBtn.disabled = false;
        }
    }

    // No Balance Physical Count Modal Functions
    openNoBalancePhysicalCountModal(itemnum, siteid, inventoryid) {
        console.log(`🔍 NO-BALANCE PHYSICAL COUNT: Opening modal for Item ${itemnum}, Site ${siteid}`);

        // Get the item data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ NO-BALANCE PHYSICAL COUNT: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let itemData;
        try {
            const itemDataAttr = button.getAttribute('data-item-data');
            itemData = itemDataAttr ? JSON.parse(itemDataAttr) : {itemnum, siteid, inventoryid};

            console.log('🔍 NO-BALANCE PHYSICAL COUNT: Item data:', itemData);

        } catch (error) {
            console.error('❌ NO-BALANCE PHYSICAL COUNT: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populateNoBalancePhysicalCountModal(itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('noBalancePhysicalCountModal'));
        modal.show();
    }

    populateNoBalancePhysicalCountModal(itemData) {
        // Populate readonly fields
        document.getElementById('nbpc_itemnum').value = itemData.itemnum || '';
        document.getElementById('nbpc_itemsetid').value = itemData.itemsetid || 'ITEMSET';
        document.getElementById('nbpc_siteid').value = itemData.siteid || '';
        document.getElementById('nbpc_location').value = itemData.location || ''; // Auto-populate from inventory record

        // Clear editable fields
        document.getElementById('nbpc_binnum').value = '';
        document.getElementById('nbpc_conditioncode').value = 'A1';
        document.getElementById('nbpc_physcnt').value = '';
        document.getElementById('nbpc_reason_code').value = '';
        document.getElementById('nbpc_notes').value = '';

        // Store the data for submission
        this.currentNoBalanceData = {
            itemData: itemData,
            adjustmentType: 'NO_BALANCE_PHYSICAL_COUNT'
        };
    }

    async submitNoBalancePhysicalCount() {
        try {
            // Validate form
            const form = document.getElementById('noBalancePhysicalCountForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form values
            const location = document.getElementById('nbpc_location').value.trim(); // From inventory record
            const binnum = document.getElementById('nbpc_binnum').value.trim();
            const conditioncode = document.getElementById('nbpc_conditioncode').value.trim() || 'A1';
            const physicalCount = parseFloat(document.getElementById('nbpc_physcnt').value);
            const reasonCode = document.getElementById('nbpc_reason_code').value;
            const notes = document.getElementById('nbpc_notes').value.trim();

            if (!this.currentNoBalanceData) {
                alert('Error: No item data available');
                return;
            }

            // Create the specific payload structure for no-balance physical count
            const currentTimestamp = new Date().toISOString().slice(0, 19); // Format: "2021-09-24T09:16:12"

            // Check if reason code requires both fields to be populated
            const reasonCodesRequiringBothFields = ["INITIAL_BALANCE", "NEW_INVENTORY", "OPENING_BALANCE", "INITIAL_COUNT"];
            const shouldPopulateBothFields = reasonCodesRequiringBothFields.includes(reasonCode);

            // Build invbalances object based on requirements
            const invbalanceRecord = {
                "binnum": binnum,
                "physcnt": physicalCount,
                "physcntdate": currentTimestamp,
                "conditioncode": conditioncode,
                "memo": reasonCode,
                "reconciled": true
            };

            // Only add curbal if reason code requires both fields
            if (shouldPopulateBothFields) {
                invbalanceRecord.curbal = physicalCount; // Set equal to physical count
                console.log(`🔍 NO-BALANCE PHYSICAL COUNT: Reason code '${reasonCode}' requires both fields - adding curbal: ${physicalCount}`);
            } else {
                console.log(`🔍 NO-BALANCE PHYSICAL COUNT: Reason code '${reasonCode}' - omitting curbal field`);
            }

            const adjustmentData = [
                {
                    "_action": "AddChange",
                    "itemnum": this.currentNoBalanceData.itemData.itemnum,
                    "itemsetid": this.currentNoBalanceData.itemData.itemsetid || "ITEMSET",
                    "siteid": this.currentNoBalanceData.itemData.siteid,
                    "location": location,
                    "issueunit": "EA", // Default issue unit
                    "minlevel": 0,
                    "orderqty": 1,
                    "invbalances": [invbalanceRecord]
                }
            ];

            console.log('🔍 NO-BALANCE PHYSICAL COUNT: Submitting adjustment:', adjustmentData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';
            submitBtn.disabled = true;

            // Submit the adjustment to the new no-balance physical count endpoint
            const response = await fetch('/api/inventory/no-balance-physical-count', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ NO-BALANCE PHYSICAL COUNT: Record created successfully');
                console.log('✅ NO-BALANCE PHYSICAL COUNT: API Response:', result);
                this.showTemporaryMessage('Inventory record created successfully with physical count - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('noBalancePhysicalCountModal'));
                modal.hide();

                // Clear the form
                document.getElementById('noBalancePhysicalCountForm').reset();
                this.currentNoBalanceData = null;

                // Automatically refresh the search results to show new record
                console.log('🔄 NO-BALANCE PHYSICAL COUNT: About to call refreshInventoryAfterAdjustment');
                this.refreshInventoryAfterAdjustment('Physical Count Creation');
            } else {
                console.error('❌ NO-BALANCE PHYSICAL COUNT: Creation failed:', result.error);
                // Show detailed error in modal without closing it
                this.showModalError('noBalancePhysicalCountModal', result.error, result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ NO-BALANCE PHYSICAL COUNT: Error creating record:', error);
            // Show network error in modal without closing it
            this.showModalError('noBalancePhysicalCountModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Create Inventory Record';
            submitBtn.disabled = false;
        }
    }

    // No Balance Current Balance Modal Functions
    openNoBalanceCurrentBalanceModal(itemnum, siteid, inventoryid) {
        console.log(`🔍 NO-BALANCE CURRENT BALANCE: Opening modal for Item ${itemnum}, Site ${siteid}`);

        // Get the item data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ NO-BALANCE CURRENT BALANCE: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let itemData;
        try {
            const itemDataAttr = button.getAttribute('data-item-data');
            itemData = itemDataAttr ? JSON.parse(itemDataAttr) : {itemnum, siteid, inventoryid};

            console.log('🔍 NO-BALANCE CURRENT BALANCE: Item data:', itemData);

        } catch (error) {
            console.error('❌ NO-BALANCE CURRENT BALANCE: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populateNoBalanceCurrentBalanceModal(itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('noBalanceCurrentBalanceModal'));
        modal.show();
    }

    populateNoBalanceCurrentBalanceModal(itemData) {
        // Populate readonly fields
        document.getElementById('nbcb_itemnum').value = itemData.itemnum || '';
        document.getElementById('nbcb_itemsetid').value = itemData.itemsetid || 'ITEMSET';
        document.getElementById('nbcb_siteid').value = itemData.siteid || '';
        document.getElementById('nbcb_location').value = itemData.location || ''; // Auto-populate from inventory record

        // Clear editable fields
        document.getElementById('nbcb_binnum').value = '';
        document.getElementById('nbcb_conditioncode').value = 'A1';
        document.getElementById('nbcb_curbal').value = '';
        document.getElementById('nbcb_reason_code').value = '';
        document.getElementById('nbcb_notes').value = '';

        // Store the data for submission
        this.currentNoBalanceData = {
            itemData: itemData,
            adjustmentType: 'NO_BALANCE_CURRENT_BALANCE'
        };
    }

    async submitNoBalanceCurrentBalance() {
        try {
            // Validate form
            const form = document.getElementById('noBalanceCurrentBalanceForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form values
            const location = document.getElementById('nbcb_location').value.trim(); // From inventory record
            const binnum = document.getElementById('nbcb_binnum').value.trim();
            const conditioncode = document.getElementById('nbcb_conditioncode').value.trim() || 'A1';
            const currentBalance = parseFloat(document.getElementById('nbcb_curbal').value);
            const reasonCode = document.getElementById('nbcb_reason_code').value;
            const notes = document.getElementById('nbcb_notes').value.trim();

            if (!this.currentNoBalanceData) {
                alert('Error: No item data available');
                return;
            }

            // Create the specific payload structure for no-balance current balance
            // Check if reason code requires both fields to be populated
            const reasonCodesRequiringBothFields = ["INITIAL_BALANCE", "NEW_INVENTORY", "OPENING_BALANCE", "INITIAL_COUNT"];
            const shouldPopulateBothFields = reasonCodesRequiringBothFields.includes(reasonCode);

            // Build invbalances object based on requirements
            const invbalanceRecord = {
                "binnum": binnum,
                "curbal": currentBalance,
                "conditioncode": conditioncode,
                "memo": reasonCode,
                "reconciled": true
            };

            // Only add physcnt and physcntdate if reason code requires both fields
            if (shouldPopulateBothFields) {
                invbalanceRecord.physcnt = currentBalance; // Set equal to current balance
                invbalanceRecord.physcntdate = new Date().toISOString().slice(0, 19);
                console.log(`🔍 NO-BALANCE CURRENT BALANCE: Reason code '${reasonCode}' requires both fields - adding physcnt: ${currentBalance}`);
            } else {
                console.log(`🔍 NO-BALANCE CURRENT BALANCE: Reason code '${reasonCode}' - omitting physcnt and physcntdate fields`);
            }

            const adjustmentData = [
                {
                    "_action": "AddChange",
                    "itemnum": this.currentNoBalanceData.itemData.itemnum,
                    "itemsetid": this.currentNoBalanceData.itemData.itemsetid || "ITEMSET",
                    "siteid": this.currentNoBalanceData.itemData.siteid,
                    "location": location,
                    "issueunit": "EA", // Default issue unit
                    "minlevel": 0,
                    "orderqty": 1,
                    "invbalances": [invbalanceRecord]
                }
            ];

            console.log('🔍 NO-BALANCE CURRENT BALANCE: Submitting adjustment:', adjustmentData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';
            submitBtn.disabled = true;

            // Submit the adjustment to the new no-balance current balance endpoint
            const response = await fetch('/api/inventory/no-balance-current-balance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ NO-BALANCE CURRENT BALANCE: Record created successfully');
                console.log('✅ NO-BALANCE CURRENT BALANCE: API Response:', result);
                this.showTemporaryMessage('Inventory record created successfully with current balance - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('noBalanceCurrentBalanceModal'));
                modal.hide();

                // Clear the form
                document.getElementById('noBalanceCurrentBalanceForm').reset();
                this.currentNoBalanceData = null;

                // Automatically refresh the search results to show new record
                console.log('🔄 NO-BALANCE CURRENT BALANCE: About to call refreshInventoryAfterAdjustment');
                this.refreshInventoryAfterAdjustment('Current Balance Creation');
            } else {
                console.error('❌ NO-BALANCE CURRENT BALANCE: Creation failed:', result.error);
                // Show detailed error in modal without closing it
                this.showModalError('noBalanceCurrentBalanceModal', result.error, result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ NO-BALANCE CURRENT BALANCE: Error creating record:', error);
            // Show network error in modal without closing it
            this.showModalError('noBalanceCurrentBalanceModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Create Inventory Record';
            submitBtn.disabled = false;
        }
    }

    // Enhanced Error Handling Functions
    showModalError(modalId, errorMessage, fullResponse = null) {

        // Remove any existing error alerts
        const modal = document.getElementById(modalId);
        const existingAlerts = modal.querySelectorAll('.error-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create detailed error message - extract exact Maximo error responses
        let detailedMessage = errorMessage;
        if (fullResponse) {
            // Check for different Maximo error response formats
            if (fullResponse.response) {
                const maximoResponse = fullResponse.response;

                // Format 1: Direct Error object
                if (maximoResponse.Error && maximoResponse.Error.message) {
                    detailedMessage = maximoResponse.Error.message;
                }
                // Format 2: OSLC Error format
                else if (maximoResponse['oslc:Error']) {
                    const oslcError = maximoResponse['oslc:Error'];
                    if (oslcError['oslc:message']) {
                        detailedMessage = oslcError['oslc:message'];
                    }
                    if (oslcError['spi:reasonCode']) {
                        detailedMessage = `${oslcError['spi:reasonCode']}: ${detailedMessage}`;
                    }
                }
                // Format 3: Array with error objects
                else if (Array.isArray(maximoResponse) && maximoResponse.length > 0) {
                    const firstItem = maximoResponse[0];
                    if (firstItem.Error && firstItem.Error.message) {
                        detailedMessage = firstItem.Error.message;
                    } else if (firstItem['oslc:Error']) {
                        const oslcError = firstItem['oslc:Error'];
                        if (oslcError['oslc:message']) {
                            detailedMessage = oslcError['oslc:message'];
                        }
                    }
                }
            }
            // Check if the error message itself contains structured error info
            else if (typeof fullResponse.error === 'string' && fullResponse.error.includes('Error')) {
                detailedMessage = fullResponse.error;
            }
        }

        // Create error alert element
        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger error-alert mb-3';
        errorAlert.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                <div class="flex-grow-1">
                    <strong>Submission Failed</strong><br>
                    <span class="error-message">${this.escapeHtml(detailedMessage)}</span>
                    ${fullResponse ? `
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.closest('.error-alert').querySelector('.error-details').style.display = this.closest('.error-alert').querySelector('.error-details').style.display === 'none' ? 'block' : 'none'">
                                <i class="fas fa-info-circle me-1"></i>Show Details
                            </button>
                            <div class="error-details mt-2" style="display: none;">
                                <small class="text-muted">
                                    <strong>Full Response:</strong><br>
                                    <pre class="bg-light p-2 rounded" style="font-size: 0.75rem; max-height: 200px; overflow-y: auto;">${this.escapeHtml(JSON.stringify(fullResponse, null, 2))}</pre>
                                </small>
                            </div>
                        </div>
                    ` : ''}
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            Please review your input and try again. The form data has been preserved.
                        </small>
                    </div>
                </div>
                <button type="button" class="btn-close" onclick="this.closest('.error-alert').remove()"></button>
            </div>
        `;

        // Insert error alert at the top of modal body
        const modalBody = modal.querySelector('.modal-body');
        modalBody.insertBefore(errorAlert, modalBody.firstChild);

        // Scroll to top of modal to show error
        modalBody.scrollTop = 0;

        // Also show a brief toast message
        this.showTemporaryMessage('Submission failed - please check the form for details', 'danger');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Average Cost Adjustment Modal Functions
    openAvgCostModal(itemnum, siteid, inventoryid) {
        console.log(`🔍 AVG COST: Opening modal for Item ${itemnum}, Site ${siteid}`);

        // Get the item data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ AVG COST: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let itemData;
        try {
            const itemDataAttr = button.getAttribute('data-item-data');

            if (!itemDataAttr) {
                console.error('❌ AVG COST: No item data found on button');
                alert('Error: No item data found on button');
                return;
            }

            itemData = JSON.parse(itemDataAttr);
            console.log('🔍 AVG COST: Item data:', itemData);

        } catch (error) {
            console.error('❌ AVG COST: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populateAvgCostModal(itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('avgCostModal'));
        modal.show();
    }

    populateAvgCostModal(itemData) {

        // Populate readonly fields
        document.getElementById('ac_itemnum').value = itemData.itemnum || '';
        document.getElementById('ac_itemsetid').value = itemData.itemsetid || 'ITEMSET';
        document.getElementById('ac_siteid').value = itemData.siteid || '';
        document.getElementById('ac_location').value = itemData.location || '';

        // Get cost data - the inventory service flattens the invcost array into individual fields
        // So we need to look for avgcost, stdcost, lastcost directly on the item, not in invcost array
        const currentAvgCost = itemData.avgcost;
        const conditionCode = itemData.conditioncode || 'A1'; // Default to A1 if not found

        document.getElementById('ac_conditioncode').value = conditionCode;
        document.getElementById('ac_current_avgcost').value = currentAvgCost ? `$${parseFloat(currentAvgCost).toFixed(2)}` : '$0.00';

        console.log('🔍 AVG COST: Current avgcost:', currentAvgCost);
        console.log('🔍 AVG COST: Condition code:', conditionCode);

        // Clear editable fields
        document.getElementById('ac_new_avgcost').value = '';
        document.getElementById('ac_notes').value = '';

        // Store the data for submission - create a cost data object for consistency
        const costData = {
            avgcost: currentAvgCost,
            stdcost: itemData.stdcost,
            lastcost: itemData.lastcost,
            conditioncode: conditionCode
        };

        this.currentCostAdjustmentData = {
            itemData: itemData,
            costData: costData,
            adjustmentType: 'AVGCOST'
        };

        console.log('🔍 AVG COST: Stored adjustment data:', this.currentCostAdjustmentData);
    }

    async submitAvgCostAdjustment() {
        try {
            // Validate form
            const form = document.getElementById('avgCostForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form values
            const newAvgCost = parseFloat(document.getElementById('ac_new_avgcost').value);
            const notes = document.getElementById('ac_notes').value;

            if (!this.currentCostAdjustmentData) {
                alert('Error: No adjustment data available');
                return;
            }

            // Create the specific payload structure for average cost adjustment
            const adjustmentData = [
                {
                    "_action": "AddChange",
                    "itemnum": this.currentCostAdjustmentData.itemData.itemnum,
                    "itemsetid": this.currentCostAdjustmentData.itemData.itemsetid || "ITEMSET",
                    "siteid": this.currentCostAdjustmentData.itemData.siteid,
                    "location": this.currentCostAdjustmentData.itemData.location,
                    "invcost": [
                        {
                            "avgcost": newAvgCost.toString(),
                            "conditioncode": this.currentCostAdjustmentData.costData.conditioncode || ""
                        }
                    ]
                }
            ];

            console.log('🔍 AVG COST: Submitting adjustment:', adjustmentData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
            submitBtn.disabled = true;

            // Submit the adjustment to the new average cost endpoint
            const response = await fetch('/api/inventory/avgcost-adjustment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ AVG COST: Adjustment submitted successfully');
                console.log('✅ AVG COST: API Response:', result);
                this.showTemporaryMessage('Average cost adjustment submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('avgCostModal'));
                modal.hide();

                // Clear the form
                document.getElementById('avgCostForm').reset();
                this.currentCostAdjustmentData = null;

                // Automatically refresh the search results to show updated costs
                console.log('🔄 AVG COST: About to call refreshInventoryAfterAdjustment');
                this.refreshInventoryAfterAdjustment('Average Cost');
            } else {
                console.error('❌ AVG COST: Adjustment failed:', result.error);
                this.showTemporaryMessage(`Adjustment failed: ${result.error}`, 'danger');
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ AVG COST: Error submitting adjustment:', error);
            this.showTemporaryMessage('Error submitting adjustment: ' + error.message, 'danger');

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Submit Adjustment';
            submitBtn.disabled = false;
        }
    }

    // Standard Cost Adjustment Modal Functions
    openStdCostModal(itemnum, siteid, inventoryid) {
        console.log(`🔍 STD COST: Opening modal for Item ${itemnum}, Site ${siteid}`);

        // Get the item data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ STD COST: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let itemData;
        try {
            const itemDataAttr = button.getAttribute('data-item-data');

            if (!itemDataAttr) {
                console.error('❌ STD COST: No item data found on button');
                alert('Error: No item data found on button');
                return;
            }

            itemData = JSON.parse(itemDataAttr);
            console.log('🔍 STD COST: Item data:', itemData);

        } catch (error) {
            console.error('❌ STD COST: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populateStdCostModal(itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('stdCostModal'));
        modal.show();
    }

    populateStdCostModal(itemData) {

        // Populate readonly fields
        document.getElementById('sc_itemnum').value = itemData.itemnum || '';
        document.getElementById('sc_itemsetid').value = itemData.itemsetid || 'ITEMSET';
        document.getElementById('sc_siteid').value = itemData.siteid || '';
        document.getElementById('sc_location').value = itemData.location || '';

        // Get cost data - the inventory service flattens the invcost array into individual fields
        // So we need to look for avgcost, stdcost, lastcost directly on the item, not in invcost array
        const currentStdCost = itemData.stdcost;
        const conditionCode = itemData.conditioncode || 'A1'; // Default to A1 if not found

        document.getElementById('sc_conditioncode').value = conditionCode;
        document.getElementById('sc_current_stdcost').value = currentStdCost ? `$${parseFloat(currentStdCost).toFixed(2)}` : '$0.00';

        console.log('🔍 STD COST: Current stdcost:', currentStdCost);
        console.log('🔍 STD COST: Condition code:', conditionCode);

        // Clear editable fields
        document.getElementById('sc_new_stdcost').value = '';
        document.getElementById('sc_notes').value = '';

        // Store the data for submission - create a cost data object for consistency
        const costData = {
            avgcost: itemData.avgcost,
            stdcost: currentStdCost,
            lastcost: itemData.lastcost,
            conditioncode: conditionCode
        };

        this.currentCostAdjustmentData = {
            itemData: itemData,
            costData: costData,
            adjustmentType: 'STDCOST'
        };

        console.log('🔍 STD COST: Stored adjustment data:', this.currentCostAdjustmentData);
    }

    async submitStdCostAdjustment() {
        try {
            // Validate form
            const form = document.getElementById('stdCostForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form values
            const newStdCost = parseFloat(document.getElementById('sc_new_stdcost').value);
            const notes = document.getElementById('sc_notes').value;

            if (!this.currentCostAdjustmentData) {
                alert('Error: No adjustment data available');
                return;
            }

            // Create the specific payload structure for standard cost adjustment
            const adjustmentData = [
                {
                    "_action": "AddChange",
                    "itemnum": this.currentCostAdjustmentData.itemData.itemnum,
                    "itemsetid": this.currentCostAdjustmentData.itemData.itemsetid || "ITEMSET",
                    "siteid": this.currentCostAdjustmentData.itemData.siteid,
                    "location": this.currentCostAdjustmentData.itemData.location,
                    "invcost": [
                        {
                            "stdcost": newStdCost,
                            "conditioncode": this.currentCostAdjustmentData.costData.conditioncode || ""
                        }
                    ]
                }
            ];

            console.log('🔍 STD COST: Submitting adjustment:', adjustmentData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
            submitBtn.disabled = true;

            // Submit the adjustment to the new standard cost endpoint
            const response = await fetch('/api/inventory/stdcost-adjustment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adjustmentData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ STD COST: Adjustment submitted successfully');
                console.log('✅ STD COST: API Response:', result);
                this.showTemporaryMessage('Standard cost adjustment submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('stdCostModal'));
                modal.hide();

                // Clear the form
                document.getElementById('stdCostForm').reset();
                this.currentCostAdjustmentData = null;

                // Automatically refresh the search results to show updated costs
                console.log('🔄 STD COST: About to call refreshInventoryAfterAdjustment');
                this.refreshInventoryAfterAdjustment('Standard Cost');
            } else {
                console.error('❌ STD COST: Adjustment failed:', result.error);
                this.showTemporaryMessage(`Adjustment failed: ${result.error}`, 'danger');
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ STD COST: Error submitting adjustment:', error);
            this.showTemporaryMessage('Error submitting adjustment: ' + error.message, 'danger');

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Submit Adjustment';
            submitBtn.disabled = false;
        }
    }

    /**
     * Automatically refresh inventory search results after successful adjustment
     * Preserves current search state and shows loading indicators
     */
    async refreshInventoryAfterAdjustment(adjustmentType) {
        try {
            console.log(`🔄 REFRESH: Starting automatic refresh after ${adjustmentType} adjustment`);

            // Check if we have an active search to refresh
            // We need either a search term or a site selected
            if (!this.currentSearchTerm && !this.currentSiteId) {
                console.log('🔄 REFRESH: No active search to refresh - no search term or site selected');
                console.log(`🔄 REFRESH: currentSearchTerm: "${this.currentSearchTerm}", currentSiteId: "${this.currentSiteId}"`);
                return;
            }

            // If we have a site but no search term, we still want to refresh if there are results displayed
            const resultsContainer = document.getElementById('inventorySearchResults');
            const hasResults = resultsContainer && resultsContainer.children.length > 0;

            if (!this.currentSearchTerm && !hasResults) {
                console.log('🔄 REFRESH: No search term and no results to refresh');
                return;
            }

            console.log(`🔄 REFRESH: Current state - searchTerm: "${this.currentSearchTerm}", siteId: "${this.currentSiteId}", page: ${this.currentPage}`);

            // Show loading indicator in the results area
            this.showRefreshLoadingIndicator();

            // Small delay to ensure the backend has processed the adjustment
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Perform the search with current parameters
            // Use the current page and search term that are already stored
            console.log(`🔄 REFRESH: Calling performSearch with page ${this.currentPage}`);
            await this.performSearch(this.currentPage);

            // Update success message to indicate refresh completion
            this.showTemporaryMessage(`${adjustmentType} adjustment completed and inventory data refreshed`, 'success');

            console.log(`✅ REFRESH: Successfully refreshed inventory after ${adjustmentType} adjustment`);

        } catch (error) {
            console.error(`❌ REFRESH: Error refreshing inventory after ${adjustmentType} adjustment:`, error);
            this.showTemporaryMessage(`${adjustmentType} adjustment completed, but failed to refresh inventory data. Please refresh manually.`, 'warning');
        }
    }

    /**
     * Show loading indicator during refresh
     */
    showRefreshLoadingIndicator() {
        const resultsContainer = document.getElementById('searchResults');
        if (resultsContainer) {
            // Add a subtle loading overlay to the existing results
            const loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'refreshLoadingOverlay';
            loadingOverlay.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
            loadingOverlay.style.cssText = `
                background: rgba(255, 255, 255, 0.8);
                z-index: 1000;
                backdrop-filter: blur(2px);
            `;
            loadingOverlay.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="small text-muted">Refreshing inventory data...</div>
                </div>
            `;

            // Make results container relative if it isn't already
            if (getComputedStyle(resultsContainer).position === 'static') {
                resultsContainer.style.position = 'relative';
            }

            resultsContainer.appendChild(loadingOverlay);

            // Remove the overlay after a maximum of 10 seconds (fallback)
            setTimeout(() => {
                const overlay = document.getElementById('refreshLoadingOverlay');
                if (overlay) {
                    overlay.remove();
                }
            }, 10000);
        }
    }

    /**
     * Remove the refresh loading indicator
     */
    hideRefreshLoadingIndicator() {
        const overlay = document.getElementById('refreshLoadingOverlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // Transfer Current Item Modal Functions
    openTransferCurrentItemModal(itemnum, siteid, inventoryid) {
        console.log(`🔄 TRANSFER: Opening modal for item ${itemnum}, site ${siteid}, inventory ${inventoryid}`);

        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ TRANSFER: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let itemData;
        try {
            const itemDataAttr = button.getAttribute('data-item-data');
            if (!itemDataAttr) {
                console.error('❌ TRANSFER: No item data found on button');
                alert('Error: No item data found on button');
                return;
            }

            itemData = JSON.parse(itemDataAttr);
            console.log('🔄 TRANSFER: Parsed item data:', itemData);

        } catch (error) {
            console.error('❌ TRANSFER: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Populate the modal fields
        this.populateTransferCurrentItemModal(itemData);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('transferCurrentItemModal'));
        modal.show();
    }

    populateTransferCurrentItemModal(itemData) {

        // Populate read-only item information
        document.getElementById('tci_itemnum').value = itemData.itemnum || '';
        document.getElementById('tci_description').value = itemData.description || '';
        document.getElementById('tci_from_storeroom').value = itemData.location || '';
        document.getElementById('tci_from_issue_unit').value = itemData.issueunit || 'EA';

        // Reset form fields
        document.getElementById('tci_quantity').value = '1.00';
        document.getElementById('tci_conversion_factor').value = '1';

        // Clear dropdowns
        this.clearTransferDropdowns();

        // Load sites for destination selection
        this.loadSitesForTransfer();

        // Load current location data for from dropdowns
        this.loadFromLocationData(itemData);

        // Store the data for submission
        this.currentTransferData = {
            itemData: itemData,
            transferType: 'TRANSFER_CURRENT_ITEM'
        };

        console.log('🔄 TRANSFER: Stored transfer data:', this.currentTransferData);

        // Update button visibility based on transfer type
        this.updateTransferButtonVisibility();
    }

    updateTransferButtonVisibility() {
        const sameSiteBtn = document.getElementById('sameSiteTransferBtn');
        const crossSiteBtn = document.getElementById('crossSiteTransferBtn');

        if (!this.currentTransferData) {
            // Show both buttons if no data
            if (sameSiteBtn) sameSiteBtn.style.display = 'inline-block';
            if (crossSiteBtn) crossSiteBtn.style.display = 'inline-block';
            return;
        }

        // Get current site selection
        const toSiteSelect = document.getElementById('tci_to_site');
        const fromSite = this.currentTransferData.itemData.siteid;

        // Add event listener to update buttons when destination site changes
        if (toSiteSelect) {
            toSiteSelect.addEventListener('change', () => {
                const toSite = toSiteSelect.value;

                if (fromSite === toSite) {
                    // Same site transfer - ENABLE Same Site, DISABLE Cross Site
                    if (sameSiteBtn) {
                        sameSiteBtn.style.display = 'inline-block';
                        sameSiteBtn.disabled = false;
                        sameSiteBtn.classList.add('btn-success');
                        sameSiteBtn.classList.remove('btn-outline-success');
                        sameSiteBtn.title = 'Transfer within the same site - RECOMMENDED';
                    }
                    if (crossSiteBtn) {
                        crossSiteBtn.style.display = 'inline-block';
                        crossSiteBtn.disabled = true;
                        crossSiteBtn.classList.add('btn-outline-secondary');
                        crossSiteBtn.classList.remove('btn-primary', 'btn-outline-primary');
                        crossSiteBtn.title = 'Cross-site transfer disabled (same site selected)';
                    }
                } else if (toSite) {
                    // Cross site transfer - ENABLE Cross Site, DISABLE Same Site
                    if (sameSiteBtn) {
                        sameSiteBtn.style.display = 'inline-block';
                        sameSiteBtn.disabled = true;
                        sameSiteBtn.classList.add('btn-outline-secondary');
                        sameSiteBtn.classList.remove('btn-success', 'btn-outline-success');
                        sameSiteBtn.title = 'Same-site transfer disabled (different site selected)';
                    }
                    if (crossSiteBtn) {
                        crossSiteBtn.style.display = 'inline-block';
                        crossSiteBtn.disabled = false;
                        crossSiteBtn.classList.add('btn-primary');
                        crossSiteBtn.classList.remove('btn-outline-primary', 'btn-outline-secondary');
                        crossSiteBtn.title = 'Transfer between different sites - RECOMMENDED';
                    }
                } else {
                    // No destination selected - show both enabled
                    if (sameSiteBtn) {
                        sameSiteBtn.style.display = 'inline-block';
                        sameSiteBtn.disabled = false;
                        sameSiteBtn.classList.add('btn-outline-success');
                        sameSiteBtn.classList.remove('btn-success', 'btn-outline-secondary');
                        sameSiteBtn.title = 'Transfer within the same site';
                    }
                    if (crossSiteBtn) {
                        crossSiteBtn.style.display = 'inline-block';
                        crossSiteBtn.disabled = false;
                        crossSiteBtn.classList.add('btn-outline-primary');
                        crossSiteBtn.classList.remove('btn-primary', 'btn-outline-secondary');
                        crossSiteBtn.title = 'Transfer between different sites';
                    }
                }
            });
        }

        // Initial button state - show both
        if (sameSiteBtn) sameSiteBtn.style.display = 'inline-block';
        if (crossSiteBtn) crossSiteBtn.style.display = 'inline-block';
    }

    clearTransferDropdowns() {
        // Clear all dropdowns
        const dropdowns = [
            'tci_to_site', 'tci_to_storeroom', 'tci_from_bin', 'tci_to_bin',
            'tci_from_lot', 'tci_to_lot', 'tci_from_condition', 'tci_to_condition', 'tci_to_issue_unit'
        ];

        dropdowns.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = '<option value="">Loading...</option>';
            }
        });
    }

    async loadSitesForTransfer() {
        try {
            const response = await fetch('/api/enhanced-workorders/available-sites');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateTransferSiteDropdown(result.sites);
                console.log(`🏢 TRANSFER: Loaded ${result.sites.length} sites for transfer`);
            } else {
                console.error('Failed to load sites for transfer:', result.error);
                this.showTransferError('Failed to load available sites');
            }
        } catch (error) {
            console.error('Error loading sites for transfer:', error);
            this.showTransferError('Error loading available sites');
        }
    }

    populateTransferSiteDropdown(sites) {
        const siteSelect = document.getElementById('tci_to_site');
        siteSelect.innerHTML = '<option value="">Select destination site...</option>';

        sites.forEach(site => {
            const option = document.createElement('option');
            option.value = site.siteid;
            option.textContent = `${site.siteid}${site.description !== site.siteid ? ' - ' + site.description : ''}`;
            siteSelect.appendChild(option);
        });

        // Add event listener for site change
        siteSelect.addEventListener('change', (e) => {
            this.onTransferSiteChange(e.target.value);
        });
    }

    async onTransferSiteChange(siteid) {
        console.log(`🏢 TRANSFER: Site changed to: ${siteid}`);

        if (!siteid) {
            document.getElementById('tci_to_storeroom').innerHTML = '<option value="">Select destination storeroom...</option>';
            return;
        }

        // Load storerooms for the selected site
        await this.loadStoreroomsForTransfer(siteid);
    }

    async loadStoreroomsForTransfer(siteid) {
        try {
            const response = await fetch(`/api/inventory/transfer-current-item/storerooms?siteid=${encodeURIComponent(siteid)}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateTransferStoreroomDropdown(result.storerooms);
                console.log(`🏢 TRANSFER: Loaded ${result.storerooms.length} storerooms for site ${siteid}`);
            } else {
                console.error('Failed to load storerooms for transfer:', result.error);
                this.showTransferError('Failed to load storerooms for selected site');
            }
        } catch (error) {
            console.error('Error loading storerooms for transfer:', error);
            this.showTransferError('Error loading storerooms');
        }
    }

    populateTransferStoreroomDropdown(storerooms) {
        const storeroomSelect = document.getElementById('tci_to_storeroom');
        storeroomSelect.innerHTML = '<option value="">Select destination storeroom...</option>';

        storerooms.forEach(storeroom => {
            const option = document.createElement('option');
            option.value = storeroom.location;
            option.textContent = storeroom.description || storeroom.location;
            storeroomSelect.appendChild(option);
        });

        // Add event listener for storeroom change
        storeroomSelect.addEventListener('change', (e) => {
            this.onTransferStoreroomChange(e.target.value);
        });
    }

    async onTransferStoreroomChange(location) {
        console.log(`🏢 TRANSFER: Storeroom changed to: ${location}`);

        if (!location) {
            this.clearToLocationDropdowns();
            return;
        }

        const siteid = document.getElementById('tci_to_site').value;
        if (siteid && location) {
            await this.loadToLocationData(siteid, location);
        }
    }

    clearToLocationDropdowns() {
        const dropdowns = ['tci_to_bin', 'tci_to_lot', 'tci_to_condition'];
        dropdowns.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = '<option value="">Select...</option>';
            }
        });
    }

    async loadFromLocationData(itemData) {
        // Load bins, lots, and conditions from current inventory balances
        if (itemData.invbalances_records && itemData.invbalances_records.length > 0) {
            this.populateFromDropdowns(itemData.invbalances_records);
        }

        // Load issue units for the item
        await this.loadIssueUnitsForTransfer(itemData.itemnum);
    }

    populateFromDropdowns(balanceRecords) {
        const bins = new Set();
        const lots = new Set();
        const conditions = new Set();

        balanceRecords.forEach(balance => {
            if (balance.binnum) bins.add(balance.binnum);
            if (balance.lotnum) lots.add(balance.lotnum);
            if (balance.conditioncode) conditions.add(balance.conditioncode);
        });

        // Populate from bins
        const fromBinSelect = document.getElementById('tci_from_bin');
        fromBinSelect.innerHTML = '<option value="">Select from bin...</option>';
        Array.from(bins).sort().forEach(bin => {
            const option = document.createElement('option');
            option.value = bin;
            option.textContent = bin;
            fromBinSelect.appendChild(option);
        });

        // Populate from lots
        const fromLotSelect = document.getElementById('tci_from_lot');
        fromLotSelect.innerHTML = '<option value="">Select from lot...</option>';
        Array.from(lots).sort().forEach(lot => {
            const option = document.createElement('option');
            option.value = lot;
            option.textContent = lot;
            fromLotSelect.appendChild(option);
        });

        // Populate from conditions
        const fromConditionSelect = document.getElementById('tci_from_condition');
        fromConditionSelect.innerHTML = '<option value="">Select from condition...</option>';
        Array.from(conditions).sort().forEach(condition => {
            const option = document.createElement('option');
            option.value = condition;
            option.textContent = condition;
            fromConditionSelect.appendChild(option);
        });
    }

    async loadToLocationData(siteid, location) {
        try {
            const response = await fetch(`/api/inventory/transfer-current-item/bins-lots-conditions?siteid=${encodeURIComponent(siteid)}&location=${encodeURIComponent(location)}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateToDropdowns(result.data);
                console.log(`📦 TRANSFER: Loaded location data for ${siteid}/${location}`);
            } else {
                console.error('Failed to load location data for transfer:', result.error);
                this.showTransferError('Failed to load location data');
            }
        } catch (error) {
            console.error('Error loading location data for transfer:', error);
            this.showTransferError('Error loading location data');
        }
    }

    populateToDropdowns(data) {
        // Populate to bins
        const toBinSelect = document.getElementById('tci_to_bin');
        toBinSelect.innerHTML = '<option value="">Select to bin...</option>';
        data.bins.forEach(bin => {
            const option = document.createElement('option');
            option.value = bin;
            option.textContent = bin;
            toBinSelect.appendChild(option);
        });

        // Populate to lots
        const toLotSelect = document.getElementById('tci_to_lot');
        toLotSelect.innerHTML = '<option value="">Select to lot...</option>';
        data.lots.forEach(lot => {
            const option = document.createElement('option');
            option.value = lot;
            option.textContent = lot;
            toLotSelect.appendChild(option);
        });

        // Populate to conditions
        const toConditionSelect = document.getElementById('tci_to_condition');
        toConditionSelect.innerHTML = '<option value="">Select to condition...</option>';
        data.conditions.forEach(condition => {
            const option = document.createElement('option');
            option.value = condition;
            option.textContent = condition;
            toConditionSelect.appendChild(option);
        });
    }

    async loadIssueUnitsForTransfer(itemnum) {
        try {
            const response = await fetch(`/api/inventory/transfer-current-item/issue-units?itemnum=${encodeURIComponent(itemnum)}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateIssueUnitsDropdown(result.units);
                console.log(`🔧 TRANSFER: Loaded ${result.units.length} issue units for item ${itemnum}`);
            } else {
                console.error('Failed to load issue units for transfer:', result.error);
                this.showTransferError('Failed to load issue units');
            }
        } catch (error) {
            console.error('Error loading issue units for transfer:', error);
            this.showTransferError('Error loading issue units');
        }
    }

    populateIssueUnitsDropdown(units) {
        const toIssueUnitSelect = document.getElementById('tci_to_issue_unit');
        toIssueUnitSelect.innerHTML = '';

        units.forEach(unit => {
            const option = document.createElement('option');
            option.value = unit;
            option.textContent = unit;
            toIssueUnitSelect.appendChild(option);
        });

        // Set default to EA if available
        if (units.includes('EA')) {
            toIssueUnitSelect.value = 'EA';
        }
    }

    async submitTransferCurrentItem() {
        try {
            // Validate form
            const form = document.getElementById('transferCurrentItemForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!this.currentTransferData) {
                alert('Error: No transfer data available');
                return;
            }

            // Get form values
            const transferData = this.buildTransferPayload();

            console.log('🔄 TRANSFER: Submitting transfer:', transferData);

            // Show loading state
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
            submitBtn.disabled = true;

            // Submit the transfer
            const response = await fetch('/api/inventory/transfer-current-item', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(transferData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ TRANSFER: Transfer submitted successfully');
                console.log('✅ TRANSFER: API Response:', result);
                this.showTemporaryMessage('Transfer submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('transferCurrentItemModal'));
                modal.hide();

                // Clear the form
                document.getElementById('transferCurrentItemForm').reset();
                this.currentTransferData = null;

                // Refresh inventory data
                this.refreshInventoryAfterAdjustment('Transfer');
            } else {
                console.error('❌ TRANSFER: Transfer failed:', result.error);
                this.showModalError('transferCurrentItemModal', result.error, result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ TRANSFER: Error submitting transfer:', error);
            this.showModalError('transferCurrentItemModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = event.target;
            submitBtn.innerHTML = '<i class="fas fa-exchange-alt me-1"></i>Submit Transfer';
            submitBtn.disabled = false;
        }
    }

    async submitSameSiteTransfer() {
        try {
            // Validate form
            const form = document.getElementById('transferCurrentItemForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!this.currentTransferData) {
                alert('Error: No transfer data available');
                return;
            }

            // Get form values
            const transferData = this.buildTransferPayload();
            transferData.transfer_type = 'same_site';

            console.log('🔄 SAME SITE TRANSFER: Submitting transfer:', transferData);

            // Show loading state
            const submitBtn = document.getElementById('sameSiteTransferBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            submitBtn.disabled = true;

            // Submit the same-site transfer
            const response = await fetch('/api/inventory/transfer-same-site', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(transferData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ SAME SITE TRANSFER: Transfer submitted successfully');
                console.log('✅ SAME SITE TRANSFER: API Response:', result);
                this.showTemporaryMessage('Same-site transfer submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('transferCurrentItemModal'));
                modal.hide();

                // Clear the form
                document.getElementById('transferCurrentItemForm').reset();
                this.currentTransferData = null;

                // Refresh inventory data
                this.refreshInventoryAfterAdjustment('Same Site Transfer');
            } else {
                console.error('❌ SAME SITE TRANSFER: Transfer failed:', result.error);
                this.showDetailedTransferError('transferCurrentItemModal', result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ SAME SITE TRANSFER: Error submitting transfer:', error);
            this.showModalError('transferCurrentItemModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = document.getElementById('sameSiteTransferBtn');
            submitBtn.innerHTML = '<i class="fas fa-building me-1"></i>Same Site Transfer';
            submitBtn.disabled = false;
        }
    }

    async submitCrossSiteTransfer() {
        try {
            // Validate form
            const form = document.getElementById('transferCurrentItemForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!this.currentTransferData) {
                alert('Error: No transfer data available');
                return;
            }

            // Get form values
            const transferData = this.buildTransferPayload();
            transferData.transfer_type = 'cross_site';

            console.log('🔄 CROSS SITE TRANSFER: Submitting transfer:', transferData);

            // Show loading state
            const submitBtn = document.getElementById('crossSiteTransferBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            submitBtn.disabled = true;

            // Submit the cross-site transfer
            const response = await fetch('/api/inventory/transfer-cross-site', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(transferData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ CROSS SITE TRANSFER: Transfer submitted successfully');
                console.log('✅ CROSS SITE TRANSFER: API Response:', result);
                this.showTemporaryMessage('Cross-site transfer submitted successfully - refreshing inventory data...', 'success');

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('transferCurrentItemModal'));
                modal.hide();

                // Clear the form
                document.getElementById('transferCurrentItemForm').reset();
                this.currentTransferData = null;

                // Refresh inventory data
                this.refreshInventoryAfterAdjustment('Cross Site Transfer');
            } else {
                console.error('❌ CROSS SITE TRANSFER: Transfer failed:', result.error);
                this.showDetailedTransferError('transferCurrentItemModal', result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ CROSS SITE TRANSFER: Error submitting transfer:', error);
            this.showModalError('transferCurrentItemModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = document.getElementById('crossSiteTransferBtn');
            submitBtn.innerHTML = '<i class="fas fa-exchange-alt me-1"></i>Cross Site Transfer';
            submitBtn.disabled = false;
        }
    }

    // ============================================================================
    // ISSUE CURRENT ITEM MODAL FUNCTIONS
    // ============================================================================

    openIssueCurrentItemModal(itemnum, siteid, inventoryid) {
        console.log(`🔄 ISSUE: Opening modal for item ${itemnum}, site ${siteid}, inventory ${inventoryid}`);

        try {
            const button = event.target.closest('button');
            if (!button) {
                console.error('❌ ISSUE: Could not find button element');
                alert('Error: Could not find button element');
                return;
            }

            let itemData;
            try {
                const itemDataAttr = button.getAttribute('data-item-data');
                if (!itemDataAttr) {
                    console.error('❌ ISSUE: No item data found on button');
                    alert('Error: No item data found on button');
                    return;
                }

                itemData = JSON.parse(itemDataAttr);
                console.log('🔄 ISSUE: Parsed item data:', itemData);

            } catch (error) {
                console.error('❌ ISSUE: Error parsing data:', error);
                alert('Error parsing data: ' + error.message);
                return;
            }

            // Populate the modal fields
            this.populateIssueCurrentItemModal(itemData);

            // Show the modal
            const modalElement = document.getElementById('issueCurrentItemModal');
            if (!modalElement) {
                console.error('❌ ISSUE: Modal element not found');
                alert('Error: Issue modal not found');
                return;
            }

            const modal = new bootstrap.Modal(modalElement);

            // Add event listener for when modal is fully shown to initialize Select2
            modalElement.addEventListener('shown.bs.modal', () => {
                console.log('🔄 ISSUE: Modal fully shown, initializing Select2 dropdowns');
                this.initializeIssueModalSelect2();
            });

            // Add event listener for when modal is hidden to cleanup Select2
            modalElement.addEventListener('hidden.bs.modal', () => {
                console.log('🔄 ISSUE: Modal hidden, cleaning up Select2 dropdowns');
                this.cleanupIssueModalSelect2();
            });

            modal.show();

            console.log('✅ ISSUE: Modal opened successfully');

            // Initialize Select2 dropdowns immediately after showing the modal
            // This is needed because the shown.bs.modal event might not be triggered reliably
            console.log('🔄 ISSUE: Initializing Select2 dropdowns directly after modal show');

            // Force initialization of persons dropdown first
            console.log('🔄 ISSUE: Force initializing persons dropdown');
            this.forceInitializePersonsDropdown();

            try {
                this.initializeIssueModalSelect2();
                console.log('✅ ISSUE: Successfully called initializeIssueModalSelect2()');
            } catch (error) {
                console.error('❌ ISSUE: Error calling initializeIssueModalSelect2():', error);
            }

        } catch (error) {
            console.error('❌ ISSUE: Error opening modal:', error);
            alert('Error opening Issue modal: ' + error.message);
        }
    }

    populateIssueCurrentItemModal(itemData) {
        console.log('🔄 ISSUE: Populating modal with item data:', itemData);

        try {
            // Store current issue data for later use
            this.currentIssueData = {
                itemnum: itemData.itemnum,
                siteid: itemData.siteid,
                inventoryid: itemData.inventoryid,
                storeroom: itemData.location || itemData.storeloc,
                description: itemData.description,
                rotating: itemData.rotating,
                unitcost: itemData.unitcost || itemData.avgcost || 0
            };

            // Populate Storeroom Section (Read-only fields)
            this.setFieldValue('ici_itemnum', itemData.itemnum || '');
            this.setFieldValue('ici_description', itemData.description || '');
            this.setFieldValue('ici_storeroom', itemData.location || itemData.storeloc || '');
            this.setFieldValue('ici_site', itemData.siteid || '');
            this.setFieldValue('ici_rotating', itemData.rotating ? 'Yes' : 'No');
            this.setFieldValue('ici_expiration_date', itemData.expiration || '');

            // Populate Details Section
            this.setFieldValue('ici_quantity', '1.00');
            this.setFieldValue('ici_transaction_type', 'ISSUE');
            this.setFieldValue('ici_unit_cost', (itemData.unitcost || itemData.avgcost || 0).toFixed(2));
            this.setFieldValue('ici_line_cost', (itemData.unitcost || itemData.avgcost || 0).toFixed(2));
            this.setFieldValue('ici_entered_by', this.getCurrentUser());

            // Set current date/time
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            this.setFieldValue('ici_actual_date', localDateTime);

            // Set default to site
            this.setFieldValue('ici_to_site', itemData.siteid || '');

            // Initialize persons dropdown with placeholder only (for AJAX search)
            const personSelect = document.getElementById('ici_issue_to');
            if (personSelect) {
                personSelect.innerHTML = '<option value="">Select person/organization...</option>';
                console.log('🔄 ISSUE: Persons dropdown initialized with placeholder for AJAX search');
            }

            // Load dropdown data (with error handling)
            try {
                this.loadIssueDropdownData(itemData);
            } catch (error) {
                console.error('❌ ISSUE: Error loading dropdown data:', error);
                // Continue without dropdown data - basic functionality will still work
            }

            // Setup event listeners for dynamic calculations (with error handling)
            try {
                this.setupIssueEventListeners();
            } catch (error) {
                console.error('❌ ISSUE: Error setting up event listeners:', error);
                // Continue without advanced event listeners
            }

            console.log('✅ ISSUE: Modal populated successfully, stored issue data:', this.currentIssueData);

            // Test dropdown functionality after population
            setTimeout(() => {
                this.testIssueDropdowns();
            }, 500);

        } catch (error) {
            console.error('❌ ISSUE: Error populating modal:', error);
            // Don't throw the error - let the modal open even if population fails
        }
    }

    loadIssueDropdownData(itemData) {
        console.log('🔄 ISSUE: Loading dropdown data for issue modal');
        console.log('🔄 ISSUE: Item data for dropdown loading:', itemData);

        // Load inventory balances (bins, lots, conditions)
        const itemnum = itemData.itemnum;
        const siteid = itemData.siteid;
        const location = itemData.location || itemData.storeloc;

        console.log('🔄 ISSUE: Parameters for inventory balances:', { itemnum, siteid, location });

        this.loadIssueInventoryBalances(itemnum, siteid, location);

        // Load issue units
        this.loadIssueUnits(itemData.itemnum);

        // Load work orders
        this.loadIssueWorkOrders(itemData.siteid);

        // Load GL accounts
        this.loadIssueGLAccounts();

        // Initialize persons dropdown with server-side search (no pre-loading)
        // The initializePersonsSelect2() method will be called after modal opens

        // Load assets
        this.loadIssueAssets(itemData.siteid);

        // Load locations
        this.loadIssueLocations(itemData.siteid);

        // Load requisitions
        this.loadIssueRequisitions(itemData.siteid);

        // Load sites
        this.loadIssueSites();

        // Populate transaction type dropdown (static data)
        this.populateTransactionTypeDropdown();

        // After all data is loaded, enhance dropdowns with Select2 if available
        setTimeout(() => {
            if (typeof window.enhanceIssueDropdowns === 'function') {
                window.enhanceIssueDropdowns();
            }
        }, 1000);
    }

    populateTransactionTypeDropdown() {
        console.log('🔄 ISSUE: Populating transaction type dropdown');
        try {
            const transactionSelect = document.getElementById('ici_transaction_type');
            if (transactionSelect) {
                transactionSelect.innerHTML = '<option value="">Select transaction type...</option>';
                transactionSelect.innerHTML += '<option value="ISSUE" selected>ISSUE</option>';
                transactionSelect.innerHTML += '<option value="TRANSFER">TRANSFER</option>';
                transactionSelect.innerHTML += '<option value="RETURN">RETURN</option>';

                // Set default to ISSUE
                transactionSelect.value = 'ISSUE';
                console.log('✅ ISSUE: Transaction type dropdown populated');
            }
        } catch (error) {
            console.error('❌ ISSUE: Error populating transaction type dropdown:', error);
        }
    }

    async loadIssueInventoryBalances(itemnum, siteid, location) {
        try {
            console.log(`🔄 ISSUE: Loading inventory balances for ${itemnum} at ${siteid}/${location}`);

            // Validate parameters
            if (!itemnum || !siteid || !location) {
                console.error('❌ ISSUE: Missing required parameters for inventory balances');
                console.log('Parameters:', { itemnum, siteid, location });
                return;
            }

            const url = `/api/inventory/issue-current-item/inventory-balances?itemnum=${encodeURIComponent(itemnum)}&siteid=${encodeURIComponent(siteid)}&location=${encodeURIComponent(location)}`;
            console.log('🔄 ISSUE: Fetching from URL:', url);

            const response = await fetch(url);
            console.log('🔄 ISSUE: Response status:', response.status);

            if (!response.ok) {
                console.error('❌ ISSUE: HTTP error:', response.status, response.statusText);
                const errorText = await response.text();
                console.error('❌ ISSUE: Error response:', errorText);
                return;
            }

            const data = await response.json();
            console.log('🔄 ISSUE: Response data:', data);

            if (data.success) {
                // Check if elements exist
                const binSelect = document.getElementById('ici_bin');
                const lotSelect = document.getElementById('ici_lot');
                const conditionSelect = document.getElementById('ici_condition_code');

                if (!binSelect || !lotSelect || !conditionSelect) {
                    console.error('❌ ISSUE: One or more dropdown elements not found');
                    console.log('Elements found:', {
                        binSelect: !!binSelect,
                        lotSelect: !!lotSelect,
                        conditionSelect: !!conditionSelect
                    });
                    return;
                }

                // Populate bins with searchable dropdown
                console.log('🔄 ISSUE: Populating bins with Select2:', data.balances.bins);
                this.populateSearchableDropdown(binSelect, data.balances.bins, 'Select bin...', 'bins');

                // Populate lots with searchable dropdown
                console.log('🔄 ISSUE: Populating lots with Select2:', data.balances.lots);
                this.populateSearchableDropdown(lotSelect, data.balances.lots, 'Select lot...', 'lots');

                // Populate condition codes with searchable dropdown
                console.log('🔄 ISSUE: Populating conditions with Select2:', data.balances.conditions);
                this.populateSearchableDropdown(conditionSelect, data.balances.conditions, 'Select condition...', 'conditions');

                console.log(`✅ ISSUE: Successfully populated searchable dropdowns - ${data.balances.bins.length} bins, ${data.balances.lots.length} lots, ${data.balances.conditions.length} conditions`);

            } else {
                console.error('❌ ISSUE: API returned error:', data.error);
                // Clear dropdowns and show error state - no hardcoded fallbacks
                this.clearInventoryBalanceDropdowns();
                this.showInventoryBalanceError(data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Exception loading inventory balances:', error);
            // Clear dropdowns and show error state - no hardcoded fallbacks
            this.clearInventoryBalanceDropdowns();
            this.showInventoryBalanceError('Failed to load inventory balance data');
        }
    }

    populateSearchableDropdown(selectElement, options, placeholder, type) {
        console.log(`🔄 ISSUE: Setting up searchable dropdown for ${type} with ${options.length} options`);

        try {
            // Clear existing options
            selectElement.innerHTML = `<option value="">${placeholder}</option>`;

            // Add options
            if (options && options.length > 0) {
                options.forEach(option => {
                    selectElement.innerHTML += `<option value="${option}">${option}</option>`;
                });
                selectElement.disabled = false;
            } else {
                selectElement.innerHTML = `<option value="">No ${type} available</option>`;
                selectElement.disabled = true;
            }

            // Initialize or reinitialize Select2 with mobile-friendly configuration
            if (typeof $ !== 'undefined' && $.fn.select2) {
                // Destroy existing Select2 instance if it exists
                if ($(selectElement).hasClass('select2-hidden-accessible')) {
                    $(selectElement).select2('destroy');
                }

                // Initialize Select2 with mobile-first configuration
                $(selectElement).select2({
                    placeholder: placeholder,
                    allowClear: true,
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: options.length > 5 ? 0 : Infinity, // Show search only if more than 5 options
                    dropdownCssClass: 'select2-dropdown-mobile-friendly',
                    containerCssClass: 'select2-container-mobile-friendly',
                    theme: 'bootstrap-5'
                });

                console.log(`✅ ISSUE: Select2 initialized for ${type} with ${options.length} options`);
            } else {
                console.log(`⚠️ ISSUE: Select2 not available, using standard dropdown for ${type}`);
            }

        } catch (error) {
            console.error(`❌ ISSUE: Error setting up searchable dropdown for ${type}:`, error);
        }
    }

    clearInventoryBalanceDropdowns() {
        console.log('🔄 ISSUE: Clearing inventory balance dropdowns');

        try {
            const dropdowns = [
                { id: 'ici_bin', type: 'bins' },
                { id: 'ici_lot', type: 'lots' },
                { id: 'ici_condition_code', type: 'conditions' }
            ];

            dropdowns.forEach(({ id, type }) => {
                const selectElement = document.getElementById(id);
                if (selectElement) {
                    // Destroy Select2 instance if it exists
                    if (typeof $ !== 'undefined' && $(selectElement).hasClass('select2-hidden-accessible')) {
                        $(selectElement).select2('destroy');
                    }

                    selectElement.innerHTML = `<option value="">No ${type} available</option>`;
                    selectElement.disabled = true;
                }
            });

            console.log('✅ ISSUE: Inventory balance dropdowns cleared');
        } catch (error) {
            console.error('❌ ISSUE: Error clearing dropdowns:', error);
        }
    }

    initializeIssueModalSelect2() {
        console.log('🔄 ISSUE: Initializing Select2 for issue modal dropdowns');

        try {
            const dropdownConfigs = [
                { id: 'ici_bin', placeholder: 'Select bin...', type: 'bins' },
                { id: 'ici_lot', placeholder: 'Select lot...', type: 'lots' },
                { id: 'ici_condition_code', placeholder: 'Select condition...', type: 'conditions' },
                { id: 'ici_work_order', placeholder: 'Select work order...', type: 'work_orders' },
                { id: 'ici_wo_task', placeholder: 'Select task...', type: 'tasks' },
                { id: 'ici_issue_unit', placeholder: 'Select unit...', type: 'units' },
                { id: 'ici_asset', placeholder: 'Select asset...', type: 'assets' },
                { id: 'ici_location', placeholder: 'Select location...', type: 'locations' },
                { id: 'ici_gl_debit_account', placeholder: 'Select debit account...', type: 'gl_accounts' },
                { id: 'ici_gl_credit_account', placeholder: 'Select credit account...', type: 'gl_accounts' },
                { id: 'ici_issue_to', placeholder: 'Select person/organization...', type: 'persons' },
                { id: 'ici_requisition', placeholder: 'Select requisition...', type: 'requisitions' },
                { id: 'ici_to_site', placeholder: 'Select destination site...', type: 'sites' }
            ];

            dropdownConfigs.forEach(config => {
                const selectElement = document.getElementById(config.id);
                if (selectElement && typeof $ !== 'undefined' && $.fn.select2) {
                    // Destroy existing Select2 instance if it exists
                    if ($(selectElement).hasClass('select2-hidden-accessible')) {
                        $(selectElement).select2('destroy');
                    }

                    // Special handling for persons dropdown - use AJAX search
                    if (config.type === 'persons') {
                        // Skip regular Select2 initialization for persons - will be handled by initializePersonsSelect2()
                        console.log(`🔄 ISSUE: Persons dropdown will use AJAX search - skipping regular Select2`);
                        return;
                    }

                    // Always initialize Select2 for work orders and tasks, for others only if they have options
                    const shouldInitialize = !selectElement.disabled &&
                        (config.type === 'work_orders' || config.type === 'tasks' || selectElement.options.length > 1);

                    if (shouldInitialize) {
                        $(selectElement).select2({
                            placeholder: config.placeholder,
                            allowClear: true,
                            width: '100%',
                            dropdownAutoWidth: true,
                            minimumResultsForSearch: selectElement.options.length > 5 ? 0 : Infinity,
                            dropdownCssClass: 'select2-dropdown-mobile-friendly',
                            containerCssClass: 'select2-container-mobile-friendly',
                            theme: 'bootstrap-5',
                            dropdownParent: $('#issueCurrentItemModal') // Ensure dropdown appears within modal
                        });

                        console.log(`✅ ISSUE: Select2 initialized for ${config.type}`);
                    } else {
                        console.log(`⚠️ ISSUE: Skipping Select2 for ${config.type} - disabled or no options`);
                    }
                }
            });

            // Initialize persons dropdown with AJAX search separately
            console.log('🔄 ISSUE: About to initialize persons dropdown with AJAX search');
            try {
                this.initializePersonsSelect2();
                console.log('✅ ISSUE: Successfully called initializePersonsSelect2()');
            } catch (error) {
                console.error('❌ ISSUE: Error calling initializePersonsSelect2():', error);
            }

        } catch (error) {
            console.error('❌ ISSUE: Error initializing Select2:', error);
        }
    }

    cleanupIssueModalSelect2() {
        console.log('🔄 ISSUE: Cleaning up Select2 for issue modal dropdowns');

        try {
            const dropdownIds = [
                'ici_bin', 'ici_lot', 'ici_condition_code', 'ici_work_order', 'ici_wo_task',
                'ici_issue_unit', 'ici_asset', 'ici_location', 'ici_gl_debit_account',
                'ici_gl_credit_account', 'ici_issue_to', 'ici_requisition', 'ici_to_site'
            ];

            dropdownIds.forEach(id => {
                const selectElement = document.getElementById(id);
                if (selectElement && typeof $ !== 'undefined' && $(selectElement).hasClass('select2-hidden-accessible')) {
                    $(selectElement).select2('destroy');
                    console.log(`✅ ISSUE: Select2 destroyed for ${id}`);
                }
            });

        } catch (error) {
            console.error('❌ ISSUE: Error cleaning up Select2:', error);
        }
    }

    showInventoryBalanceError(errorMessage) {
        console.log('⚠️ ISSUE: Showing inventory balance error:', errorMessage);

        // You could add a visual error indicator here if needed
        // For example, show a small error message near the dropdowns
        const errorContainer = document.getElementById('inventory-balance-error');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-warning alert-sm mt-2">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Unable to load inventory balance data: ${errorMessage}
                </div>
            `;
            errorContainer.style.display = 'block';
        }
    }

    async loadIssueUnits(itemnum) {
        try {
            console.log(`🔄 ISSUE: Loading issue units for ${itemnum}`);

            const response = await fetch(`/api/inventory/issue-current-item/issue-units?itemnum=${encodeURIComponent(itemnum)}`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const unitSelect = document.getElementById('ici_issue_unit');
                unitSelect.innerHTML = '<option value="">Select unit...</option>';

                data.units.forEach(unit => {
                    unitSelect.innerHTML += `<option value="${unit.unit}">${unit.description}</option>`;
                });

                // Select first unit as default
                if (data.units.length > 0) {
                    unitSelect.value = data.units[0].unit;
                }

                console.log(`✅ ISSUE: Loaded ${data.units.length} issue units`);

                // Reinitialize Select2 for units dropdown
                this.initializeDropdownSelect2('ici_issue_unit', 'Select unit...');
            } else {
                console.error('❌ ISSUE: Failed to load issue units:', data.error);
                // Add default issue units
                this.addDefaultIssueUnits();
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading issue units:', error);
            // Add default issue units
            this.addDefaultIssueUnits();
        }
    }

    addDefaultIssueUnits() {
        console.log('🔄 ISSUE: Adding default issue units');
        try {
            const unitSelect = document.getElementById('ici_issue_unit');
            if (unitSelect) {
                unitSelect.innerHTML = '<option value="">Select unit...</option>';
                unitSelect.innerHTML += '<option value="EA">Each (EA)</option>';
                unitSelect.innerHTML += '<option value="RO">Roll (RO)</option>';
                unitSelect.innerHTML += '<option value="FT">Feet (FT)</option>';
                unitSelect.innerHTML += '<option value="LB">Pounds (LB)</option>';
                unitSelect.innerHTML += '<option value="GAL">Gallons (GAL)</option>';

                // Select first unit as default
                unitSelect.value = 'EA';
                console.log('✅ ISSUE: Default issue units added');

                // Reinitialize Select2 for units dropdown
                this.initializeDropdownSelect2('ici_issue_unit', 'Select unit...');
            }
        } catch (error) {
            console.error('❌ ISSUE: Error adding default issue units:', error);
        }
    }

    async loadIssueWorkOrders(siteid) {
        try {
            console.log(`🔄 ISSUE: Loading work orders for site ${siteid}`);

            // Validate parameters
            if (!siteid) {
                console.error('❌ ISSUE: Missing site ID for work order loading');
                this.clearWorkOrderDropdown();
                this.showWorkOrderError('Site ID is required');
                return;
            }

            const woSelect = document.getElementById('ici_work_order');
            if (!woSelect) {
                console.error('❌ ISSUE: Work order dropdown element not found');
                return;
            }

            // Show loading state
            this.showWorkOrderLoading();

            const response = await fetch(`/api/inventory/issue-current-item/work-orders?siteid=${encodeURIComponent(siteid)}`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            console.log('🔍 ISSUE: API Response:', data);

            if (data.success && data.work_orders) {
                console.log(`🔍 ISSUE: Found ${data.work_orders.length} work orders:`, data.work_orders);

                // Clear existing options
                woSelect.innerHTML = '<option value="">Select work order...</option>';

                // Populate with work orders using the enhanced format from backend
                data.work_orders.forEach(wo => {
                    const option = document.createElement('option');
                    option.value = wo.wonum;
                    // Use the pre-formatted display_text from enhanced_workorder_service
                    option.textContent = wo.display_text || `${wo.wonum} - ${wo.description || 'No description'} (${wo.status})`;
                    option.setAttribute('data-status', wo.status);
                    option.setAttribute('data-siteid', wo.siteid);
                    option.setAttribute('data-assetnum', wo.assetnum || '');
                    option.setAttribute('data-location', wo.location || '');
                    option.setAttribute('data-worktype', wo.worktype || '');
                    option.setAttribute('data-description', wo.description || '');
                    woSelect.appendChild(option);
                });

                // Enable the dropdown
                woSelect.disabled = false;

                console.log(`🔍 ISSUE: Work order dropdown now has ${woSelect.options.length} options`);
                console.log(`✅ ISSUE: Successfully loaded ${data.work_orders.length} work orders using enhanced_workorder_service`);

                // Clear any previous error states
                this.clearWorkOrderError();

                // Initialize Select2 with mobile-friendly configuration (same as inventory balance dropdowns)
                this.initializeWorkOrderSelect2();

            } else {
                console.error('❌ ISSUE: API returned error:', data.error);
                this.clearWorkOrderDropdown();
                this.showWorkOrderError(data.error || 'Failed to load work orders');
            }
        } catch (error) {
            console.error('❌ ISSUE: Exception loading work orders:', error);
            this.clearWorkOrderDropdown();
            this.showWorkOrderError('Failed to load work order data');
        }
    }

    showWorkOrderLoading() {
        const woSelect = document.getElementById('ici_work_order');
        if (woSelect) {
            woSelect.innerHTML = '<option value="">Loading work orders...</option>';
            woSelect.disabled = true;
        }
    }

    clearWorkOrderDropdown() {
        const woSelect = document.getElementById('ici_work_order');
        if (woSelect) {
            woSelect.innerHTML = '<option value="">Select work order...</option>';
            woSelect.disabled = false;
        }
    }

    showWorkOrderError(errorMessage) {
        console.log('⚠️ ISSUE: Showing work order error:', errorMessage);
        const woSelect = document.getElementById('ici_work_order');
        if (woSelect) {
            woSelect.innerHTML = `<option value="">Error: ${errorMessage}</option>`;
            woSelect.disabled = true;
        }
    }

    clearWorkOrderError() {
        // Clear any error states - work order dropdown will be populated with actual data
        console.log('✅ ISSUE: Clearing work order error state');
    }

    initializeWorkOrderSelect2() {
        console.log('🔍 ISSUE: Attempting to initialize Select2 for work order dropdown');

        const woSelect = document.getElementById('ici_work_order');
        console.log('🔍 ISSUE: Work order select element:', woSelect);
        console.log('🔍 ISSUE: jQuery available:', typeof $ !== 'undefined');
        console.log('🔍 ISSUE: Select2 available:', typeof $ !== 'undefined' && $.fn.select2);

        if (woSelect && typeof $ !== 'undefined' && $.fn.select2) {
            console.log(`🔍 ISSUE: Work order dropdown has ${woSelect.options.length} options before Select2 init`);

            // Destroy existing Select2 instance if it exists
            if ($(woSelect).hasClass('select2-hidden-accessible')) {
                console.log('🔍 ISSUE: Destroying existing Select2 instance');
                $(woSelect).select2('destroy');
            }

            // Initialize Select2 for work order dropdown
            try {
                $(woSelect).select2({
                    placeholder: 'Select work order...',
                    allowClear: true,
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: 0, // Always enable search for work orders
                    dropdownCssClass: 'select2-dropdown-mobile-friendly',
                    containerCssClass: 'select2-container-mobile-friendly',
                    dropdownParent: $('#issueCurrentItemModal'),
                    // Force search to be enabled
                    minimumInputLength: 0
                });

                console.log('✅ ISSUE: Select2 initialized successfully for work order dropdown with search enabled');

                // Check if search box is created
                setTimeout(() => {
                    const searchBox = $(woSelect).next('.select2-container').find('.select2-search__field');
                    console.log('🔍 ISSUE: Work order search box found:', searchBox.length > 0);
                    if (searchBox.length > 0) {
                        console.log('🔍 ISSUE: Search box is visible and ready for typing');
                    } else {
                        console.log('❌ ISSUE: Search box not found - this might be why search is not working');
                    }
                }, 100);
            } catch (error) {
                console.error('❌ ISSUE: Error initializing Select2 for work order:', error);
            }
        } else {
            console.error('❌ ISSUE: Cannot initialize Select2 - missing element or jQuery/Select2');
        }
    }

    initializeTaskSelect2() {
        const taskSelect = document.getElementById('ici_wo_task');
        if (taskSelect && typeof $ !== 'undefined' && $.fn.select2) {
            // Destroy existing Select2 instance if it exists
            if ($(taskSelect).hasClass('select2-hidden-accessible')) {
                $(taskSelect).select2('destroy');
            }

            // Initialize Select2 for task dropdown with search always enabled
            $(taskSelect).select2({
                placeholder: 'Select task...',
                allowClear: true,
                width: '100%',
                dropdownAutoWidth: true,
                minimumResultsForSearch: 0, // Always enable search for tasks
                dropdownCssClass: 'select2-dropdown-mobile-friendly',
                containerCssClass: 'select2-container-mobile-friendly',
                dropdownParent: $('#issueCurrentItemModal'),
                // Force search to be enabled
                minimumInputLength: 0
            });

            console.log('✅ ISSUE: Select2 initialized for task dropdown with search enabled');

            // Check if search box is created
            setTimeout(() => {
                const searchBox = $(taskSelect).next('.select2-container').find('.select2-search__field');
                console.log('🔍 ISSUE: Task search box found:', searchBox.length > 0);
                if (searchBox.length > 0) {
                    console.log('🔍 ISSUE: Task search box is visible and ready for typing');
                } else {
                    console.log('❌ ISSUE: Task search box not found - this might be why search is not working');
                }
            }, 100);
        }
    }

    initializeDropdownSelect2(dropdownId, placeholder) {
        const selectElement = document.getElementById(dropdownId);
        if (selectElement && typeof $ !== 'undefined' && $.fn.select2) {
            // Destroy existing Select2 instance if it exists
            if ($(selectElement).hasClass('select2-hidden-accessible')) {
                $(selectElement).select2('destroy');
            }

            // Initialize Select2
            $(selectElement).select2({
                placeholder: placeholder,
                allowClear: true,
                width: '100%',
                dropdownAutoWidth: true,
                minimumResultsForSearch: selectElement.options.length > 5 ? 0 : Infinity,
                dropdownCssClass: 'select2-dropdown-mobile-friendly',
                containerCssClass: 'select2-container-mobile-friendly',
                theme: 'bootstrap-5',
                dropdownParent: $('#issueCurrentItemModal')
            });

            console.log(`✅ ISSUE: Select2 initialized for ${dropdownId}`);
        }
    }

    forceInitializePersonsDropdown() {
        console.log('🔧 ISSUE: Force initializing persons dropdown');
        const personSelect = document.getElementById('ici_issue_to');

        if (!personSelect) {
            console.error('❌ ISSUE: Person select element not found');
            return;
        }

        // First, ensure the dropdown is functional as a basic select
        personSelect.innerHTML = '<option value="">Loading persons...</option>';
        personSelect.disabled = false;

        // Make sure the dropdown is clickable and visible
        personSelect.style.pointerEvents = 'auto';
        personSelect.style.opacity = '1';
        personSelect.style.display = 'block';

        console.log('🔄 ISSUE: Loading persons data synchronously');

        // Load persons data immediately
        fetch('/api/inventory/issue-current-item/persons?search=')
            .then(response => {
                console.log('🔍 ISSUE: Persons API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('🔍 ISSUE: Persons API response data:', data);

                if (data.success && data.persons && data.persons.length > 0) {
                    // Clear and populate the dropdown
                    personSelect.innerHTML = '<option value="">Select person/organization...</option>';

                    data.persons.forEach(person => {
                        // Build comprehensive display text with all available fields
                        const personid = person.personid || '';
                        const displayname = person.displayname || '';
                        const locationsite = person.locationsite || '';
                        const status = person.status || '';

                        // Create rich display format: "PersonID - Display Name (Location) [Status]"
                        let displayText = personid;

                        if (displayname && displayname !== personid) {
                            displayText += ` - ${displayname}`;
                        }

                        if (locationsite) {
                            displayText += ` (${locationsite})`;
                        }

                        if (status && status !== 'ACTIVE') {
                            displayText += ` [${status}]`;
                        }

                        const option = document.createElement('option');
                        option.value = personid; // Value is always personid
                        option.textContent = displayText;
                        option.title = `ID: ${personid} | Name: ${displayname} | Location: ${locationsite} | Status: ${status}`;

                        // Store all data as attributes for search functionality
                        option.setAttribute('data-personid', personid);
                        option.setAttribute('data-displayname', displayname);
                        option.setAttribute('data-locationsite', locationsite);
                        option.setAttribute('data-status', status);
                        option.setAttribute('data-searchtext', `${personid} ${displayname} ${locationsite} ${status}`.toLowerCase());

                        personSelect.appendChild(option);
                    });

                    console.log(`✅ ISSUE: Loaded ${data.persons.length} persons with enhanced display format`);

                    // Now try to enhance with Select2 if available
                    this.enhancePersonsDropdownWithSelect2();
                } else {
                    console.warn('⚠️ ISSUE: No persons data received');
                    personSelect.innerHTML = '<option value="">No persons available</option>';
                }
            })
            .catch(error => {
                console.error('❌ ISSUE: Error loading persons:', error);
                personSelect.innerHTML = '<option value="">Error loading persons</option>';
            });
    }

    enhancePersonsDropdownWithSelect2() {
        const personSelect = document.getElementById('ici_issue_to');

        if (!personSelect || typeof $ === 'undefined' || !$.fn.select2) {
            console.log('⚠️ ISSUE: Select2 not available, using basic dropdown');
            return;
        }

        try {
            // Destroy existing Select2 instance if it exists
            if ($(personSelect).hasClass('select2-hidden-accessible')) {
                $(personSelect).select2('destroy');
            }

            // Initialize Select2 with forced search box
            $(personSelect).select2({
                placeholder: 'Type to search persons...',
                allowClear: true,
                width: '100%',
                minimumResultsForSearch: 0, // Always show search box
                minimumInputLength: 0, // Allow search from first character
                dropdownCssClass: 'select2-dropdown-mobile-friendly',
                containerCssClass: 'select2-container-mobile-friendly',
                theme: 'bootstrap-5',
                dropdownParent: $('#issueCurrentItemModal'),
                escapeMarkup: function(markup) {
                    return markup; // Allow HTML in results
                }
            });

            // Force the search box to be visible by triggering the dropdown
            setTimeout(() => {
                try {
                    $(personSelect).select2('open');
                    $(personSelect).select2('close');
                    console.log('✅ ISSUE: Search box visibility forced');
                } catch (e) {
                    console.log('⚠️ ISSUE: Could not force search box visibility');
                }
            }, 100);

            console.log('✅ ISSUE: Persons dropdown enhanced with Select2 and multi-field search');
        } catch (error) {
            console.error('❌ ISSUE: Error enhancing with Select2:', error);
            console.log('⚠️ ISSUE: Continuing with basic dropdown');
        }
    }

    initializePersonsSelect2() {
        console.log('🔍 ISSUE: initializePersonsSelect2 called (delegating to forceInitializePersonsDropdown)');
        this.forceInitializePersonsDropdown();
    }

    loadInitialPersonsData() {
        console.log('🔍 ISSUE: Loading initial persons data');
        // Make an initial AJAX call to load some persons data
        fetch('/api/inventory/issue-current-item/persons?search=')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.persons && data.persons.length > 0) {
                    const personSelect = document.getElementById('ici_issue_to');
                    if (personSelect) {
                        // Add first 10 persons as initial options
                        const initialPersons = data.persons.slice(0, 10);
                        initialPersons.forEach(person => {
                            const option = new Option(person.displayname || person.personid, person.personid, false, false);
                            personSelect.add(option);
                        });
                        console.log(`✅ ISSUE: Pre-loaded ${initialPersons.length} persons as initial options`);
                    }
                } else {
                    console.log('ℹ️ ISSUE: No initial persons data to pre-load');
                }
            })
            .catch(error => {
                console.error('❌ ISSUE: Error loading initial persons data:', error);
            });
    }

    refreshWorkOrders() {
        /**
         * Refresh work orders for the current site
         * Called from the refresh button in the UI
         */
        console.log('🔄 ISSUE: Refreshing work orders');

        if (this.currentIssueData && this.currentIssueData.siteid) {
            this.loadIssueWorkOrders(this.currentIssueData.siteid);
        } else {
            console.error('❌ ISSUE: Cannot refresh work orders - no current issue data or site ID');
            this.showWorkOrderError('Cannot refresh - no site information available');
        }
    }

    async loadIssueGLAccounts() {
        try {
            console.log('🔄 ISSUE: Loading GL accounts');

            const response = await fetch('/api/inventory/issue-current-item/gl-accounts', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const debitSelect = document.getElementById('ici_gl_debit_account');
                const creditSelect = document.getElementById('ici_gl_credit_account');

                debitSelect.innerHTML = '<option value="">Select debit account...</option>';
                creditSelect.innerHTML = '<option value="">Select credit account...</option>';

                data.accounts.forEach(account => {
                    const option = `<option value="${account.glaccount}">${account.glaccount} - ${account.description || ''}</option>`;
                    debitSelect.innerHTML += option;
                    creditSelect.innerHTML += option;
                });

                console.log(`✅ ISSUE: Loaded ${data.accounts.length} GL accounts`);

                // Reinitialize Select2 for GL account dropdowns
                this.initializeDropdownSelect2('ici_gl_debit_account', 'Select debit account...');
                this.initializeDropdownSelect2('ici_gl_credit_account', 'Select credit account...');
            } else {
                console.error('❌ ISSUE: Failed to load GL accounts:', data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading GL accounts:', error);
        }
    }

    async loadIssuePersons() {
        try {
            console.log('🔄 ISSUE: Loading persons');

            const response = await fetch('/api/inventory/issue-current-item/persons', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const personSelect = document.getElementById('ici_issue_to');
                personSelect.innerHTML = '<option value="">Select person/organization...</option>';

                data.persons.forEach(person => {
                    // Enhanced display format that works with both MXAPIPERUSER and MXAPIPERSON data
                    const displayName = person.displayname || '';
                    const locationSite = person.locationsite || '';
                    const department = person.department || '';
                    const supervisor = person.supervisor || '';

                    // Build display text with available information
                    let displayText = person.personid;

                    // Add display name if available
                    if (displayName) {
                        displayText += ` - ${displayName}`;
                    }

                    // Add location/site information if available
                    if (locationSite) {
                        displayText += ` (${locationSite})`;
                    } else if (department) {
                        displayText += ` (${department})`;
                    }

                    // Add supervisor info as additional context if available and no location
                    if (supervisor && !locationSite && !department) {
                        displayText += ` [Supervisor: ${supervisor}]`;
                    }

                    personSelect.innerHTML += `<option value="${person.personid}" title="${displayText}">${displayText}</option>`;
                });

                console.log(`✅ ISSUE: Loaded ${data.persons.length} persons with enhanced display format`);

                // Note: initializePersonsSelect2() should be called separately for AJAX search
            } else {
                console.error('❌ ISSUE: Failed to load persons:', data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading persons:', error);
        }
    }

    async loadIssueAssets(siteid) {
        try {
            console.log(`🔄 ISSUE: Loading assets for site ${siteid}`);

            const response = await fetch(`/api/inventory/issue-current-item/assets?siteid=${encodeURIComponent(siteid)}`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success && data.assets) {
                const assetSelect = document.getElementById('ici_asset');
                if (assetSelect) {
                    assetSelect.innerHTML = '<option value="">Select asset...</option>';

                    data.assets.forEach(asset => {
                        const description = asset.description || 'No description';
                        const displayText = `${asset.assetnum} - ${description}`;
                        assetSelect.innerHTML += `<option value="${asset.assetnum}" data-description="${description}">${displayText}</option>`;
                    });

                    console.log(`✅ ISSUE: Loaded ${data.assets.length} assets`);

                    // Reinitialize Select2 for assets dropdown
                    this.initializeDropdownSelect2('ici_asset', 'Select asset...');
                } else {
                    console.error('❌ ISSUE: Asset dropdown element not found');
                }
            } else {
                console.error('❌ ISSUE: Failed to load assets:', data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading assets:', error);
        }
    }

    async loadIssueLocations(siteid) {
        try {
            console.log(`🔄 ISSUE: Loading locations for site ${siteid}`);

            const response = await fetch(`/api/inventory/issue-current-item/locations?siteid=${encodeURIComponent(siteid)}`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const locationSelect = document.getElementById('ici_location');
                locationSelect.innerHTML = '<option value="">Select location...</option>';

                data.locations.forEach(location => {
                    locationSelect.innerHTML += `<option value="${location.location}">${location.location} - ${location.description || ''}</option>`;
                });

                console.log(`✅ ISSUE: Loaded ${data.locations.length} locations`);

                // Reinitialize Select2 for locations dropdown
                this.initializeDropdownSelect2('ici_location', 'Select location...');
            } else {
                console.error('❌ ISSUE: Failed to load locations:', data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading locations:', error);
        }
    }

    async loadIssueRequisitions(siteid) {
        try {
            console.log(`🔄 ISSUE: Loading requisitions for site ${siteid}`);

            const response = await fetch(`/api/inventory/issue-current-item/requisitions?siteid=${encodeURIComponent(siteid)}`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const reqSelect = document.getElementById('ici_requisition');
                reqSelect.innerHTML = '<option value="">Select requisition...</option>';

                data.requisitions.forEach(req => {
                    reqSelect.innerHTML += `<option value="${req.mrnum}">${req.mrnum} - ${req.description || ''}</option>`;
                });

                console.log(`✅ ISSUE: Loaded ${data.requisitions.length} requisitions`);

                // Reinitialize Select2 for requisitions dropdown
                this.initializeDropdownSelect2('ici_requisition', 'Select requisition...');
            } else {
                console.error('❌ ISSUE: Failed to load requisitions:', data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading requisitions:', error);
        }
    }

    async loadIssueSites() {
        try {
            console.log('🔄 ISSUE: Loading sites');

            const response = await fetch('/api/inventory/issue-current-item/sites', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const siteSelect = document.getElementById('ici_to_site');
                // Keep current selection
                const currentValue = siteSelect.value;
                siteSelect.innerHTML = '<option value="">Select destination site...</option>';

                data.sites.forEach(site => {
                    siteSelect.innerHTML += `<option value="${site.siteid}">${site.siteid} - ${site.description || ''}</option>`;
                });

                // Restore selection
                if (currentValue) {
                    siteSelect.value = currentValue;
                }

                console.log(`✅ ISSUE: Loaded ${data.sites.length} sites`);

                // Reinitialize Select2 for sites dropdown
                this.initializeDropdownSelect2('ici_to_site', 'Select destination site...');
            } else {
                console.error('❌ ISSUE: Failed to load sites:', data.error);
            }
        } catch (error) {
            console.error('❌ ISSUE: Error loading sites:', error);
        }
    }

    setupIssueEventListeners() {
        // Quantity change listener for line cost calculation
        const quantityInput = document.getElementById('ici_quantity');
        const unitCostInput = document.getElementById('ici_unit_cost');
        const lineCostInput = document.getElementById('ici_line_cost');

        const calculateLineCost = () => {
            const quantity = parseFloat(quantityInput.value) || 0;
            const unitCost = parseFloat(unitCostInput.value) || 0;
            const lineCost = quantity * unitCost;
            lineCostInput.value = lineCost.toFixed(2);
        };

        quantityInput.addEventListener('input', calculateLineCost);

        // Work order change listener for loading tasks
        const workOrderSelect = document.getElementById('ici_work_order');
        if (workOrderSelect) {
            workOrderSelect.addEventListener('change', async (e) => {
                const wonum = e.target.value;

                // Clear and disable task selection whenever work order selection changes
                this.clearTaskDropdown();

                if (wonum && this.currentIssueData) {
                    console.log(`🔄 ISSUE: Work order selected: ${wonum}, loading tasks...`);
                    await this.loadIssueWorkOrderTasks(wonum, this.currentIssueData.siteid);
                } else {
                    console.log('🔄 ISSUE: No work order selected, task dropdown cleared');
                    // Show placeholder text when no work order is selected
                    const taskSelect = document.getElementById('ici_wo_task');
                    if (taskSelect) {
                        taskSelect.innerHTML = '<option value="">Select work order first</option>';
                        taskSelect.disabled = true;
                    }
                }
            });
        }
    }

    async loadIssueWorkOrderTasks(wonum, siteid, searchTerm = '') {
        try {
            console.log(`🔄 ISSUE: Loading tasks for work order ${wonum}, search: '${searchTerm}'`);

            // Validate parameters
            if (!wonum || !siteid) {
                console.error('❌ ISSUE: Missing work order number or site ID for task loading');
                this.clearTaskDropdown();
                this.showTaskError('Work order number and site ID are required');
                return;
            }

            const taskSelect = document.getElementById('ici_wo_task');
            if (!taskSelect) {
                console.error('❌ ISSUE: Task dropdown element not found');
                return;
            }

            // Show loading state
            this.showTaskLoading();

            // Build URL with search parameter
            let url = `/api/inventory/issue-current-item/work-order-tasks?wonum=${encodeURIComponent(wonum)}&siteid=${encodeURIComponent(siteid)}`;
            if (searchTerm && searchTerm.trim()) {
                url += `&search=${encodeURIComponent(searchTerm.trim())}`;
            }

            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success && data.tasks) {
                // Clear existing options
                taskSelect.innerHTML = '<option value="">Select task...</option>';

                // Populate with tasks using the enhanced format from backend
                data.tasks.forEach(task => {
                    const option = document.createElement('option');
                    option.value = task.wonum;
                    // Use the pre-formatted display_text from backend
                    option.textContent = task.display_text || `${task.wonum} (Task: ${task.taskid || 'N/A'}) - ${task.description || 'No description'} (${task.status})`;
                    option.setAttribute('data-status', task.status);
                    option.setAttribute('data-siteid', task.siteid);
                    option.setAttribute('data-parent', task.parent || '');
                    option.setAttribute('data-description', task.description || '');
                    option.setAttribute('data-taskid', task.taskid || '');
                    taskSelect.appendChild(option);
                });

                // Enable the dropdown
                taskSelect.disabled = false;

                console.log(`✅ ISSUE: Successfully loaded ${data.tasks.length} tasks with configurable status filtering`);

                // Clear any previous error states
                this.clearTaskError();

                // Reinitialize Select2 for task dropdown after loading data
                this.initializeTaskSelect2();

            } else {
                console.error('❌ ISSUE: API returned error:', data.error);
                this.clearTaskDropdown();
                this.showTaskError(data.error || 'Failed to load tasks');
            }
        } catch (error) {
            console.error('❌ ISSUE: Exception loading work order tasks:', error);
            this.clearTaskDropdown();
            this.showTaskError('Failed to load task data');
        }
    }

    showTaskLoading() {
        const taskSelect = document.getElementById('ici_wo_task');
        if (taskSelect) {
            taskSelect.innerHTML = '<option value="">Loading tasks...</option>';
            taskSelect.disabled = true;
        }
    }

    clearTaskDropdown() {
        const taskSelect = document.getElementById('ici_wo_task');
        if (taskSelect) {
            taskSelect.innerHTML = '<option value="">Select task...</option>';
            taskSelect.disabled = false;
        }
    }

    showTaskError(errorMessage) {
        console.log('⚠️ ISSUE: Showing task error:', errorMessage);
        const taskSelect = document.getElementById('ici_wo_task');
        if (taskSelect) {
            taskSelect.innerHTML = `<option value="">Error: ${errorMessage}</option>`;
            taskSelect.disabled = true;
        }
    }

    clearTaskError() {
        // Clear any error states - task dropdown will be populated with actual data
        console.log('✅ ISSUE: Clearing task error state');
    }

    async submitIssueCurrentItem() {
        try {
            console.log('🔄 ISSUE: Submitting issue current item');

            // Validate required fields
            const quantity = document.getElementById('ici_quantity').value;
            if (!quantity || parseFloat(quantity) <= 0) {
                this.showModalError('issueCurrentItemModal', 'Please enter a valid quantity greater than 0');
                return;
            }

            // Build issue data
            const issueData = {
                itemnum: this.currentIssueData.itemnum,
                siteid: this.currentIssueData.siteid,
                storeroom: this.currentIssueData.storeroom,
                quantity: parseFloat(quantity),
                transaction_type: document.getElementById('ici_transaction_type').value,
                issue_unit: document.getElementById('ici_issue_unit').value,
                bin: document.getElementById('ici_bin').value,
                lot: document.getElementById('ici_lot').value,
                condition_code: document.getElementById('ici_condition_code').value,
                rotating_asset: document.getElementById('ici_rotating_asset').value,
                work_order: document.getElementById('ici_work_order').value,
                wo_task: document.getElementById('ici_wo_task').value,
                asset: document.getElementById('ici_asset').value,
                requisition: document.getElementById('ici_requisition').value,
                requisition_line: document.getElementById('ici_requisition_line').value,
                location: document.getElementById('ici_location').value,
                gl_debit_account: document.getElementById('ici_gl_debit_account').value,
                gl_credit_account: document.getElementById('ici_gl_credit_account').value,
                actual_date: document.getElementById('ici_actual_date').value,
                issue_to: document.getElementById('ici_issue_to').value,
                to_site: document.getElementById('ici_to_site').value,
                memo: document.getElementById('ici_memo').value
            };

            console.log('🔄 ISSUE: Submitting issue data:', issueData);

            // Show loading state
            const submitBtn = document.getElementById('submitIssueBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            submitBtn.disabled = true;

            // Submit the issue
            const response = await fetch('/api/inventory/issue-current-item', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(issueData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ ISSUE: Issue submitted successfully:', result);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('issueCurrentItemModal'));
                modal.hide();

                // Show success message
                this.showSuccessMessage(`Issue transaction submitted successfully for item ${issueData.itemnum}`);

                // Refresh inventory data
                this.refreshInventoryAfterAdjustment('Issue Current Item');
            } else {
                console.error('❌ ISSUE: Issue failed:', result.error);
                this.showDetailedIssueError('issueCurrentItemModal', result);
            }

            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

        } catch (error) {
            console.error('❌ ISSUE: Error submitting issue:', error);
            this.showModalError('issueCurrentItemModal', 'Network error: ' + error.message);

            // Restore button state
            const submitBtn = document.getElementById('submitIssueBtn');
            submitBtn.innerHTML = '<i class="fas fa-arrow-right me-1"></i>Submit Issue';
            submitBtn.disabled = false;
        }
    }

    testIssueDropdowns() {
        /**
         * Test function to verify all issue dropdowns are working
         */
        console.log('🔧 ISSUE: Testing dropdown functionality');

        const dropdownIds = [
            'ici_bin', 'ici_lot', 'ici_condition_code', 'ici_transaction_type',
            'ici_issue_unit', 'ici_work_order', 'ici_wo_task', 'ici_location',
            'ici_gl_debit_account', 'ici_gl_credit_account', 'ici_issue_to',
            'ici_requisition', 'ici_to_site'
        ];

        dropdownIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                const optionCount = element.options ? element.options.length : 0;
                const isDisabled = element.disabled;
                const isVisible = element.offsetParent !== null;

                console.log(`🔧 ISSUE: Dropdown ${id}:`, {
                    exists: true,
                    optionCount,
                    isDisabled,
                    isVisible,
                    value: element.value
                });

                // Add a test change event listener
                element.addEventListener('change', function() {
                    console.log(`✅ ISSUE: Dropdown ${id} changed to:`, this.value);
                });

            } else {
                console.error(`❌ ISSUE: Dropdown ${id} not found`);
            }
        });

        console.log('✅ ISSUE: Dropdown testing complete');
    }

    showDetailedIssueError(modalId, result) {
        console.log('🔄 ISSUE: Showing detailed error for result:', result);

        let errorMessage = 'Issue submission failed';
        let detailedError = '';

        if (result.error) {
            errorMessage = result.error;

            // Extract more specific error information if available
            if (typeof result.error === 'string') {
                if (result.error.includes('BMXAA')) {
                    // Maximo error code
                    detailedError = `\n\nMaximo Error: ${result.error}`;
                } else if (result.error.includes('validation')) {
                    detailedError = '\n\nPlease check all required fields and try again.';
                } else if (result.error.includes('authentication')) {
                    detailedError = '\n\nSession may have expired. Please refresh the page and try again.';
                }
            }
        }

        this.showModalError(modalId, errorMessage + detailedError);
    }

    // Helper functions for issue modal refresh buttons
    async refreshWorkOrders() {
        if (this.currentIssueData) {
            await this.loadIssueWorkOrders(this.currentIssueData.siteid);
        }
    }

    async refreshRequisitions() {
        if (this.currentIssueData) {
            await this.loadIssueRequisitions(this.currentIssueData.siteid);
        }
    }

    async refreshLocations() {
        if (this.currentIssueData) {
            await this.loadIssueLocations(this.currentIssueData.siteid);
        }
    }

    async refreshPersons() {
        console.log('🔄 ISSUE: Refreshing persons dropdown');
        this.forceInitializePersonsDropdown();
    }

    async refreshAssets() {
        if (this.currentIssueData) {
            await this.loadIssueAssets(this.currentIssueData.siteid);
        }
    }

    async refreshGLAccounts() {
        await this.loadIssueGLAccounts();
    }

    async refreshSites() {
        await this.loadIssueSites();
    }

    getCurrentUser() {
        // Try to get current user from various sources
        if (window.userProfile && window.userProfile.personid) {
            return window.userProfile.personid;
        }

        // Fallback to checking if there's a username in the page
        const userElements = document.querySelectorAll('[data-user], .user-name, #username');
        for (const element of userElements) {
            if (element.textContent && element.textContent.trim()) {
                return element.textContent.trim();
            }
            if (element.dataset.user) {
                return element.dataset.user;
            }
        }

        return 'SYSTEM';
    }

    getFieldValue(fieldId) {
        /**
         * Get field value, handling both regular inputs and Select2 dropdowns
         * @param {string} fieldId - The field ID (without #)
         * @returns {string} The field value
         */
        const element = $(`#${fieldId}`);

        if (element.length) {
            // Check if it's a Select2 dropdown
            if (element.hasClass('select2-hidden-accessible')) {
                return element.val() || '';
            } else {
                // Regular input/select
                return element.val() || '';
            }
        }

        return '';
    }

    setFieldValue(fieldId, value) {
        /**
         * Set field value safely, handling both regular inputs and potential errors
         * @param {string} fieldId - The field ID (without #)
         * @param {string} value - The value to set
         */
        try {
            const element = document.getElementById(fieldId);
            if (element) {
                element.value = value;
            } else {
                console.warn(`⚠️ ISSUE: Field not found: ${fieldId}`);
            }
        } catch (error) {
            console.error(`❌ ISSUE: Error setting field ${fieldId}:`, error);
        }
    }



    showDetailedTransferError(modalId, result) {
        /**
         * Show detailed transfer error with user guidance
         */
        const modal = document.getElementById(modalId);
        if (!modal) return;

        let errorContainer = modal.querySelector('.transfer-error-container');
        if (!errorContainer) {
            // Create error container if it doesn't exist
            errorContainer = document.createElement('div');
            errorContainer.className = 'transfer-error-container alert alert-danger mt-3';
            errorContainer.style.display = 'none';

            // Insert before the modal footer
            const modalBody = modal.querySelector('.modal-body');
            if (modalBody) {
                modalBody.appendChild(errorContainer);
            }
        }

        // Build detailed error message
        let errorHtml = '<div class="transfer-error-details">';
        errorHtml += '<h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Transfer Failed</h6>';

        if (result.detailed_error) {
            const error = result.detailed_error;
            errorHtml += `<div class="error-code mb-2"><strong>Error Code:</strong> ${error.reasonCode || 'Unknown'}</div>`;
            errorHtml += `<div class="error-message mb-3"><strong>Message:</strong> ${error.message || result.error}</div>`;

            if (result.error_guidance) {
                errorHtml += '<div class="error-guidance">';
                errorHtml += '<strong>How to Fix:</strong>';
                errorHtml += '<div class="mt-2" style="white-space: pre-line;">' + result.error_guidance + '</div>';
                errorHtml += '</div>';
            }
        } else {
            errorHtml += `<div class="error-message mb-3">${result.error || 'Unknown error occurred'}</div>`;
        }

        if (result.user_action_required) {
            errorHtml += '<div class="mt-3 p-2 bg-light rounded">';
            errorHtml += '<small class="text-muted"><i class="fas fa-info-circle me-1"></i>';
            errorHtml += 'Please review and correct the issue above, then try submitting the transfer again.</small>';
            errorHtml += '</div>';
        }

        errorHtml += '</div>';

        errorContainer.innerHTML = errorHtml;
        errorContainer.style.display = 'block';

        // Scroll to error
        errorContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

        console.log('📋 DETAILED ERROR: Displayed detailed error to user:', result);
    }

    buildTransferPayload() {
        // Get form values
        const quantity = parseFloat(document.getElementById('tci_quantity').value);
        const toSite = document.getElementById('tci_to_site').value;
        const toStoreroom = document.getElementById('tci_to_storeroom').value;
        const fromBin = document.getElementById('tci_from_bin').value;
        const toBin = document.getElementById('tci_to_bin').value || document.getElementById('tci_to_bin_manual').value;
        const fromLot = document.getElementById('tci_from_lot').value;
        const toLot = document.getElementById('tci_to_lot').value || document.getElementById('tci_to_lot_manual').value;
        const fromCondition = document.getElementById('tci_from_condition').value;
        const toCondition = document.getElementById('tci_to_condition').value || document.getElementById('tci_to_condition_manual').value;
        const fromIssueUnit = document.getElementById('tci_from_issue_unit').value;
        const toIssueUnit = document.getElementById('tci_to_issue_unit').value;
        const conversionFactor = parseFloat(document.getElementById('tci_conversion_factor').value);

        // Build transfer payload
        const transferData = {
            itemnum: this.currentTransferData.itemData.itemnum,
            quantity: quantity,
            from_siteid: this.currentTransferData.itemData.siteid,
            to_siteid: toSite,
            from_storeroom: this.currentTransferData.itemData.location,
            to_storeroom: toStoreroom,
            from_issue_unit: fromIssueUnit,
            to_issue_unit: toIssueUnit,
            conversion_factor: conversionFactor
        };

        // Add optional fields if provided
        if (fromBin) transferData.from_bin = fromBin;
        if (toBin) transferData.to_bin = toBin;
        if (fromLot) transferData.from_lot = fromLot;
        if (toLot) transferData.to_lot = toLot;
        if (fromCondition) transferData.from_condition = fromCondition;
        if (toCondition) transferData.to_condition = toCondition;

        return transferData;
    }

    showTransferError(message) {
        // Show error in the modal
        this.showModalError('transferCurrentItemModal', message);
    }

    // View Availability Modal Functions
    openAvailabilityModal(itemnum, siteid, inventoryid) {
        console.log(`🔍 AVAILABILITY: Opening modal for Item ${itemnum}, Site ${siteid}`);

        // Get the item data from the button's data attribute
        const button = event.target.closest('button');
        if (!button) {
            console.error('❌ AVAILABILITY: Could not find button element');
            alert('Error: Could not find button element');
            return;
        }

        let itemData;
        try {
            const itemDataAttr = button.getAttribute('data-item-data');
            itemData = itemDataAttr ? JSON.parse(itemDataAttr) : {itemnum, siteid, inventoryid};

            console.log('🔍 AVAILABILITY: Item data:', itemData);

        } catch (error) {
            console.error('❌ AVAILABILITY: Error parsing data:', error);
            alert('Error parsing data: ' + error.message);
            return;
        }

        // Show loading state in modal
        this.showAvailabilityLoading(itemData);

        // Fetch availability data from backend
        this.fetchAvailabilityData(itemData);
    }

    showAvailabilityLoading(itemData) {
        // Create or get the modal
        let modal = document.getElementById('availabilityModal');
        if (!modal) {
            // Create the modal if it doesn't exist
            modal = this.createAvailabilityModal();
            document.body.appendChild(modal);
        }

        // Set modal title
        const modalTitle = modal.querySelector('.modal-title');
        modalTitle.innerHTML = `<i class="fas fa-chart-bar me-2"></i>Item Availability - ${itemData.itemnum}`;

        // Set loading content
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading availability data from Maximo...</p>
                <small class="text-muted">Fetching real-time inventory availability for ${itemData.itemnum} in site ${itemData.siteid}</small>
            </div>
        `;

        // Show the modal
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    async fetchAvailabilityData(itemData) {
        try {
            const url = `/api/inventory/availability/${itemData.itemnum}?siteid=${encodeURIComponent(itemData.siteid)}`;
            console.log('🔍 AVAILABILITY: Fetching data from:', url);

            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            // Check if response is OK
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    this.showAvailabilityError('Authentication required. Please log in to view availability data.');
                    return;
                } else if (response.status === 404) {
                    this.showAvailabilityError('Availability service not found. Please contact support.');
                    return;
                } else {
                    this.showAvailabilityError(`Server error (${response.status}). Please try again later.`);
                    return;
                }
            }

            const data = await response.json();

            if (data.success) {
                this.displayAvailabilityData(data, itemData);
            } else {
                // Handle specific error cases
                if (data.error && (data.error.includes('Not logged in') || data.error.includes('Authentication'))) {
                    this.showAvailabilityError('Please log in to view availability data. <a href="/login/login" class="alert-link">Click here to log in</a>.');
                } else if (data.error && data.error.includes('Site ID is required')) {
                    this.showAvailabilityError('Site ID is required. Please select a valid site.');
                } else {
                    this.showAvailabilityError(data.error || 'Failed to load availability data');
                }
            }
        } catch (error) {
            console.error('❌ AVAILABILITY: Error fetching data:', error);

            // Provide more specific error messages
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                this.showAvailabilityError('Unable to connect to the server. Please check your internet connection and try again.');
            } else if (error.name === 'AbortError') {
                this.showAvailabilityError('Request timed out. Please try again.');
            } else {
                this.showAvailabilityError('Network error occurred while fetching availability data. Please try again.');
            }
        }
    }

    displayAvailabilityData(data, itemData) {
        const modal = document.getElementById('availabilityModal');
        const modalBody = modal.querySelector('.modal-body');

        // Build the comprehensive availability display with tabs
        let html = `
            <div class="availability-summary mb-4">
                <h5><i class="fas fa-chart-line me-2"></i>Comprehensive Availability Summary</h5>
                <div class="row g-2">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Available</h6>
                                <h5>${this.formatNumber(data.availability_summary.total_available_balance)}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Current</h6>
                                <h5>${this.formatNumber(data.availability_summary.total_current_balance)}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Reserved</h6>
                                <h5>${this.formatNumber(data.availability_summary.total_reserved_quantity)}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Locations</h6>
                                <h5>${data.availability_summary.total_locations}</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row g-2 mt-2">
                    <div class="col-md-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Lots/Bins</h6>
                                <h5>${data.availability_summary.total_lots_bins || 0}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-dark text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Reservations</h6>
                                <h5>${data.availability_summary.total_reservation_records || 0}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Purchase Orders</h6>
                                <h5>${data.availability_summary.total_purchase_orders || 0}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light text-dark">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1">Purchase Reqs</h6>
                                <h5>${data.availability_summary.total_purchase_requisitions || 0}</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Data source: ${data.metadata.api_endpoint} |
                        Query time: ${new Date(data.metadata.query_timestamp).toLocaleString()}
                    </small>
                </div>
            </div>
        `;

        // Add diary-style vertical sidebar interface for all availability data
        html += `
            <div class="availability-diary-view">
                <div class="availability-diary-sidebar">
                    <div class="availability-diary-tabs" role="tablist">
                        <button class="availability-diary-tab summary-tab active"
                                id="summary-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#summary"
                                type="button"
                                role="tab"
                                aria-controls="summary"
                                aria-selected="true">
                            <i class="fas fa-chart-line availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">Summary</span>
                        </button>
                        <button class="availability-diary-tab locations-tab"
                                id="locations-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#locations"
                                type="button"
                                role="tab"
                                aria-controls="locations"
                                aria-selected="false">
                            <i class="fas fa-map-marker-alt availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">Locations</span>
                            ${data.inventory_records?.length > 0 ? `<span class="availability-diary-tab-badge">${data.inventory_records.length}</span>` : ''}
                        </button>
                        <button class="availability-diary-tab lots-tab"
                                id="lots-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#lots"
                                type="button"
                                role="tab"
                                aria-controls="lots"
                                aria-selected="false">
                            <i class="fas fa-boxes availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">Lots</span>
                            ${(data.availability_summary.total_lots_bins || 0) > 0 ? `<span class="availability-diary-tab-badge">${data.availability_summary.total_lots_bins}</span>` : ''}
                        </button>
                        <button class="availability-diary-tab purchase-orders-tab"
                                id="purchase-orders-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#purchase-orders"
                                type="button"
                                role="tab"
                                aria-controls="purchase-orders"
                                aria-selected="false">
                            <i class="fas fa-shopping-cart availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">POs</span>
                            ${(data.availability_summary.total_purchase_orders || 0) > 0 ? `<span class="availability-diary-tab-badge">${data.availability_summary.total_purchase_orders}</span>` : ''}
                        </button>
                        <button class="availability-diary-tab purchase-requisitions-tab"
                                id="purchase-requisitions-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#purchase-requisitions"
                                type="button"
                                role="tab"
                                aria-controls="purchase-requisitions"
                                aria-selected="false">
                            <i class="fas fa-file-invoice availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">PRs</span>
                            ${(data.availability_summary.total_purchase_requisitions || 0) > 0 ? `<span class="availability-diary-tab-badge">${data.availability_summary.total_purchase_requisitions}</span>` : ''}
                        </button>
                        <button class="availability-diary-tab reservations-tab"
                                id="reservations-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#reservations"
                                type="button"
                                role="tab"
                                aria-controls="reservations"
                                aria-selected="false">
                            <i class="fas fa-lock availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">Reserved</span>
                            ${(data.availability_summary.total_reservations || 0) > 0 ? `<span class="availability-diary-tab-badge">${data.availability_summary.total_reservations}</span>` : ''}
                        </button>
                        <button class="availability-diary-tab alternates-tab"
                                id="alternates-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#alternates"
                                type="button"
                                role="tab"
                                aria-controls="alternates"
                                aria-selected="false">
                            <i class="fas fa-exchange-alt availability-diary-tab-icon"></i>
                            <span class="availability-diary-tab-name">Alternates</span>
                            ${(data.availability_summary.total_vendor_records || 0) > 0 ? `<span class="availability-diary-tab-badge">${data.availability_summary.total_vendor_records}</span>` : ''}
                        </button>
                    </div>
                </div>
                <div class="availability-diary-content">
                    <div class="tab-content" id="availabilityTabContent">
        `;

        // Tab 0: Summary (mobile-optimized)
        html += this.buildSummaryTab(data);

        // Tab 1: All Locations
        html += this.buildLocationsTab(data.inventory_records);

        // Tab 2: All Lots
        html += this.buildLotsTab(data.inventory_records);

        // Tab 3: Purchase Orders
        html += this.buildPurchaseOrdersTab(data.purchase_orders || []);

        // Tab 4: Purchase Requisitions
        html += this.buildPurchaseRequisitionsTab(data.purchase_requisitions || []);

        // Tab 5: Reservations
        html += this.buildReservationsTab(data.reservations || []);

        // Tab 6: Alternate Items
        html += this.buildAlternatesTab(data.inventory_records);

        html += `
                    </div>
                </div>
            </div>
        `;

        modalBody.innerHTML = html;

        // Setup responsive table scrolling after content is loaded
        setTimeout(() => {
            this.setupResponsiveTableScrolling();
            this.setupAvailabilityDiaryTabs();
        }, 100);
    }

    showAvailabilityError(errorMessage) {
        const modal = document.getElementById('availabilityModal');
        const modalBody = modal.querySelector('.modal-body');

        modalBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error Loading Availability Data</strong>
                <p class="mb-0 mt-2">${errorMessage}</p>
            </div>
        `;
    }

    createAvailabilityModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'availabilityModal';
        modal.tabIndex = -1;
        modal.innerHTML = `
            <div class="modal-dialog modal-fullscreen-sm-down modal-xl">
                <div class="modal-content availability-modal">
                    <div class="modal-header availability-header">
                        <h5 class="modal-title">
                            <i class="fas fa-chart-bar me-2"></i>Item Availability
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Content will be populated dynamically -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;
        return modal;
    }

    // Tab building functions for comprehensive availability data
    buildLocationsTab(records) {
        if (!records || records.length === 0) {
            return `
                <div class="tab-pane fade availability-tab-panel" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                    <div class="availability-tab-header">
                        <h6><i class="fas fa-map-marker-alt me-2"></i>All Locations</h6>
                        <span class="badge bg-secondary">0</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No location data found for this item.
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="tab-pane fade availability-tab-panel" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-map-marker-alt me-2"></i>All Locations</h6>
                    <span class="badge bg-primary">${records.length}</span>
                </div>

                <!-- Desktop Table View -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Location</th>
                                <th>Available</th>
                                <th>Current</th>
                                <th>Reserved</th>
                                <th>Min Level</th>
                                <th>Max Level</th>
                                <th>Status</th>
                                <th>Issue Unit</th>
                                <th>Lots/Bins</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        records.forEach(record => {
            html += `
                <tr>
                    <td><strong>${record.location || '-'}</strong></td>
                    <td class="text-end"><span class="badge bg-primary">${this.formatNumber(record.avblbalance)}</span></td>
                    <td class="text-end"><span class="badge bg-success">${this.formatNumber(record.curbaltotal)}</span></td>
                    <td class="text-end"><span class="badge bg-warning">${this.formatNumber(record.reservedqty)}</span></td>
                    <td class="text-end">${this.formatNumber(record.minlevel) || '-'}</td>
                    <td class="text-end">${this.formatNumber(record.maxlevel) || '-'}</td>
                    <td><span class="badge bg-secondary">${record.status || '-'}</span></td>
                    <td>${record.issueunit || '-'}</td>
                    <td class="text-center"><span class="badge bg-info">${record.balance_count || 0}</span></td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div class="mobile-card-container" id="locations-mobile-cards">
        `;

        // Add mobile card navigation
        html += `
                    <div class="mobile-card-navigation">
                        <button class="mobile-card-nav-button" id="locations-prev-btn" onclick="window.inventoryManager.navigateLocationCards(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="mobile-card-counter" id="locations-counter">1 of ${records.length}</span>
                        <button class="mobile-card-nav-button" id="locations-next-btn" onclick="window.inventoryManager.navigateLocationCards(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
        `;

        // Add mobile cards (initially show only first card)
        records.forEach((record, index) => {
            const isVisible = index === 0 ? '' : 'style="display: none;"';
            html += `
                <div class="mobile-card location-card" data-card-index="${index}" ${isVisible}>
                    <div class="mobile-card-header">
                        <h5 class="mobile-card-title">${record.location || 'Unknown Location'}</h5>
                        <span class="mobile-card-badge status ${(record.status || '').toLowerCase()}">${record.status || '-'}</span>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Available</span>
                            <span class="mobile-card-value highlight">${this.formatNumber(record.avblbalance)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Current</span>
                            <span class="mobile-card-value">${this.formatNumber(record.curbaltotal)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Reserved</span>
                            <span class="mobile-card-value">${this.formatNumber(record.reservedqty)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Issue Unit</span>
                            <span class="mobile-card-value">${record.issueunit || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Min Level</span>
                            <span class="mobile-card-value">${this.formatNumber(record.minlevel) || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Max Level</span>
                            <span class="mobile-card-value">${this.formatNumber(record.maxlevel) || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Lots/Bins</span>
                            <span class="mobile-card-value">${record.balance_count || 0}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    buildLotsTab(records) {
        let allLots = [];
        records.forEach(record => {
            if (record.lots_info && record.lots_info.length > 0) {
                record.lots_info.forEach(lot => {
                    allLots.push({
                        ...lot,
                        location: record.location
                    });
                });
            }
        });

        if (allLots.length === 0) {
            return `
                <div class="tab-pane fade availability-tab-panel" id="lots" role="tabpanel" aria-labelledby="lots-tab">
                    <div class="availability-tab-header">
                        <h6><i class="fas fa-boxes me-2"></i>All Lots</h6>
                        <span class="badge bg-secondary">0</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No lot/bin data found for this item.
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="tab-pane fade availability-tab-panel" id="lots" role="tabpanel" aria-labelledby="lots-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-boxes me-2"></i>All Lots</h6>
                    <span class="badge bg-purple">${allLots.length}</span>
                </div>

                <!-- Desktop Table View -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Location</th>
                                <th>Bin Number</th>
                                <th>Lot Number</th>
                                <th>Condition</th>
                                <th>Current Balance</th>
                                <th>Physical Count</th>
                                <th>Last Count Date</th>
                                <th>Reconciled</th>
                                <th>Staged</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        allLots.forEach(lot => {
            html += `
                <tr>
                    <td><strong>${lot.location || '-'}</strong></td>
                    <td>${lot.binnum || '-'}</td>
                    <td>${lot.lotnum || '-'}</td>
                    <td><span class="badge bg-secondary">${lot.conditioncode || '-'}</span></td>
                    <td class="text-end"><span class="badge bg-primary">${this.formatNumber(lot.curbal)}</span></td>
                    <td class="text-end">${this.formatNumber(lot.physcnt) || '-'}</td>
                    <td>${lot.physcntdate ? new Date(lot.physcntdate).toLocaleDateString() : '-'}</td>
                    <td class="text-center">${lot.reconciled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-warning">No</span>'}</td>
                    <td class="text-end">${this.formatNumber(lot.stagedcurbal) || '0'}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div class="mobile-card-container" id="lots-mobile-cards">
                    <div class="mobile-card-navigation">
                        <button class="mobile-card-nav-button" id="lots-prev-btn" onclick="window.inventoryManager.navigateLotsCards(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="mobile-card-counter" id="lots-counter">1 of ${allLots.length}</span>
                        <button class="mobile-card-nav-button" id="lots-next-btn" onclick="window.inventoryManager.navigateLotsCards(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
        `;

        // Add mobile cards for lots
        allLots.forEach((lot, index) => {
            const isVisible = index === 0 ? '' : 'style="display: none;"';
            html += `
                <div class="mobile-card lot-card" data-card-index="${index}" ${isVisible}>
                    <div class="mobile-card-header">
                        <h5 class="mobile-card-title">${lot.location || 'Unknown Location'} - ${lot.binnum || 'No Bin'}</h5>
                        <span class="mobile-card-badge">${lot.conditioncode || '-'}</span>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Lot Number</span>
                            <span class="mobile-card-value highlight">${lot.lotnum || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Current Balance</span>
                            <span class="mobile-card-value">${this.formatNumber(lot.curbal)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Physical Count</span>
                            <span class="mobile-card-value">${this.formatNumber(lot.physcnt) || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Staged Balance</span>
                            <span class="mobile-card-value">${this.formatNumber(lot.stagedcurbal) || '0'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Last Count Date</span>
                            <span class="mobile-card-value">${lot.physcntdate ? new Date(lot.physcntdate).toLocaleDateString() : '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Reconciled</span>
                            <span class="mobile-card-value status ${lot.reconciled ? 'active' : 'inactive'}">${lot.reconciled ? 'Yes' : 'No'}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    buildPurchaseOrdersTab(purchaseOrders) {
        if (!purchaseOrders || purchaseOrders.length === 0) {
            return `
                <div class="tab-pane fade availability-tab-panel" id="purchase-orders" role="tabpanel" aria-labelledby="purchase-orders-tab">
                    <div class="availability-tab-header">
                        <h6><i class="fas fa-shopping-cart me-2"></i>Active Purchase Orders</h6>
                        <span class="badge bg-secondary">0</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>No Active Purchase Orders Found</strong><br>
                        No active purchase orders found for this item in the selected site.
                        This shows only active POs (excludes CAN, CLOSE, REVISD statuses).
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="tab-pane fade availability-tab-panel" id="purchase-orders" role="tabpanel" aria-labelledby="purchase-orders-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-shopping-cart me-2"></i>Active Purchase Orders</h6>
                    <span class="badge bg-success">${purchaseOrders.length}</span>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover po-pr-table">
                        <thead class="table-dark">
                            <tr>
                                <th>PO</th>
                                <th>Storeroom</th>
                                <th>Internal?</th>
                                <th>Status</th>
                                <th>Currency</th>
                                <th>Unit Cost</th>
                                <th>Quantity Ordered</th>
                                <th>Quantity Received</th>
                                <th>Vendor</th>
                                <th>Order Date</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        purchaseOrders.forEach(po => {
            html += `
                <tr>
                    <td><strong>${po.ponum || '-'}</strong></td>
                    <td>${po.storeroom || '-'}</td>
                    <td class="text-center">${po.internal ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                    <td><span class="badge bg-primary">${po.status || '-'}</span></td>
                    <td>${po.currency || '-'}</td>
                    <td class="text-end">$${this.formatNumber(po.unit_cost)}</td>
                    <td class="text-end"><span class="badge bg-info">${this.formatNumber(po.quantity_ordered)}</span></td>
                    <td class="text-end"><span class="badge bg-success">${this.formatNumber(po.quantity_received)}</span></td>
                    <td>${po.vendor || '-'}</td>
                    <td>${po.order_date ? new Date(po.order_date).toLocaleDateString() : '-'}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div class="mobile-card-container" id="purchase-orders-mobile-cards">
                    <div class="mobile-card-navigation">
                        <button class="mobile-card-nav-button" id="purchase-orders-prev-btn" onclick="window.inventoryManager.navigatePOCards(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="mobile-card-counter" id="purchase-orders-counter">1 of ${purchaseOrders.length}</span>
                        <button class="mobile-card-nav-button" id="purchase-orders-next-btn" onclick="window.inventoryManager.navigatePOCards(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
        `;

        // Add mobile cards for purchase orders
        purchaseOrders.forEach((po, index) => {
            const isVisible = index === 0 ? '' : 'style="display: none;"';
            html += `
                <div class="mobile-card po-card" data-card-index="${index}" ${isVisible}>
                    <div class="mobile-card-header">
                        <h5 class="mobile-card-title">PO ${po.ponum || 'Unknown'}</h5>
                        <span class="mobile-card-badge status ${(po.status || '').toLowerCase()}">${po.status || '-'}</span>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Vendor</span>
                            <span class="mobile-card-value highlight">${po.vendor || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Storeroom</span>
                            <span class="mobile-card-value">${po.storeroom || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Unit Cost</span>
                            <span class="mobile-card-value currency">$${this.formatNumber(po.unit_cost)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Currency</span>
                            <span class="mobile-card-value">${po.currency || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Qty Ordered</span>
                            <span class="mobile-card-value">${this.formatNumber(po.quantity_ordered)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Qty Received</span>
                            <span class="mobile-card-value">${this.formatNumber(po.quantity_received)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Order Date</span>
                            <span class="mobile-card-value">${po.order_date ? new Date(po.order_date).toLocaleDateString() : '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Internal</span>
                            <span class="mobile-card-value status ${po.internal ? 'active' : 'inactive'}">${po.internal ? 'Yes' : 'No'}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>

                <div class="mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>Active Purchase Orders</h6>
                    <p class="text-muted small">
                        This tab shows ACTIVE purchase orders with statuses like: <strong>APPR</strong> (Approved), <strong>INPRG</strong> (In Progress), <strong>WAPPR</strong> (Waiting Approval), <strong>WMATL</strong> (Waiting Material), <strong>PARTIAL</strong> (Partially Received).
                        <br><span class="text-danger">Excluded:</span> CAN (Cancelled), CLOSE (Closed), REVISD (Revised).
                    </p>
                </div>
            </div>
        `;

        return html;
    }

    buildPurchaseRequisitionsTab(purchaseRequisitions) {
        if (!purchaseRequisitions || purchaseRequisitions.length === 0) {
            return `
                <div class="tab-pane fade availability-tab-panel" id="purchase-requisitions" role="tabpanel" aria-labelledby="purchase-requisitions-tab">
                    <div class="availability-tab-header">
                        <h6><i class="fas fa-file-invoice me-2"></i>Active Purchase Requisitions</h6>
                        <span class="badge bg-secondary">0</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>No Active Purchase Requisitions Found</strong><br>
                        No active purchase requisitions found for this item in the selected site.
                        This shows only active PRs (excludes CAN, CLOSE, REVISD statuses).
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="tab-pane fade availability-tab-panel" id="purchase-requisitions" role="tabpanel" aria-labelledby="purchase-requisitions-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-file-invoice me-2"></i>Active Purchase Requisitions</h6>
                    <span class="badge bg-warning">${purchaseRequisitions.length}</span>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover po-pr-table">
                        <thead class="table-dark">
                            <tr>
                                <th>PR</th>
                                <th>Storeroom</th>
                                <th>Internal?</th>
                                <th>Status</th>
                                <th>Currency</th>
                                <th>Unit Cost</th>
                                <th>Quantity Ordered</th>
                                <th>Requested By</th>
                                <th>Request Date</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        purchaseRequisitions.forEach(pr => {
            html += `
                <tr>
                    <td><strong>${pr.prnum || '-'}</strong></td>
                    <td>${pr.storeroom || '-'}</td>
                    <td class="text-center">${pr.internal ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                    <td><span class="badge bg-primary">${pr.status || '-'}</span></td>
                    <td>${pr.currency || '-'}</td>
                    <td class="text-end">$${this.formatNumber(pr.unit_cost || 0)}</td>
                    <td class="text-end"><span class="badge bg-info">${this.formatNumber(pr.quantity_ordered || 0)}</span></td>
                    <td>${pr.requested_by || '-'}</td>
                    <td>${pr.request_date ? new Date(pr.request_date).toLocaleDateString() : '-'}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div class="mobile-card-container" id="purchase-requisitions-mobile-cards">
                    <div class="mobile-card-navigation">
                        <button class="mobile-card-nav-button" id="purchase-requisitions-prev-btn" onclick="window.inventoryManager.navigatePRCards(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="mobile-card-counter" id="purchase-requisitions-counter">1 of ${purchaseRequisitions.length}</span>
                        <button class="mobile-card-nav-button" id="purchase-requisitions-next-btn" onclick="window.inventoryManager.navigatePRCards(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
        `;

        // Add mobile cards for purchase requisitions
        purchaseRequisitions.forEach((pr, index) => {
            const isVisible = index === 0 ? '' : 'style="display: none;"';
            html += `
                <div class="mobile-card pr-card" data-card-index="${index}" ${isVisible}>
                    <div class="mobile-card-header">
                        <h5 class="mobile-card-title">PR ${pr.prnum || 'Unknown'}</h5>
                        <span class="mobile-card-badge status ${(pr.status || '').toLowerCase()}">${pr.status || '-'}</span>
                    </div>
                    <div class="mobile-card-body">
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Requested By</span>
                            <span class="mobile-card-value highlight">${pr.requested_by || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Storeroom</span>
                            <span class="mobile-card-value">${pr.storeroom || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Unit Cost</span>
                            <span class="mobile-card-value currency">$${this.formatNumber(pr.unit_cost || 0)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Currency</span>
                            <span class="mobile-card-value">${pr.currency || '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Qty Ordered</span>
                            <span class="mobile-card-value">${this.formatNumber(pr.quantity_ordered || 0)}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Request Date</span>
                            <span class="mobile-card-value">${pr.request_date ? new Date(pr.request_date).toLocaleDateString() : '-'}</span>
                        </div>
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Internal</span>
                            <span class="mobile-card-value status ${pr.internal ? 'active' : 'inactive'}">${pr.internal ? 'Yes' : 'No'}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>

                <div class="mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>Active Purchase Requisitions</h6>
                    <p class="text-muted small">
                        This tab shows ACTIVE purchase requisitions with statuses like: <strong>APPR</strong> (Approved), <strong>INPRG</strong> (In Progress), <strong>WAPPR</strong> (Waiting Approval), <strong>ORDERED</strong> (Ordered), <strong>PARTIAL</strong> (Partially Received).
                        <br><span class="text-danger">Excluded:</span> CAN (Cancelled), CLOSE (Closed), REVISD (Revised).
                    </p>
                </div>
            </div>
        `;

        return html;
    }

    buildReservationsTab(reservations) {
        if (!reservations || reservations.length === 0) {
            return `
                <div class="tab-pane fade availability-tab-panel" id="reservations" role="tabpanel" aria-labelledby="reservations-tab">
                    <div class="availability-tab-header">
                        <h6><i class="fas fa-lock me-2"></i>Reservations</h6>
                        <span class="badge bg-secondary">0</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>No Reservations Found</strong><br>
                        No reservations found for this item in the selected site and locations.
                    </div>
                    <div class="mt-3">
                        <h6><i class="fas fa-info-circle me-2"></i>Reservations Data</h6>
                        <p class="text-muted small">
                            This tab shows reservation data from MXAPIINVRES endpoint.
                            Data includes request numbers, reservation types, work orders, tasks, and transfer information.
                        </p>
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="tab-pane fade availability-tab-panel" id="reservations" role="tabpanel" aria-labelledby="reservations-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-lock me-2"></i>Reservations</h6>
                    <span class="badge bg-danger">${reservations.length}</span>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover po-pr-table reservations-table">
                        <thead class="table-dark">
                            <tr>
                                <th>Request</th>
                                <th>Reservation Type</th>
                                <th>Item</th>
                                <th>Item Type</th>
                                <th>Description</th>
                                <th>Storeroom</th>
                                <th>Storeroom Site</th>
                                <th>Condition Code</th>
                                <th>Reserved Quantity</th>
                                <th>Work Order</th>
                                <th>Task</th>
                                <th>To Storeroom</th>
                                <th>To Site</th>
                                <th>Purchase Order</th>
                                <th>Required Date</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        reservations.forEach(reservation => {
            html += `
                <tr>
                    <td><strong>${reservation.request_num || '-'}</strong></td>
                    <td><span class="badge bg-primary">${reservation.reservation_type || '-'}</span></td>
                    <td>${reservation.item_num || '-'}</td>
                    <td><span class="badge bg-secondary">${reservation.item_type || '-'}</span></td>
                    <td class="text-truncate" style="max-width: 200px;" title="${reservation.description || '-'}">${reservation.description || '-'}</td>
                    <td>${reservation.storeroom || '-'}</td>
                    <td>${reservation.storeroom_site || '-'}</td>
                    <td><span class="badge bg-info">${reservation.condition_code || '-'}</span></td>
                    <td class="text-end"><span class="badge bg-warning">${this.formatNumber(reservation.reserved_quantity)}</span></td>
                    <td>${reservation.work_order || '-'}</td>
                    <td>${reservation.task || '-'}</td>
                    <td>${reservation.to_storeroom || '-'}</td>
                    <td>${reservation.to_site || '-'}</td>
                    <td>${reservation.purchase_order || '-'}</td>
                    <td>${reservation.required_date ? new Date(reservation.required_date).toLocaleDateString() : '-'}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>Reservations Data</h6>
                    <p class="text-muted small">
                        This tab shows reservation data from MXAPIINVRES endpoint.
                        Data includes request numbers, reservation types, work orders, tasks, and transfer information.
                        Reservations are filtered by item number, site ID, and inventory locations.
                    </p>
                </div>
            </div>
        `;

        return html;
    }

    setupResponsiveTableScrolling() {
        /**
         * Setup horizontal scroll indicators for responsive tables
         */
        const tableContainers = document.querySelectorAll('.table-responsive');

        tableContainers.forEach(container => {
            const table = container.querySelector('table');
            if (!table) return;

            const updateScrollIndicators = () => {
                const scrollLeft = container.scrollLeft;
                const scrollWidth = container.scrollWidth;
                const clientWidth = container.clientWidth;
                const maxScrollLeft = scrollWidth - clientWidth;

                // Add/remove scroll indicator classes
                container.classList.toggle('scrolled-left', scrollLeft > 0);
                container.classList.toggle('scrolled-right', scrollLeft < maxScrollLeft - 1);
            };

            // Initial check
            updateScrollIndicators();

            // Update on scroll
            container.addEventListener('scroll', updateScrollIndicators);

            // Update on resize
            window.addEventListener('resize', updateScrollIndicators);
        });
    }

    buildAlternatesTab(records) {
        let allAlternates = [];
        records.forEach(record => {
            if (record.alternate_items && record.alternate_items.length > 0) {
                record.alternate_items.forEach(alternate => {
                    allAlternates.push({
                        ...alternate,
                        location: record.location
                    });
                });
            }
        });

        if (allAlternates.length === 0) {
            return `
                <div class="tab-pane fade availability-tab-panel" id="alternates" role="tabpanel" aria-labelledby="alternates-tab">
                    <div class="availability-tab-header">
                        <h6><i class="fas fa-exchange-alt me-2"></i>Alternate Items</h6>
                        <span class="badge bg-secondary">0</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No alternate item/vendor data found for this item.
                    </div>
                </div>
            `;
        }

        let html = `
            <div class="tab-pane fade availability-tab-panel" id="alternates" role="tabpanel" aria-labelledby="alternates-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-exchange-alt me-2"></i>Alternate Items</h6>
                    <span class="badge bg-pink">${allAlternates.length}</span>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Item</th>
                                <th>Description</th>
                                <th>Location</th>
                                <th>Vendor</th>
                                <th>Manufacturer</th>
                                <th>Model Number</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        allAlternates.forEach(alternate => {
            html += `
                <tr>
                    <td><strong>${alternate.catalogcode || alternate.modelnum || '-'}</strong></td>
                    <td>${alternate.description || '-'}</td>
                    <td>${alternate.location || '-'}</td>
                    <td>${alternate.vendor || '-'}</td>
                    <td>${alternate.manufacturer || '-'}</td>
                    <td>${alternate.modelnum || '-'}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        return html;
    }

    // Build compact summary tab for mobile-optimized availability overview
    buildSummaryTab(data) {
        const summary = data.availability_summary;

        let html = `
            <div class="tab-pane fade show active availability-tab-panel" id="summary" role="tabpanel" aria-labelledby="summary-tab">
                <div class="availability-tab-header">
                    <h6><i class="fas fa-chart-line me-2"></i>Availability Summary</h6>
                    <span class="badge bg-info">${summary.total_locations || 0}</span>
                </div>

                <!-- Mobile-optimized compact summary grid -->
                <div class="mobile-summary-grid">
                    <div class="summary-item primary">
                        <div class="summary-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-label">Available</div>
                            <div class="summary-value">${this.formatNumber(summary.total_available_balance)}</div>
                        </div>
                    </div>

                    <div class="summary-item success">
                        <div class="summary-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-label">Current</div>
                            <div class="summary-value">${this.formatNumber(summary.total_current_balance)}</div>
                        </div>
                    </div>

                    <div class="summary-item warning">
                        <div class="summary-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-label">Reserved</div>
                            <div class="summary-value">${this.formatNumber(summary.total_reserved_quantity)}</div>
                        </div>
                    </div>

                    <div class="summary-item info">
                        <div class="summary-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-label">Locations</div>
                            <div class="summary-value">${summary.total_locations || 0}</div>
                        </div>
                    </div>

                    <div class="summary-item secondary">
                        <div class="summary-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-label">Lots/Bins</div>
                            <div class="summary-value">${summary.total_lots_bins || 0}</div>
                        </div>
                    </div>

                    <div class="summary-item danger">
                        <div class="summary-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-label">Purchase Orders</div>
                            <div class="summary-value">${summary.total_purchase_orders || 0}</div>
                        </div>
                    </div>
                </div>

                <!-- Data source info -->
                <div class="summary-metadata">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Source: ${data.metadata?.api_endpoint?.split('/').pop() || 'MXAPIINVENTORY'} |
                        ${new Date(data.metadata?.query_timestamp || Date.now()).toLocaleTimeString()}
                    </small>
                </div>
            </div>
        `;

        return html;
    }

    // Setup diary-style tabs for availability modal with smooth transitions
    setupAvailabilityDiaryTabs() {
        const tabs = document.querySelectorAll('.availability-diary-tab');
        const tabPanes = document.querySelectorAll('.availability-tab-panel');

        if (!tabs.length || !tabPanes.length) {
            console.log('🔍 AVAILABILITY: No diary tabs found, skipping setup');
            return;
        }

        console.log(`🔍 AVAILABILITY: Setting up ${tabs.length} diary tabs with smooth transitions`);

        tabs.forEach(tab => {
            tab.addEventListener('click', async (e) => {
                e.preventDefault();

                const targetId = tab.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                if (!targetPane) {
                    console.error('🔍 AVAILABILITY: Target pane not found:', targetId);
                    return;
                }

                // Remove active state from all tabs and panes
                tabs.forEach(t => {
                    t.classList.remove('active');
                    t.setAttribute('aria-selected', 'false');
                });

                tabPanes.forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Add fade-out transition to current content
                const currentActivePane = document.querySelector('.availability-tab-panel.show');
                if (currentActivePane) {
                    currentActivePane.style.opacity = '0';
                    currentActivePane.style.transform = 'translateX(-10px)';
                }

                // Wait for fade-out transition
                await new Promise(resolve => setTimeout(resolve, 150));

                // Activate new tab and pane
                tab.classList.add('active');
                tab.setAttribute('aria-selected', 'true');

                targetPane.classList.add('show', 'active');

                // Add fade-in transition
                targetPane.style.opacity = '0';
                targetPane.style.transform = 'translateX(10px)';

                // Force reflow
                targetPane.offsetHeight;

                // Animate in
                targetPane.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
                targetPane.style.opacity = '1';
                targetPane.style.transform = 'translateX(0)';

                // Clean up transition styles after animation
                setTimeout(() => {
                    targetPane.style.transition = '';
                    targetPane.style.transform = '';
                    targetPane.style.opacity = '';
                }, 200);

                console.log(`🔍 AVAILABILITY: Switched to tab: ${targetId}`);
            });

            // Add touch optimization for mobile
            tab.addEventListener('touchstart', (e) => {
                // Add visual feedback for touch
                tab.style.transform = 'translateX(1px) scale(0.98)';
            });

            tab.addEventListener('touchend', (e) => {
                // Reset visual feedback
                setTimeout(() => {
                    tab.style.transform = '';
                }, 100);
            });
        });

        // Ensure first tab is active
        if (tabs.length > 0 && tabPanes.length > 0) {
            tabs[0].classList.add('active');
            tabs[0].setAttribute('aria-selected', 'true');
            tabPanes[0].classList.add('show', 'active');
        }

        console.log('🔍 AVAILABILITY: Diary tabs setup complete with smooth transitions');
    }

    // Mobile card navigation functions for availability modal
    navigateLocationCards(direction) {
        this.navigateCards('locations', direction);
    }

    navigateLotsCards(direction) {
        this.navigateCards('lots', direction);
    }

    navigatePOCards(direction) {
        this.navigateCards('purchase-orders', direction);
    }

    navigatePRCards(direction) {
        this.navigateCards('purchase-requisitions', direction);
    }

    navigateReservationCards(direction) {
        this.navigateCards('reservations', direction);
    }

    navigateAlternateCards(direction) {
        this.navigateCards('alternates', direction);
    }

    navigateCards(tabType, direction) {
        const container = document.getElementById(`${tabType}-mobile-cards`);
        if (!container) return;

        const cards = container.querySelectorAll('.mobile-card');
        const counter = document.getElementById(`${tabType}-counter`);
        const prevBtn = document.getElementById(`${tabType}-prev-btn`);
        const nextBtn = document.getElementById(`${tabType}-next-btn`);

        if (!cards.length) return;

        // Find currently visible card
        let currentIndex = 0;
        cards.forEach((card, index) => {
            if (card.style.display !== 'none') {
                currentIndex = index;
            }
        });

        // Calculate new index
        let newIndex = currentIndex + direction;
        if (newIndex < 0) newIndex = cards.length - 1;
        if (newIndex >= cards.length) newIndex = 0;

        // Hide all cards
        cards.forEach(card => {
            card.style.display = 'none';
        });

        // Show new card with animation
        const newCard = cards[newIndex];
        newCard.style.display = 'block';
        newCard.style.opacity = '0';
        newCard.style.transform = direction > 0 ? 'translateX(20px)' : 'translateX(-20px)';

        // Animate in
        setTimeout(() => {
            newCard.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            newCard.style.opacity = '1';
            newCard.style.transform = 'translateX(0)';
        }, 10);

        // Update counter
        if (counter) {
            counter.textContent = `${newIndex + 1} of ${cards.length}`;
        }

        // Update button states
        if (prevBtn) {
            prevBtn.disabled = false;
        }
        if (nextBtn) {
            nextBtn.disabled = false;
        }

        console.log(`📱 MOBILE CARDS: Navigated to ${tabType} card ${newIndex + 1}/${cards.length}`);
    }

    // Debug function to test work order loading - can be called from browser console
    debugWorkOrderLoading() {
        console.log('🔧 DEBUG: Testing work order loading...');
        console.log('🔧 DEBUG: Current issue data:', this.currentIssueData);

        if (this.currentIssueData && this.currentIssueData.siteid) {
            console.log(`🔧 DEBUG: Calling loadIssueWorkOrders with siteid: ${this.currentIssueData.siteid}`);
            this.loadIssueWorkOrders(this.currentIssueData.siteid);
        } else {
            console.log('🔧 DEBUG: No current issue data available');
            // Test with a known site
            console.log('🔧 DEBUG: Testing with LCVKWT site');
            this.loadIssueWorkOrders('LCVKWT');
        }
    }

    // Debug function to test all dropdowns - can be called from browser console
    debugAllDropdowns() {
        console.log('🔧 DEBUG: Testing all issue modal dropdowns...');

        const testSite = 'LCVKWT';
        const testItem = 'TEST001';

        console.log('🔧 DEBUG: Testing work orders...');
        this.loadIssueWorkOrders(testSite);

        console.log('🔧 DEBUG: Testing GL accounts...');
        this.loadIssueGLAccounts();

        console.log('🔧 DEBUG: Testing persons...');
        // Persons dropdown uses AJAX search - no pre-loading needed

        console.log('🔧 DEBUG: Testing assets...');
        this.loadIssueAssets(testSite);

        console.log('🔧 DEBUG: Testing locations...');
        this.loadIssueLocations(testSite);

        console.log('🔧 DEBUG: Testing requisitions...');
        this.loadIssueRequisitions(testSite);

        console.log('🔧 DEBUG: Testing sites...');
        this.loadIssueSites();

        console.log('🔧 DEBUG: Testing issue units...');
        this.loadIssueUnits(testItem);

        console.log('🔧 DEBUG: All dropdown tests initiated. Check console for results.');
    }
}

// Global functions
function clearInventoryCache() {
    fetch('/api/inventory/management/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ Inventory cache cleared');
            // Show success notification
            showNotification('Cache cleared successfully', 'success');

            // Refresh current search if any
            if (inventoryManager.currentSearchTerm) {
                inventoryManager.performSearch(0);
            }
        } else {
            console.error('❌ Failed to clear cache:', data.error);
            showNotification('Failed to clear cache', 'error');
        }
    })
    .catch(error => {
        console.error('❌ Error clearing cache:', error);
        showNotification('Error clearing cache', 'error');
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is ready
let inventoryManager;
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inventory Management page loaded');
    inventoryManager = new InventoryManagement();

    // Make it globally accessible for onclick handlers
    window.inventoryManager = inventoryManager;
});

// Global functions for HTML onclick handlers
function generateQRCodeForItem(itemnum, inventoryid) {
    if (window.inventoryManager) {
        window.inventoryManager.generateQRCodeForItem(itemnum, inventoryid);
    }
}



function showInventoryItemDetails(itemnum, siteid, inventoryid) {
    if (window.inventoryManager) {
        window.inventoryManager.showItemDetails(itemnum, siteid, inventoryid);
    }
}

function downloadQRCode() {
    const img = document.getElementById('qrCodeImage');
    if (img && img.src) {
        const itemnum = img.getAttribute('data-itemnum') || 'unknown';
        const link = document.createElement('a');
        link.download = `qr-code-${itemnum}.png`;
        link.href = img.src;
        link.click();
    }
}

function printQRCode() {
    const img = document.getElementById('qrCodeImage');
    if (img && img.src) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head><title>QR Code</title></head>
                <body style="text-align: center; margin: 20px;">
                    <img src="${img.src}" style="max-width: 100%;">
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}
