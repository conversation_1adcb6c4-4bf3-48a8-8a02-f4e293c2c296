/**
 * Asset Management JavaScript
 * 
 * Handles search, pagination, and display of asset items
 * with responsive design and theme support.
 */

class AssetManagement {
    constructor() {
        this.currentPage = 0;
        this.totalPages = 0;
        this.currentSearchTerm = '';
        this.currentSiteId = '';
        this.currentStatusFilter = '';
        this.currentTypeFilter = '';
        this.currentLimit = 20;
        this.isSearching = false;
        this.searchTimeout = null;
        this.sortColumn = null;
        this.sortDirection = 'asc';

        this.init();
        this.initializeResponsiveHandling();
    }

    init() {
        console.log('🔧 Initializing Asset Management');

        // Load available sites first
        this.loadAvailableSites();

        // Initialize event listeners
        this.initializeEventListeners();

        // Set initial limit based on screen size
        this.setInitialLimit();

        console.log('✅ Asset Management initialized');
    }

    async loadAvailableSites() {
        try {
            const response = await fetch('/api/inventory/available-sites');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateSiteDropdown(result.sites, result.default_site);
                this.currentSiteId = result.default_site;
                console.log(`🏢 ASSET: Loaded ${result.sites.length} sites, default: ${result.default_site}`);
            } else {
                console.error('Failed to load sites:', result.error);
                this.showSiteLoadError();
            }
        } catch (error) {
            console.error('Error loading available sites:', error);
            this.showSiteLoadError();
        }
    }

    populateSiteDropdown(sites, defaultSite) {
        const siteFilter = document.getElementById('assetSiteFilter');

        // Clear existing options
        siteFilter.innerHTML = '';

        // Add "All Sites" option
        const allOption = document.createElement('option');
        allOption.value = '';
        allOption.textContent = 'All Sites';
        siteFilter.appendChild(allOption);

        if (sites.length === 0) {
            const noSitesOption = document.createElement('option');
            noSitesOption.value = '';
            noSitesOption.textContent = 'No sites available';
            siteFilter.appendChild(noSitesOption);
            return;
        }

        // Add sites as options
        sites.forEach(site => {
            const option = document.createElement('option');
            option.value = site.siteid;
            option.textContent = `${site.siteid}${site.description !== site.siteid ? ' - ' + site.description : ''}`;

            // Pre-select the user's default site
            if (site.siteid === defaultSite) {
                option.selected = true;
            }

            siteFilter.appendChild(option);
        });

        console.log(`✅ Loaded ${sites.length} available sites for assets, default: ${defaultSite}`);
    }

    showSiteLoadError() {
        const siteFilter = document.getElementById('assetSiteFilter');
        siteFilter.innerHTML = '<option value="">Error loading sites</option>';
    }

    setInitialLimit() {
        const isMobile = window.innerWidth <= 768;
        const limitSelect = document.getElementById('assetLimitFilter');
        
        if (isMobile) {
            limitSelect.value = '10';
            this.currentLimit = 10;
        } else {
            limitSelect.value = '20';
            this.currentLimit = 20;
        }
    }

    initializeEventListeners() {
        // Search form submission
        const searchForm = document.getElementById('assetManagementSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Real-time search on input
        const searchInput = document.getElementById('assetSearchTerm');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    if (e.target.value.length >= 2 || e.target.value.length === 0) {
                        this.performSearch();
                    }
                }, 500);
            });
        }

        // Filter changes
        const filters = ['assetSiteFilter', 'assetStatusFilter', 'assetTypeFilter', 'assetLimitFilter'];
        filters.forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', () => {
                    this.currentPage = 0; // Reset to first page
                    this.performSearch();
                });
            }
        });

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('selectAllAssetsCheckbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
    }

    initializeResponsiveHandling() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResponsiveChanges();
        });

        // Initial responsive setup
        this.handleResponsiveChanges();
    }

    handleResponsiveChanges() {
        const isMobile = window.innerWidth <= 768;
        const tableView = document.getElementById('assetTableView');
        const cardView = document.getElementById('assetCardView');

        if (isMobile) {
            if (tableView) tableView.style.display = 'none';
            if (cardView) cardView.style.display = 'block';
        } else {
            if (tableView) tableView.style.display = 'block';
            if (cardView) cardView.style.display = 'none';
        }
    }

    async performSearch() {
        if (this.isSearching) {
            console.log('🔍 ASSET: Search already in progress, skipping...');
            return;
        }

        this.isSearching = true;
        this.showLoadingState();

        try {
            // Get search parameters
            const searchTerm = document.getElementById('assetSearchTerm')?.value?.trim() || '';
            const siteId = document.getElementById('assetSiteFilter')?.value || '';
            const statusFilter = document.getElementById('assetStatusFilter')?.value || '';
            const typeFilter = document.getElementById('assetTypeFilter')?.value || '';
            const limit = parseInt(document.getElementById('assetLimitFilter')?.value || '20');

            // Update current state
            this.currentSearchTerm = searchTerm;
            this.currentSiteId = siteId;
            this.currentStatusFilter = statusFilter;
            this.currentTypeFilter = typeFilter;
            this.currentLimit = limit;

            console.log(`🔍 ASSET: Searching for "${searchTerm}" in site "${siteId}" with status "${statusFilter}" and type "${typeFilter}"`);

            // Build query parameters
            const params = new URLSearchParams({
                q: searchTerm,
                siteid: siteId,
                status: statusFilter,
                type: typeFilter,
                limit: limit.toString(),
                page: this.currentPage.toString()
            });

            // Make API request
            const response = await fetch(`/api/asset/search?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.displaySearchResults(result.assets, result.metadata);
                this.updateResultsInfo(result.metadata);
                this.updatePagination(result.metadata);
            } else {
                this.showError(result.error || 'Search failed');
            }

        } catch (error) {
            console.error('Asset search error:', error);
            this.showError('Failed to search assets. Please try again.');
        } finally {
            this.isSearching = false;
            this.hideLoadingState();
        }
    }

    displaySearchResults(assets, metadata) {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            this.displayMobileCards(assets);
        } else {
            this.displayDesktopTable(assets);
        }

        // Show/hide export button
        const exportBtn = document.getElementById('exportAssetsBtn');
        if (exportBtn) {
            exportBtn.style.display = assets.length > 0 ? 'inline-block' : 'none';
        }

        // Show/hide no results message
        const noResults = document.getElementById('assetNoResults');
        if (noResults) {
            noResults.style.display = assets.length === 0 ? 'block' : 'none';
        }
    }

    displayDesktopTable(assets) {
        const tableBody = document.getElementById('assetTableBody');
        if (!tableBody) return;

        if (assets.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="11" class="text-center text-muted">No assets found</td></tr>';
            return;
        }

        tableBody.innerHTML = assets.map(asset => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input asset-checkbox" value="${asset.assetnum}" data-siteid="${asset.siteid}">
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="showAssetDetails('${asset.assetnum}', '${asset.siteid}')" class="text-decoration-none">
                        <strong class="text-primary">${asset.assetnum || '-'}</strong>
                    </a>
                </td>
                <td>
                    <span class="badge bg-secondary">${asset.siteid || '-'}</span>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${asset.description || ''}">
                        ${asset.description || '-'}
                    </div>
                </td>
                <td>${this.getStatusBadge(asset.status)}</td>
                <td>
                    <div>
                        <strong>${asset.location || '-'}</strong>
                        ${asset.location_description ? `<br><small class="text-muted">${asset.location_description}</small>` : ''}
                    </div>
                </td>
                <td>${asset.assettag || '-'}</td>
                <td>${asset.serialnum || '-'}</td>
                <td>${asset.model || '-'}</td>
                <td>${this.getTypeBadge(asset)}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="showAssetDetails('${asset.assetnum}', '${asset.siteid}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="showRelatedRecords('${asset.assetnum}', '${asset.siteid}')" title="Work Orders & Service Requests">
                            <i class="fas fa-list-alt"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    displayMobileCards(assets) {
        const cardView = document.getElementById('assetCardView');
        if (!cardView) return;

        if (assets.length === 0) {
            cardView.innerHTML = '<div class="text-center text-muted">No assets found</div>';
            return;
        }

        // Store assets for navigation
        this.mobileAssets = assets;
        this.currentMobileIndex = 0;

        // Create single card view with navigation
        this.renderMobileSingleCard();
    }

    renderMobileSingleCard() {
        const cardView = document.getElementById('assetCardView');
        if (!cardView || !this.mobileAssets || this.mobileAssets.length === 0) return;

        const asset = this.mobileAssets[this.currentMobileIndex];
        const totalAssets = this.mobileAssets.length;
        const currentPosition = this.currentMobileIndex + 1;

        cardView.innerHTML = `
            <div class="mobile-single-card-container">
                <!-- Navigation Header -->
                <div class="mobile-nav-header">
                    <div class="mobile-nav-counter">
                        <span class="current-position">${currentPosition}</span>
                        <span class="separator">of</span>
                        <span class="total-assets">${totalAssets}</span>
                    </div>
                    <div class="mobile-nav-buttons">
                        <button class="btn btn-outline-secondary btn-sm" onclick="assetManager.navigateMobileCard(-1)" ${this.currentMobileIndex === 0 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="assetManager.navigateMobileCard(1)" ${this.currentMobileIndex === totalAssets - 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Asset Card -->
                <div class="mobile-asset-card">
                    <div class="mobile-asset-header">
                        <div class="mobile-asset-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="mobile-asset-info">
                            <!-- Asset Number with Label -->
                            <div class="mobile-header-field">
                                <div class="mobile-header-label">
                                    <i class="fas fa-hashtag"></i>
                                    <span>Asset #</span>
                                </div>
                                <h5 class="mobile-asset-title">${asset.assetnum || 'Unknown Asset'}</h5>
                            </div>

                            <!-- Description with Label -->
                            <div class="mobile-header-field">
                                <div class="mobile-header-label">
                                    <i class="fas fa-file-text"></i>
                                    <span>Description</span>
                                </div>
                                <p class="mobile-asset-description">${asset.description || 'No description'}</p>
                            </div>

                            <!-- Site and Status with Labels -->
                            <div class="mobile-header-badges">
                                <div class="mobile-badge-item">
                                    <div class="mobile-badge-label">
                                        <i class="fas fa-building"></i>
                                        <span>Site</span>
                                    </div>
                                    <span class="badge bg-secondary">${asset.siteid || '-'}</span>
                                </div>
                                <div class="mobile-badge-item">
                                    <div class="mobile-badge-label">
                                        <i class="fas fa-circle"></i>
                                        <span>Status</span>
                                    </div>
                                    ${this.getStatusBadge(asset.status)}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Complete Asset Information -->
                    <div class="mobile-asset-details">
                        <div class="mobile-detail-row">
                            <div class="mobile-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Location</span>
                                    <span class="mobile-detail-value">${asset.location || '-'}</span>
                                </div>
                            </div>
                            <div class="mobile-detail-item">
                                <i class="fas fa-tag"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Asset Tag</span>
                                    <span class="mobile-detail-value">${asset.assettag || '-'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-detail-row">
                            <div class="mobile-detail-item">
                                <i class="fas fa-barcode"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Serial Number</span>
                                    <span class="mobile-detail-value">${asset.serialnum || '-'}</span>
                                </div>
                            </div>
                            <div class="mobile-detail-item">
                                <i class="fas fa-cube"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Model</span>
                                    <span class="mobile-detail-value">${asset.model || '-'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-detail-row">
                            <div class="mobile-detail-item">
                                <i class="fas fa-layer-group"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Asset Type</span>
                                    <span class="mobile-detail-value">${asset.assettype || asset.type || '-'}</span>
                                </div>
                            </div>
                            <div class="mobile-detail-item">
                                <i class="fas fa-industry"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Manufacturer</span>
                                    <span class="mobile-detail-value">${asset.manufacturer || '-'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mobile-action-buttons">
                        <button class="btn btn-primary mobile-detail-btn" onclick="showAssetDetails('${asset.assetnum}', '${asset.siteid}')">
                            <i class="fas fa-eye me-1"></i>Details
                        </button>
                        <button class="btn btn-outline-success mobile-action-btn" onclick="showRelatedRecords('${asset.assetnum}', '${asset.siteid}')">
                            <i class="fas fa-list-alt me-1"></i>Work Orders & SRs
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    navigateMobileCard(direction) {
        if (!this.mobileAssets) return;

        const newIndex = this.currentMobileIndex + direction;
        if (newIndex >= 0 && newIndex < this.mobileAssets.length) {
            this.currentMobileIndex = newIndex;
            this.renderMobileSingleCard();
        }
    }

    getStatusBadge(status) {
        if (!status) return '<span class="badge bg-secondary">Unknown</span>';

        const statusClass = `status-${status}`;
        return `<span class="badge ${statusClass}">${status}</span>`;
    }

    getTypeBadge(asset) {
        // Try multiple type fields in order of preference
        const type = asset.type || asset.assettype || '';
        if (!type || type.trim() === '') {
            return '<span class="type-badge type-unknown">Unknown</span>';
        }
        return `<span class="type-badge">${type}</span>`;
    }

    updateResultsInfo(metadata) {
        const resultsInfo = document.getElementById('assetResultsInfo');
        if (!resultsInfo) return;

        if (metadata.count === 0) {
            resultsInfo.textContent = 'No assets found';
        } else {
            const start = (metadata.page * this.currentLimit) + 1;
            const end = Math.min(start + metadata.count - 1, metadata.count);
            resultsInfo.textContent = `Showing ${start}-${end} of ${metadata.count} assets (${metadata.load_time?.toFixed(2)}s)`;
        }
    }

    updatePagination(metadata) {
        const paginationContainer = document.getElementById('assetPaginationContainer');
        const pagination = document.getElementById('assetPagination');

        if (!paginationContainer || !pagination) return;

        this.totalPages = metadata.total_pages || 0;

        if (this.totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'block';

        // Build pagination HTML
        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 0 ? 'disabled' : ''}">
                <a class="page-link" href="javascript:void(0)" onclick="assetManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(0, this.currentPage - 2);
        const endPage = Math.min(this.totalPages - 1, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="javascript:void(0)" onclick="assetManager.goToPage(${i})">${i + 1}</a>
                </li>
            `;
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage >= this.totalPages - 1 ? 'disabled' : ''}">
                <a class="page-link" href="javascript:void(0)" onclick="assetManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        if (page < 0 || page >= this.totalPages || page === this.currentPage) {
            return;
        }

        this.currentPage = page;
        this.performSearch();
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.asset-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    showLoadingState() {
        const loadingIndicator = document.getElementById('assetLoadingIndicator');
        const tableView = document.getElementById('assetTableView');
        const cardView = document.getElementById('assetCardView');
        const noResults = document.getElementById('assetNoResults');

        if (loadingIndicator) loadingIndicator.style.display = 'block';
        if (tableView) tableView.style.display = 'none';
        if (cardView) cardView.style.display = 'none';
        if (noResults) noResults.style.display = 'none';
    }

    hideLoadingState() {
        const loadingIndicator = document.getElementById('assetLoadingIndicator');
        if (loadingIndicator) loadingIndicator.style.display = 'none';

        // Restore responsive view
        this.handleResponsiveChanges();
    }

    showError(message) {
        console.error('Asset Management Error:', message);

        const errorDiv = document.getElementById('assetErrorMessage');
        if (errorDiv) {
            errorDiv.querySelector('span').textContent = message;
            errorDiv.style.display = 'block';

            // Auto-hide after 8 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 8000);
        } else {
            // Fallback to alert if element not found
            alert(`Error: ${message}`);
        }
    }

    showLoading(message = 'Loading...') {
        const loadingDiv = document.getElementById('assetLoadingMessage');
        if (loadingDiv) {
            loadingDiv.textContent = message;
            loadingDiv.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingDiv = document.getElementById('assetLoadingMessage');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    async clearAssetCache() {
        try {
            const response = await fetch('/api/asset/cache/clear', {
                method: 'POST'
            });

            if (response.ok) {
                console.log('✅ Asset cache cleared');
                // Refresh current search
                this.performSearch();
            } else {
                console.error('Failed to clear asset cache');
            }
        } catch (error) {
            console.error('Error clearing asset cache:', error);
        }
    }

    async showAssetDetails(assetnum, siteid) {
        try {
            console.log(`🔍 Loading asset details for ${assetnum} in site ${siteid}`);

            // Show loading indicator
            this.showLoading('Loading asset details...');

            const response = await fetch(`/api/asset/details/${assetnum}?siteid=${siteid}`);

            console.log(`📡 Asset details response status: ${response.status}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ Asset details HTTP error: ${response.status} - ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`📦 Asset details result:`, result);

            this.hideLoading();

            if (result.success && result.asset) {
                this.displayAssetDetailModal(result.asset);
            } else {
                console.error(`❌ Asset details failed:`, result);
                this.showError(result.error || 'Asset not found or no data available');
            }

        } catch (error) {
            console.error('❌ Error loading asset details:', error);
            this.hideLoading();
            this.showError(`Failed to load asset details: ${error.message}`);
        }
    }

    displayAssetDetailModal(asset) {
        const modal = new bootstrap.Modal(document.getElementById('assetDetailModal'));
        const modalTitle = document.getElementById('assetDetailModalLabel');
        const modalBody = document.getElementById('assetDetailModalBody');

        // Store current asset for navigation
        this.currentDetailAsset = asset;
        this.currentDetailIndex = this.findAssetIndex(asset);

        // Set modal title with navigation
        modalTitle.innerHTML = this.createModalTitleWithNavigation(asset);

        // Create both mobile and desktop views
        const isMobile = window.innerWidth <= 768;
        const mobileView = this.createMobileTabbedView(asset);
        const desktopView = this.createDesktopCardView(asset);

        modalBody.innerHTML = `
            <div class="mobile-view d-md-none">
                ${mobileView}
            </div>
            <div class="desktop-view d-none d-md-block">
                ${desktopView}
            </div>
        `;

        modal.show();
    }

    findAssetIndex(asset) {
        if (!this.mobileAssets || !asset) return -1;
        return this.mobileAssets.findIndex(a => a.assetnum === asset.assetnum && a.siteid === asset.siteid);
    }

    createModalTitleWithNavigation(asset) {
        const currentIndex = this.currentDetailIndex;
        const totalAssets = this.mobileAssets ? this.mobileAssets.length : 0;
        const position = currentIndex >= 0 ? currentIndex + 1 : 1;

        const isMobile = window.innerWidth <= 768;

        if (isMobile && totalAssets > 1) {
            return `
                <div class="modal-title-with-nav">
                    <div class="modal-nav-controls">
                        <button class="btn btn-outline-light btn-sm modal-nav-btn" onclick="assetManager.navigateDetailAsset(-1)" ${currentIndex <= 0 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="modal-nav-counter">
                            <span class="current-asset">${position}</span>
                            <span class="separator">of</span>
                            <span class="total-assets">${totalAssets}</span>
                        </div>
                        <button class="btn btn-outline-light btn-sm modal-nav-btn" onclick="assetManager.navigateDetailAsset(1)" ${currentIndex >= totalAssets - 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="modal-asset-title">
                        <i class="fas fa-cogs me-2"></i>Asset Details - ${asset.assetnum || 'Unknown'}
                    </div>
                </div>
            `;
        } else {
            return `<i class="fas fa-cogs me-2"></i>Asset Details - ${asset.assetnum || 'Unknown'}`;
        }
    }

    async navigateDetailAsset(direction) {
        if (!this.mobileAssets || this.currentDetailIndex < 0) return;

        const newIndex = this.currentDetailIndex + direction;
        if (newIndex < 0 || newIndex >= this.mobileAssets.length) return;

        const newAsset = this.mobileAssets[newIndex];

        // Get current active tab to maintain selection
        const activeTab = document.querySelector('.diary-tab.active');
        const activeTabId = activeTab ? activeTab.id : null;

        // Show loading state with smooth transition
        this.showAssetNavigationLoading();

        try {
            // Load new asset details
            const response = await fetch(`/api/asset/details/${newAsset.assetnum}?siteid=${newAsset.siteid}`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);

            const result = await response.json();
            if (result.success && result.asset) {
                // Update the modal with smooth transition
                await this.updateAssetDetailWithTransition(result.asset, newIndex, activeTabId);
            }
        } catch (error) {
            console.error('Error navigating to asset:', error);
            this.showError('Failed to load asset details');
            this.hideAssetNavigationLoading();
        }
    }

    showAssetNavigationLoading() {
        const modalBody = document.getElementById('assetDetailModalBody');
        if (modalBody) {
            modalBody.classList.add('asset-transition-loading');

            // Disable navigation buttons
            const navButtons = document.querySelectorAll('.modal-nav-btn');
            navButtons.forEach(btn => btn.disabled = true);
        }
    }

    hideAssetNavigationLoading() {
        const modalBody = document.getElementById('assetDetailModalBody');
        if (modalBody) {
            modalBody.classList.remove('asset-transition-loading');

            // Re-enable navigation buttons
            const navButtons = document.querySelectorAll('.modal-nav-btn');
            navButtons.forEach(btn => btn.disabled = false);
        }
    }

    async updateAssetDetailWithTransition(asset, newIndex, activeTabId) {
        const modalTitle = document.getElementById('assetDetailModalLabel');
        const modalBody = document.getElementById('assetDetailModalBody');

        // Start fade out transition
        modalBody.classList.add('asset-transition-fade-out');

        // Wait for fade out to complete
        await new Promise(resolve => setTimeout(resolve, 200));

        // Update the modal data
        this.currentDetailAsset = asset;
        this.currentDetailIndex = newIndex;

        // Update title with navigation
        modalTitle.innerHTML = this.createModalTitleWithNavigation(asset);

        // Update content
        const isMobile = window.innerWidth <= 768;
        const mobileView = this.createMobileTabbedView(asset);
        const desktopView = this.createDesktopCardView(asset);

        modalBody.innerHTML = `
            <div class="mobile-view d-md-none">
                ${mobileView}
            </div>
            <div class="desktop-view d-none d-md-block">
                ${desktopView}
            </div>
        `;

        // Start fade in transition
        modalBody.classList.remove('asset-transition-fade-out', 'asset-transition-loading');
        modalBody.classList.add('asset-transition-fade-in');

        // Wait for fade in to complete
        await new Promise(resolve => setTimeout(resolve, 200));

        // Clean up transition classes
        modalBody.classList.remove('asset-transition-fade-in');

        // Restore active tab if it exists
        if (activeTabId && isMobile) {
            setTimeout(() => {
                const newActiveTab = document.getElementById(activeTabId);
                if (newActiveTab) {
                    newActiveTab.click();
                }
            }, 50);
        }

        // Re-enable navigation buttons
        this.hideAssetNavigationLoading();
    }

    createStreamlinedAssetDetailView(asset) {
        // Check if mobile view
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            return this.createMobileTabbedView(asset);
        } else {
            return this.createDesktopCardView(asset);
        }
    }

    createMobileTabbedView(asset) {
        // Organize asset data into categories
        const categories = this.organizeAssetData(asset);
        const validCategories = categories.filter(category => category.fields.length > 0);

        if (validCategories.length === 0) {
            return '<div class="text-center text-muted p-4">No asset details available</div>';
        }

        // Define colors for vintage diary style tabs
        const tabColors = [
            { bg: '#e3f2fd', border: '#2196f3', text: '#1976d2' }, // Blue
            { bg: '#e8f5e8', border: '#4caf50', text: '#388e3c' }, // Green
            { bg: '#fff3e0', border: '#ff9800', text: '#f57c00' }, // Orange
            { bg: '#fce4ec', border: '#e91e63', text: '#c2185b' }, // Pink
            { bg: '#f3e5f5', border: '#9c27b0', text: '#7b1fa2' }  // Purple
        ];

        // Generate vertical tab navigation (diary style)
        const tabNavHtml = validCategories.map((category, index) => {
            const color = tabColors[index % tabColors.length];
            return `
                <button class="diary-tab ${index === 0 ? 'active' : ''}"
                        id="tab-${category.id}"
                        data-bs-toggle="tab"
                        data-bs-target="#content-${category.id}"
                        type="button"
                        role="tab"
                        style="--tab-bg: ${color.bg}; --tab-border: ${color.border}; --tab-text: ${color.text};">
                    <div class="diary-tab-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="diary-tab-label">${this.getShortTabName(category.title)}</div>
                </button>
            `;
        }).join('');

        // Generate tab content
        const tabContentHtml = validCategories.map((category, index) => `
            <div class="tab-pane fade ${index === 0 ? 'show active' : ''}"
                 id="content-${category.id}"
                 role="tabpanel">
                ${this.createMobileTabContent(category)}
            </div>
        `).join('');

        return `
            <div class="mobile-diary-view">
                <div class="diary-sidebar">
                    <div class="diary-tabs" role="tablist">
                        ${tabNavHtml}
                    </div>
                </div>
                <div class="diary-content">
                    <div class="tab-content">
                        ${tabContentHtml}
                    </div>
                </div>
            </div>
        `;
    }

    getShortTabName(title) {
        const shortNames = {
            'Basic Information': 'Basic',
            'Location & Organization': 'Location',
            'Technical Details': 'Technical',
            'Financial Information': 'Financial',
            'Dates & Status': 'Dates'
        };
        return shortNames[title] || title;
    }

    createDesktopCardView(asset) {
        // Organize asset data into categories
        const categories = this.organizeAssetData(asset);

        // Generate cards for each category
        let cardsHtml = '';

        categories.forEach(category => {
            if (category.fields.length > 0) {
                cardsHtml += this.createAssetDetailCard(category);
            }
        });

        return `<div class="asset-detail-grid">${cardsHtml}</div>`;
    }

    createMobileTabContent(category) {
        const fieldsHtml = category.fields.map(field => `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="${field.icon}"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">${field.label}</div>
                    <div class="mobile-field-value">
                        ${field.isStatus ?
                            `<span class="badge ${this.getStatusBadgeClass(field.value)}">${field.value}</span>` :
                            field.value || 'N/A'
                        }
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="mobile-tab-panel">
                <div class="mobile-tab-header">
                    <div class="mobile-tab-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <h6 class="mobile-tab-title">${category.title}</h6>
                </div>
                <div class="mobile-detail-fields">
                    ${fieldsHtml}
                </div>
            </div>
        `;
    }

    organizeAssetData(asset) {
        return [
            {
                id: 'basic_information',
                title: 'Basic Information',
                icon: 'fas fa-info-circle',
                fields: [
                    { label: 'Asset Number', value: asset.assetnum, icon: 'fas fa-hashtag' },
                    { label: 'Site ID', value: asset.siteid, icon: 'fas fa-map-marker-alt' },
                    { label: 'Description', value: asset.description, icon: 'fas fa-file-text' },
                    { label: 'Status', value: asset.status, icon: 'fas fa-circle', isStatus: true },
                    { label: 'Asset Tag', value: asset.assettag, icon: 'fas fa-tag' },
                    { label: 'Serial Number', value: asset.serialnum, icon: 'fas fa-barcode' }
                ].filter(field => field.value)
            },
            {
                id: 'location_information',
                title: 'Location & Organization',
                icon: 'fas fa-map-marker-alt',
                fields: [
                    { label: 'Location', value: asset.location, icon: 'fas fa-building' },
                    { label: 'Organization', value: asset.orgid, icon: 'fas fa-sitemap' },
                    { label: 'Parent Asset', value: asset.parent, icon: 'fas fa-link' }
                ].filter(field => field.value)
            },
            {
                id: 'technical_information',
                title: 'Technical Details',
                icon: 'fas fa-cog',
                fields: [
                    { label: 'Model', value: asset.model, icon: 'fas fa-cube' },
                    { label: 'Type', value: asset.type, icon: 'fas fa-shapes' },
                    { label: 'Asset Type', value: asset.assettype, icon: 'fas fa-layer-group' },
                    { label: 'Is Running', value: asset.isrunning ? 'Yes' : 'No', icon: 'fas fa-power-off' },
                    { label: 'Disabled', value: asset.disabled ? 'Yes' : 'No', icon: 'fas fa-ban' }
                ].filter(field => field.value && field.value !== 'No')
            },
            {
                id: 'financial_information',
                title: 'Financial Information',
                icon: 'fas fa-dollar-sign',
                fields: [
                    { label: 'Purchase Price', value: this.formatCurrency(asset.purchaseprice), icon: 'fas fa-shopping-cart' },
                    { label: 'Replace Cost', value: this.formatCurrency(asset.replacecost), icon: 'fas fa-exchange-alt' },
                    { label: 'Total Cost', value: this.formatCurrency(asset.totalcost), icon: 'fas fa-calculator' },
                    { label: 'Vendor', value: asset.vendor, icon: 'fas fa-store' },
                    { label: 'Manufacturer', value: asset.manufacturer, icon: 'fas fa-industry' }
                ].filter(field => field.value)
            },
            {
                id: 'dates_and_status',
                title: 'Dates & Status',
                icon: 'fas fa-calendar-alt',
                fields: [
                    { label: 'Install Date', value: this.formatDate(asset.installdate), icon: 'fas fa-calendar-plus' },
                    { label: 'Change Date', value: this.formatDate(asset.changedate), icon: 'fas fa-calendar-edit' },
                    { label: 'Status Date', value: this.formatDate(asset.statusdate), icon: 'fas fa-calendar-check' },
                    { label: 'Changed By', value: asset.changeby, icon: 'fas fa-user-edit' }
                ].filter(field => field.value)
            }
        ];
    }

    createAssetDetailCard(category) {
        const fieldsHtml = category.fields.map(field => `
            <div class="asset-detail-field">
                <div class="asset-detail-field-icon">
                    <i class="${field.icon}"></i>
                </div>
                <div class="asset-detail-field-content">
                    <div class="asset-detail-field-label">${field.label}</div>
                    <div class="asset-detail-field-value">
                        ${field.isStatus ?
                            `<span class="badge ${this.getStatusBadgeClass(field.value)}">${field.value}</span>` :
                            field.value || 'N/A'
                        }
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="asset-detail-card" data-category="${category.id}">
                <div class="asset-detail-card-header">
                    <div class="asset-detail-card-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <h6 class="asset-detail-card-title">${category.title}</h6>
                </div>
                <div class="asset-detail-fields">
                    ${fieldsHtml}
                </div>
            </div>
        `;
    }

    async showAssetActions(assetnum, siteid) {
        try {
            console.log(`🔧 Loading asset actions for ${assetnum} in site ${siteid}`);

            const response = await fetch(`/api/asset/actions/${assetnum}?siteid=${siteid}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.displayAssetActionsModal(assetnum, siteid, result.actions);
            } else {
                this.showError(result.error || 'Failed to load asset actions');
            }

        } catch (error) {
            console.error('Error loading asset actions:', error);
            this.showError('Failed to load asset actions');
        }
    }

    displayAssetActionsModal(assetnum, siteid, actions) {
        // Skip modal - directly execute the view related records action
        console.log(`🔧 Directly executing view related records for ${assetnum}`);
        showRelatedRecordsModal(assetnum, siteid);
    }

    async exportAssetResults() {
        try {
            // Get current search parameters
            const params = new URLSearchParams({
                q: this.currentSearchTerm,
                siteid: this.currentSiteId,
                status: this.currentStatusFilter,
                type: this.currentTypeFilter,
                export: 'true'
            });

            // Create download link
            const downloadUrl = `/api/asset/export?${params}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `assets_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('Error exporting assets:', error);
            this.showError('Failed to export assets');
        }
    }

    // Helper methods for asset details formatting
    formatCurrency(value) {
        if (!value || isNaN(value)) return null;
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(value);
    }

    formatDate(dateString) {
        if (!dateString) return null;
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            return dateString; // Return original if parsing fails
        }
    }

    getStatusBadgeClass(status) {
        if (!status) return 'bg-secondary';

        const statusLower = status.toLowerCase();

        if (statusLower.includes('active') || statusLower.includes('operating')) {
            return 'bg-success';
        } else if (statusLower.includes('inactive') || statusLower.includes('decommissioned')) {
            return 'bg-danger';
        } else if (statusLower.includes('maintenance') || statusLower.includes('repair')) {
            return 'bg-warning';
        } else if (statusLower.includes('planned') || statusLower.includes('pending')) {
            return 'bg-info';
        } else {
            return 'bg-secondary';
        }
    }
}

// Global functions for HTML onclick handlers
function showAssetDetails(assetnum, siteid) {
    if (window.assetManager) {
        window.assetManager.showAssetDetails(assetnum, siteid);
    }
}

function showRelatedRecords(assetnum, siteid) {
    console.log(`🔧 BUTTON CLICKED: ${assetnum} in ${siteid}`);
    showRelatedRecordsModal(assetnum, siteid);
}

function showAssetActions(assetnum, siteid) {
    if (window.assetManager) {
        window.assetManager.showAssetActions(assetnum, siteid);
    }
}

function clearAssetCache() {
    if (window.assetManager) {
        window.assetManager.clearAssetCache();
    }
}

function exportAssetResults() {
    if (window.assetManager) {
        window.assetManager.exportAssetResults();
    }
}

function executeAssetAction(actionId, assetnum, siteid) {
    console.log(`🔧 Executing action ${actionId} for asset ${assetnum} in site ${siteid}`);

    // Prevent event bubbling
    if (typeof event !== 'undefined') {
        event.preventDefault();
        event.stopPropagation();
    }

    // Only handle view_related_records action
    if (actionId === 'view_related_records') {
        console.log(`📋 Opening related records modal for ${assetnum}`);
        showRelatedRecordsModal(assetnum, siteid);
    } else {
        console.warn(`Unknown action: ${actionId}`);
    }
}

function showRelatedRecordsModal(assetnum, siteid) {
    console.log(`🔍 Showing related records for asset ${assetnum} in site ${siteid}`);

    // Create modal HTML with enhanced tabbed interface and side navigation
    const modalHtml = `
        <div class="modal fade" id="relatedRecordsModal" tabindex="-1" aria-labelledby="relatedRecordsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="relatedRecordsModalLabel">
                            <i class="fas fa-list-alt"></i> Related Records - ${assetnum}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="loading-spinner text-center p-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading related records...</p>
                        </div>
                        <div class="related-records-content" style="display: none;">
                            <div class="row g-0">
                                <!-- Side Navigation -->
                                <div class="col-md-3 border-end bg-light">
                                    <div class="side-nav p-3">
                                        <h6 class="text-muted mb-3">NAVIGATION</h6>
                                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                            <button class="nav-link active" id="v-pills-workorders-tab" data-bs-toggle="pill" data-bs-target="#v-pills-workorders" type="button" role="tab" aria-controls="v-pills-workorders" aria-selected="true">
                                                <i class="fas fa-wrench me-2"></i>Work Orders
                                                <span class="badge bg-primary ms-auto" id="workorders-count">0</span>
                                            </button>
                                            <button class="nav-link" id="v-pills-service-requests-tab" data-bs-toggle="pill" data-bs-target="#v-pills-service-requests" type="button" role="tab" aria-controls="v-pills-service-requests" aria-selected="false">
                                                <i class="fas fa-ticket-alt me-2"></i>Service Requests
                                                <span class="badge bg-primary ms-auto" id="service-requests-count">0</span>
                                            </button>
                                            <button class="nav-link" id="v-pills-asset-status-tab" data-bs-toggle="pill" data-bs-target="#v-pills-asset-status" type="button" role="tab" aria-controls="v-pills-asset-status" aria-selected="false">
                                                <i class="fas fa-info-circle me-2"></i>Asset Status
                                                <span class="badge bg-info ms-auto">1</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <!-- Main Content -->
                                <div class="col-md-9">
                                    <div class="tab-content p-3" id="v-pills-tabContent">
                                        <!-- Work Orders Tab -->
                                        <div class="tab-pane fade show active" id="v-pills-workorders" role="tabpanel" aria-labelledby="v-pills-workorders-tab">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Work Orders</h6>
                                                <div class="pagination-info">
                                                    <small class="text-muted" id="workorders-pagination-info">Loading...</small>
                                                </div>
                                            </div>
                                            <div class="workorders-content">
                                                <!-- Desktop View -->
                                                <div class="d-none d-md-block">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th>WO Number</th>
                                                                    <th>Description</th>
                                                                    <th>Status</th>
                                                                    <th>Priority</th>
                                                                    <th>Date</th>
                                                                    <th>Lead</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="workorders-table-body">
                                                                <!-- Work orders will be populated here -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <!-- Mobile View -->
                                                <div class="d-md-none">
                                                    <div id="workorders-cards-container">
                                                        <!-- Work order cards will be populated here -->
                                                    </div>
                                                </div>
                                                <!-- Pagination -->
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <div class="pagination-controls">
                                                        <nav aria-label="Work orders pagination">
                                                            <ul class="pagination pagination-sm mb-0" id="workorders-pagination">
                                                                <!-- Pagination will be populated here -->
                                                            </ul>
                                                        </nav>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Service Requests Tab -->
                                        <div class="tab-pane fade" id="v-pills-service-requests" role="tabpanel" aria-labelledby="v-pills-service-requests-tab">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Service Requests</h6>
                                                <div class="pagination-info">
                                                    <small class="text-muted" id="service-requests-pagination-info">Loading...</small>
                                                </div>
                                            </div>
                                            <div class="service-requests-content">
                                                <!-- Desktop View -->
                                                <div class="d-none d-md-block">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th>Ticket ID</th>
                                                                    <th>Description</th>
                                                                    <th>Status</th>
                                                                    <th>Priority</th>
                                                                    <th>Date</th>
                                                                    <th>Reported By</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="service-requests-table-body">
                                                                <!-- Service requests will be populated here -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <!-- Mobile View -->
                                                <div class="d-md-none">
                                                    <div id="service-requests-cards-container">
                                                        <!-- Service request cards will be populated here -->
                                                    </div>
                                                </div>
                                                <!-- Pagination -->
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <div class="pagination-controls">
                                                        <nav aria-label="Service requests pagination">
                                                            <ul class="pagination pagination-sm mb-0" id="service-requests-pagination">
                                                                <!-- Pagination will be populated here -->
                                                            </ul>
                                                        </nav>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Asset Status Tab -->
                                        <div class="tab-pane fade" id="v-pills-asset-status" role="tabpanel" aria-labelledby="v-pills-asset-status-tab">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Asset Availability & Status</h6>
                                            </div>
                                            <div id="asset-status-content">
                                                <!-- Asset status will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('relatedRecordsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal using Bootstrap 5
    const modal = new bootstrap.Modal(document.getElementById('relatedRecordsModal'));
    modal.show();

    // Initialize pagination state
    window.relatedRecordsState = {
        workorders: { currentPage: 1, totalPages: 0, data: [] },
        serviceRequests: { currentPage: 1, totalPages: 0, data: [] },
        recordsPerPage: 10
    };

    // Fetch related records
    fetchRelatedRecords(assetnum, siteid);
}

function fetchRelatedRecords(assetnum, siteid) {
    console.log(`📡 Fetching related records for asset ${assetnum} using OSLC`);

    fetch(`/oslc/os/mxapiasset/${assetnum}/related-records?siteid=${siteid}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(oslcResponse => {
            console.log('✅ OSLC related records fetched:', oslcResponse);
            // Extract the actual data from OSLC response
            const data = oslcResponse.member && oslcResponse.member.length > 0 ? oslcResponse.member[0] : {};
            displayRelatedRecords(data);
        })
        .catch(error => {
            console.error('❌ Error fetching related records:', error);
            displayRelatedRecordsError(error.message);
        });
}

function displayRelatedRecords(data) {
    console.log('📊 Displaying related records:', data);

    // Hide loading spinner
    const loadingSpinner = document.querySelector('#relatedRecordsModal .loading-spinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'none';
    }

    // Show content
    const content = document.querySelector('#relatedRecordsModal .related-records-content');
    if (content) {
        content.style.display = 'block';
    }

    // Store data in global state
    const workorders = data.workorders || [];
    const serviceRequests = data.service_requests || [];

    window.relatedRecordsState.workorders.data = workorders;
    window.relatedRecordsState.serviceRequests.data = serviceRequests;

    // Calculate total pages
    const recordsPerPage = window.relatedRecordsState.recordsPerPage;
    window.relatedRecordsState.workorders.totalPages = Math.ceil(workorders.length / recordsPerPage);
    window.relatedRecordsState.serviceRequests.totalPages = Math.ceil(serviceRequests.length / recordsPerPage);

    // Update counts
    document.getElementById('workorders-count').textContent = workorders.length;
    document.getElementById('service-requests-count').textContent = serviceRequests.length;

    // Display first page of each
    displayWorkOrdersPage(1);
    displayServiceRequestsPage(1);

    // Display asset status
    displayAssetStatus(data.asset || {});
}

function displayWorkOrdersPage(page) {
    const state = window.relatedRecordsState.workorders;
    const recordsPerPage = window.relatedRecordsState.recordsPerPage;
    const startIndex = (page - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageData = state.data.slice(startIndex, endIndex);

    state.currentPage = page;

    // Update pagination info
    const totalRecords = state.data.length;
    const showingStart = totalRecords > 0 ? startIndex + 1 : 0;
    const showingEnd = Math.min(endIndex, totalRecords);
    document.getElementById('workorders-pagination-info').textContent =
        `Showing ${showingStart}-${showingEnd} of ${totalRecords} records`;

    // Display desktop table
    displayWorkOrdersTable(pageData);

    // Display mobile cards
    displayWorkOrdersCards(pageData);

    // Update pagination controls
    updateWorkOrdersPagination();
}

function displayWorkOrdersTable(workorders) {
    const tableBody = document.getElementById('workorders-table-body');
    if (!tableBody) return;

    if (workorders.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No work orders found for this asset.</td></tr>';
        return;
    }

    const workordersHtml = workorders.map(wo => `
        <tr>
            <td>
                <strong>${wo.wonum || 'N/A'}</strong>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${wo.description || 'No description'}">
                    ${wo.description || 'No description'}
                </div>
            </td>
            <td>
                <span class="badge bg-${getStatusBadgeClass(wo.status)}">${wo.status || 'N/A'}</span>
            </td>
            <td>
                ${wo.priority ? `<span class="badge bg-${getPriorityBadgeClass(wo.priority)}">${wo.priority}</span>` : '-'}
            </td>
            <td>
                <small>${formatDate(wo.statusdate)}</small>
            </td>
            <td>
                ${wo.lead || '-'}
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = workordersHtml;
}

function displayWorkOrdersCards(workorders) {
    const container = document.getElementById('workorders-cards-container');
    if (!container) return;

    if (workorders.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-3">No work orders found for this asset.</div>';
        return;
    }

    const workordersHtml = workorders.map(wo => `
        <div class="card mb-3 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-wrench text-primary me-2"></i>${wo.wonum || 'N/A'}
                    </h6>
                    <span class="badge bg-${getStatusBadgeClass(wo.status)}">${wo.status || 'N/A'}</span>
                </div>
                <p class="card-text text-muted mb-2">${wo.description || 'No description'}</p>
                <div class="row g-2">
                    <div class="col-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>${formatDate(wo.statusdate)}
                        </small>
                    </div>
                    <div class="col-6 text-end">
                        ${wo.priority ? `<span class="badge bg-${getPriorityBadgeClass(wo.priority)}">${wo.priority}</span>` : ''}
                    </div>
                </div>
                ${wo.lead ? `<div class="mt-2"><small class="text-muted"><i class="fas fa-user me-1"></i>${wo.lead}</small></div>` : ''}
            </div>
        </div>
    `).join('');

    container.innerHTML = workordersHtml;
}

function displayServiceRequestsPage(page) {
    const state = window.relatedRecordsState.serviceRequests;
    const recordsPerPage = window.relatedRecordsState.recordsPerPage;
    const startIndex = (page - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageData = state.data.slice(startIndex, endIndex);

    state.currentPage = page;

    // Update pagination info
    const totalRecords = state.data.length;
    const showingStart = totalRecords > 0 ? startIndex + 1 : 0;
    const showingEnd = Math.min(endIndex, totalRecords);
    document.getElementById('service-requests-pagination-info').textContent =
        `Showing ${showingStart}-${showingEnd} of ${totalRecords} records`;

    // Display desktop table
    displayServiceRequestsTable(pageData);

    // Display mobile cards
    displayServiceRequestsCards(pageData);

    // Update pagination controls
    updateServiceRequestsPagination();
}

function displayServiceRequestsTable(serviceRequests) {
    const tableBody = document.getElementById('service-requests-table-body');
    if (!tableBody) return;

    if (serviceRequests.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No service requests found for this asset.</td></tr>';
        return;
    }

    const serviceRequestsHtml = serviceRequests.map(sr => `
        <tr>
            <td>
                <strong>${sr.ticketid || 'N/A'}</strong>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${sr.description || 'No description'}">
                    ${sr.description || 'No description'}
                </div>
            </td>
            <td>
                <span class="badge bg-${getStatusBadgeClass(sr.status)}">${sr.status || 'N/A'}</span>
            </td>
            <td>
                ${sr.reportedpriority ? `<span class="badge bg-${getPriorityBadgeClass(sr.reportedpriority)}">${sr.reportedpriority}</span>` : '-'}
            </td>
            <td>
                <small>${formatDate(sr.statusdate)}</small>
            </td>
            <td>
                ${sr.reportedby || '-'}
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = serviceRequestsHtml;
}

function displayServiceRequestsCards(serviceRequests) {
    const container = document.getElementById('service-requests-cards-container');
    if (!container) return;

    if (serviceRequests.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-3">No service requests found for this asset.</div>';
        return;
    }

    const serviceRequestsHtml = serviceRequests.map(sr => `
        <div class="card mb-3 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-ticket-alt text-info me-2"></i>${sr.ticketid || 'N/A'}
                    </h6>
                    <span class="badge bg-${getStatusBadgeClass(sr.status)}">${sr.status || 'N/A'}</span>
                </div>
                <p class="card-text text-muted mb-2">${sr.description || 'No description'}</p>
                <div class="row g-2">
                    <div class="col-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>${formatDate(sr.statusdate)}
                        </small>
                    </div>
                    <div class="col-6 text-end">
                        ${sr.reportedpriority ? `<span class="badge bg-${getPriorityBadgeClass(sr.reportedpriority)}">${sr.reportedpriority}</span>` : ''}
                    </div>
                </div>
                ${sr.reportedby ? `<div class="mt-2"><small class="text-muted"><i class="fas fa-user me-1"></i>${sr.reportedby}</small></div>` : ''}
            </div>
        </div>
    `).join('');

    container.innerHTML = serviceRequestsHtml;
}

function updateWorkOrdersPagination() {
    const state = window.relatedRecordsState.workorders;
    const paginationContainer = document.getElementById('workorders-pagination');
    if (!paginationContainer) return;

    const totalPages = state.totalPages;
    const currentPage = state.currentPage;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // Previous button
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayWorkOrdersPage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="displayWorkOrdersPage(${i}); return false;">${i}</a>
            </li>
        `;
    }

    // Next button
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayWorkOrdersPage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    paginationContainer.innerHTML = paginationHtml;
}

function updateServiceRequestsPagination() {
    const state = window.relatedRecordsState.serviceRequests;
    const paginationContainer = document.getElementById('service-requests-pagination');
    if (!paginationContainer) return;

    const totalPages = state.totalPages;
    const currentPage = state.currentPage;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // Previous button
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayServiceRequestsPage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="displayServiceRequestsPage(${i}); return false;">${i}</a>
            </li>
        `;
    }

    // Next button
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayServiceRequestsPage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    paginationContainer.innerHTML = paginationHtml;
}

function displayAssetStatus(asset) {
    const container = document.getElementById('asset-status-content');
    if (!container) return;

    const statusHtml = `
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info me-2"></i>Asset Information
                        </h6>
                        <div class="row g-2">
                            <div class="col-6"><strong>Asset Number:</strong></div>
                            <div class="col-6">${asset.assetnum || 'N/A'}</div>
                            <div class="col-6"><strong>Description:</strong></div>
                            <div class="col-6">${asset.description || 'N/A'}</div>
                            <div class="col-6"><strong>Status:</strong></div>
                            <div class="col-6">
                                <span class="badge bg-${getStatusBadgeClass(asset.status)}">${asset.status || 'N/A'}</span>
                            </div>
                            <div class="col-6"><strong>Site:</strong></div>
                            <div class="col-6">${asset.siteid || 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-chart-line text-success me-2"></i>Availability Summary
                        </h6>
                        <div class="row g-2">
                            <div class="col-6"><strong>Total Work Orders:</strong></div>
                            <div class="col-6">${window.relatedRecordsState.workorders.data.length}</div>
                            <div class="col-6"><strong>Service Requests:</strong></div>
                            <div class="col-6">${window.relatedRecordsState.serviceRequests.data.length}</div>
                            <div class="col-6"><strong>Last Updated:</strong></div>
                            <div class="col-6">${formatDate(new Date().toISOString())}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = statusHtml;
}

function displayRelatedRecordsError(errorMessage) {
    // Hide loading spinner
    const loadingSpinner = document.querySelector('#relatedRecordsModal .loading-spinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'none';
    }

    // Show error message
    const modalBody = document.querySelector('#relatedRecordsModal .modal-body');
    if (modalBody) {
        modalBody.innerHTML = `
            <div class="alert alert-danger m-3">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Error:</strong> ${errorMessage}
            </div>
        `;
    }
}

function getStatusBadgeClass(status) {
    if (!status) return 'secondary';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('comp') || statusLower.includes('close')) return 'success';
    if (statusLower.includes('inprg') || statusLower.includes('progress')) return 'primary';
    if (statusLower.includes('wappr') || statusLower.includes('pending')) return 'warning';
    if (statusLower.includes('cancel')) return 'danger';
    return 'secondary';
}

function getPriorityBadgeClass(priority) {
    if (!priority) return 'secondary';
    const priorityNum = parseInt(priority);
    if (priorityNum <= 1) return 'danger';
    if (priorityNum <= 2) return 'warning';
    if (priorityNum <= 3) return 'info';
    return 'secondary';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch (e) {
        return dateString;
    }
}

function displayRelatedRecords(data) {
    const modalBody = document.querySelector('#relatedRecordsModal .modal-body');

    if (data.error) {
        displayRelatedRecordsError(data.error);
        return;
    }

    const workorders = data.workorders || [];
    const serviceRequests = data.service_requests || [];
    const summary = data.summary || {};

    const html = `
        <div class="related-records-container">
            <div class="row mb-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <strong>Summary:</strong>
                        ${summary.workorder_count || 0} Work Orders,
                        ${summary.service_request_count || 0} Service Requests
                        ${data.fetch_time ? ` (loaded in ${data.fetch_time}s)` : ''}
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <ul class="nav nav-tabs" id="relatedRecordsTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="workorders-tab" data-toggle="tab" href="#workorders" role="tab">
                        <i class="fas fa-clipboard-list"></i> Work Orders (${workorders.length})
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="servicerequests-tab" data-toggle="tab" href="#servicerequests" role="tab">
                        <i class="fas fa-ticket-alt"></i> Service Requests (${serviceRequests.length})
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content mt-3" id="relatedRecordsTabContent">
                <!-- Work Orders Tab -->
                <div class="tab-pane fade show active" id="workorders" role="tabpanel">
                    ${renderWorkOrdersList(workorders)}
                </div>

                <!-- Service Requests Tab -->
                <div class="tab-pane fade" id="servicerequests" role="tabpanel">
                    ${renderServiceRequestsList(serviceRequests)}
                </div>
            </div>
        </div>
    `;

    modalBody.innerHTML = html;
}

function renderWorkOrdersList(workorders) {
    if (workorders.length === 0) {
        return `
            <div class="text-center text-muted py-4">
                <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                <p>No work orders found for this asset.</p>
            </div>
        `;
    }

    let html = '<div class="list-group">';

    workorders.forEach(wo => {
        const statusClass = getStatusClass(wo.status);
        const priorityClass = getPriorityClass(wo.priority);

        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        <strong>${wo.wonum || 'N/A'}</strong>
                        <span class="badge badge-${statusClass} ml-2">${wo.status || 'Unknown'}</span>
                        ${wo.priority ? `<span class="badge badge-${priorityClass} ml-1">${wo.priority}</span>` : ''}
                    </h6>
                    <small class="text-muted">${formatDate(wo.reportdate)}</small>
                </div>
                <p class="mb-1">${wo.description || 'No description'}</p>
                <small class="text-muted">
                    ${wo.worktype ? `Type: ${wo.worktype} | ` : ''}
                    ${wo.schedstart ? `Scheduled: ${formatDate(wo.schedstart)}` : ''}
                </small>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function renderServiceRequestsList(serviceRequests) {
    if (serviceRequests.length === 0) {
        return `
            <div class="text-center text-muted py-4">
                <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                <p>No service requests found for this asset.</p>
            </div>
        `;
    }

    let html = '<div class="list-group">';

    serviceRequests.forEach(sr => {
        const statusClass = getStatusClass(sr.status);
        const priorityClass = getPriorityClass(sr.priority);

        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        <strong>${sr.ticketid || 'N/A'}</strong>
                        <span class="badge badge-${statusClass} ml-2">${sr.status || 'Unknown'}</span>
                        ${sr.priority ? `<span class="badge badge-${priorityClass} ml-1">${sr.priority}</span>` : ''}
                    </h6>
                    <small class="text-muted">${formatDate(sr.reportdate)}</small>
                </div>
                <p class="mb-1">${sr.description || 'No description'}</p>
                <small class="text-muted">
                    ${sr.reportedby ? `Reported by: ${sr.reportedby}` : ''}
                </small>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function displayRelatedRecordsError(errorMessage) {
    const modalBody = document.querySelector('#relatedRecordsModal .modal-body');
    modalBody.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Error:</strong> ${errorMessage}
        </div>
    `;
}

function getStatusClass(status) {
    if (!status) return 'secondary';

    const statusUpper = status.toUpperCase();
    switch (statusUpper) {
        case 'COMP': case 'COMPLETED': return 'success';
        case 'INPRG': case 'IN PROGRESS': return 'primary';
        case 'APPR': case 'APPROVED': return 'info';
        case 'WMATL': case 'WAITING FOR MATERIAL': return 'warning';
        case 'CLOSE': case 'CLOSED': return 'dark';
        case 'CANCEL': case 'CANCELLED': return 'danger';
        default: return 'secondary';
    }
}

function getPriorityClass(priority) {
    if (!priority) return 'secondary';

    const priorityUpper = priority.toString().toUpperCase();
    switch (priorityUpper) {
        case '1': case 'HIGH': case 'URGENT': return 'danger';
        case '2': case 'MEDIUM': return 'warning';
        case '3': case 'LOW': return 'success';
        default: return 'secondary';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } catch (e) {
        return dateString;
    }
}

// Initialize when DOM is ready
let assetManager;
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Asset Management page loaded');
    assetManager = new AssetManagement();

    // Make it globally accessible for onclick handlers
    window.assetManager = assetManager;
});
