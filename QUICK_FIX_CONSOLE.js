// QUICK FIX - Run this in browser console to immediately fix the data attributes issue
// Copy and paste this entire script into the browser console when on the workorder page

console.log('🚀 QUICK FIX: Starting data attributes repair...');

// Function to fix data attributes for all labor containers
function fixDataAttributes() {
    // Get tasks data
    const tasksDataElement = document.getElementById('tasksData');
    if (!tasksDataElement) {
        console.error('❌ No tasksData found');
        return false;
    }
    
    let tasksData;
    try {
        tasksData = JSON.parse(tasksDataElement.textContent);
        console.log(`✅ Found ${tasksData.length} tasks in data`);
    } catch (e) {
        console.error('❌ Error parsing tasksData:', e);
        return false;
    }
    
    // Find all labor content containers
    const laborContainers = document.querySelectorAll('[id^="labor-content-"]');
    console.log(`🔍 Found ${laborContainers.length} labor containers`);
    
    let fixedCount = 0;
    
    laborContainers.forEach((container, index) => {
        const taskWonum = container.id.replace('labor-content-', '');
        console.log(`\n🔧 Processing container ${index + 1}: ${taskWonum}`);
        
        // Find corresponding task data
        const task = tasksData.find(t => t.wonum === taskWonum);
        if (!task) {
            console.log(`❌ No task data found for ${taskWonum}`);
            return;
        }
        
        console.log(`📋 Task data:`, {
            wonum: task.wonum,
            taskid: task.taskid,
            siteid: task.siteid,
            parent: task.parent
        });
        
        // Check current attributes
        const currentTaskId = container.getAttribute('data-task-id');
        const currentSiteId = container.getAttribute('data-site-id');
        const currentParentWonum = container.getAttribute('data-parent-wonum');
        
        console.log(`📋 Current attributes:`, {
            'data-task-id': currentTaskId,
            'data-site-id': currentSiteId,
            'data-parent-wonum': currentParentWonum
        });
        
        // Fix missing or null attributes
        let needsFix = false;
        
        if (!currentTaskId || currentTaskId === 'null' || currentTaskId === 'undefined') {
            container.setAttribute('data-task-id', task.taskid || '');
            console.log(`✅ Fixed data-task-id: ${task.taskid}`);
            needsFix = true;
        }
        
        if (!currentSiteId || currentSiteId === 'null' || currentSiteId === 'undefined') {
            container.setAttribute('data-site-id', task.siteid || '');
            console.log(`✅ Fixed data-site-id: ${task.siteid}`);
            needsFix = true;
        }
        
        if (!currentParentWonum || currentParentWonum === 'null' || currentParentWonum === 'undefined') {
            const parentWonum = task.parent || task.wonum || '';
            container.setAttribute('data-parent-wonum', parentWonum);
            console.log(`✅ Fixed data-parent-wonum: ${parentWonum}`);
            needsFix = true;
        }
        
        // Always ensure task-wonum is set
        container.setAttribute('data-task-wonum', task.wonum);
        
        if (needsFix) {
            fixedCount++;
            console.log(`✅ Fixed container ${taskWonum}`);
        } else {
            console.log(`✅ Container ${taskWonum} already OK`);
        }
    });
    
    console.log(`\n🎉 QUICK FIX COMPLETE: Fixed ${fixedCount} containers`);
    return true;
}

// Function to test negative hours functionality
function testNegativeHours() {
    console.log('\n🧪 Testing negative hours functionality...');
    
    const subtractBtn = document.querySelector('.delete-labor-btn');
    if (!subtractBtn) {
        console.log('❌ No subtract button found. Load some labor first.');
        return;
    }
    
    console.log('✅ Found subtract button, testing parameter extraction...');
    
    const laborContent = subtractBtn.closest('[id^="labor-content-"]');
    if (!laborContent) {
        console.log('❌ Could not find labor content container');
        return;
    }
    
    const taskWonum = laborContent.id.replace('labor-content-', '');
    const taskId = laborContent.getAttribute('data-task-id');
    const siteId = laborContent.getAttribute('data-site-id');
    const parentWonum = laborContent.getAttribute('data-parent-wonum');
    
    console.log('📋 Extracted parameters:');
    console.log(`  - Task wonum: ${taskWonum}`);
    console.log(`  - Task ID: ${taskId}`);
    console.log(`  - Site ID: ${siteId}`);
    console.log(`  - Parent wonum: ${parentWonum}`);
    
    const missing = [];
    if (!taskId || taskId === 'null') missing.push('Task ID');
    if (!siteId || siteId === 'null') missing.push('Site ID');
    if (!parentWonum || parentWonum === 'null') missing.push('Parent Work Order');
    
    if (missing.length > 0) {
        console.log(`❌ Still missing: ${missing.join(', ')}`);
        console.log('🔧 Try running fixDataAttributes() first');
        return false;
    } else {
        console.log('✅ All parameters present! Negative hours should work now.');
        return true;
    }
}

// Run the fix automatically
console.log('🚀 Running automatic fix...');
const success = fixDataAttributes();

if (success) {
    console.log('\n🧪 Testing functionality...');
    const testResult = testNegativeHours();
    
    if (testResult) {
        console.log('\n🎉 SUCCESS! You can now use the "Subtract Hours" button.');
        console.log('💡 If you still get errors, refresh the page and the fix should persist.');
    } else {
        console.log('\n❌ Still having issues. Check the logs above for details.');
    }
} else {
    console.log('\n❌ Fix failed. Check the logs above for details.');
}

// Make functions available globally for manual use
window.fixDataAttributes = fixDataAttributes;
window.testNegativeHours = testNegativeHours;

console.log('\n📋 Available functions:');
console.log('  - fixDataAttributes() - Fix missing data attributes');
console.log('  - testNegativeHours() - Test if negative hours will work');
