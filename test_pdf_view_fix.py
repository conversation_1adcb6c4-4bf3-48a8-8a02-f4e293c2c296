#!/usr/bin/env python3
"""
Test script to debug PDF view functionality specifically
"""

import requests
import json

def test_pdf_view_functionality():
    """Test PDF view functionality to see what's failing"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2219753'
    docinfoid = '1926183'  # Comments.pdf that we know works for download
    
    print(f"🔍 Testing PDF view functionality for Comments.pdf")
    print("=" * 60)
    
    # First test download to confirm it works
    print(f"📥 Step 1: Testing download (should work)")
    download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
    
    try:
        download_response = requests.get(download_url, timeout=60)
        print(f"   📤 Download URL: {download_url}")
        print(f"   🔄 Status: {download_response.status_code}")
        print(f"   📊 Content Length: {len(download_response.content)} bytes")
        print(f"   📋 Content Type: {download_response.headers.get('content-type', 'Unknown')}")
        
        if download_response.status_code == 200:
            content = download_response.content
            if content.startswith(b'%PDF'):
                print(f"   ✅ Download SUCCESS: Got valid PDF content")
                print(f"   📄 PDF version: {content[:8].decode('utf-8', errors='ignore')}")
            else:
                print(f"   ❌ Download got non-PDF content: {content[:50]}")
        else:
            print(f"   ❌ Download failed: {download_response.status_code}")
            try:
                error_data = download_response.json()
                print(f"   📝 Error: {error_data}")
            except:
                print(f"   📝 Response: {download_response.text[:200]}")
    
    except Exception as e:
        print(f"   ❌ Download exception: {e}")
    
    # Now test view to see what fails
    print(f"\n👁️ Step 2: Testing view (currently failing)")
    view_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/view'
    
    try:
        view_response = requests.get(view_url, timeout=60)
        print(f"   📤 View URL: {view_url}")
        print(f"   🔄 Status: {view_response.status_code}")
        print(f"   📊 Content Length: {len(view_response.content)} bytes")
        print(f"   📋 Content Type: {view_response.headers.get('content-type', 'Unknown')}")
        
        if view_response.status_code == 200:
            content = view_response.content
            if content.startswith(b'%PDF'):
                print(f"   ✅ View SUCCESS: Got valid PDF content for viewing")
                print(f"   📄 PDF version: {content[:8].decode('utf-8', errors='ignore')}")
            else:
                print(f"   ❌ View got non-PDF content: {content[:100].decode('utf-8', errors='ignore')}")
        else:
            print(f"   ❌ View failed: {view_response.status_code}")
            try:
                error_data = view_response.json()
                print(f"   📝 View Error: {error_data}")
                
                # Check if it's the same error as download or different
                if 'error' in error_data:
                    print(f"   🔍 Error details: {error_data['error']}")
                    if 'details' in error_data:
                        print(f"   🔍 Error details: {error_data['details']}")
            except:
                print(f"   📝 View Response: {view_response.text[:200]}")
    
    except Exception as e:
        print(f"   ❌ View exception: {e}")
    
    # Test what the difference should be between download and view
    print(f"\n🔍 Step 3: Analysis")
    print("=" * 40)
    print("📋 Expected behavior:")
    print("   📥 Download: Should return PDF as attachment (Content-Disposition: attachment)")
    print("   👁️ View: Should return PDF as inline content (Content-Disposition: inline)")
    print("   📄 Both should have same PDF content, just different headers")
    
    # Test if we can manually check the attachments list
    print(f"\n📋 Step 4: Checking attachment metadata")
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        attachments_response = requests.get(attachments_url, timeout=30)
        print(f"   📤 Attachments URL: {attachments_url}")
        print(f"   🔄 Status: {attachments_response.status_code}")
        
        if attachments_response.status_code == 200:
            attachments_data = attachments_response.json()
            if attachments_data.get('success') and attachments_data.get('attachments'):
                attachments = attachments_data['attachments']
                
                # Find our PDF
                pdf_attachment = None
                for attachment in attachments:
                    if str(attachment.get('docinfoid')) == str(docinfoid):
                        pdf_attachment = attachment
                        break
                
                if pdf_attachment:
                    print(f"   ✅ Found PDF attachment metadata:")
                    print(f"      📄 Filename: {pdf_attachment.get('filename')}")
                    print(f"      🆔 DocInfoID: {pdf_attachment.get('docinfoid')}")
                    print(f"      📊 Size: {pdf_attachment.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 'Unknown')} bytes")
                    print(f"      📋 Content Type: {pdf_attachment.get('original_data', {}).get('describedBy', {}).get('format', {}).get('label', 'Unknown')}")
                    print(f"      🔗 HREF: {pdf_attachment.get('href', 'Unknown')}")
                else:
                    print(f"   ❌ Could not find PDF attachment with docinfoid {docinfoid}")
            else:
                print(f"   ❌ Failed to get attachments: {attachments_data}")
        else:
            print(f"   ❌ Attachments request failed: {attachments_response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Attachments exception: {e}")

if __name__ == "__main__":
    print("🧪 Testing PDF View Functionality")
    print("=" * 60)
    
    test_pdf_view_functionality()
    
    print("\n" + "=" * 60)
    print("🎯 PDF View Test Complete")
    print("\n📋 Next steps:")
    print("1. Identify why view fails while download works")
    print("2. Check if it's a header issue or content issue")
    print("3. Fix the view endpoint to work like download")
    print("4. Ensure proper Content-Disposition headers")
