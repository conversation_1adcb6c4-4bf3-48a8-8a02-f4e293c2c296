#!/usr/bin/env python3
"""
Test mxapiinventory endpoint with curl using API key authentication.
"""
import sys
import os
import subprocess
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def get_api_key(token_manager):
    """Get API key from Maximo."""
    print("🔑 Getting API key from Maximo...")
    
    api_key = token_manager.get_api_key()
    
    if api_key:
        print(f"✅ API key obtained: {api_key[:10]}...{api_key[-10:]}")
        return api_key
    else:
        print("❌ Failed to get API key")
        return None

def test_mxapiinventory_with_apikey(api_key):
    """Test mxapiinventory endpoint with curl using API key."""
    
    print(f"\n🔧 Testing mxapiinventory with API key")
    print("=" * 60)
    
    # The exact payload structure from your example
    payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 0,
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "lotnum": "",
                    "reconciled": True,
                    "memo": "",
                    "controlacc": "",
                    "shrinkageacc": ""
                }
            ]
        }
    ]
    
    # Convert to JSON string
    payload_json = json.dumps(payload, separators=(',', ':'))
    
    print(f"📋 Payload:")
    print(json.dumps(payload, indent=2))
    print(f"\n🌐 URL: {BASE_URL}/api/os/mxapiinventory")
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-10:]}")
    print("")
    
    # Build curl command
    url = f"{BASE_URL}/api/os/mxapiinventory"
    
    cmd = [
        'curl', '-X', 'POST',
        url,
        '-H', 'Accept: application/json',
        '-H', 'Content-Type: application/json',
        '-H', f'apikey: {api_key}',
        '-d', payload_json,
        '-v',
        '--max-time', '30'
    ]
    
    print("🚀 Running curl command...")
    print(f"Command: curl -X POST {url} -H 'apikey: {api_key[:10]}...' [payload]")
    print("")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print(f"📊 Exit code: {result.returncode}")
        print(f"📤 STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print(f"📥 STDERR:")
            print(result.stderr)
        
        # Try to parse the response as JSON
        if result.stdout:
            try:
                response_data = json.loads(result.stdout)
                print(f"\n✅ Response parsed as JSON:")
                print(json.dumps(response_data, indent=2))
                
                # Check for success indicators
                if isinstance(response_data, list) and len(response_data) > 0:
                    first_item = response_data[0]
                    if 'inventoryid' in first_item or 'itemnum' in first_item:
                        print(f"\n🎉 SUCCESS: Inventory adjustment processed!")
                        print(f"📋 Item: {first_item.get('itemnum', 'Unknown')}")
                        if 'inventoryid' in first_item:
                            print(f"📋 Inventory ID: {first_item['inventoryid']}")
                        return True
                elif 'oslc:Error' in response_data:
                    error_info = response_data['oslc:Error']
                    print(f"\n❌ MAXIMO ERROR:")
                    print(f"📋 Status: {error_info.get('oslc:statusCode', 'Unknown')}")
                    print(f"📋 Reason: {error_info.get('spi:reasonCode', 'Unknown')}")
                    print(f"📋 Message: {error_info.get('oslc:message', 'Unknown')}")
                    return False
                elif 'Error' in response_data:
                    print(f"\n❌ API ERROR:")
                    print(json.dumps(response_data['Error'], indent=2))
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"\n⚠️ Response is not valid JSON: {e}")
                print("Raw response:")
                print(result.stdout[:500])
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 30 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running curl: {e}")
        return False

def test_simple_get_with_apikey(api_key):
    """Test a simple GET request first to verify API key works."""
    
    print(f"\n🔧 Testing simple GET with API key")
    print("=" * 40)
    
    # Test with a simple inventory query
    url = f"{BASE_URL}/api/os/mxapiinventory"
    params = "lean=1&oslc.pageSize=1&oslc.select=itemnum,siteid,location"
    
    cmd = [
        'curl', '-X', 'GET',
        f"{url}?{params}",
        '-H', 'Accept: application/json',
        '-H', f'apikey: {api_key}',
        '-v',
        '--max-time', '15'
    ]
    
    print(f"🌐 URL: {url}?{params}")
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-10:]}")
    print("🚀 Running GET test...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        print(f"📊 Exit code: {result.returncode}")
        
        if result.stdout:
            try:
                response_data = json.loads(result.stdout)
                if 'member' in response_data:
                    print(f"✅ GET request successful! Found {len(response_data['member'])} items")
                    if len(response_data['member']) > 0:
                        print(f"📋 Sample item: {response_data['member'][0].get('itemnum', 'Unknown')}")
                    return True
                elif 'oslc:Error' in response_data:
                    error_info = response_data['oslc:Error']
                    print(f"❌ MAXIMO ERROR in GET:")
                    print(f"📋 Status: {error_info.get('oslc:statusCode', 'Unknown')}")
                    print(f"📋 Message: {error_info.get('oslc:message', 'Unknown')}")
                elif 'Error' in response_data:
                    print(f"❌ API ERROR in GET:")
                    print(json.dumps(response_data['Error'], indent=2))
                else:
                    print(f"⚠️ Unexpected response structure: {list(response_data.keys())}")
                    print(f"📋 Response: {json.dumps(response_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"⚠️ Response not JSON: {result.stdout[:200]}")
        
        if result.stderr:
            print(f"📥 STDERR: {result.stderr}")
            
        return False
        
    except Exception as e:
        print(f"❌ Error in GET test: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Starting mxapiinventory API key test")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Check if logged in
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo. Please login first through the web app.")
        print("💡 Go to http://127.0.0.1:5010 and login, then run this script again.")
        return False
    
    print(f"✅ Logged in to Maximo as: {getattr(token_manager, 'username', 'Unknown')}")
    
    # Get API key
    api_key = get_api_key(token_manager)
    
    if not api_key:
        print("❌ Failed to get API key")
        return False
    
    # Test simple GET first
    if test_simple_get_with_apikey(api_key):
        print("\n" + "=" * 60)
        # If GET works, test the POST
        success = test_mxapiinventory_with_apikey(api_key)
        
        if success:
            print("\n🎉 CURL TEST WITH API KEY COMPLETED SUCCESSFULLY!")
            print("✅ The mxapiinventory endpoint is working with API key authentication")
            print("✅ Your payload structure is correct")
            print("✅ Ready to integrate into the QR scanner app")
        else:
            print("\n❌ CURL TEST WITH API KEY FAILED")
            print("🔍 Check the error messages above for details")
            
        return success
    else:
        print("\n❌ GET test failed - API key authentication issue")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
