#!/bin/bash

# Systematic Payload Testing - Find Working Structure
# ===================================================

echo "🔬 SYSTEMATIC PAYLOAD TESTING FOR 204 SUCCESS"
echo "=============================================="

echo "🎯 OBJECTIVE: Find payload structure that avoids cross-site validation"
echo "🔍 STRATEGY: Test multiple payload variations until we get 204 response"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for success
test_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..60})"
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "Response:"
    echo "$response"
    
    # Check for success patterns
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"statusCode": "204"'; then
        echo "🎉 SUCCESS! Found working payload structure!"
        echo "💾 Saving successful payload..."
        echo "$payload" > "successful_payload_test_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        echo "❌ Business logic error (but API working)"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 STARTING SYSTEMATIC PAYLOAD TESTING"
echo "======================================"

# Test 1: Original structure (baseline)
test_payload "Baseline - Original Structure" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 2: Same site transfer (should work)
test_payload "Same Site Transfer" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 3: Reverse direction (IKWAJ to LCVKWT)
test_payload "Reverse Direction Transfer" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "LCVKWT",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "RIP001",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 4: Without explicit site IDs (let system infer)
test_payload "No Explicit Site IDs" '{
    "itemnum": "5975-60-V00-0529",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 5: Only destination site specified
test_payload "Only Destination Site" '{
    "itemnum": "5975-60-V00-0529",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 6: Different field names (alternative API structure)
test_payload "Alternative Field Names" '{
    "itemnum": "5975-60-V00-0529",
    "fromsiteid": "LCVKWT",
    "tositeid": "IKWAJ",
    "fromstoreloc": "RIP001",
    "tostoreloc": "KWAJ-1058",
    "quantity": 1.0,
    "issueunit": "RO"
}'

# Test 7: With transfer type specified
test_payload "With Transfer Type" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "transfer_type": "TRANSFER"
}'

# Test 8: With issue type
test_payload "With Issue Type" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "issue_type": "TRANSFER"
}'

# Test 9: Minimal required fields only
test_payload "Minimal Fields Only" '{
    "itemnum": "5975-60-V00-0529",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0
}'

# Test 10: With explicit action
test_payload "With Explicit Action" '{
    "action": "transfer",
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

echo ""
echo "📊 FIRST ROUND SUMMARY"
echo "====================="
echo "✅ Successful tests: $SUCCESS_COUNT"
echo "❌ Failed tests: $((TEST_COUNT - SUCCESS_COUNT))"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo "🎉 Found working payload(s)! Check successful_payload_test_*.json files"
    exit 0
fi

echo ""
echo "🔄 CONTINUING WITH ADVANCED PAYLOAD STRUCTURES..."
echo "================================================"
