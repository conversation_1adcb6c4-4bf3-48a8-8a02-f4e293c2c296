#!/usr/bin/env python3
"""
Debug script for COMP status signature issue
Tests the specific COMP status configuration and submission
"""

import requests
import json
import base64
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5010"
TEST_TASK_WONUM = "15643630"  # Replace with actual task work order number

# Session for maintaining cookies
session = requests.Session()

def create_test_signature():
    """Create a simple test signature as base64 PNG"""
    # Minimal PNG image for testing
    test_signature_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    return f"data:image/png;base64,{test_signature_b64}"

def authenticate():
    """Authenticate with the application"""
    print("🔐 Authenticating...")

    # Try to access the main page to get session
    response = session.get(f"{BASE_URL}/")
    if response.status_code == 200:
        print("✅ Session established")
        return True
    else:
        print(f"❌ Failed to establish session: {response.status_code}")
        return False

def test_comp_signature_config():
    """Test COMP status signature configuration"""
    print("🧪 Testing COMP Status Signature Configuration...")

    # First, configure COMP status to require signature
    config_data = {
        "statuses": ["COMP"],
        "scope": ["parent", "task"],
        "enabled": True
    }

    print("📝 Setting signature config for COMP status...")
    response = session.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=config_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Config response: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Config saved: {result}")
    else:
        print(f"Config failed: {response.text}")
        return False
    
    # Verify the configuration
    print("\n🔍 Verifying signature requirement for COMP...")
    check_response = session.post(
        f"{BASE_URL}/api/admin/signature-required",
        json={"status": "COMP", "wo_type": "task"},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Check response: {check_response.status_code}")
    if check_response.status_code == 200:
        check_result = check_response.json()
        print(f"Signature required for COMP: {check_result}")
        return check_result.get('signature_required', False)
    else:
        print(f"Check failed: {check_response.text}")
        return False

def test_task_status_change_comp():
    """Test task status change to COMP (should trigger signature requirement)"""
    print("\n🧪 Testing Task Status Change to COMP...")
    
    response = session.post(
        f"{BASE_URL}/api/task/{TEST_TASK_WONUM}/status",
        json={"status": "COMP"},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Task status change response: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Status change result: {result}")
        
        if result.get('signature_required'):
            print("✅ Signature requirement correctly triggered")
            return True
        else:
            print("❌ Signature requirement NOT triggered")
            return False
    else:
        print(f"Status change failed: {response.text}")
        return False

def test_signature_submission_comp():
    """Test signature submission for COMP status"""
    print("\n🧪 Testing Signature Submission for COMP...")
    
    signature_data = {
        "wonum": TEST_TASK_WONUM,
        "status": "COMP",
        "wo_type": "task",
        "signature_data": create_test_signature(),
        "customer_name": "Test Customer",
        "comments": "Test COMP status signature",
        "date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    print(f"📝 Submitting signature for task {TEST_TASK_WONUM}...")
    response = session.post(
        f"{BASE_URL}/api/signature/submit",
        json=signature_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Signature submission response: {response.status_code}")
    print(f"Response headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"Signature submission result: {result}")
            
            if result.get('success'):
                print("✅ Signature submission successful")
                print(f"PDF attached: {result.get('pdf_attached')}")
                print(f"Status change: {result.get('status_change')}")
                return True
            else:
                print(f"❌ Signature submission failed: {result.get('error')}")
                return False
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON response: {e}")
            print(f"Raw response: {response.text[:500]}")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        print(f"Response text: {response.text[:500]}")
        return False

def test_direct_mxapi_call():
    """Test direct MXAPI call for COMP status"""
    print("\n🧪 Testing Direct MXAPI Call for COMP...")
    
    # This would require authentication, but let's see what happens
    try:
        response = session.post(
            f"{BASE_URL}/api/mxapiwodetail/{TEST_TASK_WONUM}/execute/changeStatus",
            json={"status": "COMP"},
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Direct MXAPI response: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Direct MXAPI result: {result}")
        else:
            print(f"Direct MXAPI failed: {response.text}")
    except Exception as e:
        print(f"Direct MXAPI exception: {e}")

def debug_comp_status():
    """Main debug function for COMP status issues"""
    print("🔧 COMP Status Debug Session")
    print("=" * 50)
    print(f"Testing with task work order: {TEST_TASK_WONUM}")
    print("Make sure the Flask app is running on localhost:5000")
    print("=" * 50)

    # Step 0: Authenticate
    if not authenticate():
        print("❌ Authentication failed, stopping tests")
        return

    # Step 1: Configure signature for COMP
    config_success = test_comp_signature_config()
    if not config_success:
        print("❌ Configuration failed, stopping tests")
        return
    
    # Step 2: Test status change (should trigger signature requirement)
    status_change_success = test_task_status_change_comp()
    if not status_change_success:
        print("❌ Status change test failed")
    
    # Step 3: Test signature submission
    signature_success = test_signature_submission_comp()
    if not signature_success:
        print("❌ Signature submission failed")
    
    # Step 4: Test direct MXAPI call
    test_direct_mxapi_call()
    
    print("\n" + "=" * 50)
    print("🏁 Debug Session Complete")
    print("=" * 50)
    
    if config_success and status_change_success and signature_success:
        print("✅ All tests passed - COMP status signature is working")
    else:
        print("❌ Some tests failed - check the output above")
        print("\nTroubleshooting tips:")
        print("1. Check Flask logs for detailed error messages")
        print("2. Verify the task work order number exists")
        print("3. Ensure you're logged into the application")
        print("4. Check browser console for JavaScript errors")

if __name__ == "__main__":
    print("COMP Status Debug Tool")
    print("Update TEST_TASK_WONUM with an actual task work order number")
    
    # Get task work order number from user
    user_wonum = input(f"Enter task work order number (or press Enter for {TEST_TASK_WONUM}): ").strip()
    if user_wonum:
        TEST_TASK_WONUM = user_wonum
    
    debug_comp_status()
