#!/usr/bin/env python3
"""
Test script to debug attachment download functionality
"""

import requests
import json
import os

def test_download_debug():
    """Test downloading attachments to debug the download functionality"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing download functionality for work order {wonum}")
    print("=" * 60)
    
    # Step 1: Get list of attachments
    print("\n📋 Step 1: Getting list of attachments...")
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        response = requests.get(attachments_url, timeout=30)
        print(f"   📤 URL: {attachments_url}")
        print(f"   🔄 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('attachments'):
                attachments = data['attachments']
                print(f"   ✅ Found {len(attachments)} attachments")
                
                # Show first few attachments
                for i, attachment in enumerate(attachments[:5]):
                    print(f"   📎 {i+1}. {attachment.get('filename', 'Unknown')} (ID: {attachment.get('docinfoid')}) - {attachment.get('doctype', 'Unknown type')}")
                
                # Test downloading the first attachment
                if attachments:
                    test_attachment = attachments[0]
                    docinfoid = test_attachment.get('docinfoid')
                    filename = test_attachment.get('filename', 'unknown_file')
                    
                    print(f"\n📥 Step 2: Testing download of '{filename}' (ID: {docinfoid})...")
                    
                    download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
                    
                    try:
                        download_response = requests.get(download_url, timeout=60)
                        print(f"   📤 Download URL: {download_url}")
                        print(f"   🔄 Download Status: {download_response.status_code}")
                        print(f"   📊 Content Length: {len(download_response.content)} bytes")
                        print(f"   📋 Content Type: {download_response.headers.get('content-type', 'Unknown')}")
                        
                        if download_response.status_code == 200:
                            # Check if it's actually file content or an error
                            content = download_response.content
                            
                            # Check if it's HTML (error page)
                            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                                print(f"   ❌ Got HTML response (likely login page): {content[:100].decode('utf-8', errors='ignore')}")
                            else:
                                print(f"   ✅ Got file content! First 100 bytes: {content[:100]}")
                                
                                # Save to test file
                                test_filename = f"downloaded_{filename}"
                                with open(test_filename, 'wb') as f:
                                    f.write(content)
                                print(f"   💾 Saved to: {test_filename}")
                        else:
                            print(f"   ❌ Download failed with status {download_response.status_code}")
                            try:
                                error_data = download_response.json()
                                print(f"   📝 Error: {error_data.get('error', 'Unknown error')}")
                            except:
                                print(f"   📝 Response: {download_response.text[:200]}")
                    
                    except Exception as e:
                        print(f"   ❌ Download exception: {e}")
                    
                    # Test view functionality
                    print(f"\n👁️ Step 3: Testing view of '{filename}' (ID: {docinfoid})...")
                    
                    view_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/view'
                    
                    try:
                        view_response = requests.get(view_url, timeout=60)
                        print(f"   📤 View URL: {view_url}")
                        print(f"   🔄 View Status: {view_response.status_code}")
                        print(f"   📊 Content Length: {len(view_response.content)} bytes")
                        print(f"   📋 Content Type: {view_response.headers.get('content-type', 'Unknown')}")
                        
                        if view_response.status_code == 200:
                            # Check if it's actually file content or an error
                            content = view_response.content
                            
                            # Check if it's HTML (error page)
                            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                                print(f"   ❌ Got HTML response (likely login page): {content[:100].decode('utf-8', errors='ignore')}")
                            else:
                                print(f"   ✅ Got file content for viewing! First 100 bytes: {content[:100]}")
                        else:
                            print(f"   ❌ View failed with status {view_response.status_code}")
                            try:
                                error_data = view_response.json()
                                print(f"   📝 Error: {error_data.get('error', 'Unknown error')}")
                            except:
                                print(f"   📝 Response: {view_response.text[:200]}")
                    
                    except Exception as e:
                        print(f"   ❌ View exception: {e}")
                
            else:
                print(f"   ❌ No attachments found or API error: {data}")
        else:
            print(f"   ❌ Failed to get attachments: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   📝 Error: {error_data}")
            except:
                print(f"   📝 Response: {response.text[:200]}")
                
    except Exception as e:
        print(f"   ❌ Exception getting attachments: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Download Debug Test Complete")
    print("\n📋 Next Steps:")
    print("1. Check if authentication is working properly")
    print("2. Verify the download URLs are correct")
    print("3. Test different attachment types")
    print("4. Implement fixes based on findings")

if __name__ == "__main__":
    test_download_debug()
