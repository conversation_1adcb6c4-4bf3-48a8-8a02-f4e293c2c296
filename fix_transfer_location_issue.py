#!/usr/bin/env python3
"""
Fix Transfer Location Issue
===========================

Based on the error you showed, the issue is that the location KWAJ-1058 
doesn't exist in site LCVKWT. Let's fix this and test multiple variations
until we get a successful 204 response.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import subprocess
import time
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def test_transfer_variations():
    """Test transfer variations with corrected location references."""
    print("🚀 TESTING TRANSFER VARIATIONS - FIXING LOCATION ISSUE")
    print("=" * 60)
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    
    # Try to initialize token manager
    try:
        token_manager = MaximoTokenManager(base_url)
        if token_manager.is_logged_in():
            print("✅ Using Python session (authenticated)")
            use_python = True
        else:
            print("❌ Python session not available, will try curl with cookies")
            use_python = False
    except:
        print("❌ Python session failed, will try curl with cookies")
        use_python = False
    
    endpoint = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    # Test variations - fixing the location issue
    test_cases = [
        {
            "name": "Test 1: Minimal - No bins/lots",
            "description": "Basic transfer without bins or lots",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 2: With issue units",
            "description": "Transfer with issue units specified",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "issueunit": "RO"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 3: With bins only",
            "description": "Transfer with bin numbers",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 4: With lots only",
            "description": "Transfer with lot numbers",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "fromlotnum": "TEST",
                            "tolotnum": "TEST"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 5: Complete with all fields",
            "description": "Transfer with all optional fields",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP",
                            "fromlotnum": "TEST",
                            "tolotnum": "TEST",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        }
    ]
    
    successful_tests = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 {test_case['name']}")
        print(f"📝 {test_case['description']}")
        print("=" * 60)
        
        if use_python:
            success, response_data = test_with_python_session(token_manager, endpoint, test_case)
        else:
            success, response_data = test_with_curl(endpoint, test_case)
        
        if success:
            print(f"🎉 SUCCESS! {test_case['name']} worked!")
            successful_tests.append({
                'test_case': test_case,
                'response': response_data
            })
            
            # Save successful payload immediately
            filename = f"successful_transfer_test_{i}.json"
            with open(filename, 'w') as f:
                json.dump(test_case['payload'], f, indent=2)
            print(f"💾 Successful payload saved to: {filename}")
        
        # Brief pause between tests
        time.sleep(1)
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 30)
    print(f"✅ Successful tests: {len(successful_tests)}")
    print(f"❌ Failed tests: {len(test_cases) - len(successful_tests)}")
    
    if successful_tests:
        print(f"\n🎯 WORKING TRANSFER PATTERNS:")
        for i, success in enumerate(successful_tests, 1):
            print(f"  {i}. {success['test_case']['name']}")
        
        # Save all successful patterns
        with open('all_successful_transfer_patterns.json', 'w') as f:
            json.dump(successful_tests, f, indent=2)
        print(f"\n💾 All successful patterns saved to: all_successful_transfer_patterns.json")
    else:
        print(f"\n❌ No successful transfers found")

def test_with_python_session(token_manager, endpoint, test_case):
    """Test using Python session."""
    try:
        print(f"🐍 Testing with Python session...")
        
        response = token_manager.session.post(
            endpoint,
            json=test_case['payload'],
            headers={
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'x-method-override': 'BULK'
            },
            timeout=(3.05, 30)
        )
        
        print(f"📊 HTTP Status: {response.status_code}")
        
        if response.text:
            try:
                data = response.json()
                print(f"📋 Response: {json.dumps(data, indent=2)}")
                
                # Check for success patterns
                if response.status_code == 204:
                    return True, data
                
                if isinstance(data, list) and len(data) > 0:
                    first_item = data[0]
                    if '_responsemeta' in first_item:
                        meta_status = first_item['_responsemeta'].get('status')
                        if meta_status == '204':
                            print("🎉 Found 204 in response meta!")
                            return True, data
                        else:
                            print(f"❌ Meta status: {meta_status}")
                    
                    if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                        error = first_item['_responsedata']['Error']
                        print(f"❌ Error: {error.get('reasonCode')} - {error.get('message')}")
                
                return False, data
                
            except json.JSONDecodeError:
                print(f"📄 Response (non-JSON): {response.text}")
                return False, response.text
        
        return False, None
        
    except Exception as e:
        print(f"❌ Python session error: {str(e)}")
        return False, str(e)

def test_with_curl(endpoint, test_case):
    """Test using curl with cookies file."""
    try:
        print(f"🌐 Testing with curl...")
        
        payload_json = json.dumps(test_case['payload'])
        
        cmd = [
            'curl', '-X', 'POST', endpoint,
            '-H', 'Accept: application/json',
            '-H', 'Content-Type: application/json',
            '-H', 'x-method-override: BULK',
            '--cookie', 'cookies.txt',
            '--cookie-jar', 'cookies.txt',
            '-d', payload_json,
            '-w', '\\nHTTP_STATUS:%{http_code}\\nTIME:%{time_total}\\n',
            '-s'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        output = result.stdout
        
        # Extract HTTP status
        status_code = None
        if 'HTTP_STATUS:' in output:
            status_line = [line for line in output.split('\n') if 'HTTP_STATUS:' in line][0]
            status_code = int(status_line.split(':')[1])
        
        print(f"📊 HTTP Status: {status_code}")
        
        # Parse JSON response
        json_part = output.split('HTTP_STATUS:')[0].strip()
        if json_part:
            try:
                response_data = json.loads(json_part)
                print(f"📋 Response: {json.dumps(response_data, indent=2)}")
                
                # Check for success
                if status_code == 204:
                    return True, response_data
                
                if isinstance(response_data, list) and len(response_data) > 0:
                    first_item = response_data[0]
                    if '_responsemeta' in first_item:
                        meta_status = first_item['_responsemeta'].get('status')
                        if meta_status == '204':
                            print("🎉 Found 204 in response meta!")
                            return True, response_data
                
                return False, response_data
                
            except json.JSONDecodeError:
                print(f"📄 Response (non-JSON): {json_part}")
                return False, json_part
        
        return False, None
        
    except Exception as e:
        print(f"❌ Curl error: {str(e)}")
        return False, str(e)

def main():
    """Main function."""
    test_transfer_variations()

if __name__ == "__main__":
    main()
