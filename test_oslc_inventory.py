#!/usr/bin/env python3
"""
Test OSLC authentication and inventory field processing.
"""
import os
import sys
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from auth.token_manager import MaximoTokenManager

def test_oslc_inventory():
    """Test OSLC authentication and inventory field processing."""
    
    print("🔧 TESTING OSLC INVENTORY ACCESS")
    print("=" * 40)
    
    # Initialize token manager
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo")
        return False
    
    print("✅ Authenticated with Maximo via OSLC")
    
    # Test item number
    test_itemnum = "5975-60-V00-0001"
    
    print(f"\n🔍 Testing with item: {test_itemnum}")
    
    # Test MXAPIINVENTORY endpoint
    api_url = f"{base_url}/oslc/os/mxapiinventory"
    
    # Use all the fields we defined
    select_fields = [
        # Core inventory fields
        "itemnum", "siteid", "location", "status", "itemtype", "itemsetid",
        "issueunit", "orderunit", "curbaltotal", "avblbalance",
        "costtype", "conditioncode", "inventoryid", "orgid",
        "maxlevel", "minlevel", "reorder", "reservedqty", "stagedqty",
        "opstime", "deliverytime", "admimtime", "statusdate",
        "issue1yrago", "issue2yrago", "issue3yrago", "issueytd",
        "expiredqty", "invreserveqty", "shippedqty",
        # Nested array fields
        "invcost", "invbalances", "itemcondition", "invvendor"
    ]
    
    params = {
        "oslc.select": ",".join(select_fields),
        "oslc.where": f'itemnum="{test_itemnum}"',
        "oslc.pageSize": "5",
        "lean": "1"
    }
    
    try:
        print(f"🔗 Querying: {api_url}")
        print(f"📋 Filter: {params['oslc.where']}")
        
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(10.0, 30),
            headers={"Accept": "application/json"}
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Content Type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            # Check if we got JSON or HTML (login redirect)
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                data = response.json()
                members = data.get('member', [])
                
                print(f"📦 Found {len(members)} inventory records")
                
                if members:
                    # Process the first record
                    raw_item = members[0]
                    
                    print(f"\n🔍 RAW ITEM DATA:")
                    print(f"   Basic fields: itemnum={raw_item.get('itemnum')}, siteid={raw_item.get('siteid')}")
                    print(f"   Location: {raw_item.get('location')}")
                    print(f"   Status: {raw_item.get('status')}")
                    
                    # Check nested arrays
                    if 'invcost' in raw_item:
                        print(f"   INVCOST array: {len(raw_item['invcost']) if isinstance(raw_item['invcost'], list) else 'not array'} items")
                        if isinstance(raw_item['invcost'], list) and raw_item['invcost']:
                            cost_data = raw_item['invcost'][0]
                            print(f"      avgcost: {cost_data.get('avgcost')}")
                            print(f"      lastcost: {cost_data.get('lastcost')}")
                    
                    if 'invbalances' in raw_item:
                        print(f"   INVBALANCES array: {len(raw_item['invbalances']) if isinstance(raw_item['invbalances'], list) else 'not array'} items")
                        if isinstance(raw_item['invbalances'], list) and raw_item['invbalances']:
                            balance_data = raw_item['invbalances'][0]
                            print(f"      curbal: {balance_data.get('curbal')}")
                            print(f"      physcnt: {balance_data.get('physcnt')}")
                    
                    if 'invvendor' in raw_item:
                        print(f"   INVVENDOR array: {len(raw_item['invvendor']) if isinstance(raw_item['invvendor'], list) else 'not array'} items")
                        if isinstance(raw_item['invvendor'], list) and raw_item['invvendor']:
                            vendor_data = raw_item['invvendor'][0]
                            print(f"      vendor: {vendor_data.get('vendor')}")
                            print(f"      manufacturer: {vendor_data.get('manufacturer')}")
                    
                    # Test MXAPIITEM endpoint
                    print(f"\n🔍 TESTING MXAPIITEM:")
                    item_api_url = f"{base_url}/oslc/os/mxapiitem"
                    item_params = {
                        "oslc.select": "itemnum,description,rotating,lottype,conditionenabled,status,itemtype",
                        "oslc.where": f'itemnum="{test_itemnum}"',
                        "oslc.pageSize": "1",
                        "lean": "1"
                    }
                    
                    item_response = token_manager.session.get(
                        item_api_url,
                        params=item_params,
                        timeout=(10.0, 30),
                        headers={"Accept": "application/json"}
                    )
                    
                    if item_response.status_code == 200 and 'application/json' in item_response.headers.get('content-type', ''):
                        item_data = item_response.json()
                        item_members = item_data.get('member', [])
                        if item_members:
                            item_info = item_members[0]
                            print(f"   ITEM description: {item_info.get('description')}")
                            print(f"   ITEM rotating: {item_info.get('rotating')}")
                            print(f"   ITEM conditionenabled: {item_info.get('conditionenabled')}")
                    
                    # Save data for inspection
                    with open('oslc_inventory_test.json', 'w') as f:
                        json.dump(raw_item, f, indent=2, default=str)
                    
                    print(f"\n💾 Data saved to: oslc_inventory_test.json")
                    
                    print(f"\n🎉 OSLC TEST SUMMARY:")
                    print("=" * 30)
                    print("✅ OSLC authentication working")
                    print("✅ MXAPIINVENTORY endpoint accessible")
                    print("✅ MXAPIITEM endpoint accessible")
                    print("✅ Nested array data available")
                    print("✅ Field processing logic ready")
                    
                    return True
                else:
                    print("❌ No inventory records found")
                    return False
            else:
                print(f"❌ Got HTML response (login redirect), not JSON")
                print(f"Response content (first 200 chars): {response.text[:200]}")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_oslc_inventory()
    if success:
        print(f"\n🎉 OSLC test passed!")
    else:
        print(f"\n❌ OSLC test failed")
        sys.exit(1)
