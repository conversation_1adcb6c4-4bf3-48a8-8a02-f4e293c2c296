#!/bin/bash

# Test Hardcode Removed - Verify Any Site Combination Works
# =========================================================

echo "🚀 TEST HARDCODE REMOVED - VERIFY ANY SITE COMBINATION WORKS"
echo "============================================================"

echo "🎯 OBJECTIVE: Verify hardcoded LCVKWT→IKWAJ restriction is removed"
echo "📋 ISSUE FOUND: Line 251-252 had hardcoded if condition limiting to specific sites"
echo "🔧 FIX APPLIED: Removed hardcoded site restriction - now works for ALL combinations"
echo ""

echo "⚠️  IMPORTANT: Login at http://127.0.0.1:5010 first"
echo ""

read -p "Ready to test? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please login first and try again"
    exit 1
fi

# Function to test any site combination
test_any_site_combination() {
    local test_name="$1"
    local from_site="$2"
    local to_site="$3"
    local from_store="$4"
    local to_store="$5"
    
    echo ""
    echo "🔍 TESTING: $test_name"
    echo "$(printf '=%.0s' {1..60})"
    echo "📋 From: $from_site/$from_store → To: $to_site/$to_store"
    
    payload='{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "'$from_site'",
        "to_siteid": "'$to_site'",
        "from_storeroom": "'$from_store'",
        "to_storeroom": "'$to_store'",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'
    
    echo "🔄 Submitting to cross-site endpoint..."
    echo "👀 WATCH FLASK TERMINAL FOR PAYLOAD STRUCTURE!"
    
    response=$(curl -X POST "http://127.0.0.1:5010/api/inventory/transfer-cross-site" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -s)
    
    echo ""
    echo "📊 Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    # Check if it reaches Maximo (not blocked by hardcode)
    if echo "$response" | grep -q '"success": true'; then
        echo "✅ SUCCESS! Reached Maximo successfully"
        return 0
    elif echo "$response" | grep -q '"success": false'; then
        if echo "$response" | grep -q 'BMXAA'; then
            echo "✅ HARDCODE REMOVED! Getting real Maximo error (not hardcoded block)"
            return 0
        else
            echo "❌ May still be blocked by hardcode"
            return 1
        fi
    elif echo "$response" | grep -q "401"; then
        echo "🔐 Authentication issue"
        return 1
    else
        echo "⚠️  Unexpected response"
        return 1
    fi
}

echo "🚀 TESTING DIFFERENT SITE COMBINATIONS"
echo "====================================="

# Test 1: Original working combination (should still work)
test_any_site_combination "Original Working: LCVKWT → IKWAJ" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1500"

# Test 2: Different destination storeroom (should work now)
test_any_site_combination "Different Destination: LCVKWT → IKWAJ" \
    "LCVKWT" "IKWAJ" "CMW-AJ" "KWAJ-1115"

# Test 3: Reverse direction (should work now)
test_any_site_combination "Reverse Direction: IKWAJ → LCVKWT" \
    "IKWAJ" "LCVKWT" "KWAJ-1058" "CMW-AJ"

# Test 4: Same site (should work)
test_any_site_combination "Same Site: IKWAJ → IKWAJ" \
    "IKWAJ" "IKWAJ" "KWAJ-1058" "KWAJ-1115"

# Test 5: Different sites entirely (should work now)
test_any_site_combination "Different Sites: LCVIRQ → IKWAJ" \
    "LCVIRQ" "IKWAJ" "CMW-ALA" "KWAJ-1058"

echo ""
echo "📊 HARDCODE REMOVAL TEST SUMMARY"
echo "==============================="

echo ""
echo "🎯 WHAT TO LOOK FOR IN FLASK TERMINAL:"
echo "====================================="
echo ""
echo "✅ EXPECTED PAYLOAD STRUCTURE (All Tests):"
echo "{"
echo "  \"_action\": \"AddChange\","
echo "  \"itemnum\": \"5975-60-V00-0529\","
echo "  \"siteid\": \"[DESTINATION_SITE]\",     ← Should be destination site"
echo "  \"location\": \"[DESTINATION_STORE]\",  ← Should be destination storeroom"
echo "  \"matrectrans\": [{"
echo "    \"fromsiteid\": \"[SOURCE_SITE]\","
echo "    \"tositeid\": \"[DESTINATION_SITE]\","
echo "    \"fromstoreloc\": \"[SOURCE_STORE]\","
echo "    \"tostoreloc\": \"[DESTINATION_STORE]\","
echo "    \"toissueunit\": \"EA\""
echo "  }]"
echo "}"
echo ""
echo "❌ WHAT SHOULD NOT HAPPEN:"
echo "• No hardcoded blocking before reaching Maximo"
echo "• No special handling only for LCVKWT→IKWAJ"
echo "• All site combinations should use same payload structure"
echo ""
echo "🎯 SUCCESS INDICATORS:"
echo "• All tests reach Maximo (get BMXAA errors or 204 success)"
echo "• No hardcoded validation blocking transfers"
echo "• Same payload structure for all site combinations"
echo "• Destination site context used consistently"
echo ""
echo "🔧 IF STILL GETTING BMXAA1861E:"
echo "• This is now the REAL Maximo error (not hardcoded)"
echo "• Try different bin/lot combinations"
echo "• Use different storeroom combinations"
echo "• Check if inventory already exists in destination"
echo ""
echo "🎉 EXPECTED RESULT:"
echo "All site combinations should now work with the same logic!"
echo "No more hardcoded restrictions to specific site pairs!"
