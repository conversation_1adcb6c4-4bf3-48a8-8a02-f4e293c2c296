#!/usr/bin/env python3
"""
Test script to verify negative labor fixes
Tests the missing parameters fix and API functionality
"""

import requests
import json
import sys
import time

def test_api_with_proper_parameters():
    """Test that the API works with proper parameters"""
    print("🧪 Testing API with proper parameters...")
    
    # Test data with all required parameters
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -0.1,  # Small negative amount for testing
        "siteid": "LCVKWT",
        "taskid": 10,
        "parent_wonum": "2219753",
        "craft": "MATCTRLSPCSR"
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    print(f"🔧 URL: {url}")
    print(f"📋 Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ Response JSON: {json.dumps(response_data, indent=2)}")
                
                if response_data.get('success'):
                    print("🎉 SUCCESS: API accepts proper parameters and returns success!")
                    return True
                else:
                    print(f"❌ FAILED: API returned success=false: {response_data.get('error', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                print(f"📄 Response text: {response.text}")
                print("❌ FAILED: Expected JSON response")
                return False
        else:
            try:
                response_data = response.json()
                print(f"❌ Error response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"❌ Response text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to Flask app. Is it running on port 5010?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_api_missing_parameters():
    """Test that the API properly rejects missing parameters"""
    print("\n🧪 Testing API with missing parameters...")
    
    # Test data missing required parameters
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -0.1,
        # Missing siteid, taskid, parent_wonum - these should cause the error
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    print(f"🔧 URL: {url}")
    print(f"📋 Data (missing params): {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 400:
            try:
                response_data = response.json()
                print(f"📄 Error response: {json.dumps(response_data, indent=2)}")
                
                error_msg = response_data.get('error', '')
                if not response_data.get('success') and 'Missing required parameters' in error_msg:
                    print("✅ SUCCESS: API correctly rejects missing parameters!")
                    return True
                else:
                    print(f"❌ FAILED: Expected 'Missing required parameters' error, got: {error_msg}")
                    return False
            except json.JSONDecodeError:
                print(f"📄 Response text: {response.text}")
                print("❌ FAILED: Expected JSON error response")
                return False
        else:
            print(f"❌ FAILED: Expected 400 status for missing params, got {response.status_code}")
            try:
                response_data = response.json()
                print(f"📄 Unexpected response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"📄 Response text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to Flask app. Is it running on port 5010?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_labor_records_api():
    """Test that we can fetch labor records and see negative entries"""
    print("\n🧪 Testing labor records API to verify negative entries...")
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/labor?status=COMP&refresh=true"
    
    print(f"🔧 URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            if response_data.get('success'):
                labor_records = response_data.get('labor_records', [])
                print(f"📊 Found {len(labor_records)} labor records")
                
                positive_count = 0
                negative_count = 0
                
                for i, record in enumerate(labor_records, 1):
                    laborcode = record.get('laborcode', 'Unknown')
                    hours = record.get('regularhrs', 0)
                    
                    if hours > 0:
                        positive_count += 1
                        print(f"   {i}. {laborcode}: +{hours}h (positive)")
                    elif hours < 0:
                        negative_count += 1
                        print(f"   {i}. {laborcode}: {hours}h (negative) ✅")
                    else:
                        print(f"   {i}. {laborcode}: {hours}h (zero)")
                
                print(f"\n📊 Summary: {positive_count} positive, {negative_count} negative records")
                
                if negative_count > 0:
                    print("✅ SUCCESS: Found negative labor records in the system!")
                    return True
                else:
                    print("⚠️  WARNING: No negative labor records found (but API might still work)")
                    return True  # Not a failure, just no negative records yet
            else:
                print(f"❌ FAILED: API returned success=false: {response_data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            try:
                response_data = response.json()
                print(f"📄 Error response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"📄 Response text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to Flask app. Is it running on port 5010?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Negative Labor API Tests")
    print("="*60)
    
    # Test 1: Check current labor records
    test1_success = test_labor_records_api()
    
    # Test 2: API with proper parameters
    test2_success = test_api_with_proper_parameters()
    
    # Test 3: API with missing parameters
    test3_success = test_api_missing_parameters()
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS:")
    print(f"✅ Labor records API: {'PASSED' if test1_success else 'FAILED'}")
    print(f"✅ API with proper parameters: {'PASSED' if test2_success else 'FAILED'}")
    print(f"✅ API missing parameters validation: {'PASSED' if test3_success else 'FAILED'}")
    
    all_passed = test1_success and test2_success and test3_success
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Negative labor API functionality is working correctly!")
        print("✅ Missing parameters issue has been fixed!")
        print("✅ You can now successfully post negative labor hours in Maximo!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("❌ There may be issues with the negative labor functionality")
        sys.exit(1)

if __name__ == "__main__":
    main()
