#!/usr/bin/env python3
"""
Query MXAPIINVENTORY endpoint for item 5975-60-V00-0001 to analyze field structure.
"""
import os
import sys
import json
import logging
import requests
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('query_inventory_fields')

# Load environment variables
load_dotenv()

def query_inventory_fields():
    """Query MXAPIINVENTORY for item 5975-60-V00-0001 and analyze field structure."""

    # Get credentials from environment
    base_url = os.getenv('MAXIMO_BASE_URL', 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
    api_key = os.getenv('MAXIMO_API_KEY')

    if not api_key:
        print("❌ MAXIMO_API_KEY not found in environment")
        return

    print("✅ Using API Key authentication")

    # Try different endpoint formats
    endpoints = [
        f"{base_url}/api/os/mxapiinventory",  # Standard REST API format
        f"{base_url}/oslc/os/mxapiinventory",  # OSLC format
    ]

    # Use * to get all available fields
    params = {
        "oslc.select": "*",
        "oslc.where": 'itemnum="5975-60-V00-0001"',
        "oslc.pageSize": "10",
        "lean": "1"
    }

    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    # Try each endpoint until one works
    for api_url in endpoints:
        print(f"🔗 Trying: {api_url}")
        print(f"📋 Filter: {params['oslc.where']}")
        print("=" * 60)

        try:
            response = requests.get(
                api_url,
                params=params,
                timeout=(10.0, 30),
                headers=headers
            )
        
            print(f"📊 Response Status: {response.status_code}")
            print(f"📊 Response Headers: {dict(response.headers)}")
            print(f"📊 Response Content Length: {len(response.text)}")
            print(f"📊 Response Content (first 500 chars): {response.text[:500]}")

            if response.status_code == 200:
                if not response.text.strip():
                    print("❌ Empty response received")
                    continue

                try:
                    data = response.json()
                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error: {e}")
                    print(f"Raw response: {response.text}")
                    continue

                members = data.get('member', [])
            
                print(f"📦 Found {len(members)} inventory records")

                if members:
                    print("\n🔍 ANALYZING FIELD STRUCTURE:")
                    print("=" * 60)

                    for i, record in enumerate(members):
                        print(f"\n📋 RECORD {i+1}:")
                        print(f"   Site ID: {record.get('siteid', 'N/A')}")
                        print(f"   Location: {record.get('location', 'N/A')}")
                        print(f"   Status: {record.get('status', 'N/A')}")

                        print(f"\n🏷️  ALL AVAILABLE FIELDS IN RECORD {i+1}:")
                        print("-" * 40)

                        # Sort fields for better readability
                        sorted_fields = sorted(record.keys())

                        for field in sorted_fields:
                            value = record[field]
                            # Handle nested objects
                            if isinstance(value, dict):
                                print(f"   {field}: (nested object)")
                                for sub_field, sub_value in value.items():
                                    print(f"      {field}.{sub_field}: {sub_value}")
                            elif isinstance(value, list):
                                print(f"   {field}: (array with {len(value)} items)")
                                if value:  # Show first item if array is not empty
                                    first_item = value[0]
                                    if isinstance(first_item, dict):
                                        print(f"      First item fields: {list(first_item.keys())}")
                                    else:
                                        print(f"      First item: {first_item}")
                            else:
                                print(f"   {field}: {value}")

                    # Save full response to file for detailed analysis
                    with open('inventory_field_analysis.json', 'w') as f:
                        json.dump(data, f, indent=2, default=str)

                    print(f"\n💾 Full response saved to: inventory_field_analysis.json")
                    return  # Success, exit the function

                else:
                    print("❌ No inventory records found for item 5975-60-V00-0001")

            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")

        except Exception as e:
            print(f"❌ Error querying inventory: {e}")
            logger.exception("Error in query_inventory_fields")

    print("❌ All endpoints failed")

if __name__ == "__main__":
    query_inventory_fields()
