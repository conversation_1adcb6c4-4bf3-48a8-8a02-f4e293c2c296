#!/usr/bin/env python3
"""
Comprehensive Availability Data Discovery Script

This script discovers ALL availability-related data from MXAPIINVENTORY including:
- Locations and balances
- Lots and lot tracking
- Purchasing data (matrectrans)
- Reservations and usage (matusetrans)
- Alternate items and substitutions

Author: Augment Agent
Date: 2025-07-16
"""

import sys
import os
import json
import requests
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.auth.token_manager import MaximoTokenManager

def discover_comprehensive_availability():
    """Discover comprehensive availability data from MXAPIINVENTORY."""
    print("🔍 DISCOVERING COMPREHENSIVE AVAILABILITY DATA")
    print("=" * 80)
    
    # Initialize token manager
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated - please login first")
        return None

    print("✅ Authenticated successfully")

    # Verify session is working by testing a simple endpoint first
    try:
        verify_url = f"{base_url}/oslc/whoami"
        verify_response = token_manager.session.get(verify_url, timeout=(3.05, 10))
        if verify_response.status_code != 200:
            print(f"⚠️ Session verification failed: {verify_response.status_code}")
            # Try to refresh session
            if not token_manager.force_session_refresh():
                print("❌ Failed to refresh session")
                return None
        print("✅ Session verified and working")
    except Exception as e:
        print(f"⚠️ Session verification error: {e}")
        return None
    
    # Test parameters - using known working item
    test_itemnum = "5975-60-V00-0001"
    test_siteid = "LCVKWT"
    
    print(f"📋 Testing with Item: {test_itemnum}, Site: {test_siteid}")
    
    # Build comprehensive field selection including ALL nested objects
    # Use OSLC endpoint with session authentication (working pattern)
    api_url = f"{base_url}/oslc/os/mxapiinventory"
    
    # Core availability fields
    core_fields = [
        "itemnum", "siteid", "location", "inventoryid", "itemsetid", "status",
        "avblbalance", "curbaltotal", "reservedqty", "hardreservedqty", "softreservedqty",
        "minlevel", "maxlevel", "orderqty", "deliverytime", "benchstock",
        "issueunit", "orderunit", "itemtype", "abc", "statusdate", "lastissuedate",
        "orgid", "expiredqty", "invreserveqty", "shippedqty", "stagedqty"
    ]
    
    # ALL nested objects that contain availability-related data
    nested_objects = [
        "invcost",           # Cost information
        "invbalances",       # Detailed balance records with lots/bins
        "invvendor",         # Vendor information
        "itemcondition",     # Item condition data
        "matusetrans",       # Material use transactions (reservations/usage)
        "matrectrans",       # Material receipt transactions (purchasing)
        "transfercuritem"    # Transfer information
    ]
    
    all_fields = core_fields + nested_objects
    
    params = {
        "oslc.select": ",".join(all_fields),
        "oslc.where": f'itemnum="{test_itemnum}" and siteid="{test_siteid}"',
        "oslc.pageSize": "50",
        "lean": "1"  # Use lean=1 like the working endpoint
    }
    
    try:
        print(f"🔗 Making comprehensive request to: {api_url}")
        print(f"📋 Fields requested: {len(all_fields)} total")
        
        # Use session authentication (working pattern from codebase)
        response = token_manager.session.get(
            api_url,
            params=params,
            timeout=(10.0, 30),  # Longer timeout for comprehensive data
            headers={"Accept": "application/json"}
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        print(f"📊 Raw Response (first 500 chars): {response.text[:500]}")

        if response.status_code == 200:
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"📊 Full Response Text: {response.text}")
                return None
            records = data.get('member', [])
            
            if records:
                print(f"✅ Found {len(records)} inventory records")
                
                # Analyze the comprehensive data structure
                comprehensive_data = analyze_comprehensive_structure(records)
                
                # Save the raw data for reference
                save_raw_data(records, test_itemnum, test_siteid)
                
                return comprehensive_data
            else:
                print("❌ No inventory records found")
                return None
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        return None

def analyze_comprehensive_structure(records):
    """Analyze the comprehensive data structure."""
    print("\n📊 ANALYZING COMPREHENSIVE DATA STRUCTURE")
    print("=" * 60)
    
    comprehensive_data = {
        'locations': [],
        'lots': [],
        'purchasing': [],
        'reservations': [],
        'alternates': [],
        'summary': {}
    }
    
    for record in records:
        print(f"\n📋 Record: {record.get('itemnum')} at {record.get('location')}")
        
        # 1. LOCATIONS AND BALANCES
        location_data = {
            'location': record.get('location'),
            'available_balance': record.get('avblbalance'),
            'current_balance': record.get('curbaltotal'),
            'reserved_qty': record.get('reservedqty'),
            'hard_reserved': record.get('hardreservedqty'),
            'soft_reserved': record.get('softreservedqty'),
            'expired_qty': record.get('expiredqty'),
            'shipped_qty': record.get('shippedqty'),
            'staged_qty': record.get('stagedqty')
        }
        comprehensive_data['locations'].append(location_data)
        
        # 2. LOTS AND DETAILED BALANCES
        invbalances = record.get('invbalances', [])
        if invbalances:
            print(f"  📦 Found {len(invbalances)} balance records")
            for balance in invbalances:
                lot_data = {
                    'binnum': balance.get('binnum'),
                    'lotnum': balance.get('lotnum'),
                    'conditioncode': balance.get('conditioncode'),
                    'curbal': balance.get('curbal'),
                    'physcnt': balance.get('physcnt'),
                    'physcntdate': balance.get('physcntdate'),
                    'reconciled': balance.get('reconciled'),
                    'stagedcurbal': balance.get('stagedcurbal'),
                    'stagingbin': balance.get('stagingbin')
                }
                comprehensive_data['lots'].append(lot_data)
        
        # 3. PURCHASING DATA (MATRECTRANS)
        matrectrans = record.get('matrectrans', [])
        if matrectrans:
            print(f"  🛒 Found {len(matrectrans)} receipt transactions")
            for receipt in matrectrans[:5]:  # Show first 5
                purchase_data = {
                    'receiptquantity': receipt.get('receiptquantity'),
                    'unitcost': receipt.get('unitcost'),
                    'linecost': receipt.get('linecost'),
                    'actualdate': receipt.get('actualdate'),
                    'transdate': receipt.get('transdate'),
                    'ponum': receipt.get('ponum'),
                    'polinenum': receipt.get('polinenum'),
                    'receivedunit': receipt.get('receivedunit'),
                    'fromsiteid': receipt.get('fromsiteid'),
                    'tostoreloc': receipt.get('tostoreloc'),
                    'vendor': receipt.get('vendor')
                }
                comprehensive_data['purchasing'].append(purchase_data)
        
        # 4. RESERVATIONS AND USAGE (MATUSETRANS)
        matusetrans = record.get('matusetrans', [])
        if matusetrans:
            print(f"  📋 Found {len(matusetrans)} use transactions")
            for usage in matusetrans[:5]:  # Show first 5
                reservation_data = {
                    'quantity': usage.get('quantity'),
                    'actualdate': usage.get('actualdate'),
                    'wonum': usage.get('wonum'),
                    'mrnum': usage.get('mrnum'),
                    'mrlinenum': usage.get('mrlinenum'),
                    'issuetype': usage.get('issuetype'),
                    'binnum': usage.get('binnum'),
                    'lotnum': usage.get('lotnum'),
                    'conditioncode': usage.get('conditioncode'),
                    'storeloc': usage.get('storeloc'),
                    'assetnum': usage.get('assetnum'),
                    'linecost': usage.get('linecost')
                }
                comprehensive_data['reservations'].append(reservation_data)
        
        # 5. VENDOR AND ALTERNATE INFORMATION
        invvendor = record.get('invvendor', [])
        if invvendor:
            print(f"  🏢 Found {len(invvendor)} vendor records")
            for vendor in invvendor:
                alternate_data = {
                    'vendor': vendor.get('vendor'),
                    'manufacturer': vendor.get('manufacturer'),
                    'modelnum': vendor.get('modelnum'),
                    'catalogcode': vendor.get('catalogcode'),
                    'orderunit': vendor.get('orderunit'),
                    'conversion': vendor.get('conversion')
                }
                comprehensive_data['alternates'].append(alternate_data)
    
    # Calculate summary
    comprehensive_data['summary'] = {
        'total_locations': len(comprehensive_data['locations']),
        'total_lots': len(comprehensive_data['lots']),
        'total_purchase_records': len(comprehensive_data['purchasing']),
        'total_reservation_records': len(comprehensive_data['reservations']),
        'total_vendor_records': len(comprehensive_data['alternates']),
        'total_available': sum(float(loc.get('available_balance', 0) or 0) for loc in comprehensive_data['locations']),
        'total_current': sum(float(loc.get('current_balance', 0) or 0) for loc in comprehensive_data['locations']),
        'total_reserved': sum(float(loc.get('reserved_qty', 0) or 0) for loc in comprehensive_data['locations'])
    }
    
    print(f"\n📊 SUMMARY:")
    print(f"  • Locations: {comprehensive_data['summary']['total_locations']}")
    print(f"  • Lots/Bins: {comprehensive_data['summary']['total_lots']}")
    print(f"  • Purchase Records: {comprehensive_data['summary']['total_purchase_records']}")
    print(f"  • Reservation Records: {comprehensive_data['summary']['total_reservation_records']}")
    print(f"  • Vendor/Alternate Records: {comprehensive_data['summary']['total_vendor_records']}")
    
    return comprehensive_data

def save_raw_data(records, itemnum, siteid):
    """Save raw data for reference."""
    filename = f"comprehensive_availability_data_{itemnum}_{siteid}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(filename, 'w') as f:
        json.dump(records, f, indent=2, default=str)
    
    print(f"\n💾 Raw data saved to: {filename}")

if __name__ == "__main__":
    comprehensive_data = discover_comprehensive_availability()
    
    if comprehensive_data:
        print("\n🎉 Comprehensive availability data discovery completed!")
        print("📋 This data structure will be used to enhance the availability feature")
    else:
        print("\n⚠️ Failed to discover comprehensive availability data")
