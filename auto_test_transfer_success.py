#!/usr/bin/env python3
import subprocess
import json
import time
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoT<PERSON>Mana<PERSON>

def run_curl_test(payload, description):
    """Run curl test and return result"""
    print(f"\n🔍 Testing: {description}")
    print("=" * 60)
    
    cmd = [
        'curl', '-X', 'POST',
        'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange',
        '-H', 'Accept: application/json',
        '-H', 'Content-Type: application/json', 
        '-H', 'x-method-override: BULK',
        '--cookie-jar', 'cookies.txt',
        '--cookie', 'cookies.txt',
        '-d', json.dumps(payload),
        '-w', '\\nHTTP_STATUS:%{http_code}\\nTIME:%{time_total}\\n',
        '-s'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        output = result.stdout
        
        # Extract HTTP status
        status_code = None
        if 'HTTP_STATUS:' in output:
            status_line = [line for line in output.split('\n') if 'HTTP_STATUS:' in line][0]
            status_code = int(status_line.split(':')[1])
        
        print(f"HTTP Status: {status_code}")
        
        # Parse JSON response
        json_part = output.split('HTTP_STATUS:')[0].strip()
        if json_part:
            try:
                response_data = json.loads(json_part)
                print(f"Response: {json.dumps(response_data, indent=2)}")
                
                # Check for success
                if isinstance(response_data, list) and len(response_data) > 0:
                    first_item = response_data[0]
                    if '_responsemeta' in first_item:
                        meta_status = first_item['_responsemeta'].get('status')
                        if meta_status == '204':
                            print("🎉 SUCCESS! Found 204 status!")
                            return True, response_data
                        else:
                            print(f"❌ Meta status: {meta_status}")
                    
                    if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                        error = first_item['_responsedata']['Error']
                        print(f"❌ Error: {error.get('reasonCode')} - {error.get('message')}")
                
            except json.JSONDecodeError:
                print(f"Response (non-JSON): {json_part}")
        
        return False, output
        
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False, str(e)

def main():
    """Continuous testing until success"""
    print("🚀 CONTINUOUS TESTING UNTIL SUCCESS")
    print("=" * 50)
    
    # Test variations
    test_cases = [
        {
            "description": "1. Original with IKWAJ fix",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "Replace",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": "2024-12-19T10:30:00+00:00",
                            "issueunit": "RO",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP",
                            "fromlotnum": "TEST",
                            "tolotnum": "TEST",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        },
        {
            "description": "2. AddChange for matrectrans",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": "2024-12-19T10:30:00+00:00",
                            "issueunit": "RO"
                        }
                    ]
                }
            ]
        },
        {
            "description": "3. Minimal required fields",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058"
                        }
                    ]
                }
            ]
        },
        {
            "description": "4. Different bin numbers",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "58-A-A01-1"
                        }
                    ]
                }
            ]
        },
        {
            "description": "5. Without lot numbers",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": "2024-12-19T10:30:00+00:00",
                            "issueunit": "RO",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        }
    ]
    
    success_found = False
    successful_payload = None
    
    for i, test_case in enumerate(test_cases):
        if success_found:
            break
            
        success, result = run_curl_test(test_case["payload"], test_case["description"])
        
        if success:
            print(f"\n🎉 SUCCESS FOUND! Test case {i+1}")
            successful_payload = test_case["payload"]
            success_found = True
            break
        else:
            print(f"❌ Test {i+1} failed, trying next...")
            time.sleep(1)  # Brief pause between tests
    
    if success_found:
        print(f"\n🎯 SUCCESSFUL PAYLOAD:")
        print("=" * 50)
        print(json.dumps(successful_payload, indent=2))
        
        # Save successful payload
        with open('successful_transfer_payload.json', 'w') as f:
            json.dump(successful_payload, f, indent=2)
        print(f"\n💾 Successful payload saved to: successful_transfer_payload.json")
        
    else:
        print(f"\n❌ NO SUCCESS FOUND")
        print("All test variations failed. Need to investigate further.")

if __name__ == "__main__":
    main()