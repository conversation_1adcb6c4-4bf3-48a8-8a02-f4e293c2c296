
import sys
import os
import json
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def test_cross_site_payload(payload_type="destination_site"):
    """Test cross-site transfer with different payload structures."""
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        return {"error": "Not authenticated", "success": False}
    
    api_endpoint = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    # Different payload structures based on type
    if payload_type == "destination_site":
        # Test Case 1: Destination Site as Top-Level
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "itemsetid": "ITEMSET",
                "siteid": "IKWAJ",
                "location": "KWAJ-1058",
                "issueunit": "RO",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058",
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": "RO",
                        "frombinnum": "DEFAULT",
                        "tobinnum": "DEFAULT",
                        "fromlotnum": "DEFAULT",
                        "tolotnum": "DEFAULT",
                        "fromconditioncode": "A1",
                        "toconditioncode": "A1"
                    }
                ]
            }
        ]
    elif payload_type == "source_site":
        # Test Case 2: Source Site as Top-Level
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "itemsetid": "ITEMSET",
                "siteid": "LCVKWT",
                "location": "RIP001",
                "issueunit": "RO",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058",
                        "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                        "issueunit": "RO",
                        "frombinnum": "DEFAULT",
                        "tobinnum": "DEFAULT",
                        "fromlotnum": "DEFAULT",
                        "tolotnum": "DEFAULT",
                        "fromconditioncode": "A1",
                        "toconditioncode": "A1"
                    }
                ]
            }
        ]
    elif payload_type == "minimal_destination":
        # Test Case 3: Minimal Destination Context
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "siteid": "IKWAJ",
                "location": "KWAJ-1058",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058"
                    }
                ]
            }
        ]
    elif payload_type == "dual_record":
        # Test Case 4: Dual Record Approach
        payload = [
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "siteid": "LCVKWT",
                "location": "RIP001",
                "issueunit": "RO",
                "matrectrans": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "issuetype": "TRANSFER",
                        "quantity": 1.0,
                        "fromsiteid": "LCVKWT",
                        "tositeid": "IKWAJ",
                        "fromstoreloc": "RIP001",
                        "tostoreloc": "KWAJ-1058",
                        "issueunit": "RO"
                    }
                ]
            },
            {
                "_action": "AddChange",
                "itemnum": "5975-60-V00-0529",
                "siteid": "IKWAJ",
                "location": "KWAJ-1058",
                "issueunit": "RO"
            }
        ]
    else:
        return {"error": f"Unknown payload type: {payload_type}", "success": False}
    
    try:
        # Submit to Maximo API
        response = token_manager.session.post(
            api_endpoint,
            json=payload,
            headers={
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-method-override": "BULK"
            },
            timeout=(5.0, 30)
        )
        
        result = {
            "payload_type": payload_type,
            "http_status": response.status_code,
            "success": False,
            "response_data": None,
            "error": None
        }
        
        if response.text:
            try:
                data = response.json()
                result["response_data"] = data
                
                # Check for success
                if response.status_code == 200:
                    if isinstance(data, list) and len(data) > 0:
                        first_item = data[0]
                        if first_item.get('_responsemeta', {}).get('status') == '204':
                            result["success"] = True
                            return result
                        elif '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                            error = first_item['_responsedata']['Error']
                            result["error"] = f"{error.get('reasonCode')} - {error.get('message')}"
                
            except json.JSONDecodeError:
                result["error"] = "Non-JSON response"
                result["response_data"] = response.text
        
        return result
        
    except Exception as e:
        return {
            "payload_type": payload_type,
            "success": False,
            "error": str(e),
            "http_status": None,
            "response_data": None
        }

if __name__ == "__main__":
    import sys
    payload_type = sys.argv[1] if len(sys.argv) > 1 else "destination_site"
    result = test_cross_site_payload(payload_type)
    print(json.dumps(result, indent=2))
