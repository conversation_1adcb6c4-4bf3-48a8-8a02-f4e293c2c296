#!/usr/bin/env python3
"""
Test script for inventory adjustment functionality
Tests the new Physical Count and Current Balance adjustment buttons and forms
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_SITE = "LCVKWT"  # Default test site
TEST_ITEM = "5975-60-V00-0529"  # Example item from requirements

def test_inventory_search():
    """Test that we can search for inventory items"""
    print("🔍 Testing inventory search...")
    
    url = f"{BASE_URL}/api/inventory/management/search"
    params = {
        'search_term': TEST_ITEM,
        'site_id': TEST_SITE,
        'limit': 10,
        'page': 0
    }
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                items = data.get('items', [])
                print(f"✅ Found {len(items)} items")
                
                # Check if any items have balance records
                for item in items:
                    if item.get('invbalances_records'):
                        print(f"✅ Item {item['itemnum']} has {len(item['invbalances_records'])} balance records")
                        return item
                
                print("⚠️ No items with balance records found")
                return None
            else:
                print(f"❌ Search failed: {data.get('error')}")
                return None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_physical_count_adjustment():
    """Test physical count adjustment API with new payload structure"""
    print("\n🔍 Testing physical count adjustment...")

    # New specific payload structure for physical count
    adjustment_data = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": "RIP001",
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "physcnt": 25,
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "memo": "CYCLE_COUNT"
                }
            ]
        }
    ]

    url = f"{BASE_URL}/api/inventory/physical-count-adjustment"
    
    try:
        response = requests.post(url, json=adjustment_data)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Physical count adjustment test passed")
                return True
            else:
                print(f"❌ Adjustment failed: {data.get('error')}")
                return False
        elif response.status_code == 401:
            print("⚠️ Authentication required - this is expected for API test")
            return True  # Expected for unauthenticated test
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_current_balance_adjustment():
    """Test current balance adjustment API with new payload structure"""
    print("\n🔍 Testing current balance adjustment...")

    # New specific payload structure for current balance
    adjustment_data = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": "RIP001",
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 25,  # New balance
                    "conditioncode": "A1",
                    "memo": "ADJUSTMENT"
                }
            ]
        }
    ]

    url = f"{BASE_URL}/api/inventory/current-balance-adjustment"
    
    try:
        response = requests.post(url, json=adjustment_data)
        print(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Current balance adjustment test passed")
                return True
            else:
                print(f"❌ Adjustment failed: {data.get('error')}")
                return False
        elif response.status_code == 401:
            print("⚠️ Authentication required - this is expected for API test")
            return True  # Expected for unauthenticated test
        elif response.status_code == 400:
            # Check the error details
            try:
                data = response.json()
                print(f"❌ Bad Request: {data.get('error', 'Unknown error')}")
            except:
                print(f"❌ Bad Request: {response.text}")
            return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_inventory_page_loads():
    """Test that the inventory management page loads"""
    print("\n🔍 Testing inventory management page...")
    
    url = f"{BASE_URL}/inventory-management"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            content = response.text
            
            # Check for key elements
            checks = [
                ("Physical Count Modal", "physicalCountModal" in content),
                ("Current Balance Modal", "currentBalanceModal" in content),
                ("Physical Count Button", "openPhysicalCountModal" in content),
                ("Current Balance Button", "openCurrentBalanceModal" in content),
                ("JavaScript Functions", "submitPhysicalCountAdjustment" in content),
            ]
            
            all_passed = True
            for check_name, passed in checks:
                if passed:
                    print(f"✅ {check_name} found")
                else:
                    print(f"❌ {check_name} missing")
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ Page load failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting inventory adjustment tests...\n")
    
    tests = [
        ("Inventory Page Load", test_inventory_page_loads),
        ("Inventory Search", test_inventory_search),
        ("Physical Count Adjustment API", test_physical_count_adjustment),
        ("Current Balance Adjustment API", test_current_balance_adjustment),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed - check implementation")

if __name__ == "__main__":
    main()
