#!/usr/bin/env python3
"""
Test script to verify frontend negative labor functionality
Tests both the missing parameters fix and the subtract button visibility fix
"""

import requests
import json
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def test_api_with_proper_parameters():
    """Test that the API works with proper parameters"""
    print("🧪 Testing API with proper parameters...")
    
    # Test data with all required parameters
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -0.25,  # Small negative amount
        "siteid": "LCVKWT",
        "taskid": 10,
        "parent_wonum": "2219753",
        "craft": "MATCTRLSPCSR"
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Response: {json.dumps(response_data, indent=2)}")
            
            if response_data.get('success'):
                print("🎉 SUCCESS: API accepts proper parameters!")
                return True
            else:
                print(f"❌ FAILED: {response_data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_api_missing_parameters():
    """Test that the API properly rejects missing parameters"""
    print("\n🧪 Testing API with missing parameters...")
    
    # Test data missing required parameters
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -0.25,
        # Missing siteid, taskid, parent_wonum
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 400:
            response_data = response.json()
            print(f"📄 Response: {json.dumps(response_data, indent=2)}")
            
            if not response_data.get('success') and 'Missing required parameters' in response_data.get('error', ''):
                print("✅ SUCCESS: API correctly rejects missing parameters!")
                return True
            else:
                print("❌ FAILED: Should have detected missing parameters")
                return False
        else:
            print(f"❌ FAILED: Expected 400 status, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def setup_chrome_driver():
    """Setup Chrome driver for testing"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in background
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"❌ Could not setup Chrome driver: {e}")
        print("ℹ️  Skipping browser tests...")
        return None

def test_subtract_button_visibility():
    """Test that subtract buttons are only shown for positive labor records"""
    print("\n🧪 Testing subtract button visibility in browser...")
    
    driver = setup_chrome_driver()
    if not driver:
        return True  # Skip test if no browser available
    
    try:
        # Navigate to the work order page
        driver.get("http://127.0.0.1:5010/workorder/2219753")
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "task-card-enhanced"))
        )
        
        # Find and click the "Load Labor" button for the first task
        load_labor_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, ".load-labor-btn"))
        )
        load_labor_btn.click()
        
        # Wait for labor data to load
        time.sleep(3)
        
        # Check labor records and subtract buttons
        labor_rows = driver.find_elements(By.CSS_SELECTOR, "tr[data-labor-id]")
        
        positive_records_with_buttons = 0
        negative_records_with_buttons = 0
        negative_records_with_info = 0
        
        for row in labor_rows:
            try:
                # Get the hours value
                hours_cell = row.find_element(By.CLASS_NAME, "labor-hours")
                hours_text = hours_cell.text.strip()
                hours_value = float(hours_text.replace('h', ''))
                
                # Check for subtract button
                try:
                    subtract_btn = row.find_element(By.CSS_SELECTOR, ".delete-labor-btn")
                    if hours_value > 0:
                        positive_records_with_buttons += 1
                        print(f"✅ Positive record ({hours_value}h) has subtract button")
                    else:
                        negative_records_with_buttons += 1
                        print(f"❌ Negative record ({hours_value}h) has subtract button - SHOULD NOT!")
                except NoSuchElementException:
                    # No subtract button found
                    if hours_value <= 0:
                        # Check for "Negative Entry" info instead
                        try:
                            info_span = row.find_element(By.XPATH, ".//span[contains(text(), 'Negative Entry')]")
                            negative_records_with_info += 1
                            print(f"✅ Negative record ({hours_value}h) shows 'Negative Entry' info")
                        except NoSuchElementException:
                            print(f"❌ Negative record ({hours_value}h) has no button or info")
                    else:
                        print(f"❌ Positive record ({hours_value}h) missing subtract button")
                        
            except Exception as e:
                print(f"⚠️  Could not process labor row: {e}")
        
        print(f"\n📊 Summary:")
        print(f"   Positive records with subtract buttons: {positive_records_with_buttons}")
        print(f"   Negative records with subtract buttons: {negative_records_with_buttons}")
        print(f"   Negative records with info text: {negative_records_with_info}")
        
        # Test passes if:
        # 1. At least one positive record has a subtract button
        # 2. No negative records have subtract buttons
        # 3. At least one negative record shows info text
        success = (
            positive_records_with_buttons > 0 and
            negative_records_with_buttons == 0 and
            negative_records_with_info > 0
        )
        
        if success:
            print("✅ SUCCESS: Subtract buttons are correctly shown/hidden!")
        else:
            print("❌ FAILED: Subtract button visibility is incorrect")
        
        return success
        
    except TimeoutException:
        print("❌ FAILED: Page did not load in time")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False
    finally:
        driver.quit()

def main():
    """Run all tests"""
    print("🚀 Starting Frontend Negative Labor Tests")
    print("="*60)
    
    # Test 1: API with proper parameters
    test1_success = test_api_with_proper_parameters()
    
    # Test 2: API with missing parameters
    test2_success = test_api_missing_parameters()
    
    # Test 3: Subtract button visibility (browser test)
    test3_success = test_subtract_button_visibility()
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS:")
    print(f"✅ API with proper parameters: {'PASSED' if test1_success else 'FAILED'}")
    print(f"✅ API missing parameters validation: {'PASSED' if test2_success else 'FAILED'}")
    print(f"✅ Subtract button visibility: {'PASSED' if test3_success else 'FAILED'}")
    
    all_passed = test1_success and test2_success and test3_success
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Frontend negative labor functionality is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("❌ There may be issues with the frontend functionality")
        sys.exit(1)

if __name__ == "__main__":
    main()
