#!/usr/bin/env python3
"""
Test mxapiinventory endpoint with curl using proper authentication.
This script extracts cookies from the current session and tests the exact payload structure.
"""
import sys
import os
import subprocess
import tempfile
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def extract_cookies_to_file(token_manager):
    """Extract cookies from token manager session and save to a file for curl."""
    if not hasattr(token_manager, 'session') or not token_manager.session.cookies:
        print("❌ No session or cookies found in token manager")
        return None
    
    # Create a temporary file for cookies
    cookie_fd, cookie_file = tempfile.mkstemp(suffix='.txt', prefix='maximo_cookies_')
    
    try:
        with os.fdopen(cookie_fd, 'w') as f:
            # Write cookies in Netscape format for curl
            f.write("# Netscape HTTP Cookie File\n")
            f.write("# This is a generated file! Do not edit.\n\n")
            
            for cookie in token_manager.session.cookies:
                # Format: domain, domain_specified, path, secure, expires, name, value
                domain = cookie.domain if cookie.domain else ".maximo.com"
                domain_specified = "TRUE" if cookie.domain_specified else "FALSE"
                path = cookie.path if cookie.path else "/"
                secure = "TRUE" if cookie.secure else "FALSE"
                expires = str(int(cookie.expires)) if cookie.expires else "0"
                
                f.write(f"{domain}\t{domain_specified}\t{path}\t{secure}\t{expires}\t{cookie.name}\t{cookie.value}\n")
        
        print(f"✅ Cookies extracted to: {cookie_file}")
        print(f"📊 Total cookies: {len(token_manager.session.cookies)}")
        
        # Show cookie names for debugging
        cookie_names = [cookie.name for cookie in token_manager.session.cookies]
        print(f"🍪 Cookie names: {', '.join(cookie_names)}")
        
        return cookie_file
        
    except Exception as e:
        print(f"❌ Error writing cookies: {e}")
        os.unlink(cookie_file)
        return None

def test_mxapiinventory_with_curl(cookie_file):
    """Test the mxapiinventory endpoint with curl using the exact payload structure."""
    
    print(f"\n🔧 Testing mxapiinventory endpoint with curl")
    print("=" * 60)
    
    # The exact payload structure from your example
    payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 0,
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "lotnum": "",
                    "reconciled": True,
                    "memo": "",
                    "controlacc": "",
                    "shrinkageacc": ""
                }
            ]
        }
    ]
    
    # Convert to JSON string
    payload_json = json.dumps(payload, separators=(',', ':'))
    
    print(f"📋 Payload:")
    print(json.dumps(payload, indent=2))
    print(f"\n🌐 URL: {BASE_URL}/api/os/mxapiinventory")
    print(f"🍪 Cookie File: {cookie_file}")
    print("")
    
    # Build curl command
    url = f"{BASE_URL}/api/os/mxapiinventory"
    
    cmd = [
        'curl', '-X', 'POST',
        url,
        '-H', 'Accept: application/json',
        '-H', 'Content-Type: application/json',
        '-d', payload_json,
        '-b', cookie_file,
        '-v',
        '--max-time', '30'
    ]
    
    print("🚀 Running curl command...")
    print(f"Command: {' '.join(cmd[:8])} [payload] {' '.join(cmd[9:])}")
    print("")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print(f"📊 Exit code: {result.returncode}")
        print(f"📤 STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print(f"📥 STDERR:")
            print(result.stderr)
        
        # Try to parse the response as JSON
        if result.stdout:
            try:
                response_data = json.loads(result.stdout)
                print(f"\n✅ Response parsed as JSON:")
                print(json.dumps(response_data, indent=2))
                
                # Check for success indicators
                if isinstance(response_data, list) and len(response_data) > 0:
                    first_item = response_data[0]
                    if 'inventoryid' in first_item or 'itemnum' in first_item:
                        print(f"\n🎉 SUCCESS: Inventory adjustment appears to have been processed!")
                        print(f"📋 Item: {first_item.get('itemnum', 'Unknown')}")
                        if 'inventoryid' in first_item:
                            print(f"📋 Inventory ID: {first_item['inventoryid']}")
                        return True
                elif 'oslc:Error' in response_data:
                    error_info = response_data['oslc:Error']
                    print(f"\n❌ MAXIMO ERROR:")
                    print(f"📋 Status: {error_info.get('oslc:statusCode', 'Unknown')}")
                    print(f"📋 Reason: {error_info.get('spi:reasonCode', 'Unknown')}")
                    print(f"📋 Message: {error_info.get('oslc:message', 'Unknown')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"\n⚠️ Response is not valid JSON: {e}")
                print("Raw response:")
                print(result.stdout[:500])
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 30 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running curl: {e}")
        return False

def test_simple_get_first(cookie_file):
    """Test a simple GET request first to verify authentication works."""
    
    print(f"\n🔧 Testing simple GET request first")
    print("=" * 40)
    
    # Test with a simple inventory query
    url = f"{BASE_URL}/api/os/mxapiinventory"
    params = "lean=1&oslc.pageSize=1&oslc.select=itemnum,siteid,location"
    
    cmd = [
        'curl', '-X', 'GET',
        f"{url}?{params}",
        '-H', 'Accept: application/json',
        '-b', cookie_file,
        '-v',
        '--max-time', '15'
    ]
    
    print(f"🌐 URL: {url}?{params}")
    print("🚀 Running GET test...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        print(f"📊 Exit code: {result.returncode}")
        
        if result.stdout:
            try:
                response_data = json.loads(result.stdout)
                if 'member' in response_data:
                    print(f"✅ GET request successful! Found {len(response_data['member'])} items")
                    return True
                elif 'Error' in response_data:
                    print(f"❌ API Error Response:")
                    print(json.dumps(response_data, indent=2))
                else:
                    print(f"⚠️ Unexpected response structure: {list(response_data.keys())}")
                    print(f"📋 Response: {json.dumps(response_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"⚠️ Response not JSON: {result.stdout}")

        if result.stderr:
            print(f"📥 STDERR: {result.stderr}")
            
        return False
        
    except Exception as e:
        print(f"❌ Error in GET test: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Starting mxapiinventory curl test")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Check if logged in
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo. Please login first through the web app.")
        print("💡 Go to http://127.0.0.1:5010 and login, then run this script again.")
        return False
    
    print(f"✅ Logged in to Maximo as: {getattr(token_manager, 'username', 'Unknown')}")
    
    # Extract cookies
    cookie_file = extract_cookies_to_file(token_manager)
    
    if not cookie_file:
        print("❌ Failed to extract cookies")
        return False
    
    try:
        # Test simple GET first
        if test_simple_get_first(cookie_file):
            print("\n" + "=" * 60)
            # If GET works, test the POST
            success = test_mxapiinventory_with_curl(cookie_file)
            
            if success:
                print("\n🎉 CURL TEST COMPLETED SUCCESSFULLY!")
                print("✅ The mxapiinventory endpoint is working with proper authentication")
                print("✅ Your payload structure is correct")
                print("✅ Ready to integrate into the QR scanner app")
            else:
                print("\n❌ CURL TEST FAILED")
                print("🔍 Check the error messages above for details")
                
            return success
        else:
            print("\n❌ GET test failed - authentication issue")
            return False
            
    finally:
        # Clean up cookie file
        try:
            os.unlink(cookie_file)
            print(f"\n🧹 Cleaned up cookie file: {cookie_file}")
        except:
            pass

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
