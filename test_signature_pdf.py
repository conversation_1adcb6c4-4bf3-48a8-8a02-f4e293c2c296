#!/usr/bin/env python3
"""
Test script to verify PDF generation and attachment functionality
"""
import requests
import json
import base64
from datetime import datetime

BASE_URL = "http://localhost:5010"

def test_pdf_generation():
    """Test PDF generation functionality"""
    print("🧪 Testing PDF Generation and Attachment")
    print("=" * 60)
    
    # Test data for signature
    signature_data = {
        "wonum": "2021-1994269",
        "status": "COMP",
        "customerName": "Test User",
        "comments": "Test signature for PDF generation",
        "dateTime": datetime.now().isoformat(),
        "signatureData": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    }
    
    print(f"📝 Testing with signature data: {json.dumps({k: v if k != 'signatureData' else 'base64_image_data...' for k, v in signature_data.items()}, indent=2)}")
    
    try:
        # Test signature submission
        response = requests.post(
            f"{BASE_URL}/api/signature/submit",
            json=signature_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\n📄 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_attachment_api():
    """Test attachment API directly"""
    print("\n🔗 Testing Attachment API")
    print("=" * 40)
    
    # Test getting attachments for a work order
    test_wonum = "2021-1744762"
    
    try:
        response = requests.get(f"{BASE_URL}/api/workorder/{test_wonum}/attachments")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Attachments API working")
            print(f"📎 Found {len(result.get('attachments', []))} attachments")
            
            # Show first few attachments
            attachments = result.get('attachments', [])
            for i, att in enumerate(attachments[:3]):
                print(f"📎 Attachment {i+1}: {att.get('fileName', 'Unknown')} ({att.get('docType', 'Unknown')})")
            
            return True
        else:
            print(f"❌ Attachments API failed: {response.status_code}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_task_attachments():
    """Test task-level attachment functionality"""
    print("\n📋 Testing Task Attachments")
    print("=" * 40)
    
    # Test getting attachments for a task
    test_task_wonum = "2021-1994269"
    
    try:
        response = requests.get(f"{BASE_URL}/api/workorder/{test_task_wonum}/attachments")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Task attachments API working")
            print(f"📎 Found {len(result.get('attachments', []))} attachments for task {test_task_wonum}")
            
            # Show attachments
            attachments = result.get('attachments', [])
            for i, att in enumerate(attachments):
                print(f"📎 Task Attachment {i+1}: {att.get('fileName', 'Unknown')} ({att.get('docType', 'Unknown')})")
            
            return True
        else:
            print(f"❌ Task attachments API failed: {response.status_code}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_signature_config():
    """Test signature configuration"""
    print("\n⚙️ Testing Signature Configuration")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/admin/signature-config")
        
        if response.status_code == 200:
            result = response.json()
            config = result.get('config', {})
            print(f"✅ Signature config loaded")
            print(f"📝 Enabled: {config.get('enabled')}")
            print(f"📝 Statuses: {config.get('statuses')}")
            print(f"📝 Scope: {config.get('scope')}")
            
            # Test signature requirement check
            test_data = {"status": "COMP", "wo_type": "task"}
            response = requests.post(
                f"{BASE_URL}/api/admin/signature-required",
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                req_result = response.json()
                print(f"✅ Signature requirement check: {req_result.get('signature_required')}")
                return True
            else:
                print(f"❌ Signature requirement check failed: {response.status_code}")
                return False
                
        else:
            print(f"❌ Signature config failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Signature System Tests")
    print("=" * 60)
    
    # Test sequence
    tests = [
        ("Signature Configuration", test_signature_config),
        ("Attachment API", test_attachment_api),
        ("Task Attachments", test_task_attachments),
        ("PDF Generation", test_pdf_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
    
    print(f"\n📊 Test Summary")
    print("=" * 30)
    for test_name, result in results:
        print(f"{'✅' if result else '❌'} {test_name}")
    
    all_passed = all(result for _, result in results)
    print(f"\n🎯 Overall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
