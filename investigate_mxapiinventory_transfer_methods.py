#!/usr/bin/env python3
"""
MXAPIINVENTORY Transfer Methods Investigation
============================================

Focused investigation of transfer-related web service methods in the MXAPIINVENTORY endpoint.
Uses OSLC token session authentication exclusively via MaximoTokenManager.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

class MXAPIInventoryTransferInvestigator:
    """Investigates transfer-related methods in MXAPIINVENTORY endpoint."""
    
    def __init__(self, base_url):
        """Initialize the investigator with base URL."""
        self.base_url = base_url
        self.token_manager = None
        self.session = None
        self.api_endpoint = f"{base_url}/api/os/mxapiinventory"
        self.oslc_endpoint = f"{base_url}/oslc/os/mxapiinventory"
        
        # Transfer methods to investigate
        self.transfer_methods = [
            'transfercurrentitem',
            'transfercuritem', 
            'issuecurrentitem',
            'receivecurrentitem',
            'transferitem',
            'transferinventory',
            'transferstock',
            'transfermaterial',
            'transferasset',
            'transferbalance',
            'transferquantity',
            'movetransfer',
            'createtransfer',
            'processtransfer',
            'completetransfer',
            'validatetransfer'
        ]
        
        self.results = {
            'investigation_date': datetime.now().isoformat(),
            'endpoint': self.api_endpoint,
            'oslc_endpoint': self.oslc_endpoint,
            'authentication': 'OSLC Token Session',
            'discovered_methods': [],
            'failed_methods': [],
            'endpoint_info': {},
            'test_results': []
        }
    
    def initialize_authentication(self):
        """Initialize OSLC token session authentication."""
        print("🔐 Initializing OSLC Token Session Authentication")
        print("=" * 60)

        try:
            self.token_manager = MaximoTokenManager(self.base_url)

            # Check if already logged in
            if self.token_manager.is_logged_in():
                self.session = self.token_manager.session
                print("✅ OSLC token session authenticated successfully")

                # Verify session with whoami endpoint
                whoami_url = f"{self.base_url}/oslc/whoami"
                response = self.session.get(whoami_url, timeout=(3.05, 10))

                if response.status_code == 200:
                    print(f"✅ Session verified via whoami endpoint")
                    return True
                else:
                    print(f"⚠️  Session verification returned status: {response.status_code}")

            # Try to load existing cookies from file
            print("🔄 Attempting to use existing session cookies...")
            cookie_file = "cookies.txt"
            if os.path.exists(cookie_file):
                # Create a new session and load cookies
                self.session = requests.Session()

                # Parse Netscape cookie file format
                with open(cookie_file, 'r') as f:
                    for line in f:
                        if line.startswith('#') or not line.strip():
                            continue
                        parts = line.strip().split('\t')
                        if len(parts) >= 7:
                            domain, _, path, secure, expires, name, value = parts[:7]
                            # Add cookie to session
                            self.session.cookies.set(
                                name=name,
                                value=value,
                                domain=domain.lstrip('#HttpOnly_'),
                                path=path
                            )

                # Test the session
                whoami_url = f"{self.base_url}/oslc/whoami"
                response = self.session.get(whoami_url, timeout=(3.05, 10))

                if response.status_code == 200:
                    print("✅ Session authenticated using existing cookies")
                    return True
                else:
                    print(f"❌ Cookie-based session failed: {response.status_code}")

            print("❌ OSLC token session not available")
            print("💡 Please ensure you are logged in through the web interface")
            return False

        except Exception as e:
            print(f"❌ Authentication initialization failed: {str(e)}")
            return False
    
    def discover_endpoint_capabilities(self):
        """Discover basic endpoint capabilities and structure."""
        print("\n🔍 Discovering Endpoint Capabilities")
        print("=" * 60)
        
        # Test basic endpoint access
        test_params = {
            "oslc.select": "*",
            "oslc.pageSize": "1",
            "lean": "1"
        }
        
        try:
            # Test OSLC endpoint
            print(f"🔗 Testing OSLC endpoint: {self.oslc_endpoint}")
            response = self.session.get(
                self.oslc_endpoint,
                params=test_params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"}
            )
            
            print(f"📊 OSLC Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'member' in data and len(data['member']) > 0:
                        sample_record = data['member'][0]
                        self.results['endpoint_info']['sample_fields'] = list(sample_record.keys())
                        print(f"✅ OSLC endpoint accessible - {len(sample_record.keys())} fields available")
                    else:
                        print("⚠️  OSLC endpoint accessible but no records returned")
                except json.JSONDecodeError:
                    print("⚠️  OSLC endpoint accessible but response not JSON")
            else:
                print(f"❌ OSLC endpoint not accessible: {response.status_code}")
            
            # Test API endpoint
            print(f"🔗 Testing API endpoint: {self.api_endpoint}")
            response = self.session.get(
                self.api_endpoint,
                params=test_params,
                timeout=(3.05, 30),
                headers={"Accept": "application/json"}
            )
            
            print(f"📊 API Response Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ API endpoint accessible")
                self.results['endpoint_info']['api_accessible'] = True
            else:
                print(f"❌ API endpoint not accessible: {response.status_code}")
                self.results['endpoint_info']['api_accessible'] = False
                
        except Exception as e:
            print(f"❌ Endpoint discovery failed: {str(e)}")
            self.results['endpoint_info']['error'] = str(e)
    
    def test_transfer_method(self, method_name):
        """Test a specific transfer method."""
        print(f"\n🧪 Testing Method: {method_name}")
        print("-" * 40)
        
        test_result = {
            'method': method_name,
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'http_status': None,
            'response_data': None,
            'error': None,
            'url_tested': None
        }
        
        # Test different URL patterns for the method
        url_patterns = [
            f"{self.api_endpoint}/{method_name}",
            f"{self.oslc_endpoint}/{method_name}",
            f"{self.api_endpoint}?action={method_name}",
            f"{self.oslc_endpoint}?action={method_name}",
            f"{self.api_endpoint}?wsmethod={method_name}",
            f"{self.oslc_endpoint}?wsmethod={method_name}"
        ]
        
        for url in url_patterns:
            try:
                test_result['url_tested'] = url
                print(f"  🔗 Testing: {url}")
                
                # Try GET first
                response = self.session.get(
                    url,
                    timeout=(3.05, 15),
                    headers={"Accept": "application/json"}
                )
                
                test_result['http_status'] = response.status_code
                print(f"  📊 GET Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        test_result['response_data'] = data
                        test_result['status'] = 'accessible_get'
                        print(f"  ✅ Method accessible via GET")
                        self.results['discovered_methods'].append(test_result.copy())
                        return test_result
                    except json.JSONDecodeError:
                        print(f"  ⚠️  Non-JSON response")
                        
                elif response.status_code == 405:  # Method not allowed
                    print(f"  🔄 GET not allowed, trying POST")
                    
                    # Try POST with minimal payload
                    post_payload = {"action": method_name}
                    post_response = self.session.post(
                        url,
                        json=post_payload,
                        timeout=(3.05, 15),
                        headers={"Accept": "application/json", "Content-Type": "application/json"}
                    )
                    
                    print(f"  📊 POST Status: {post_response.status_code}")
                    test_result['http_status'] = post_response.status_code
                    
                    if post_response.status_code in [200, 201, 204]:
                        try:
                            data = post_response.json() if post_response.content else {}
                            test_result['response_data'] = data
                            test_result['status'] = 'accessible_post'
                            print(f"  ✅ Method accessible via POST")
                            self.results['discovered_methods'].append(test_result.copy())
                            return test_result
                        except json.JSONDecodeError:
                            test_result['status'] = 'accessible_post_no_json'
                            print(f"  ✅ Method accessible via POST (no JSON response)")
                            self.results['discovered_methods'].append(test_result.copy())
                            return test_result
                            
                elif response.status_code == 404:
                    print(f"  ❌ Method not found")
                    continue
                else:
                    print(f"  ⚠️  Unexpected status: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"  ⏱️  Timeout")
                test_result['error'] = 'timeout'
            except Exception as e:
                print(f"  ❌ Error: {str(e)}")
                test_result['error'] = str(e)
        
        # If we get here, method was not found
        test_result['status'] = 'not_found'
        self.results['failed_methods'].append(test_result)
        return test_result
    
    def investigate_all_methods(self):
        """Investigate all transfer methods."""
        print("\n🔬 Investigating Transfer Methods")
        print("=" * 60)
        
        for i, method in enumerate(self.transfer_methods, 1):
            print(f"\n[{i}/{len(self.transfer_methods)}] Investigating: {method}")
            result = self.test_transfer_method(method)
            self.results['test_results'].append(result)
            
            # Brief pause between tests
            time.sleep(0.5)
    
    def generate_report(self):
        """Generate comprehensive investigation report."""
        print("\n📋 INVESTIGATION REPORT")
        print("=" * 60)
        
        print(f"🕐 Investigation Date: {self.results['investigation_date']}")
        print(f"🔗 Endpoint: {self.results['endpoint']}")
        print(f"🔐 Authentication: {self.results['authentication']}")
        
        discovered = self.results['discovered_methods']
        failed = self.results['failed_methods']
        
        print(f"\n📊 SUMMARY:")
        print(f"  ✅ Discovered Methods: {len(discovered)}")
        print(f"  ❌ Failed Methods: {len(failed)}")
        print(f"  📝 Total Tested: {len(self.transfer_methods)}")
        
        if discovered:
            print(f"\n🎯 DISCOVERED TRANSFER METHODS:")
            for method in discovered:
                print(f"  ✅ {method['method']}")
                print(f"     Status: {method['status']}")
                print(f"     HTTP: {method['http_status']}")
                print(f"     URL: {method['url_tested']}")
                if method.get('response_data'):
                    print(f"     Response: {json.dumps(method['response_data'], indent=6)}")
                print()
        
        if failed:
            print(f"\n❌ METHODS NOT FOUND:")
            for method in failed:
                print(f"  ❌ {method['method']} - {method['status']}")
        
        # Save detailed report
        report_filename = f"mxapiinventory_transfer_investigation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Detailed report saved: {report_filename}")
        
        return self.results

def main():
    """Main investigation function."""
    print("🚀 MXAPIINVENTORY TRANSFER METHODS INVESTIGATION")
    print("=" * 60)
    print("Objective: Discover transfer-related web service methods")
    print("Authentication: OSLC Token Session (MaximoTokenManager)")
    print("Scope: MXAPIINVENTORY endpoint only")
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    investigator = MXAPIInventoryTransferInvestigator(base_url)
    
    # Initialize authentication
    if not investigator.initialize_authentication():
        print("❌ Authentication failed. Cannot proceed.")
        return
    
    # Discover endpoint capabilities
    investigator.discover_endpoint_capabilities()
    
    # Investigate transfer methods
    investigator.investigate_all_methods()
    
    # Generate report
    results = investigator.generate_report()
    
    print("\n🎯 INVESTIGATION COMPLETE")
    print("=" * 60)
    
    if results['discovered_methods']:
        print("✅ Transfer methods discovered! Check the detailed report for implementation details.")
    else:
        print("❌ No transfer methods found. The endpoint may not support web service methods.")

if __name__ == "__main__":
    main()
