#!/bin/bash

# SUCCESS Transfer Test - Using Confirmed Valid Location
# ======================================================

echo "🎉 SUCCESS TRANSFER TEST"
echo "======================="

echo "🔍 BREAKTHROUGH DISCOVERY:"
echo "  ✅ KWAJ-1058 DOES exist in IKWAJ site!"
echo "  ✅ API calls are working perfectly"
echo "  ❌ Issue: Payload structure causing wrong site validation"
echo ""
echo "💡 SOLUTION: Use the confirmed existing location KWAJ-1058"
echo "   From storeroom lookup: 'Storeroom KWAJ-1058' exists in IKWAJ"
echo ""

# Test 1: Transfer to KWAJ-1058 (confirmed to exist)
echo "📋 TEST 1: Transfer to confirmed existing KWAJ-1058"
echo "================================================"

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 This is the exact same call that's failing - confirming the issue"
echo ""

# Test 2: Try with different storeroom from the list
echo "📋 TEST 2: Transfer to KWAJ-1115 (also confirmed to exist)"
echo "======================================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1115",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 Testing with another confirmed location"
echo ""

# Test 3: Try with 8051 FLOOR-X (space in name)
echo "📋 TEST 3: Transfer to '8051 FLOOR-X' (space in name)"
echo "=================================================="

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "8051 FLOOR-X",
    "quantity": 1.0,
    "from_issue_unit": "RO"
  }' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s

echo ""
echo "🎯 Testing with location that has spaces"
echo ""

echo "🔍 ANALYSIS OF RESULTS"
echo "====================="
echo "If all tests still show the same error pattern:"
echo "  'Location X does not exist in site LCVKWT'"
echo ""
echo "Then the issue is in the PAYLOAD STRUCTURE:"
echo "  ❌ Maximo is validating destination location against source site"
echo "  ✅ The locations DO exist in the destination site"
echo "  🔧 Need to fix how the transfer payload is constructed"
echo ""
echo "🛠️ SOLUTION APPROACHES:"
echo "1. Check if inventory record needs to be in destination site"
echo "2. Modify payload structure to avoid cross-site validation"
echo "3. Use different transfer method/endpoint"
echo "4. Create inventory record in destination site first"
echo ""
echo "🎯 THE API CALLS ARE WORKING - IT'S A BUSINESS LOGIC ISSUE!"
