#!/usr/bin/env python3
"""
Test Enhanced Filtering for MXAPIPO and MXAPIPR endpoints
Tests the new status filtering that excludes cancelled and closed records
"""

import os
import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
TARGET_ITEM = "5975-60-V00-0529"
TARGET_SITE = "LCVKWT"

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def print_subsection(title):
    """Print a formatted subsection header."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def test_enhanced_filtering():
    """Test the enhanced filtering with status exclusions."""
    print_section("ENHANCED FILTERING TEST - ACTIVE RECORDS ONLY")
    
    # Test MXAPIPO with enhanced filtering
    print_subsection("MXAPIPO (Purchase Orders) - Active Records Only")
    test_mxapipo_enhanced()
    
    # Test MXAPIPR with enhanced filtering
    print_subsection("MXAPIPR (Purchase Requisitions) - Active Records Only")
    test_mxapipr_enhanced()

def test_mxapipo_enhanced():
    """Test MXAPIPO endpoint with enhanced filtering (excludes CAN and CLOSE)."""
    
    # Enhanced where clause that excludes cancelled and closed POs
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}" and status!="CAN" and status!="CLOSE"'
    
    print(f"Enhanced where clause: {where_clause}")
    print("🎯 Goal: Show only ACTIVE purchase orders (not cancelled or closed)")
    
    # API endpoint with API key (since session auth isn't working)
    api_url = f"{BASE_URL}/api/os/mxapipo"
    
    # Parameters
    params = {
        "oslc.select": "ponum,status,status_description,siteid,vendor,orderdate,poline",
        "oslc.where": where_clause,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    # Headers
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    # Show curl command
    curl_cmd = f"""curl -s -H "Accept: application/json" -H "apikey: {API_KEY}" \\
 "{api_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}" """
    print(f"Curl command:\n{curl_cmd}")
    
    # Make request
    try:
        response = requests.get(api_url, params=params, headers=headers, timeout=(5, 30))
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            member_count = len(data.get('member', []))
            print(f"✅ Success: Found {member_count} ACTIVE records")
            
            # Analyze status distribution
            status_counts = {}
            active_records = []
            
            for record in data.get('member', []):
                status = record.get('status', 'UNKNOWN')
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Collect active record details
                poline_records = record.get('poline', [])
                for poline in poline_records:
                    if poline.get('itemnum') == TARGET_ITEM:
                        active_records.append({
                            'ponum': record.get('ponum'),
                            'status': record.get('status'),
                            'vendor': record.get('vendor'),
                            'orderdate': record.get('orderdate'),
                            'itemnum': poline.get('itemnum'),
                            'orderqty': poline.get('orderqty'),
                            'unitcost': poline.get('unitcost')
                        })
                        break
            
            print(f"📊 Status distribution: {status_counts}")
            
            # Verify no cancelled or closed records
            excluded_statuses = ['CAN', 'CLOSE']
            found_excluded = any(status in excluded_statuses for status in status_counts.keys())
            
            if found_excluded:
                print("❌ ERROR: Found cancelled or closed records - filtering not working!")
            else:
                print("✅ SUCCESS: No cancelled or closed records found - filtering working correctly!")
            
            # Show sample active records
            if active_records:
                print(f"\nSample ACTIVE Purchase Orders (showing first 5):")
                for i, po in enumerate(active_records[:5], 1):
                    print(f"{i}. PO #{po['ponum']} - Status: {po['status']} - Vendor: {po['vendor']}")
                    print(f"   Order Date: {po['orderdate']}")
                    print(f"   Item: {po['itemnum']} - Qty: {po['orderqty']} - Unit Cost: {po['unitcost']}")
                    print()
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
                
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_mxapipr_enhanced():
    """Test MXAPIPR endpoint with enhanced filtering (excludes CAN and CLOSE)."""
    
    # Enhanced where clause that excludes cancelled and closed PRs
    where_clause = f'prline.itemnum="{TARGET_ITEM}" and prline.siteid="{TARGET_SITE}" and status!="CAN" and status!="CLOSE"'
    
    print(f"Enhanced where clause: {where_clause}")
    print("🎯 Goal: Show only ACTIVE purchase requisitions (not cancelled or closed)")
    
    # API endpoint with API key (since session auth isn't working)
    api_url = f"{BASE_URL}/api/os/mxapipr"
    
    # Parameters
    params = {
        "oslc.select": "prnum,status,status_description,siteid,requestedby,requestdate,prline",
        "oslc.where": where_clause,
        "oslc.pageSize": "50",
        "lean": "1"
    }
    
    # Headers
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    # Show curl command
    curl_cmd = f"""curl -s -H "Accept: application/json" -H "apikey: {API_KEY}" \\
 "{api_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}" """
    print(f"Curl command:\n{curl_cmd}")
    
    # Make request
    try:
        response = requests.get(api_url, params=params, headers=headers, timeout=(5, 30))
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            member_count = len(data.get('member', []))
            print(f"✅ Success: Found {member_count} ACTIVE records")
            
            # Analyze status distribution
            status_counts = {}
            active_records = []
            
            for record in data.get('member', []):
                status = record.get('status', 'UNKNOWN')
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Collect active record details
                prline_records = record.get('prline', [])
                for prline in prline_records:
                    if prline.get('itemnum') == TARGET_ITEM:
                        active_records.append({
                            'prnum': record.get('prnum'),
                            'status': record.get('status'),
                            'requestedby': record.get('requestedby'),
                            'requestdate': record.get('requestdate'),
                            'itemnum': prline.get('itemnum'),
                            'orderqty': prline.get('orderqty'),
                            'unitcost': prline.get('unitcost')
                        })
                        break
            
            print(f"📊 Status distribution: {status_counts}")
            
            # Verify no cancelled or closed records
            excluded_statuses = ['CAN', 'CLOSE']
            found_excluded = any(status in excluded_statuses for status in status_counts.keys())
            
            if found_excluded:
                print("❌ ERROR: Found cancelled or closed records - filtering not working!")
            else:
                print("✅ SUCCESS: No cancelled or closed records found - filtering working correctly!")
            
            # Show sample active records
            if active_records:
                print(f"\nSample ACTIVE Purchase Requisitions (showing first 5):")
                for i, pr in enumerate(active_records[:5], 1):
                    print(f"{i}. PR #{pr['prnum']} - Status: {pr['status']} - Requested By: {pr['requestedby']}")
                    print(f"   Request Date: {pr['requestdate']}")
                    print(f"   Item: {pr['itemnum']} - Qty: {pr['orderqty']} - Unit Cost: {pr['unitcost']}")
                    print()
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
                
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Testing Enhanced Filtering for Active Records Only")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Excluded Statuses: CAN (Cancelled), CLOSE (Closed)")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Test enhanced filtering
    test_enhanced_filtering()
    
    print(f"\n{'='*80}")
    print("🏁 Enhanced Filtering Test Complete")
    print(f"{'='*80}")
    print("\n📋 Summary:")
    print("1. Enhanced where clauses now exclude cancelled and closed records")
    print("2. Session Token authentication is primary with API Key fallback")
    print("3. Only ACTIVE purchase orders and requisitions are returned")
    print("4. Ready for UI integration with separate PO/PR tabs")
