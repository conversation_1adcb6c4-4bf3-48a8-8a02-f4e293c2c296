# Asset Search Fixes Summary

## Overview
This document summarizes the fixes implemented to resolve the OSLC query parsing error in asset search functionality and the "Asset not found" error when clicking asset details.

## Issues Fixed

### 1. OSLC Query Parsing Error
**Problem**: Asset search was failing with OSLC query parsing error:
```
********** - The OSLC query was not parsed. Ensure that the query in the HTTP request follows the correct syntax of the OSLC query specification.
Encountered " <IDENTIFIER> \"or \"" at line 1, column 69.
Was expecting: <BOOLEAN_OP> ...
```

**Root Cause**: The OR conditions in the asset search query were not properly grouped with parentheses, causing operator precedence ambiguity in the OSLC parser.

**Old Query (causing error)**:
```
status!="DECOMMISSIONED" and siteid="LCVKWT" and assetnum="%PUMP%" or description="%PUMP%" or assettag="%PUMP%" or serialnum="%PUMP%" or model="%PUMP%"
```

**Attempted Fix (still failed)**:
```
status!="DECOMMISSIONED" and siteid="LCVKWT" and (assetnum="%PUMP%" or description="%PUMP%" or assettag="%PUMP%" or serialnum="%PUMP%" or model="%PUMP%")
```

**Final Solution (working)**:
Multiple separate queries without OR conditions:
```
Query 1: status!="DECOMMISSIONED" and siteid="LCVKWT" and assetnum="%PUMP%"
Query 2: status!="DECOMMISSIONED" and siteid="LCVKWT" and description="%PUMP%"
Query 3: status!="DECOMMISSIONED" and siteid="LCVKWT" and assettag="%PUMP%"
Query 4: status!="DECOMMISSIONED" and siteid="LCVKWT" and serialnum="%PUMP%"
Query 5: status!="DECOMMISSIONED" and siteid="LCVKWT" and model="%PUMP%"
```

**Fix Applied**:
- Replaced single complex OR query with multiple simple AND-only queries
- Each query searches one field at a time to avoid OSLC parsing issues
- Results are combined and deduplicated on the application side
- This specific Maximo instance doesn't support OR conditions with parentheses
- File: `backend/services/asset_management_service.py` lines 145-319

### 2. Asset Details "Asset not found" Error
**Problem**: When clicking on asset details, users were getting "Asset not found" error even for valid assets.

**Root Cause**:
1. Exact matching was too restrictive (assetnum + siteid)
2. Assets might exist in different sites or with different case
3. Session expiration during API calls without proper retry logic
4. No fallback strategies when exact match failed

**Fix Applied**:
- **Multi-Strategy Search**: Implemented 3 fallback strategies:
  1. **Exact Match**: `assetnum="ASSET-001" and siteid="SITE"`
  2. **Cross-Site Search**: `assetnum="ASSET-001"` (any site)
  3. **Case-Insensitive**: `assetnum="%ASSET-001%" and siteid="SITE"`
- **Enhanced Error Handling**: Better error messages with specific details
- **Input Validation**: Clean and validate asset numbers and site IDs
- **Session Management**: Retry logic with session refresh for each strategy
- **Comprehensive Logging**: Detailed logs for debugging asset lookup issues
- Files: `backend/services/asset_management_service.py` lines 545-724 and `app.py` lines 7241-7254

### 3. Missing Cache Statistics Method
**Problem**: Asset service was missing the `get_cache_stats()` method that was being called by test scripts.

**Fix Applied**:
- Added `get_cache_stats()` method to provide cache statistics
- File: `backend/services/asset_management_service.py` lines 327-336

## Technical Details

### OSLC Query Syntax Rules
Based on analysis and testing with this specific Maximo instance:

1. **Simple conditions**: `field="value"` ✅ Works
2. **LIKE patterns**: `field="%value%"` ✅ Works
3. **NOT conditions**: `field!="value"` ✅ Works
4. **AND conditions**: `condition1 and condition2` ✅ Works
5. **OR conditions**: `(condition1 or condition2)` ❌ Causes parsing errors
6. **Complex combinations**: `condition1 and (condition2 or condition3)` ❌ Not supported

### Multi-Query Approach
Since OR conditions cause parsing errors, the solution uses:
1. **Multiple separate queries**: Each searching one field at a time
2. **Result combination**: Merge results from all queries
3. **Deduplication**: Remove duplicate assets based on assetnum+siteid
4. **Limit enforcement**: Stop when enough results are found

### Session Management Improvements
The asset service now includes:

1. **Session validation**: Check if session is still valid before API calls
2. **Session refresh**: Automatic session refresh when session expires
3. **Retry logic**: Retry API calls once after session refresh
4. **Error detection**: Detect login redirects and handle appropriately

## Files Modified

### 1. `backend/services/asset_management_service.py`
- **Lines 93-95**: Updated to use enhanced multi-field search method
- **Lines 145-319**: Added `_search_mxapiasset_enhanced()` and `_search_mxapiasset_single_field()` methods
- **Lines 320-332**: Simplified original search method for single field queries
- **Lines 333-378**: Added session refresh and retry logic for search method
- **Lines 433-442**: Added `get_cache_stats()` method
- **Lines 552-597**: Added session refresh and retry logic for details method

## Testing

### Test Scripts Created
1. **`test_asset_search_fix.py`**: Comprehensive test for asset search functionality
2. **`test_oslc_query_syntax.py`**: Verification of OSLC query syntax
3. **`debug_oslc_error.py`**: Analysis of the original error and fix

### Expected Behavior After Fix
1. **Asset Search**: Should work without OSLC parsing errors
2. **Asset Details**: Should load successfully without "Asset not found" errors
3. **Session Handling**: Should automatically refresh sessions when they expire
4. **Error Recovery**: Should retry failed requests once with fresh session

## Verification Steps

To verify the fixes work correctly:

1. **Test Asset Search**:
   - Navigate to asset management page
   - Enter search terms like "PUMP", "VALVE", "MOTOR"
   - Verify search results load without errors

2. **Test Asset Details**:
   - Click on any asset in search results
   - Verify asset details modal opens successfully
   - Check that all asset information is displayed

3. **Test Session Handling**:
   - Let session expire (or force logout/login)
   - Try asset search and details
   - Verify automatic session refresh works

## Related Services Patterns

The fix follows established patterns from other working services:

- **Inventory Service**: Uses `(condition1 or condition2)` for OR grouping
- **Workorder Service**: Uses `(woclass="WORKORDER" or woclass="ACTIVITY")` pattern
- **Enhanced Workorder Service**: Has robust session refresh and retry logic

## Future Considerations

1. **Performance**: The session refresh logic adds minimal overhead
2. **Reliability**: Improved error handling makes the service more robust
3. **Consistency**: Asset service now follows same patterns as other services
4. **Maintenance**: Easier to debug with better logging and error messages
