#!/usr/bin/env python3
"""
Test script for mxapiinventory integration

This script tests the inventory adjustment functionality by:
1. Authenticating with Maximo
2. Creating a test payload with the exact structure provided
3. Submitting to mxapiinventory endpoint
4. Verifying the response
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager
from backend.services.inventory_adjustment_service import InventoryAdjustmentService

def test_mxapiinventory_integration():
    """Test the mxapiinventory integration with exact payload structure"""
    
    print("🔧 MXAPI TEST: Starting mxapiinventory integration test")
    
    # Initialize token manager
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    token_manager = MaximoTokenManager(base_url)
    
    # Check if already logged in
    if not token_manager.is_logged_in():
        print("❌ MXAPI TEST: Not logged in to Maximo. Please log in first through the web app.")
        return False
    
    print(f"✅ MXAPI TEST: Authenticated as {getattr(token_manager, 'username', 'unknown')}")
    
    # Create test inventory data (simulating QR scan result)
    test_inventory_data = {
        'itemnum': '5975-60-V00-0529',
        'siteid': 'LCVKNT',
        'storeloc': 'RIP001',
        'binnum': '28-800-0004',
        'currentbalance': 30,
        'conditioncode': 'A1',
        'issueunit': 'RO',
        'lotnum': '',
        'controlacc': '',
        'shrinkageacc': ''
    }
    
    # Create test adjustment data
    test_adjustment_data = {
        'adjustment_type': 'POSITIVE',
        'quantity': 5,
        'reason_code': 'TEST_ADJUSTMENT',
        'notes': 'Test adjustment via QR scanner integration'
    }
    
    print(f"🔧 MXAPI TEST: Testing adjustment for item {test_inventory_data['itemnum']}")
    print(f"🔧 MXAPI TEST: Adjustment: {test_adjustment_data['adjustment_type']} {test_adjustment_data['quantity']}")
    
    # Initialize the adjustment service
    adjustment_service = InventoryAdjustmentService(token_manager)
    
    # Test payload building first
    print("🔧 MXAPI TEST: Building payload...")
    try:
        payload = adjustment_service._build_adjustment_payload(test_inventory_data, test_adjustment_data)
        print("✅ MXAPI TEST: Payload built successfully")
        print(f"🔧 MXAPI TEST: Payload structure:")
        print(json.dumps(payload, indent=2))
        
        # Verify payload matches expected structure
        expected_structure = {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 35,  # 30 + 5 (positive adjustment)
                    "conditioncode": "A1",
                    "reconciled": True
                }
            ]
        }
        
        # Validate key fields
        if (payload[0]['itemnum'] == expected_structure['itemnum'] and
            payload[0]['siteid'] == expected_structure['siteid'] and
            payload[0]['invbalances'][0]['binnum'] == expected_structure['invbalances'][0]['binnum']):
            print("✅ MXAPI TEST: Payload structure validation passed")
        else:
            print("❌ MXAPI TEST: Payload structure validation failed")
            return False
            
    except Exception as e:
        print(f"❌ MXAPI TEST: Failed to build payload: {str(e)}")
        return False
    
    # Test the full submission
    print("🔧 MXAPI TEST: Testing full submission to mxapiinventory...")
    try:
        result = adjustment_service.submit_inventory_adjustment(test_inventory_data, test_adjustment_data)
        
        if result['success']:
            print("✅ MXAPI TEST: Inventory adjustment submitted successfully!")
            print(f"🔧 MXAPI TEST: Response: {result.get('message', 'No message')}")
            if 'response' in result:
                print(f"🔧 MXAPI TEST: Maximo response: {json.dumps(result['response'], indent=2)}")
        else:
            print(f"❌ MXAPI TEST: Inventory adjustment failed: {result.get('error')}")
            if 'status_code' in result:
                print(f"🔧 MXAPI TEST: HTTP Status: {result['status_code']}")
            return False
            
    except Exception as e:
        print(f"❌ MXAPI TEST: Exception during submission: {str(e)}")
        return False
    
    print("✅ MXAPI TEST: All tests completed successfully!")
    return True

def test_payload_structure_only():
    """Test just the payload structure without submitting to Maximo"""
    
    print("🔧 PAYLOAD TEST: Testing payload structure only")
    
    # Mock token manager for structure testing
    class MockTokenManager:
        def __init__(self):
            self.username = "test_user"
            self.base_url = "https://test.maximo.com"
        
        def is_logged_in(self):
            return True
    
    token_manager = MockTokenManager()
    adjustment_service = InventoryAdjustmentService(token_manager)
    
    # Test data from your example
    test_inventory_data = {
        'itemnum': '5975-60-V00-0529',
        'siteid': 'LCVKNT',
        'storeloc': 'RIP001',
        'binnum': '28-800-0004',
        'currentbalance': 30,
        'conditioncode': 'A1',
        'issueunit': 'RO',
        'lotnum': '',
        'controlacc': '',
        'shrinkageacc': ''
    }
    
    test_adjustment_data = {
        'adjustment_type': 'POSITIVE',
        'quantity': 0,  # Testing with 0 to match your physcnt example
        'reason_code': 'CYCLE_COUNT',
        'notes': 'Test cycle count adjustment'
    }
    
    try:
        payload = adjustment_service._build_adjustment_payload(test_inventory_data, test_adjustment_data)
        print("✅ PAYLOAD TEST: Payload built successfully")
        print("🔧 PAYLOAD TEST: Generated payload:")
        print(json.dumps(payload, indent=2))
        
        # Compare with your expected structure
        expected = {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 30,  # Same as current balance since adjustment is 0
                    "conditioncode": "A1",
                    "reconciled": True,
                    "memo": "CYCLE_COUNT: Test cycle count adjustment"
                }
            ]
        }
        
        print("🔧 PAYLOAD TEST: Expected structure:")
        print(json.dumps([expected], indent=2))
        
        # Validate structure
        actual = payload[0]
        if (actual['_action'] == expected['_action'] and
            actual['itemnum'] == expected['itemnum'] and
            actual['itemsetid'] == expected['itemsetid'] and
            actual['siteid'] == expected['siteid'] and
            actual['location'] == expected['location'] and
            actual['issueunit'] == expected['issueunit'] and
            len(actual['invbalances']) == 1):
            print("✅ PAYLOAD TEST: Structure validation passed")
            return True
        else:
            print("❌ PAYLOAD TEST: Structure validation failed")
            return False
            
    except Exception as e:
        print(f"❌ PAYLOAD TEST: Failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting mxapiinventory integration tests")
    print("=" * 60)
    
    # Test payload structure first
    if test_payload_structure_only():
        print("\n" + "=" * 60)
        # Test full integration if structure test passes
        test_mxapiinventory_integration()
    else:
        print("❌ Payload structure test failed, skipping integration test")
    
    print("\n🏁 Test completed")
