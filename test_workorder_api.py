#!/usr/bin/env python3
"""
Test the work order API endpoint to debug the dropdown issue
"""
import requests
import json

def test_workorder_api():
    """Test the work order API endpoint"""
    
    print("🔧 TESTING WORK ORDER API")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:5010"
    
    # Test with different sites
    test_sites = ["LCVKWT", "IKWAJ"]
    
    for site in test_sites:
        print(f"\n🔍 Testing with site: {site}")
        
        # Test work order endpoint
        print(f"\n1️⃣ Testing work order API:")
        wo_url = f"{base_url}/api/inventory/issue-current-item/work-orders"
        
        wo_params = {
            'siteid': site
        }
        
        try:
            response = requests.get(wo_url, params=wo_params, timeout=10)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
                
                if data.get('success') and data.get('work_orders'):
                    print(f"✅ Found {len(data['work_orders'])} work orders")
                    for wo in data['work_orders'][:3]:  # Show first 3
                        print(f"   - {wo.get('wonum')} - {wo.get('description', 'No desc')} ({wo.get('status')})")
                else:
                    print(f"⚠️ No work orders found or API error: {data.get('error', 'Unknown')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_workorder_api()
