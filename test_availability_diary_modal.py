#!/usr/bin/env python3
"""
Test script to validate the enhanced availability modal with diary-style navigation.
This script checks if the new vertical sidebar navigation is properly implemented.
"""

import os
import re
import sys

def test_css_implementation():
    """Test if the CSS for diary-style availability modal is properly implemented."""
    print("🎨 Testing CSS Implementation...")
    
    css_file_path = "frontend/static/css/style.css"
    
    if not os.path.exists(css_file_path):
        print(f"❌ CSS file not found: {css_file_path}")
        return False
    
    try:
        with open(css_file_path, 'r') as f:
            css_content = f.read()
        
        # Check for required CSS classes
        required_css_classes = [
            "availability-diary-view",
            "availability-diary-sidebar", 
            "availability-diary-tabs",
            "availability-diary-tab",
            "availability-diary-tab-icon",
            "availability-diary-tab-name",
            "availability-diary-tab-badge",
            "availability-diary-content",
            "availability-tab-panel",
            "availability-tab-header",
            "locations-tab",
            "lots-tab", 
            "purchase-orders-tab",
            "purchase-requisitions-tab",
            "reservations-tab",
            "alternates-tab"
        ]
        
        missing_classes = []
        for css_class in required_css_classes:
            if css_class not in css_content:
                missing_classes.append(css_class)
        
        if missing_classes:
            print(f"❌ Missing CSS classes: {missing_classes}")
            return False
        else:
            print("✅ All required CSS classes found")
        
        # Check for mobile optimizations
        mobile_checks = [
            "@media (max-width: 768px)",
            "min-height: 44px",  # Touch target size
            "-webkit-overflow-scrolling: touch"
        ]
        
        for check in mobile_checks:
            if check in css_content:
                print(f"✅ Mobile optimization found: {check}")
            else:
                print(f"⚠️ Mobile optimization missing: {check}")
        
        # Check for color schemes
        color_schemes = [
            "--tab-bg: linear-gradient",
            "--tab-border:",
            "--tab-text:"
        ]
        
        for scheme in color_schemes:
            if scheme in css_content:
                print(f"✅ Color scheme found: {scheme}")
            else:
                print(f"⚠️ Color scheme missing: {scheme}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking CSS implementation: {str(e)}")
        return False

def test_javascript_implementation():
    """Test if the JavaScript for diary-style availability modal is properly implemented."""
    print("\n🔧 Testing JavaScript Implementation...")
    
    js_file_path = "frontend/static/js/inventory_management.js"
    
    if not os.path.exists(js_file_path):
        print(f"❌ JavaScript file not found: {js_file_path}")
        return False
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        # Check for required JavaScript functions and elements
        required_elements = [
            "availability-diary-view",
            "availability-diary-sidebar",
            "availability-diary-tabs",
            "availability-diary-tab",
            "setupAvailabilityDiaryTabs",
            "availability-tab-panel",
            "aria-labelledby",
            "data-bs-toggle=\"tab\"",
            "role=\"tab\"",
            "aria-controls",
            "aria-selected"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in js_content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing JavaScript elements: {missing_elements}")
            return False
        else:
            print("✅ All required JavaScript elements found")
        
        # Check for transition functionality
        transition_checks = [
            "translateX",
            "opacity",
            "transition",
            "setTimeout",
            "addEventListener",
            "touchstart",
            "touchend"
        ]
        
        for check in transition_checks:
            if check in js_content:
                print(f"✅ Transition functionality found: {check}")
            else:
                print(f"⚠️ Transition functionality missing: {check}")
        
        # Check for tab structure updates
        tab_updates = [
            "buildLocationsTab",
            "buildLotsTab", 
            "buildPurchaseOrdersTab",
            "buildPurchaseRequisitionsTab",
            "buildReservationsTab",
            "buildAlternatesTab"
        ]
        
        updated_tabs = 0
        for tab in tab_updates:
            if "availability-tab-panel" in js_content and tab in js_content:
                updated_tabs += 1
        
        if updated_tabs == len(tab_updates):
            print(f"✅ All {updated_tabs} tab building functions updated")
        else:
            print(f"⚠️ Only {updated_tabs}/{len(tab_updates)} tab building functions updated")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking JavaScript implementation: {str(e)}")
        return False

def test_modal_structure():
    """Test if the modal structure has been updated correctly."""
    print("\n🏗️ Testing Modal Structure...")
    
    js_file_path = "frontend/static/js/inventory_management.js"
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        # Check for updated modal classes
        modal_checks = [
            "availability-modal",
            "availability-header",
            "modal-fullscreen-sm-down"
        ]
        
        for check in modal_checks:
            if check in js_content:
                print(f"✅ Modal structure updated: {check}")
            else:
                print(f"⚠️ Modal structure missing: {check}")
        
        # Check if old horizontal tabs are replaced
        if "nav nav-tabs" in js_content and "availability-diary-view" in js_content:
            print("✅ Both old and new tab structures present (good for fallback)")
        elif "availability-diary-view" in js_content:
            print("✅ New diary-style structure implemented")
        else:
            print("❌ New diary-style structure not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking modal structure: {str(e)}")
        return False

def main():
    """Run all tests for the availability modal enhancement."""
    print("🧪 Testing Enhanced Availability Modal with Diary-Style Navigation")
    print("=" * 70)
    
    tests = [
        test_css_implementation,
        test_javascript_implementation, 
        test_modal_structure
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All {total} tests passed! The availability modal enhancement is ready.")
        print("\n📋 Features implemented:")
        print("   • Vertical sidebar navigation with vintage diary styling")
        print("   • Colorful tabs with rounded edges and compact spacing")
        print("   • Smooth fade/slide transitions between views")
        print("   • Mobile-optimized touch targets (44px minimum)")
        print("   • Consistent design pattern with asset detail modal")
        print("   • Space-efficient layout maximizing content area")
        
        print("\n🧪 To test manually:")
        print("   1. Go to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for an item")
        print("   3. Click the 'View Availability' button")
        print("   4. Verify the new vertical sidebar navigation")
        print("   5. Test tab switching and smooth transitions")
        print("   6. Test on mobile device for touch optimization")
        
        return True
    else:
        print(f"❌ {passed}/{total} tests passed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
