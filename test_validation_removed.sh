#!/bin/bash

# Test Validation Removed - Verify Hardcoded Validation is Gone
# =============================================================

echo "🚀 TEST VALIDATION REMOVED - VERIFY HARDCODED VALIDATION IS GONE"
echo "================================================================"

echo "🎯 OBJECTIVE: Verify that hardcoded validation is removed"
echo "📋 ISSUE FOUND: There was hardcoded validation preventing same location transfers"
echo "🔧 FIX APPLIED: Removed hardcoded validation to let Maximo handle BMXAA1861E"
echo ""

echo "⚠️  IMPORTANT: Before running this test:"
echo "1. Go to http://127.0.0.1:5010"
echo "2. Login with your Maximo credentials"
echo "3. Keep the Flask terminal open to see detailed logs"
echo ""

read -p "Have you logged in to the Flask app? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please login first at http://127.0.0.1:5010"
    exit 1
fi

echo "🔄 Testing transfer that was previously blocked by hardcoded validation..."
echo ""

# Test: Same location transfer that was previously blocked
echo "📋 TESTING: Same Location Transfer (Previously Blocked by Hardcoded Validation)"
echo "$(printf '=%.0s' {1..75})"
echo ""
echo "📋 PAYLOAD BEING SENT:"
echo '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "IKWAJ",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "to_issue_unit": "EA",
    "conversion_factor": 1.0,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'
echo ""

echo "🔄 Submitting to Flask application..."
echo "👀 WATCH THE FLASK TERMINAL FOR DETAILED PAYLOAD AND MAXIMO RESPONSE!"
echo ""

response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-same-site \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 FLASK APPLICATION RESPONSE:"
echo "$response" | jq '.' 2>/dev/null || echo "$response"
echo ""

# Analyze the response
if echo "$response" | grep -q '"success": false'; then
    if echo "$response" | grep -q "Validation failed"; then
        echo "❌ STILL BLOCKED BY HARDCODED VALIDATION!"
        echo "🔧 The hardcoded validation is still active"
        echo "📋 Error message from our validation, not Maximo"
    elif echo "$response" | grep -q 'BMXAA1861E'; then
        echo "✅ SUCCESS! HARDCODED VALIDATION REMOVED!"
        echo "🎉 Now getting actual BMXAA1861E error from Maximo"
        echo "📋 This is the real Maximo response, not our validation"
        echo ""
        echo "🔍 WHAT THIS MEANS:"
        echo "• The transfer request is now reaching Maximo"
        echo "• Maximo is responding with the actual BMXAA1861E error"
        echo "• User can see the real error and fix it properly"
        echo "• No more hardcoded blocking of valid transfer attempts"
    else
        echo "❓ UNEXPECTED ERROR RESPONSE"
        echo "📋 Getting an error, but not the expected BMXAA1861E"
    fi
elif echo "$response" | grep -q '"success": true'; then
    echo "🎉 TRANSFER SUCCESSFUL!"
    echo "✅ No duplicate error - transfer completed successfully"
elif echo "$response" | grep -q "401"; then
    echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
else
    echo "⚠️  UNEXPECTED RESPONSE FORMAT"
fi

echo ""
echo "📋 WHAT TO LOOK FOR IN FLASK TERMINAL:"
echo "====================================="
echo ""
echo "1. 📋 EXACT PAYLOAD BEING SENT TO MAXIMO:"
echo "   Should see the complete JSON payload with all fields"
echo ""
echo "2. 📋 EXACT RESPONSE FROM MAXIMO:"
echo "   Should see either:"
echo "   • BMXAA1861E error (duplicate combination)"
echo "   • 204 success status (transfer completed)"
echo "   • Other Maximo error (different validation issue)"
echo ""
echo "3. 🚫 NO MORE HARDCODED BLOCKING:"
echo "   Should NOT see 'Validation failed: Cannot transfer to the same location and bin'"
echo ""

# Test with different bins to see if it works
echo ""
echo "📋 TESTING: Same Location with Different Bins (Should Work)"
echo "$(printf '=%.0s' {1..60})"

response2=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-same-site \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "TRANSFER",
        "from_lot": "DEFAULT",
        "to_lot": "TRANSFER",
        "from_condition": "A1",
        "to_condition": "A1"
    }' \
    -s)

echo "📊 RESPONSE (Different Bins):"
echo "$response2" | jq '.' 2>/dev/null || echo "$response2"
echo ""

if echo "$response2" | grep -q '"success": true'; then
    echo "🎉 SUCCESS! Different bins/lots work!"
elif echo "$response2" | grep -q 'BMXAA1861E'; then
    echo "❌ Still getting BMXAA1861E - may need different bin/lot values"
elif echo "$response2" | grep -q "Validation failed"; then
    echo "❌ Still blocked by hardcoded validation"
else
    echo "📋 Other response - check details above"
fi

echo ""
echo "📊 VALIDATION REMOVAL TEST SUMMARY"
echo "=================================="

if echo "$response" | grep -q "Validation failed"; then
    echo "❌ HARDCODED VALIDATION STILL ACTIVE"
    echo "🔧 Need to check the validation removal"
elif echo "$response" | grep -q 'BMXAA1861E'; then
    echo "✅ HARDCODED VALIDATION SUCCESSFULLY REMOVED!"
    echo "🎉 Now getting real Maximo responses"
    echo "📋 Users can see actual errors and fix them"
else
    echo "✅ HARDCODED VALIDATION REMOVED"
    echo "📋 Getting different responses - check Flask logs"
fi

echo ""
echo "🎯 NEXT STEPS:"
echo "• Check Flask terminal for detailed payload/response logs"
echo "• Try different bin/lot combinations to avoid duplicates"
echo "• Test the UI at http://127.0.0.1:5010/inventory-management"
echo "• Verify error messages show real Maximo guidance"
