#!/usr/bin/env python3
"""
Simple test to verify signature flow works correctly
Tests the core signature functionality without authentication issues
"""

import requests
import json
import base64
from datetime import datetime

BASE_URL = "http://localhost:5010"
session = requests.Session()

def create_test_signature():
    """Create a simple test signature as base64 PNG"""
    test_signature_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    return f"data:image/png;base64,{test_signature_b64}"

def test_signature_flow():
    """Test the complete signature flow"""
    print("🧪 Testing Complete Signature Flow...")
    
    # Test data
    wonum = "15643630"
    status = "COMP"
    
    print(f"📝 Testing signature submission for WO {wonum} -> {status}")
    
    signature_data = {
        "wonum": wonum,
        "status": status,
        "wo_type": "task",
        "signature_data": create_test_signature(),
        "customer_name": "Test Customer",
        "comments": "Simple test signature",
        "date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Test signature submission directly
    response = session.post(
        f"{BASE_URL}/api/signature/submit",
        json=signature_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print("✅ SUCCESS: Signature flow completed successfully!")
                print(f"   - PDF attached: {result.get('pdf_attached', 'Unknown')}")
                print(f"   - Message: {result.get('message', 'No message')}")
                return True
            else:
                print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
                return False
                
        except json.JSONDecodeError:
            print(f"❌ FAILED: Invalid JSON response")
            print(f"Raw response: {response.text[:200]}")
            return False
    else:
        print(f"❌ FAILED: HTTP {response.status_code}")
        print(f"Response: {response.text[:200]}")
        return False

def configure_signature():
    """Configure COMP status to require signature"""
    print("📝 Configuring COMP status for signature...")

    config_data = {
        "statuses": ["COMP"],
        "scope": ["parent", "task"],
        "enabled": True
    }

    response = session.post(
        f"{BASE_URL}/api/admin/signature-config",
        json=config_data,
        headers={'Content-Type': 'application/json'}
    )

    print(f"Config response: {response.status_code}")

    if response.status_code == 200:
        try:
            result = response.json()
            if result.get('success'):
                print("✅ Configuration saved successfully")
                return True
            else:
                print(f"❌ Configuration failed: {result.get('error')}")
                return False
        except:
            print("❌ Failed to parse config response")
            return False
    else:
        print(f"❌ Config HTTP error: {response.status_code}")
        return False

def test_simple_status_check():
    """Test if signature is required for COMP status"""
    print("\n🔍 Testing signature requirement check...")

    response = session.post(
        f"{BASE_URL}/api/admin/signature-required",
        json={"status": "COMP", "wo_type": "task"},
        headers={'Content-Type': 'application/json'}
    )

    print(f"Status check response: {response.status_code}")

    if response.status_code == 200:
        try:
            result = response.json()
            print(f"Signature required: {result.get('signature_required', 'Unknown')}")
            return result.get('signature_required', False)
        except:
            print("❌ Failed to parse response")
            return False
    else:
        print(f"❌ HTTP error: {response.status_code}")
        return False

if __name__ == "__main__":
    print("🚀 Simple Signature Test")
    print("=" * 40)

    # Step 1: Configure signature requirement
    if not configure_signature():
        print("❌ Failed to configure signature - stopping")
        exit(1)

    # Step 2: Check if signature is required
    signature_required = test_simple_status_check()

    # Step 3: Test complete signature flow
    if signature_required:
        print("✅ Signature is required for COMP - testing flow...")
        success = test_signature_flow()

        if success:
            print("\n🎉 ALL TESTS PASSED!")
            print("The signature system is working correctly.")
        else:
            print("\n❌ SIGNATURE FLOW FAILED")
            print("Check the Flask logs for detailed error information.")
    else:
        print("❌ Signature not required for COMP - configuration issue")

    print("\n" + "=" * 40)
    print("Test complete.")
