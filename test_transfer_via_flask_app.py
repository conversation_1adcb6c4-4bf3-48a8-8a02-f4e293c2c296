#!/usr/bin/env python3
"""
Test Transfer via Flask App
============================

Since your Flask application is successfully making API calls but our standalone
scripts are getting 403 errors, let's test the transfer by making HTTP requests
to your Flask application's transfer endpoint.

Author: Maximo Architect
Date: 2025-07-16
"""

import requests
import json
import time

def test_transfer_via_flask():
    """Test transfer by calling your Flask application's API endpoint."""
    print("🚀 TESTING TRANSFER VIA FLASK APPLICATION")
    print("=" * 50)
    
    # Your Flask app should be running on localhost:5010
    flask_base_url = "http://127.0.0.1:5010"
    transfer_endpoint = f"{flask_base_url}/api/inventory/transfer-current-item"
    
    print(f"🔗 Flask endpoint: {transfer_endpoint}")
    
    # Test cases with different parameter combinations
    test_cases = [
        {
            "name": "Test 1: Minimal transfer",
            "description": "Basic transfer without bins or lots",
            "payload": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "RIP001",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO"
            }
        },
        {
            "name": "Test 2: With bins",
            "description": "Transfer with bin numbers",
            "payload": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "RIP001",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "from_bin": "28-800-0004",
                "to_bin": "1058-TEMP"
            }
        },
        {
            "name": "Test 3: With lots",
            "description": "Transfer with lot numbers",
            "payload": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "RIP001",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "from_lot": "TEST",
                "to_lot": "TEST"
            }
        },
        {
            "name": "Test 4: Complete transfer",
            "description": "Transfer with all optional fields",
            "payload": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "RIP001",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "from_bin": "28-800-0004",
                "to_bin": "1058-TEMP",
                "from_lot": "TEST",
                "to_lot": "TEST",
                "from_condition": "A1",
                "to_condition": "A1"
            }
        },
        {
            "name": "Test 5: Different bin combination",
            "description": "Transfer with alternative bin numbers",
            "payload": {
                "itemnum": "5975-60-V00-0529",
                "from_siteid": "LCVKWT",
                "to_siteid": "IKWAJ",
                "from_storeroom": "RIP001",
                "to_storeroom": "KWAJ-1058",
                "quantity": 1.0,
                "from_issue_unit": "RO",
                "from_bin": "28-800-0004",
                "to_bin": "58-A-A01-1"
            }
        }
    ]
    
    successful_tests = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 {test_case['name']}")
        print(f"📝 {test_case['description']}")
        print("=" * 60)
        
        try:
            print(f"🔄 Submitting to Flask app...")
            print(f"Payload: {json.dumps(test_case['payload'], indent=2)}")
            
            # Make POST request to Flask app
            response = requests.post(
                transfer_endpoint,
                json=test_case['payload'],
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=30
            )
            
            print(f"📊 HTTP Status: {response.status_code}")
            
            if response.text:
                try:
                    response_data = response.json()
                    print(f"📋 Response: {json.dumps(response_data, indent=2)}")
                    
                    # Check for success patterns
                    if response.status_code == 200:
                        if response_data.get('success'):
                            print("🎉 SUCCESS! Transfer completed via Flask app!")
                            successful_tests.append({
                                'test_case': test_case,
                                'response': response_data
                            })
                            
                            # Save successful payload
                            filename = f"successful_flask_transfer_test_{i}.json"
                            with open(filename, 'w') as f:
                                json.dump(test_case['payload'], f, indent=2)
                            print(f"💾 Successful payload saved to: {filename}")
                        else:
                            error_msg = response_data.get('error', 'Unknown error')
                            print(f"❌ Transfer failed: {error_msg}")
                            
                            # Check if it's a business logic error (not auth error)
                            if 'BMXAA7901E' not in str(error_msg):
                                print("ℹ️  This is a business logic error, not an authentication error")
                                print("   The API call is working, just need to fix the data")
                    else:
                        print(f"❌ HTTP error: {response.status_code}")
                        
                except json.JSONDecodeError:
                    print(f"📄 Response (non-JSON): {response.text}")
            
        except requests.exceptions.ConnectionError:
            print("❌ Connection error - Is your Flask app running on localhost:5010?")
            print("   Please start your Flask app first: python app.py")
            break
            
        except requests.exceptions.Timeout:
            print("⏱️  Request timeout")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Brief pause between tests
        time.sleep(2)
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 30)
    print(f"✅ Successful tests: {len(successful_tests)}")
    print(f"❌ Failed tests: {len(test_cases) - len(successful_tests)}")
    
    if successful_tests:
        print(f"\n🎯 WORKING TRANSFER PATTERNS:")
        for i, success in enumerate(successful_tests, 1):
            print(f"  {i}. {success['test_case']['name']}")
        
        # Save all successful patterns
        with open('all_successful_flask_transfer_patterns.json', 'w') as f:
            json.dump(successful_tests, f, indent=2)
        print(f"\n💾 All successful patterns saved to: all_successful_flask_transfer_patterns.json")
        
        return True
    else:
        print(f"\n❌ No successful transfers found via Flask app")
        return False

def generate_curl_commands():
    """Generate curl commands based on successful Flask tests."""
    print("\n🌐 GENERATING CURL COMMANDS")
    print("=" * 40)
    
    # Check if we have successful patterns
    try:
        with open('all_successful_flask_transfer_patterns.json', 'r') as f:
            successful_patterns = json.load(f)
    except FileNotFoundError:
        print("❌ No successful patterns found. Run Flask tests first.")
        return
    
    print("📋 CURL COMMANDS FOR SUCCESSFUL PATTERNS:")
    print("=" * 50)
    
    for i, pattern in enumerate(successful_patterns, 1):
        payload = pattern['test_case']['payload']
        
        print(f"\n# {pattern['test_case']['name']}")
        print(f"# {pattern['test_case']['description']}")
        
        curl_cmd = f"""curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{json.dumps(payload, separators=(',', ':'))}' \\
  -w "\\nHTTP_STATUS:%{{http_code}}\\nTIME:%{{time_total}}\\n" \\
  -s"""
        
        print(curl_cmd)
        
        # Save individual curl command
        curl_filename = f"curl_transfer_test_{i}.sh"
        with open(curl_filename, 'w') as f:
            f.write("#!/bin/bash\n\n")
            f.write(f"# {pattern['test_case']['name']}\n")
            f.write(f"# {pattern['test_case']['description']}\n\n")
            f.write(curl_cmd)
            f.write("\n")
        
        print(f"💾 Curl command saved to: {curl_filename}")

def main():
    """Main function."""
    print("🚀 TEST TRANSFER VIA FLASK APPLICATION")
    print("=" * 50)
    print("This script tests transfers by calling your Flask app's API endpoint")
    print("Make sure your Flask app is running: python app.py")
    print("")
    
    # Test via Flask app
    success = test_transfer_via_flask()
    
    if success:
        # Generate curl commands for successful patterns
        generate_curl_commands()
        
        print("\n🎉 TESTING COMPLETE!")
        print("=" * 30)
        print("✅ Found working transfer patterns via Flask app")
        print("📋 Curl commands generated for successful patterns")
        print("🔧 You can now use these patterns to fix your application")
    else:
        print("\n❌ NO SUCCESSFUL TRANSFERS FOUND")
        print("Check if your Flask app is running and accessible")

if __name__ == "__main__":
    main()
