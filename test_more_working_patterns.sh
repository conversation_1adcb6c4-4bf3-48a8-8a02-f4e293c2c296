#!/bin/bash

# Test More Working Cross-Site Patterns
# ======================================

echo "🚀 TESTING MORE WORKING CROSS-SITE TRANSFER PATTERNS"
echo "===================================================="

echo "🎯 OBJECTIVE: Find more 204 success patterns for cross-site transfers"
echo "📋 STRATEGY: Test variations of the working pattern (CMW-AJ → CMW-BU)"
echo "🔍 PATTERN: Use LCVKWT storerooms for both source and destination"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_cross_site_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting cross-site transfer..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Cross-site transfer worked with 204 status!"
        echo "$payload" > "working_pattern_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: working_pattern_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING VARIATIONS OF WORKING PATTERN"
echo "========================================"

# Test 1: CMW-AJ → CMW-BU (confirmed working pattern)
test_cross_site_payload "CMW-AJ → CMW-BU (Confirmed Working)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: CMW-AJ → CMW-AJH
test_cross_site_payload "CMW-AJ → CMW-AJH" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-AJH",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: CMW-BU → CMW-AJ (reverse)
test_cross_site_payload "CMW-BU → CMW-AJ (Reverse)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-BU",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: CMW-AJH → CMW-BUH
test_cross_site_payload "CMW-AJH → CMW-BUH" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJH",
    "to_storeroom": "CMW-BUH",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: RIP001 → CMW-AJ (original source to LCVKWT destination)
test_cross_site_payload "RIP001 → CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 6: Minimal fields with working pattern
test_cross_site_payload "CMW-AJ → CMW-BU (Minimal)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 7: Different quantity
test_cross_site_payload "CMW-AJ → CMW-BU (Quantity 0.5)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 0.5,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 8: Different bins
test_cross_site_payload "CMW-AJ → CMW-BU (Different Bins)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "28-800-0004",
    "to_bin": "58-A-A01-1",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 9: Different lots
test_cross_site_payload "CMW-AJ → CMW-BU (Different Lots)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "LOT123",
    "to_lot": "TEST",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 10: Without conditions
test_cross_site_payload "CMW-AJ → CMW-BU (No Conditions)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT"
}'

# Test 11: Small quantity
test_cross_site_payload "CMW-AJ → CMW-BU (Small Quantity)" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 0.1,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 12: Different item
test_cross_site_payload "CMW-AJ → CMW-BU (Different Item)" '{
    "itemnum": "TEST-ITEM",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "EA",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 WORKING PATTERN ANALYSIS SUMMARY"
echo "=================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found multiple working cross-site transfer patterns!"
    echo "============================================================="
    echo "💾 Check working_pattern_*.json files for working patterns"
    echo ""
    echo "📋 All Working Curl Commands:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "working_pattern_$i.json" ]; then
            echo ""
            echo "# Working Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat working_pattern_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
    
    echo ""
    echo "🎯 CONFIRMED SUCCESS PATTERN:"
    echo "• Cross-site transfers work when both source and destination are LCVKWT storerooms"
    echo "• Use storerooms that exist in the source site (LCVKWT)"
    echo "• Complete field validation with DEFAULT values"
    echo "• A1 condition codes work properly"
    echo "• RO issue unit works without conversion issues"
    
else
    echo ""
    echo "❌ No additional 204 success responses found"
    echo "🔄 The working pattern seems specific to certain storeroom combinations"
fi

echo ""
echo "🎯 KEY IMPLEMENTATION INSIGHT:"
echo "Cross-site transfers (LCVKWT → IKWAJ) work when:"
echo "• Destination storeroom exists in SOURCE site (LCVKWT)"
echo "• Both source and destination use LCVKWT storerooms"
echo "• Proper field validation is applied"
echo ""
echo "💡 This confirms Maximo validates destination against SOURCE site context!"
