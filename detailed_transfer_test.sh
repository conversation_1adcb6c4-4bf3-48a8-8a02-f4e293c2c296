#!/bin/bash

# Detailed Transfer Current Item Test Script
# Shows full responses and status codes

echo "🚀 Detailed Transfer Current Item Testing"
echo "========================================"

# Base configuration
MAXIMO_BASE="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
HEADERS="-H 'Accept: application/json' -H 'Content-Type: application/json'"

# Test payload from terminal logs
PAYLOAD='{
  "itemnum": "5975-60-V00-0529",
  "fromsiteid": "LCVKWT",
  "tositeid": "IKWAJ",
  "fromlocation": "RIP001",
  "tolocation": "KWAJ-1058",
  "quantity": 1.0,
  "fromissueunit": "RO",
  "toissueunit": "RO",
  "frombinnum": "28-800-0004",
  "fromlotnum": "TEST",
  "tolotnum": "TEST",
  "fromconditioncode": "A1",
  "toconditioncode": "A1"
}'

# Object Structure payload (current implementation)
OBJECT_PAYLOAD='{
  "itemnum": "5975-60-V00-0529",
  "siteid": "LCVKWT",
  "location": "RIP001",
  "transfercuritem": [
    {
      "itemnum": "5975-60-V00-0529",
      "fromsiteid": "LCVKWT",
      "tositeid": "IKWAJ",
      "fromlocation": "RIP001",
      "tolocation": "KWAJ-1058",
      "quantity": 1.0,
      "fromissueunit": "RO",
      "toissueunit": "RO",
      "frombinnum": "28-800-0004",
      "fromlotnum": "TEST",
      "tolotnum": "TEST",
      "fromconditioncode": "A1",
      "toconditioncode": "A1"
    }
  ]
}'

echo ""
echo "1️⃣ Testing Object Structure Approach (Current Implementation)"
echo "=============================================================="
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinventory"
echo "Payload: Object Structure with transfercuritem child"
echo ""
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$OBJECT_PAYLOAD" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -v 2>&1 | head -50

echo ""
echo "2️⃣ Testing MXAPITRANSFER Endpoint"
echo "=================================="
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapitransfer"
echo "Payload: Direct transfer fields"
echo ""
curl -X POST "$MAXIMO_BASE/oslc/os/mxapitransfer" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -v 2>&1 | head -50

echo ""
echo "3️⃣ Testing MXAPIINVTRANS Endpoint"
echo "=================================="
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinvtrans"
echo "Payload: Direct transfer fields"
echo ""
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinvtrans" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -v 2>&1 | head -50

echo ""
echo "4️⃣ Testing Action Parameter Approach"
echo "====================================="
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem"
echo "Payload: Direct transfer fields"
echo ""
curl -X POST "$MAXIMO_BASE/oslc/os/mxapiinventory?action=TransferCurrentItem" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$PAYLOAD" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -v 2>&1 | head -50

echo ""
echo "5️⃣ Testing MATRECTRANS Endpoint (Material Transaction)"
echo "======================================================"
echo "Endpoint: $MAXIMO_BASE/oslc/os/mxapimatrectrans"
echo "Payload: Material transaction format"
echo ""

MATRECTRANS_PAYLOAD='{
  "itemnum": "5975-60-V00-0529",
  "fromsiteid": "LCVKWT",
  "tositeid": "IKWAJ",
  "fromlocation": "RIP001",
  "tolocation": "KWAJ-1058",
  "quantity": 1.0,
  "issuetype": "TRANSFER",
  "transdate": "'$(date -u +"%Y-%m-%dT%H:%M:%S+00:00")'"
}'

curl -X POST "$MAXIMO_BASE/oslc/os/mxapimatrectrans" \
  $HEADERS \
  --cookie-jar cookies.txt --cookie cookies.txt \
  -d "$MATRECTRANS_PAYLOAD" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -v 2>&1 | head -50

echo ""
echo "✅ Detailed testing completed!"
echo ""
echo "📋 Analysis Notes:"
echo "- Look for HTTP status codes (200, 201 = success, 400 = bad request, 401 = auth, 404 = not found)"
echo "- Check if any endpoint accepts the request without authentication errors"
echo "- Identify which endpoint structure matches Maximo's expected format"
echo "- The correct endpoint should return JSON with transfer details or success confirmation"
