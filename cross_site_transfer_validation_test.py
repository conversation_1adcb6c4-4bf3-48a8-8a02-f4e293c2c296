#!/usr/bin/env python3
"""
Cross-Site Transfer Validation Test
===================================

Test cross-site transfers (LCVKWT → IKWAJ) by modifying payload structure
to work with Maximo's validation logic using the confirmed working same-site
pattern as baseline.

Hypothesis: Top-level inventory record site context determines validation scope.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import requests
import time
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

class CrossSiteTransferTester:
    """Test cross-site transfers with different payload structures."""
    
    def __init__(self):
        self.base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
        self.flask_endpoint = "http://127.0.0.1:5010/api/inventory/transfer-current-item"
        self.api_endpoint = f"{self.base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
        
        self.token_manager = None
        self.test_results = []
        self.successful_patterns = []
        
    def initialize_authentication(self):
        """Initialize authentication for direct API testing."""
        try:
            self.token_manager = MaximoTokenManager(self.base_url)
            if self.token_manager.is_logged_in():
                print("✅ Direct API authentication available")
                return True
            else:
                print("⚠️  Direct API authentication not available, using Flask app only")
                return False
        except Exception as e:
            print(f"⚠️  Direct API authentication failed: {str(e)}")
            return False
    
    def test_via_flask_app(self, test_name, payload):
        """Test transfer via Flask application."""
        print(f"\n🧪 Testing via Flask App: {test_name}")
        print("=" * 70)
        
        try:
            response = requests.post(
                self.flask_endpoint,
                json=payload,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=30
            )
            
            print(f"📊 HTTP Status: {response.status_code}")
            
            if response.text:
                try:
                    data = response.json()
                    print(f"📋 Response: {json.dumps(data, indent=2)}")
                    
                    # Check for success
                    if response.status_code == 200 and data.get('success'):
                        response_list = data.get('response', [])
                        if response_list and len(response_list) > 0:
                            first_response = response_list[0]
                            if first_response.get('_responsemeta', {}).get('status') == '204':
                                print("🎉 SUCCESS! Cross-site transfer worked!")
                                return True, data
                            else:
                                error_data = first_response.get('_responsedata', {})
                                if 'Error' in error_data:
                                    error = error_data['Error']
                                    print(f"❌ Business Logic Error: {error.get('reasonCode')} - {error.get('message')}")
                                else:
                                    print(f"⚠️  Unexpected response structure")
                    
                    return False, data
                    
                except json.JSONDecodeError:
                    print(f"📄 Non-JSON Response: {response.text}")
                    return False, response.text
            
            return False, None
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False, str(e)
    
    def test_direct_api(self, test_name, payload):
        """Test transfer via direct API call."""
        if not self.token_manager:
            return False, "No authentication available"
            
        print(f"\n🔧 Testing via Direct API: {test_name}")
        print("=" * 70)
        
        try:
            response = self.token_manager.session.post(
                self.api_endpoint,
                json=payload,
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "x-method-override": "BULK"
                },
                timeout=(5.0, 30)
            )
            
            print(f"📊 HTTP Status: {response.status_code}")
            
            if response.text:
                try:
                    data = response.json()
                    print(f"📋 Response: {json.dumps(data, indent=2)}")
                    
                    # Check for success
                    if response.status_code == 200:
                        if isinstance(data, list) and len(data) > 0:
                            first_item = data[0]
                            if first_item.get('_responsemeta', {}).get('status') == '204':
                                print("🎉 SUCCESS! Direct API cross-site transfer worked!")
                                return True, data
                    
                    return False, data
                    
                except json.JSONDecodeError:
                    print(f"📄 Non-JSON Response: {response.text}")
                    return False, response.text
            
            return False, None
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False, str(e)
    
    def run_comprehensive_test(self):
        """Run comprehensive cross-site transfer tests."""
        print("🚀 CROSS-SITE TRANSFER VALIDATION TEST")
        print("=" * 50)
        print("🎯 Objective: Enable LCVKWT → IKWAJ transfers")
        print("📋 Strategy: Test different payload structures")
        print("✅ Baseline: Confirmed working same-site pattern")
        print("")
        
        # Initialize authentication
        has_direct_api = self.initialize_authentication()
        
        # Test cases based on your specifications
        test_cases = [
            {
                "name": "Approach A: Destination Site as Top-Level (IKWAJ)",
                "description": "Top-level record in destination site, transfer from LCVKWT to IKWAJ",
                "flask_payload": {
                    "itemnum": "5975-60-V00-0529",
                    "from_siteid": "LCVKWT",
                    "to_siteid": "IKWAJ",
                    "from_storeroom": "RIP001",
                    "to_storeroom": "KWAJ-1058",
                    "quantity": 1.0,
                    "from_issue_unit": "RO",
                    "from_bin": "DEFAULT",
                    "to_bin": "DEFAULT",
                    "from_lot": "DEFAULT",
                    "to_lot": "DEFAULT",
                    "from_condition": "A1",
                    "to_condition": "A1",
                    "top_level_site": "IKWAJ"  # Hint for service to use destination site
                },
                "direct_payload": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "itemsetid": "ITEMSET",
                        "siteid": "IKWAJ",
                        "location": "KWAJ-1058",
                        "issueunit": "RO",
                        "matrectrans": [
                            {
                                "_action": "AddChange",
                                "itemnum": "5975-60-V00-0529",
                                "issuetype": "TRANSFER",
                                "quantity": 1.0,
                                "fromsiteid": "LCVKWT",
                                "tositeid": "IKWAJ",
                                "fromstoreloc": "RIP001",
                                "tostoreloc": "KWAJ-1058",
                                "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                                "issueunit": "RO",
                                "frombinnum": "DEFAULT",
                                "tobinnum": "DEFAULT",
                                "fromlotnum": "DEFAULT",
                                "tolotnum": "DEFAULT",
                                "fromconditioncode": "A1",
                                "toconditioncode": "A1"
                            }
                        ]
                    }
                ]
            },
            {
                "name": "Approach B: Source Site as Top-Level (LCVKWT)",
                "description": "Top-level record in source site, transfer from LCVKWT to IKWAJ",
                "flask_payload": {
                    "itemnum": "5975-60-V00-0529",
                    "from_siteid": "LCVKWT",
                    "to_siteid": "IKWAJ",
                    "from_storeroom": "RIP001",
                    "to_storeroom": "KWAJ-1058",
                    "quantity": 1.0,
                    "from_issue_unit": "RO",
                    "from_bin": "DEFAULT",
                    "to_bin": "DEFAULT",
                    "from_lot": "DEFAULT",
                    "to_lot": "DEFAULT",
                    "from_condition": "A1",
                    "to_condition": "A1",
                    "top_level_site": "LCVKWT"  # Hint for service to use source site
                },
                "direct_payload": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "itemsetid": "ITEMSET",
                        "siteid": "LCVKWT",
                        "location": "RIP001",
                        "issueunit": "RO",
                        "matrectrans": [
                            {
                                "_action": "AddChange",
                                "itemnum": "5975-60-V00-0529",
                                "issuetype": "TRANSFER",
                                "quantity": 1.0,
                                "fromsiteid": "LCVKWT",
                                "tositeid": "IKWAJ",
                                "fromstoreloc": "RIP001",
                                "tostoreloc": "KWAJ-1058",
                                "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                                "issueunit": "RO",
                                "frombinnum": "DEFAULT",
                                "tobinnum": "DEFAULT",
                                "fromlotnum": "DEFAULT",
                                "tolotnum": "DEFAULT",
                                "fromconditioncode": "A1",
                                "toconditioncode": "A1"
                            }
                        ]
                    }
                ]
            },
            {
                "name": "Approach C: Minimal Cross-Site (Destination Context)",
                "description": "Minimal fields with destination site context",
                "flask_payload": {
                    "itemnum": "5975-60-V00-0529",
                    "from_siteid": "LCVKWT",
                    "to_siteid": "IKWAJ",
                    "from_storeroom": "RIP001",
                    "to_storeroom": "KWAJ-1058",
                    "quantity": 1.0,
                    "from_issue_unit": "RO"
                },
                "direct_payload": [
                    {
                        "_action": "AddChange",
                        "itemnum": "5975-60-V00-0529",
                        "siteid": "IKWAJ",
                        "location": "KWAJ-1058",
                        "matrectrans": [
                            {
                                "_action": "AddChange",
                                "itemnum": "5975-60-V00-0529",
                                "issuetype": "TRANSFER",
                                "quantity": 1.0,
                                "fromsiteid": "LCVKWT",
                                "tositeid": "IKWAJ",
                                "fromstoreloc": "RIP001",
                                "tostoreloc": "KWAJ-1058"
                            }
                        ]
                    }
                ]
            }
        ]
        
        # Run tests
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔬 TEST CASE {i}: {test_case['name']}")
            print(f"📝 {test_case['description']}")
            print("=" * 80)
            
            # Test via Flask app
            flask_success, flask_response = self.test_via_flask_app(
                f"Flask - {test_case['name']}", 
                test_case['flask_payload']
            )
            
            # Test via direct API if available
            direct_success = False
            direct_response = None
            if has_direct_api:
                direct_success, direct_response = self.test_direct_api(
                    f"Direct API - {test_case['name']}", 
                    test_case['direct_payload']
                )
            
            # Record results
            result = {
                'test_case': test_case['name'],
                'flask_success': flask_success,
                'flask_response': flask_response,
                'direct_success': direct_success,
                'direct_response': direct_response,
                'timestamp': datetime.now().isoformat()
            }
            
            self.test_results.append(result)
            
            # Save successful patterns
            if flask_success:
                self.successful_patterns.append({
                    'method': 'flask',
                    'test_case': test_case['name'],
                    'payload': test_case['flask_payload'],
                    'response': flask_response
                })
                
                # Save to file
                filename = f"successful_cross_site_flask_{i}.json"
                with open(filename, 'w') as f:
                    json.dump(test_case['flask_payload'], f, indent=2)
                print(f"💾 Successful Flask payload saved: {filename}")
            
            if direct_success:
                self.successful_patterns.append({
                    'method': 'direct_api',
                    'test_case': test_case['name'],
                    'payload': test_case['direct_payload'],
                    'response': direct_response
                })
                
                # Save to file
                filename = f"successful_cross_site_direct_{i}.json"
                with open(filename, 'w') as f:
                    json.dump(test_case['direct_payload'], f, indent=2)
                print(f"💾 Successful Direct API payload saved: {filename}")
            
            # Brief pause between tests
            time.sleep(2)
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate comprehensive test summary."""
        print(f"\n📊 CROSS-SITE TRANSFER TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        successful_tests = len(self.successful_patterns)
        
        print(f"🔬 Total test cases: {total_tests}")
        print(f"✅ Successful patterns: {successful_tests}")
        print(f"❌ Failed patterns: {total_tests - successful_tests}")
        
        if self.successful_patterns:
            print(f"\n🎉 SUCCESSFUL CROSS-SITE TRANSFER PATTERNS FOUND!")
            print("=" * 60)
            
            for i, pattern in enumerate(self.successful_patterns, 1):
                print(f"\n✅ Pattern {i}: {pattern['test_case']}")
                print(f"   Method: {pattern['method']}")
                print(f"   Payload: {json.dumps(pattern['payload'], indent=4)[:200]}...")
            
            # Save all successful patterns
            with open('all_successful_cross_site_patterns.json', 'w') as f:
                json.dump(self.successful_patterns, f, indent=2)
            print(f"\n💾 All successful patterns saved: all_successful_cross_site_patterns.json")
            
            # Generate curl commands
            self.generate_curl_commands()
            
        else:
            print(f"\n❌ NO SUCCESSFUL CROSS-SITE PATTERNS FOUND")
            print("=" * 50)
            print("📋 Analysis of failures:")
            
            for result in self.test_results:
                print(f"\n• {result['test_case']}: Failed")
                if result['flask_response']:
                    # Try to extract error message
                    if isinstance(result['flask_response'], dict):
                        response_list = result['flask_response'].get('response', [])
                        if response_list and len(response_list) > 0:
                            error_data = response_list[0].get('_responsedata', {})
                            if 'Error' in error_data:
                                error = error_data['Error']
                                print(f"  Error: {error.get('reasonCode')} - {error.get('message')}")
        
        # Save complete test results
        with open('cross_site_transfer_test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\n💾 Complete test results saved: cross_site_transfer_test_results.json")
    
    def generate_curl_commands(self):
        """Generate curl commands for successful patterns."""
        print(f"\n🌐 CURL COMMANDS FOR SUCCESSFUL PATTERNS")
        print("=" * 50)
        
        for i, pattern in enumerate(self.successful_patterns, 1):
            if pattern['method'] == 'flask':
                print(f"\n# Pattern {i}: {pattern['test_case']}")
                print(f"curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\")
                print(f"  -H \"Content-Type: application/json\" \\")
                print(f"  -H \"Accept: application/json\" \\")
                print(f"  -d '{json.dumps(pattern['payload'], separators=(',', ':'))}' \\")
                print(f"  -w \"\\nHTTP_STATUS:%{{http_code}}\\nTIME:%{{time_total}}\\n\" \\")
                print(f"  -s")
                
                # Save individual curl script
                curl_filename = f"curl_cross_site_pattern_{i}.sh"
                with open(curl_filename, 'w') as f:
                    f.write("#!/bin/bash\n\n")
                    f.write(f"# {pattern['test_case']}\n")
                    f.write(f"# Cross-site transfer: LCVKWT → IKWAJ\n\n")
                    f.write(f"curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\\n")
                    f.write(f"  -H \"Content-Type: application/json\" \\\n")
                    f.write(f"  -H \"Accept: application/json\" \\\n")
                    f.write(f"  -d '{json.dumps(pattern['payload'], separators=(',', ':'))}' \\\n")
                    f.write(f"  -w \"\\nHTTP_STATUS:%{{http_code}}\\nTIME:%{{time_total}}\\n\" \\\n")
                    f.write(f"  -s\n")
                
                print(f"💾 Curl script saved: {curl_filename}")

def main():
    """Main function."""
    tester = CrossSiteTransferTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
