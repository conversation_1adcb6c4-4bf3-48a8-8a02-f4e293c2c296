#!/usr/bin/env python3
"""
Test what data the frontend is receiving
"""

import requests
import json
from datetime import datetime

TARGET_ITEM = "8010-60-V00-0113"
TARGET_SITE = "LCVKWT"

def test_api_response():
    """Test the exact API response that the frontend receives."""
    print(f"🔍 Testing API Response for Frontend")
    print("=" * 60)
    
    try:
        response = requests.get(
            f"http://127.0.0.1:5010/api/inventory/availability/{TARGET_ITEM}",
            params={'siteid': TARGET_SITE},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Save the full response to a file for inspection
            with open('api_response_debug.json', 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            print(f"✅ Full response saved to 'api_response_debug.json'")
            
            if data.get('success'):
                print(f"\n📊 Response Structure:")
                for key, value in data.items():
                    if isinstance(value, list):
                        print(f"  {key}: list with {len(value)} items")
                    elif isinstance(value, dict):
                        print(f"  {key}: dict with keys: {list(value.keys())}")
                    else:
                        print(f"  {key}: {type(value).__name__}")
                
                # Focus on purchase orders
                po_data = data.get('purchase_orders', [])
                print(f"\n🛒 Purchase Orders Analysis:")
                print(f"  Type: {type(po_data)}")
                print(f"  Length: {len(po_data) if po_data else 'None'}")
                
                if po_data and len(po_data) > 0:
                    print(f"  First PO keys: {list(po_data[0].keys()) if isinstance(po_data[0], dict) else 'Not a dict'}")
                    print(f"  First PO sample:")
                    print(f"    PO Number: {po_data[0].get('ponum', 'N/A')}")
                    print(f"    Status: {po_data[0].get('status', 'N/A')}")
                    print(f"    Site: {po_data[0].get('siteid', 'N/A')}")
                    
                    # Check all statuses
                    statuses = {}
                    for po in po_data:
                        status = po.get('status', 'UNKNOWN')
                        statuses[status] = statuses.get(status, 0) + 1
                    
                    print(f"  Status breakdown:")
                    for status, count in sorted(statuses.items()):
                        print(f"    {status}: {count}")
                else:
                    print(f"  ❌ No purchase orders in response!")
                
                # Check summary
                summary = data.get('availability_summary', {})
                print(f"\n📈 Summary:")
                print(f"  Total POs: {summary.get('total_purchase_orders', 0)}")
                print(f"  Total PRs: {summary.get('total_purchase_requisitions', 0)}")
                print(f"  Total Reservations: {summary.get('total_reservations', 0)}")
                
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Testing Frontend Data for Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_api_response()
    
    print(f"\n{'='*60}")
    print("🏁 Test Complete")
    print(f"{'='*60}")
    print("\n💡 Next Steps:")
    print("1. Check 'api_response_debug.json' for full response")
    print("2. Compare with what frontend JavaScript receives")
    print("3. Check browser console for debug messages")
