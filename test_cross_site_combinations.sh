#!/bin/bash

# Test Cross-Site Combinations - Find Working 204 Patterns
# =========================================================

echo "🚀 CROSS-SITE COMBINATION TESTING - FIND WORKING 204 PATTERNS"
echo "=============================================================="

echo "🎯 OBJECTIVE: Test multiple cross-site combinations to find 204 success patterns"
echo "📋 STRATEGY: Test different storeroom combinations, units, and payload structures"
echo "🔍 ANALYSIS: Watch Flask terminal for exact payload/response details"
echo ""

echo "⚠️  IMPORTANT: Before running this test:"
echo "1. Go to http://127.0.0.1:5010"
echo "2. Login with your Maximo credentials"
echo "3. Keep the Flask terminal open to see detailed payload/response logs"
echo "4. This will test multiple combinations systematically"
echo ""

read -p "Have you logged in to the Flask app? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please login first at http://127.0.0.1:5010"
    exit 1
fi

# Counter for tracking results
SUCCESS_COUNT=0
ERROR_COUNT=0
TEST_COUNT=0

# Function to test cross-site transfer and analyze response
test_cross_site_combination() {
    local test_name="$1"
    local endpoint="$2"
    local payload="$3"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo ""
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    echo "🔗 Endpoint: $endpoint"
    echo ""
    echo "📋 PAYLOAD:"
    echo "$payload" | jq '.' 2>/dev/null || echo "$payload"
    echo ""
    
    echo "🔄 Submitting to Flask application..."
    echo "👀 WATCH FLASK TERMINAL FOR DETAILED PAYLOAD AND MAXIMO RESPONSE!"
    echo ""
    
    response=$(curl -X POST "http://127.0.0.1:5010$endpoint" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 RESPONSE:"
    echo "$response"
    echo ""
    
    # Analyze response
    if echo "$response" | grep -q '"success": true'; then
        if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
            echo "🎉 SUCCESS! 204 STATUS ACHIEVED!"
            echo "✅ This combination works - saving pattern"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            echo "$payload" > "working_cross_site_pattern_$TEST_COUNT.json"
            echo "💾 Saved to: working_cross_site_pattern_$TEST_COUNT.json"
            return 0
        else
            echo "✅ SUCCESS! Transfer accepted (non-204 success)"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            return 0
        fi
    elif echo "$response" | grep -q '"success": false'; then
        echo "❌ FAILED! Analyzing error..."
        ERROR_COUNT=$((ERROR_COUNT + 1))
        
        if echo "$response" | grep -q 'BMXAA1861E'; then
            echo "📋 BMXAA1861E: Duplicate location/item/bin/lot combination"
        elif echo "$response" | grep -q 'BMXAA2694E'; then
            echo "📋 BMXAA2694E: Location does not exist in site"
        elif echo "$response" | grep -q 'BMXAA1785E'; then
            echo "📋 BMXAA1785E: Unit conversion error"
        else
            echo "📋 Other error - check response above"
        fi
        return 1
    elif echo "$response" | grep -q "401"; then
        echo "🔐 AUTHENTICATION FAILED - Please login at http://127.0.0.1:5010"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
}

echo "🚀 TESTING CROSS-SITE COMBINATIONS"
echo "=================================="

# Test 1: Cross-Site with CMW-AJ → KWAJ-1058 (Destination Context)
test_cross_site_combination "Cross-Site: CMW-AJ → KWAJ-1058 (Destination Context)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 2: Cross-Site with CMW-AJ → CMW-BU (Both LCVKWT storerooms)
test_cross_site_combination "Cross-Site: CMW-AJ → CMW-BU (Both LCVKWT storerooms)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-BU",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 3: Cross-Site with Same Units (RO to RO)
test_cross_site_combination "Cross-Site: CMW-AJ → KWAJ-1058 (RO to RO)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 4: Cross-Site with Small Quantity
test_cross_site_combination "Cross-Site: CMW-AJ → KWAJ-1058 (Small Quantity)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 0.1,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 5: Cross-Site with CMW-AJH → KWAJ-1058
test_cross_site_combination "Cross-Site: CMW-AJH → KWAJ-1058" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJH",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 6: Cross-Site with CMW-BU → KWAJ-1115
test_cross_site_combination "Cross-Site: CMW-BU → KWAJ-1115" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-BU",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 7: Original Endpoint with Auto-Detection (Cross-Site)
test_cross_site_combination "Original Endpoint: Auto-Detection (Cross-Site)" \
    "/api/inventory/transfer-current-item" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 8: Destination Context Endpoint (Your Requested Structure)
test_cross_site_combination "Destination Context: CMW-AJ → KWAJ-1058" \
    "/api/inventory/transfer-current-item-destination-context" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 9: Cross-Site with EA Units
test_cross_site_combination "Cross-Site: CMW-AJ → KWAJ-1058 (EA to EA)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "EA",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 10: Cross-Site with Different Item (if available)
test_cross_site_combination "Cross-Site: Different Item (if available)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 2.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

echo ""
echo "📊 CROSS-SITE COMBINATION TEST SUMMARY"
echo "======================================"
echo "✅ Successful transfers (including 204): $SUCCESS_COUNT"
echo "❌ Failed transfers: $ERROR_COUNT"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found working cross-site patterns!"
    echo "============================================="
    echo "💾 Check working_cross_site_pattern_*.json files for successful combinations"
    echo ""
    echo "📋 Working patterns found:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "working_cross_site_pattern_$i.json" ]; then
            echo "✅ Test $i: Working pattern saved"
        fi
    done
    
    echo ""
    echo "🎯 ANALYSIS POINTS TO CHECK IN FLASK TERMINAL:"
    echo "• Which payload structure achieved 204 status?"
    echo "• What site context was used (source vs destination)?"
    echo "• Which storeroom combinations work?"
    echo "• What unit combinations are successful?"
    echo "• Are bin/lot fields included or excluded?"
    
else
    echo ""
    echo "❌ No successful cross-site transfers found"
    echo "🔄 Check Flask application logs for detailed error analysis"
    echo ""
    echo "💡 TROUBLESHOOTING STEPS:"
    echo "• Check authentication status"
    echo "• Verify storeroom existence in both sites"
    echo "• Check unit conversion setup in Maximo"
    echo "• Verify item availability in source location"
fi

echo ""
echo "🔍 DETAILED ANALYSIS INSTRUCTIONS:"
echo "================================="
echo "1. 📋 Check Flask terminal for EXACT PAYLOAD structures"
echo "2. 🎯 Look for 204 status responses in Maximo responses"
echo "3. 📊 Compare successful vs failed payload differences"
echo "4. 🔧 Note which site context (source/destination) works"
echo "5. 💾 Use successful patterns for UI implementation"
echo ""
echo "🎯 NEXT STEPS:"
echo "• Analyze Flask logs to identify successful payload patterns"
echo "• Update cross-site implementation with working structure"
echo "• Test successful patterns in the UI"
echo "• Document working combinations for future use"
