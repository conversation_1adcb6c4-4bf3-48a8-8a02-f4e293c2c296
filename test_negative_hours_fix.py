#!/usr/bin/env python3
"""
Test script to verify the negative hours fix
"""

import requests
import json
import time

def test_negative_hours_api():
    """Test the negative hours API with proper parameters"""
    
    print("🧪 Testing Negative Hours API Fix")
    print("=" * 50)
    
    # Test data with all required parameters
    test_data = {
        "laborcode": "TINU.THOMAS",
        "negative_hours": -0.1,  # Small negative amount for testing
        "siteid": "LCVKWT",
        "taskid": 10,
        "parent_wonum": "2219753",
        "craft": "MATCTRLSPCSR"
    }
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    print(f"🔧 URL: {url}")
    print(f"📋 Test Data:")
    for key, value in test_data.items():
        print(f"  - {key}: {value} (type: {type(value).__name__})")
    
    try:
        print(f"\n🚀 Making API request...")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ Response JSON: {json.dumps(result, indent=2)}")
                
                if result.get('success'):
                    print(f"🎉 SUCCESS: Negative hours added successfully!")
                    return True
                else:
                    print(f"❌ API Error: {result.get('error', 'Unknown error')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON Decode Error: {e}")
                print(f"📄 Raw Response: {response.text}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def test_missing_parameters():
    """Test API behavior with missing parameters"""
    
    print(f"\n🧪 Testing Missing Parameters Handling")
    print("-" * 30)
    
    # Test cases with missing parameters
    test_cases = [
        {
            "name": "Missing taskid",
            "data": {
                "laborcode": "TINU.THOMAS",
                "negative_hours": -0.1,
                "siteid": "LCVKWT",
                "parent_wonum": "2219753",
                "craft": "MATCTRLSPCSR"
                # taskid missing
            }
        },
        {
            "name": "Missing parent_wonum",
            "data": {
                "laborcode": "TINU.THOMAS",
                "negative_hours": -0.1,
                "siteid": "LCVKWT",
                "taskid": 10,
                "craft": "MATCTRLSPCSR"
                # parent_wonum missing
            }
        },
        {
            "name": "Missing siteid",
            "data": {
                "laborcode": "TINU.THOMAS",
                "negative_hours": -0.1,
                "taskid": 10,
                "parent_wonum": "2219753",
                "craft": "MATCTRLSPCSR"
                # siteid missing
            }
        }
    ]
    
    task_wonum = "2219754"
    url = f"http://127.0.0.1:5010/api/task/{task_wonum}/add-negative-labor"
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('success'):
                    print(f"✅ Correctly rejected: {result.get('error', 'Unknown error')}")
                else:
                    print(f"❌ Unexpectedly succeeded: {result}")
            else:
                print(f"✅ Correctly rejected with HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing {test_case['name']}: {e}")

def test_frontend_data_attributes():
    """Test that the frontend has proper data attributes"""
    
    print(f"\n🧪 Testing Frontend Data Attributes")
    print("-" * 30)
    
    # Test the workorder detail page
    wonum = "2219753"
    url = f"http://127.0.0.1:5010/workorder/{wonum}"
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            html_content = response.text
            
            # Check for data attributes in the HTML
            data_attributes = [
                'data-task-id=',
                'data-site-id=',
                'data-task-wonum=',
                'data-parent-wonum='
            ]
            
            print(f"🔍 Checking for data attributes in HTML...")
            for attr in data_attributes:
                if attr in html_content:
                    print(f"✅ Found: {attr}")
                else:
                    print(f"❌ Missing: {attr}")
            
            # Check for the deleteLaborEntry function
            if 'function deleteLaborEntry(' in html_content:
                print(f"✅ Found: deleteLaborEntry function")
            else:
                print(f"❌ Missing: deleteLaborEntry function")
            
            # Check for the labor negative hours manager
            if 'LaborNegativeHoursManager' in html_content:
                print(f"✅ Found: LaborNegativeHoursManager")
            else:
                print(f"❌ Missing: LaborNegativeHoursManager")
                
        else:
            print(f"❌ Failed to load workorder page: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing frontend: {e}")

def main():
    """Run all tests"""
    
    print("🚀 Negative Hours Fix Test Suite")
    print("=" * 60)
    
    # Test 1: API with proper parameters
    api_success = test_negative_hours_api()
    
    # Test 2: Missing parameters handling
    test_missing_parameters()
    
    # Test 3: Frontend data attributes
    test_frontend_data_attributes()
    
    print(f"\n" + "=" * 60)
    if api_success:
        print("🎉 OVERALL RESULT: Tests completed - API is working!")
    else:
        print("❌ OVERALL RESULT: API test failed - check the logs above")
    
    print("\n📋 Next Steps:")
    print("1. Open http://127.0.0.1:5010/workorder/ in browser")
    print("2. Navigate to a work order with tasks")
    print("3. Load labor for a task")
    print("4. Click 'Subtract Hours' button")
    print("5. Check browser console for debug logs")

if __name__ == "__main__":
    main()
