#!/usr/bin/env python3
"""
Test MXAPIINVENTORY with MATRECTRANS using Flask app's authenticated session
"""

import requests
import json
import sys
import os

def test_matrectrans_via_flask():
    """Test the new MXAPIINVENTORY with MATRECTRANS approach via Flask"""
    
    print("🔧 Testing MXAPIINVENTORY with MATRECTRANS via Flask App")
    print("=" * 60)
    
    # Flask app base URL
    flask_base = "http://127.0.0.1:5010"
    
    # Test data from terminal logs
    transfer_data = {
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "RIP001",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_bin": "28-800-0004",
        "to_bin": "1058-TEMP",
        "from_lot": "TEST",
        "to_lot": "TEST",
        "from_condition": "A1",
        "to_condition": "A1",
        "conversion_factor": 1.0
    }
    
    print(f"Transfer Data: {json.dumps(transfer_data, indent=2)}")
    
    # Test the Flask endpoint with new MXAPIINVENTORY + MATRECTRANS approach
    try:
        print(f"\n🔧 Testing Flask Transfer Endpoint (MXAPIINVENTORY + MATRECTRANS)")
        print(f"URL: {flask_base}/api/inventory/transfer-current-item")
        
        response = requests.post(
            f"{flask_base}/api/inventory/transfer-current-item",
            json=transfer_data,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ SUCCESS")
                print(f"Response: {json.dumps(response_data, indent=2)}")
                return True
            except:
                print(f"✅ SUCCESS (non-JSON response)")
                print(f"Response: {response.text}")
                return True
                
        elif response.status_code == 400:
            try:
                error_data = response.json()
                print(f"❌ BAD REQUEST")
                print(f"Error: {json.dumps(error_data, indent=2)}")
                
                # Check if it's still the old MXAPIMATRECTRANS error
                error_msg = error_data.get('error', '')
                if 'MXAPIMATRECTRANS does not exist' in str(error_msg):
                    print(f"\n⚠️ Still getting old MXAPIMATRECTRANS error - service not updated yet")
                    return False
                elif 'MATRECTRANS' in str(error_msg):
                    print(f"\n🎯 New MATRECTRANS-related error - service updated!")
                    return False
                else:
                    print(f"\n🔍 Different error - need to investigate")
                    return False
            except:
                print(f"❌ BAD REQUEST (non-JSON error)")
                print(f"Error: {response.text}")
                return False
                
        else:
            print(f"⚠️ OTHER STATUS: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 MXAPIINVENTORY + MATRECTRANS Testing via Flask")
    print("=" * 60)
    
    # Test current implementation
    success = test_matrectrans_via_flask()
    
    if success:
        print(f"\n🎉 SUCCESS: MXAPIINVENTORY + MATRECTRANS approach works!")
    else:
        print(f"\n⚠️ FAILED: Need to debug the MXAPIINVENTORY + MATRECTRANS approach")
    
    print(f"\n✅ Testing completed!")
