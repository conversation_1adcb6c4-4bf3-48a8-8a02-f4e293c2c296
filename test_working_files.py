#!/usr/bin/env python3
"""
Test script to focus on files that should work and understand the pattern
"""

import requests
import json

def test_working_files():
    """Test files that we know should work to understand the pattern"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing files that should work for {wonum}")
    print("=" * 60)
    
    # Get attachments first
    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
    
    try:
        response = requests.get(attachments_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('attachments'):
                attachments = data['attachments']
                
                # Find files that should work (recent uploads by SOFG118757)
                working_files = []
                for attachment in attachments:
                    changeby = attachment.get('changeby', '')
                    filename = attachment.get('filename', '')
                    size = attachment.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
                    
                    # Look for our recent uploads or files with non-zero size
                    if (changeby == 'SOFG118757' or size > 0):
                        working_files.append(attachment)
                
                print(f"📋 Found {len(working_files)} files that should work:")
                for i, file in enumerate(working_files):
                    size = file.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
                    print(f"   {i+1}. {file.get('filename')} (ID: {file.get('docinfoid')}) - Size: {size} - By: {file.get('changeby')}")
                
                # Test downloading each working file
                for i, test_file in enumerate(working_files):
                    docinfoid = test_file.get('docinfoid')
                    filename = test_file.get('filename')
                    expected_size = test_file.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
                    
                    print(f"\n📥 Test {i+1}: Downloading {filename} (ID: {docinfoid})")
                    print(f"   Expected size: {expected_size} bytes")
                    
                    download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
                    
                    try:
                        download_response = requests.get(download_url, timeout=60)
                        print(f"   📤 Download URL: {download_url}")
                        print(f"   🔄 Status: {download_response.status_code}")
                        print(f"   📊 Content Length: {len(download_response.content)} bytes")
                        print(f"   📋 Content Type: {download_response.headers.get('content-type', 'Unknown')}")
                        
                        if download_response.status_code == 200:
                            content = download_response.content
                            
                            # Check if it's HTML error page
                            if content.startswith(b'<!doctype html') or content.startswith(b'<html'):
                                print(f"   ❌ Got HTML error page")
                            # Check file type
                            elif content.startswith(b'%PDF'):
                                print(f"   ✅ SUCCESS! Got PDF content")
                                # Save the PDF
                                test_filename = f"downloaded_{filename}"
                                with open(test_filename, 'wb') as f:
                                    f.write(content)
                                print(f"   💾 Saved PDF to: {test_filename}")
                            elif filename.lower().endswith('.png') and content.startswith(b'\x89PNG'):
                                print(f"   ✅ SUCCESS! Got PNG image content")
                                # Save the image
                                test_filename = f"downloaded_{filename}"
                                with open(test_filename, 'wb') as f:
                                    f.write(content)
                                print(f"   💾 Saved PNG to: {test_filename}")
                            elif filename.lower().endswith('.txt'):
                                print(f"   ✅ SUCCESS! Got text content: {content.decode('utf-8', errors='ignore')[:100]}")
                                # Save the text
                                test_filename = f"downloaded_{filename}"
                                with open(test_filename, 'wb') as f:
                                    f.write(content)
                                print(f"   💾 Saved text to: {test_filename}")
                            else:
                                print(f"   ⚠️  Got unknown content type: {content[:50]}")
                                
                            # Compare sizes
                            if expected_size and expected_size > 0:
                                size_diff = abs(len(content) - expected_size)
                                if size_diff == 0:
                                    print(f"   🎉 PERFECT! Size matches exactly")
                                elif size_diff < 100:
                                    print(f"   ✅ GOOD! Size close to expected (diff: {size_diff})")
                                else:
                                    print(f"   ⚠️  Size differs significantly (diff: {size_diff})")
                        else:
                            print(f"   ❌ Download failed with status {download_response.status_code}")
                            try:
                                error_data = download_response.json()
                                print(f"   📝 Error: {error_data.get('error')}")
                            except:
                                print(f"   📝 Response: {download_response.text[:200]}")
                    
                    except Exception as e:
                        print(f"   ❌ Download exception: {e}")
                
                # Now let's analyze the URL patterns
                print(f"\n🔍 ANALYSIS: URL Patterns")
                print("=" * 40)
                
                for file in working_files:
                    docinfoid = file.get('docinfoid')
                    href = file.get('href', '')
                    original_href = file.get('original_data', {}).get('href', '')
                    identifier = file.get('original_data', {}).get('describedBy', {}).get('identifier', '')
                    
                    print(f"\n📄 {file.get('filename')}:")
                    print(f"   docinfoid: {docinfoid}")
                    print(f"   identifier: {identifier}")
                    print(f"   href: {href}")
                    print(f"   original_href: {original_href}")
                
            else:
                print(f"❌ Failed to get attachments: {data}")
        else:
            print(f"❌ Failed to get attachments: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Exception getting attachments: {e}")

if __name__ == "__main__":
    print("🧪 Testing Working Files to Understand Patterns")
    print("=" * 60)
    
    test_working_files()
    
    print("\n" + "=" * 60)
    print("🎯 Analysis Complete")
    print("\n📋 Key Findings:")
    print("1. Check which files download successfully")
    print("2. Compare URL patterns between working and failing files")
    print("3. Identify the correct URL structure for different file types")
    print("4. Implement the working pattern for all file types")
