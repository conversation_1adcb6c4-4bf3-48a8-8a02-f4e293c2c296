{"timestamp": "2025-07-16T09:30:06.362649", "base_url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo", "api_key_preview": "dj9sia0tu2...r0ahlsn70o", "soap_wsdl": {"status": "No WSDL endpoints found"}, "rest_oslc": {"API": {"basic_get": {"status": "success", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "response_structure": ["member", "href", "responseInfo"], "sample_count": 1}}, "wsmethods": {"ADDCHANGE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method ADDCHANGE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resourc..."}, "ADJUSTCURRENTBALANCE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method ADJUSTCURRENTBALANCE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"..."}, "ADJUSTPHYSICALCOUNT": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method ADJUSTPHYSICALCOUNT was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"r..."}, "TRANSFERCURITEM": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method TRANSFERCURITEM was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:r..."}, "ISSUECURITEM": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method ISSUECURITEM was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:reso..."}, "RECEIVECURITEM": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method RECEIVECURITEM was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:re..."}, "RETURNCURITEM": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method RETURNCURITEM was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:res..."}, "CREATE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method CREATE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resource\":..."}, "UPDATE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method UPDATE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resource\":..."}, "DELETE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method DELETE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resource\":..."}, "SAVE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method SAVE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resource\": \"..."}, "VALIDATE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method VALIDATE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resource..."}, "GETINVENTORY": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method GETINVENTORY was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:reso..."}, "GETBALANCE": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method GETBALANCE was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:resour..."}, "GETAVAILABILITY": {"status_code": 400, "exists": true, "response_preview": "{\n  \"oslc:Error\": {\n    \"oslc:statusCode\": \"400\",\n    \"spi:reasonCode\": \"BMXAA9372E\",\n    \"oslc:message\": \"BMXAA9372E - The action method GETAVAILABILITY was not found. Fix the name of the action to point to the right method name.\",\n    \"oslc:extendedError\": {\n      \"oslc:moreInfo\": {\n        \"rdf:r..."}}, "nested_objects": {"transfercuritem": {"endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_MTA1MzE0Ng==/transfercuritem", "get_status": 400, "get_success": false}, "invbalances": {"endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_MTA1MzE0Ng==/invbalances", "get_status": 400, "get_success": false}, "invcost": {"endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_MTA1MzE0Ng==/invcost", "get_status": 400, "get_success": false}, "invvendor": {"endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_MTA1MzE0Ng==/invvendor", "get_status": 400, "get_success": false}}}, "working_patterns": {}, "consolidated_methods": [{"name": "ADDCHANGE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:ADDCHANGE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "ADJUSTCURRENTBALANCE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:ADJUSTCURRENTBALANCE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "ADJUSTPHYSICALCOUNT", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:ADJUSTPHYSICALCOUNT", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "TRANSFERCURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:TRANSFERCURITEM", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "ISSUECURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:ISSUECURITEM", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "RECEIVECURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:RECEIVECURITEM", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "RETURNCURITEM", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:RETURNCURITEM", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "CREATE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:CREATE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "UPDATE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:UPDATE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "DELETE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:DELETE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "SAVE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:SAVE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "VALIDATE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:VALIDATE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "GETINVENTORY", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:GETINVENTORY", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "GETBALANCE", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:GETBALANCE", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}, {"name": "GETAVAILABILITY", "type": "WSMethod", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?action=wsmethod:GETAVAILABILITY", "method": "POST", "authentication": ["API Key", "Session"], "status_code": 400, "description": "Web service method for inventory operations"}]}