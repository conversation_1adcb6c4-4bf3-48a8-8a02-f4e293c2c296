#!/usr/bin/env python3
"""
Test script to validate the mobile layout fix for the availability modal.
This script checks if the critical layout issue has been resolved.
"""

import os
import re
import sys

def test_mobile_css_fixes():
    """Test if the mobile CSS fixes are properly implemented."""
    print("📱 Testing Mobile CSS Fixes...")
    
    css_file_path = "frontend/static/css/style.css"
    
    if not os.path.exists(css_file_path):
        print(f"❌ CSS file not found: {css_file_path}")
        return False
    
    try:
        with open(css_file_path, 'r') as f:
            css_content = f.read()
        
        # Check for critical mobile fixes
        critical_fixes = [
            "display: none !important;",  # Hide summary cards on mobile
            "height: calc(100vh - 120px)",  # Proper height calculation
            "overflow: hidden",  # Prevent scrolling
            "-webkit-overflow-scrolling: touch",  # Touch scrolling optimization
            "max-height: calc(100vh",  # Viewport height constraints
        ]
        
        for fix in critical_fixes:
            if fix in css_content:
                print(f"✅ Critical mobile fix found: {fix}")
            else:
                print(f"❌ Critical mobile fix missing: {fix}")
                return False
        
        # Check for mobile summary grid
        mobile_summary_checks = [
            ".mobile-summary-grid",
            "grid-template-columns: 1fr 1fr",
            ".summary-item",
            ".summary-icon",
            ".summary-content",
            ".summary-label",
            ".summary-value"
        ]
        
        for check in mobile_summary_checks:
            if check in css_content:
                print(f"✅ Mobile summary component found: {check}")
            else:
                print(f"❌ Mobile summary component missing: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking mobile CSS fixes: {str(e)}")
        return False

def test_summary_tab_integration():
    """Test if the summary tab has been properly integrated into the sidebar."""
    print("\n📊 Testing Summary Tab Integration...")
    
    js_file_path = "frontend/static/js/inventory_management.js"
    
    if not os.path.exists(js_file_path):
        print(f"❌ JavaScript file not found: {js_file_path}")
        return False
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        # Check for summary tab implementation
        summary_checks = [
            "summary-tab",
            "buildSummaryTab",
            "mobile-summary-grid",
            "summary-item",
            "chart-line",  # Summary tab icon
            "aria-selected=\"true\"",  # Summary tab should be active by default
        ]
        
        for check in summary_checks:
            if check in js_content:
                print(f"✅ Summary tab feature found: {check}")
            else:
                print(f"❌ Summary tab feature missing: {check}")
                return False
        
        # Check if summary tab is first and active
        if 'class="availability-diary-tab summary-tab active"' in js_content:
            print("✅ Summary tab is properly set as active by default")
        else:
            print("❌ Summary tab is not set as active by default")
            return False
        
        # Check if locations tab is no longer active by default
        if 'class="availability-diary-tab locations-tab active"' not in js_content:
            print("✅ Locations tab is no longer active by default")
        else:
            print("❌ Locations tab is still active by default (conflict)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking summary tab integration: {str(e)}")
        return False

def test_mobile_layout_structure():
    """Test if the mobile layout structure prevents scrolling issues."""
    print("\n🏗️ Testing Mobile Layout Structure...")
    
    js_file_path = "frontend/static/js/inventory_management.js"
    css_file_path = "frontend/static/css/style.css"
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        with open(css_file_path, 'r') as f:
            css_content = f.read()
        
        # Check for proper tab structure
        tab_structure_checks = [
            "buildSummaryTab(data)",  # Summary tab is called first
            "tab-pane fade show active",  # Summary tab has active state
            "availability-tab-panel",  # All tabs use new panel class
        ]
        
        for check in tab_structure_checks:
            if check in js_content:
                print(f"✅ Tab structure check passed: {check}")
            else:
                print(f"❌ Tab structure check failed: {check}")
                return False
        
        # Check for mobile viewport optimizations
        viewport_checks = [
            "@media (max-width: 768px)",
            "height: 100vh",
            "overflow: hidden",
            "flex-direction: column",
        ]
        
        for check in viewport_checks:
            if check in css_content:
                print(f"✅ Viewport optimization found: {check}")
            else:
                print(f"⚠️ Viewport optimization missing: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking mobile layout structure: {str(e)}")
        return False

def test_accessibility_improvements():
    """Test if accessibility improvements are in place."""
    print("\n♿ Testing Accessibility Improvements...")
    
    js_file_path = "frontend/static/js/inventory_management.js"
    css_file_path = "frontend/static/css/style.css"
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        with open(css_file_path, 'r') as f:
            css_content = f.read()
        
        # Check for accessibility features
        accessibility_checks = [
            "aria-labelledby",
            "aria-controls",
            "aria-selected",
            "role=\"tab\"",
            "role=\"tabpanel\"",
            "min-height: 44px",  # Touch target size
        ]
        
        for check in accessibility_checks:
            if check in js_content or check in css_content:
                print(f"✅ Accessibility feature found: {check}")
            else:
                print(f"❌ Accessibility feature missing: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking accessibility improvements: {str(e)}")
        return False

def main():
    """Run all tests for the mobile availability modal fix."""
    print("🧪 Testing Mobile Availability Modal Layout Fix")
    print("=" * 60)
    
    tests = [
        test_mobile_css_fixes,
        test_summary_tab_integration,
        test_mobile_layout_structure,
        test_accessibility_improvements
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All {total} tests passed! The mobile layout fix is ready.")
        print("\n📋 Critical fixes implemented:")
        print("   • Hidden summary cards on mobile to prevent obstruction")
        print("   • Added Summary tab as first tab in vertical sidebar")
        print("   • Implemented compact mobile-optimized summary grid")
        print("   • Ensured no-scroll mobile experience with proper height calculations")
        print("   • Maintained desktop functionality unchanged")
        print("   • Added proper touch targets and accessibility features")
        
        print("\n🧪 To test manually:")
        print("   1. Go to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for item '5975-60-V00-0001' or '8010-60-V00-0113'")
        print("   3. Click 'View Availability' button")
        print("   4. On mobile: Verify Summary tab is visible and accessible")
        print("   5. Test tab switching without scrolling")
        print("   6. Verify all navigation tabs are accessible")
        
        return True
    else:
        print(f"❌ {passed}/{total} tests passed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
