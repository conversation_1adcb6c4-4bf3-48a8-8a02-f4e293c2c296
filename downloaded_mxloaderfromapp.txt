2025-06-10 14:31:56 [INFO]  ------------------------------------------------------------
2025-06-10 14:31:56 [DEBUG] MxLoader started
2025-06-10 14:31:56 [DEBUG] ------------------------------------------------------------
2025-06-10 14:31:56 [DEBUG] Configuration
2025-06-10 14:31:56 [DEBUG] Operating System  : Windows (64-bit) NT 10.00
2025-06-10 14:31:56 [DEBUG] Excel version     : 16.0 - Excel 2016
2025-06-10 14:31:56 [DEBUG] Workbook Name     : MxLoader.xlsm
2025-06-10 14:31:56 [INFO]  MxLoader version  : 8.4.1
2025-06-10 14:31:56 [INFO]  Server address    : https://vectrustst01.manage.v2x.maximotest.gov2x.com
2025-06-10 14:31:56 [INFO]  Service           : REST
2025-06-10 14:31:56 [INFO]  Authentication    : API
2025-06-10 14:31:56 [DEBUG] User              : 
2025-06-10 14:31:56 [DEBUG] Proxy             : 
2025-06-10 14:31:56 [DEBUG] Proxy user        : 
2025-06-10 14:31:56 [DEBUG] Resolve Timeout   : 5000
2025-06-10 14:31:56 [DEBUG] Connect Timeout   : 10000
2025-06-10 14:31:56 [DEBUG] Send Timeout      : 10000
2025-06-10 14:31:56 [DEBUG] Receive Timeout   : 120000
2025-06-10 14:31:56 [DEBUG] OS Context Root   : /meaweb/os/
2025-06-10 14:31:56 [DEBUG] OSLC Context Root : /maximo_aw/oslc/
2025-06-10 14:31:56 [DEBUG] API Context Root  : /maximo/api/
2025-06-10 14:31:56 [DEBUG] Timezone          : 0
2025-06-10 14:31:56 [DEBUG] DateTime Format   : yyyy-mm-dd hh:mm
2025-06-10 14:31:56 [DEBUG] Date Format       : yyyy-mm-dd
2025-06-10 14:31:56 [DEBUG] Time Format       : hh:mm
2025-06-10 14:31:56 [DEBUG] Log Level         : Trace
2025-06-10 14:31:56 [DEBUG] Log file name     : [XLSDIR]\[XLSNAME].log
2025-06-10 14:31:56 [DEBUG] Full filename     : C:\Users\<USER>\OneDrive\Documents\MxLoader.log
2025-06-10 14:31:56 [DEBUG] Maximum objects   : 0
2025-06-10 14:31:56 [DEBUG] Batch size        : 1
2025-06-10 14:31:56 [DEBUG] Split child rows  : False
2025-06-10 14:31:56 [DEBUG] Start Row         : 3
2025-06-10 14:31:56 [DEBUG] Stop on error     : True
2025-06-10 14:31:56 [DEBUG] Continue sync     : True
2025-06-10 14:31:56 [DEBUG] Display summary   : True
2025-06-10 14:31:56 [INFO]  ------------------------------------------------------------
2025-06-10 14:31:56 [INFO]  Sheet name      : WO. (2)
2025-06-10 14:31:56 [INFO]  Object service  : MXAPIWODETAIL
2025-06-10 14:31:56 [INFO]  Object name     : WORKORDER
2025-06-10 14:31:56 [INFO]  Action          : AddChange
2025-06-10 14:31:56 [INFO]  Where           : WONUM="2021-1744762"
2025-06-10 14:31:56 [DEBUG] Order By        : 
2025-06-10 14:31:56 [DEBUG] OSLC Directives : 
2025-06-10 14:31:56 [DEBUG] Attr(1)         : WONUM ()
2025-06-10 14:31:56 [DEBUG] Attr(2)         : SITEID ()
2025-06-10 14:31:56 [DEBUG] Attr(3)         : WPMATERIAL.ITEMNUM ()
2025-06-10 14:31:56 [DEBUG] Attr(4)         : WPMATERIAL.ITEMQTY (DECIMAL)
2025-06-10 14:31:56 [DEBUG] Attr(5)         : WPMATERIAL.LOCATION ()
2025-06-10 14:31:56 [DEBUG] Attr(6)         : WPMATERIAL.DIRECTREQ (YORN)
2025-06-10 14:31:56 [DEBUG] Attr(7)         : WPMATERIAL.TASKID (INTEGER)
2025-06-10 14:31:56 [DEBUG] Attr(8)         : WPMATERIAL.REQUESTEDBY ()
2025-06-10 14:31:56 [INFO]  ------------------------------------------------------------
2025-06-10 14:31:56 [DEBUG] Parsing row 3
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WONUM = 2021-1744762
2025-06-10 14:31:56 [TRACE] >> WORKORDER.SITEID = LCVKWT
2025-06-10 14:31:56 [TRACE] >>>> Object name changed WORKORDER > WORKORDER.WPMATERIAL
2025-06-10 14:31:56 [TRACE] >>>> Adding current object 2021-1744762 to array WORKORDER
2025-06-10 14:31:56 [TRACE] >>>> Creating child array
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WPMATERIAL.ITEMNUM = 5975-60-V00-0394
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WPMATERIAL.ITEMQTY = 1
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WPMATERIAL.LOCATION = LCVK-CMW-AJ
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WPMATERIAL.DIRECTREQ = 0
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WPMATERIAL.TASKID = 40
2025-06-10 14:31:56 [TRACE] >> WORKORDER.WPMATERIAL.REQUESTEDBY = TINU.THOMAS
2025-06-10 14:31:56 [TRACE] >>>> Adding current object 5975-60-V00-0394 to array wpmaterial
2025-06-10 14:31:56 [DEBUG] Objects parsed from table: 1
2025-06-10 14:31:56 [DEBUG] HTTP call POST: /maximo/api/os/MXAPIWODETAIL?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange
2025-06-10 14:31:56 [DEBUG] Setting header: x-method-override=BULK
2025-06-10 14:31:56 [INFO]  HTTP POST request: https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/OSLC/os/MXAPIWODETAIL?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange
2025-06-10 14:31:56 [INFO]  HTTP request body size: 330 bytes
2025-06-10 14:31:56 [DEBUG] HTTP request body : 
[
  {
    "_action": "AddChange",
    "wonum": "2021-1744762",
    "siteid": "LCVKWT",
    "wpmaterial" : [
      {
        "itemnum": "5975-60-V00-0394",
        "itemqty": 1,
        "location": "LCVK-CMW-AJ",
        "directreq": 0,
        "taskid": 40,
        "requestedby": "TINU.THOMAS"
      }
    ]
  }
] f
2025-06-10 14:32:06 [INFO]  HTTP response received in 9.82 seconds, status=200, len=36 bytes
2025-06-10 14:32:06 [DEBUG] HTTP statusText  : OK
2025-06-10 14:32:06 [DEBUG] HTTP responseText: [{"_responsemeta":{"status":"204"}}]
2025-06-10 14:32:06 [INFO]  Parsing JSON
2025-06-10 14:32:06 [DEBUG] Parsing JSON: 
2025-06-10 14:32:06 [TRACE]   _responsemeta
2025-06-10 14:32:06 [TRACE]     status
2025-06-10 14:32:06 [DEBUG] Parsing JSON: 
2025-06-10 14:32:06 [TRACE]   _responsemeta
2025-06-10 14:32:06 [TRACE]     status
2025-06-10 14:32:06 [TRACE] Parsed response

  {
,

    {
      "STATUS": "204"
    }
  }
]
2025-06-10 14:32:06 [TRACE] >> 3 0 >> 204 - 
2025-06-10 14:32:06 [INFO]  Finished synching 1 objects in 9.86 seconds - 0.1 (objs/s)
