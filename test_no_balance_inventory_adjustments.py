#!/usr/bin/env python3
"""
Test script for the new no-balance inventory adjustment functionality.

This script tests the two new inventory management actions:
1. Physical Count Adjustment for items with no existing invbalances
2. Current Balance Adjustment for items with no existing invbalances

Usage:
    python test_no_balance_inventory_adjustments.py
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5010"
TEST_ITEM = "TEST-ITEM-001"  # Replace with actual test item
TEST_SITE = "LCVKNT"  # Replace with actual test site

def test_no_balance_physical_count():
    """Test the no-balance physical count adjustment API"""
    print("\n🔍 Testing No-Balance Physical Count Adjustment...")

    # Test with standard reason code (only physcnt field)
    payload_standard = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": "RIP001",  # This should come from inventory record
            "issueunit": "EA",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "TEST-BIN-001",
                    "physcnt": 10.0,
                    # curbal OMITTED for standard reason codes
                    "physcntdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
                    "conditioncode": "A1",
                    "memo": "CYCLE_COUNT",  # Standard reason code
                    "reconciled": True
                }
            ]
        }
    ]

    # Test with exception reason code (both physcnt and curbal)
    payload_exception = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": "RIP001",
            "issueunit": "EA",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "TEST-BIN-002",
                    "physcnt": 15.0,
                    "curbal": 15.0,  # Both fields for exception reason codes
                    "physcntdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
                    "conditioncode": "A1",
                    "memo": "INITIAL_COUNT",  # Exception reason code
                    "reconciled": True
                }
            ]
        }
    ]

    # Test standard payload first
    payload = payload_standard
    
    url = f"{BASE_URL}/api/inventory/no-balance-physical-count"
    
    try:
        print(f"📤 Sending request to: {url}")
        print(f"📤 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print(f"📥 Response Body: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print("✅ No-Balance Physical Count test PASSED")
                return True
            else:
                print(f"❌ No-Balance Physical Count test FAILED: {result.get('error')}")
                return False
        else:
            print(f"📥 Response Body (non-JSON): {response.text[:500]}")
            print("❌ No-Balance Physical Count test FAILED: Non-JSON response")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ No-Balance Physical Count test FAILED: Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ No-Balance Physical Count test FAILED: {e}")
        return False

def test_no_balance_current_balance():
    """Test the no-balance current balance adjustment API"""
    print("\n🔍 Testing No-Balance Current Balance Adjustment...")

    # Test with standard reason code (only curbal field)
    payload_standard = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": "RIP001",  # This should come from inventory record
            "issueunit": "EA",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "TEST-BIN-003",
                    "curbal": 20.0,
                    # physcnt and physcntdate OMITTED for standard reason codes
                    "conditioncode": "A1",
                    "memo": "ADJUSTMENT",  # Standard reason code
                    "reconciled": True
                }
            ]
        }
    ]

    # Test with exception reason code (both curbal and physcnt)
    payload_exception = [
        {
            "_action": "AddChange",
            "itemnum": TEST_ITEM,
            "itemsetid": "ITEMSET",
            "siteid": TEST_SITE,
            "location": "RIP001",
            "issueunit": "EA",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "TEST-BIN-004",
                    "curbal": 25.0,
                    "physcnt": 25.0,  # Both fields for exception reason codes
                    "physcntdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
                    "conditioncode": "A1",
                    "memo": "INITIAL_BALANCE",  # Exception reason code
                    "reconciled": True
                }
            ]
        }
    ]

    # Test standard payload first
    payload = payload_standard
    
    url = f"{BASE_URL}/api/inventory/no-balance-current-balance"
    
    try:
        print(f"📤 Sending request to: {url}")
        print(f"📤 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print(f"📥 Response Body: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print("✅ No-Balance Current Balance test PASSED")
                return True
            else:
                print(f"❌ No-Balance Current Balance test FAILED: {result.get('error')}")
                return False
        else:
            print(f"📥 Response Body (non-JSON): {response.text[:500]}")
            print("❌ No-Balance Current Balance test FAILED: Non-JSON response")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ No-Balance Current Balance test FAILED: Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ No-Balance Current Balance test FAILED: {e}")
        return False

def test_authentication():
    """Test if the server is running and authentication is working"""
    print("\n🔍 Testing Server Connection and Authentication...")
    
    try:
        # Test basic server connectivity
        response = requests.get(f"{BASE_URL}/", timeout=10)
        print(f"📥 Server Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Server connection failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting No-Balance Inventory Adjustment Tests")
    print("=" * 60)
    
    # Test server connectivity first
    if not test_authentication():
        print("\n❌ Cannot connect to server. Please ensure the Flask app is running.")
        sys.exit(1)
    
    # Run the API tests
    results = []
    results.append(test_no_balance_physical_count())
    results.append(test_no_balance_current_balance())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests PASSED!")
        sys.exit(0)
    else:
        print("❌ Some tests FAILED!")
        sys.exit(1)

if __name__ == "__main__":
    main()
