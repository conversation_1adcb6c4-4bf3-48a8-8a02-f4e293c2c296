#!/usr/bin/env python3
"""
Test MXAPIPO and MXAPIPR endpoints with different authentication methods
to diagnose purchase order and purchase requisition data retrieval issues.

Target Item: 5975-60-V00-0529
Target Site: LCVKWT
Expected: 4 purchase order records
"""

import os
import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"
TARGET_ITEM = "5975-60-V00-0529"
TARGET_SITE = "LCVKWT"

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def print_subsection(title):
    """Print a formatted subsection header."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def test_api_key_authentication():
    """Test both endpoints using API Key authentication."""
    print_section("STEP 2: API KEY AUTHENTICATION TESTS")
    
    # Test MXAPIPO with API Key
    print_subsection("MXAPIPO (Purchase Orders) - API Key Auth")
    test_mxapipo_api_key()
    
    # Test MXAPIPR with API Key  
    print_subsection("MXAPIPR (Purchase Requisitions) - API Key Auth")
    test_mxapipr_api_key()

def test_mxapipo_api_key():
    """Test MXAPIPO endpoint with API key authentication."""
    
    # Test different where clause approaches
    test_cases = [
        {
            "name": "Site-only filter (current approach)",
            "where": f'siteid="{TARGET_SITE}"',
            "description": "Filter by site, then post-process poline"
        },
        {
            "name": "Nested poline filter (recommended)",
            "where": f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}"',
            "description": "Direct filter on poline table"
        },
        {
            "name": "Combined filter",
            "where": f'siteid="{TARGET_SITE}" and poline.itemnum="{TARGET_ITEM}"',
            "description": "Filter both parent and child tables"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        print(f"   Description: {test_case['description']}")
        print(f"   Where clause: {test_case['where']}")
        
        # API endpoint
        api_url = f"{BASE_URL}/api/os/mxapipo"
        
        # Parameters
        params = {
            "oslc.select": "ponum,status,siteid,vendor,orderdate,poline",
            "oslc.where": test_case['where'],
            "oslc.pageSize": "50",
            "lean": "1"
        }
        
        # Headers
        headers = {
            "Accept": "application/json",
            "apikey": API_KEY
        }
        
        # Show curl command
        curl_cmd = f"""curl -s -H "Accept: application/json" -H "apikey: {API_KEY}" \\
     "{api_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}" """
        print(f"   Curl command:\n   {curl_cmd}")
        
        # Make request
        try:
            response = requests.get(api_url, params=params, headers=headers, timeout=(5, 30))
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                member_count = len(data.get('member', []))
                print(f"   ✅ Success: Found {member_count} records")
                
                # Count records with matching item in poline
                matching_records = 0
                for record in data.get('member', []):
                    poline_records = record.get('poline', [])
                    for poline in poline_records:
                        if poline.get('itemnum') == TARGET_ITEM:
                            matching_records += 1
                            break
                
                print(f"   📊 Records with target item in poline: {matching_records}")
                
                # Show sample record structure
                if member_count > 0:
                    sample = data['member'][0]
                    print(f"   📝 Sample record keys: {list(sample.keys())}")
                    if 'poline' in sample and sample['poline']:
                        print(f"   📝 Sample poline keys: {list(sample['poline'][0].keys())}")
                        
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response: {response.text[:500]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")

def test_mxapipr_api_key():
    """Test MXAPIPR endpoint with API key authentication."""
    
    # Test different where clause approaches
    test_cases = [
        {
            "name": "Site-only filter (current approach)",
            "where": f'siteid="{TARGET_SITE}"',
            "description": "Filter by site, then post-process prline"
        },
        {
            "name": "Nested prline filter (recommended)",
            "where": f'prline.itemnum="{TARGET_ITEM}" and prline.siteid="{TARGET_SITE}"',
            "description": "Direct filter on prline table"
        },
        {
            "name": "Combined filter",
            "where": f'siteid="{TARGET_SITE}" and prline.itemnum="{TARGET_ITEM}"',
            "description": "Filter both parent and child tables"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        print(f"   Description: {test_case['description']}")
        print(f"   Where clause: {test_case['where']}")
        
        # API endpoint
        api_url = f"{BASE_URL}/api/os/mxapipr"
        
        # Parameters
        params = {
            "oslc.select": "prnum,status,siteid,requestedby,requestdate,prline",
            "oslc.where": test_case['where'],
            "oslc.pageSize": "50",
            "lean": "1"
        }
        
        # Headers
        headers = {
            "Accept": "application/json",
            "apikey": API_KEY
        }
        
        # Show curl command
        curl_cmd = f"""curl -s -H "Accept: application/json" -H "apikey: {API_KEY}" \\
     "{api_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}" """
        print(f"   Curl command:\n   {curl_cmd}")
        
        # Make request
        try:
            response = requests.get(api_url, params=params, headers=headers, timeout=(5, 30))
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                member_count = len(data.get('member', []))
                print(f"   ✅ Success: Found {member_count} records")
                
                # Count records with matching item in prline
                matching_records = 0
                for record in data.get('member', []):
                    prline_records = record.get('prline', [])
                    for prline in prline_records:
                        if prline.get('itemnum') == TARGET_ITEM:
                            matching_records += 1
                            break
                
                print(f"   📊 Records with target item in prline: {matching_records}")
                
                # Show sample record structure
                if member_count > 0:
                    sample = data['member'][0]
                    print(f"   📝 Sample record keys: {list(sample.keys())}")
                    if 'prline' in sample and sample['prline']:
                        print(f"   📝 Sample prline keys: {list(sample['prline'][0].keys())}")
                        
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response: {response.text[:500]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")

def show_current_where_clauses():
    """Show the current where clauses being used."""
    print_section("STEP 1: CURRENT WHERE CLAUSES")
    
    print("📋 Current MXAPIPO where clause:")
    print(f'   siteid="{TARGET_SITE}"')
    print("   ❌ Issue: Only filters by site, relies on post-processing for item filtering")
    
    print("\n📋 Current MXAPIPR where clause:")
    print(f'   siteid="{TARGET_SITE}"')
    print("   ❌ Issue: Only filters by site, relies on post-processing for item filtering")
    
    print("\n📋 Recommended where clauses:")
    print("   MXAPIPO: poline.itemnum=\"5975-60-V00-0529\" and poline.siteid=\"LCVKWT\"")
    print("   MXAPIPR: prline.itemnum=\"5975-60-V00-0529\" and prline.siteid=\"LCVKWT\"")

def test_session_authentication():
    """Test both endpoints using Session Token authentication."""
    print_section("STEP 3: SESSION TOKEN AUTHENTICATION TESTS")

    # Import token manager
    try:
        sys.path.append('backend/auth')
        from token_api import MaximoTokenManager

        # Initialize token manager
        token_manager = MaximoTokenManager(BASE_URL)

        if not token_manager.is_logged_in():
            print("❌ Session authentication not available - user not logged in")
            print("   Please log in through the web interface first")
            return

        print("✅ Session authentication available")

        # Test MXAPIPO with Session Auth
        print_subsection("MXAPIPO (Purchase Orders) - Session Auth")
        test_mxapipo_session(token_manager)

        # Test MXAPIPR with Session Auth
        print_subsection("MXAPIPR (Purchase Requisitions) - Session Auth")
        test_mxapipr_session(token_manager)

    except ImportError as e:
        print(f"❌ Cannot import token manager: {e}")
        print("   Session authentication tests skipped")
    except Exception as e:
        print(f"❌ Session authentication error: {e}")

def test_mxapipo_session(token_manager):
    """Test MXAPIPO endpoint with session authentication."""

    # Test the recommended where clause with session auth
    test_case = {
        "name": "Nested poline filter with session auth",
        "where": f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}"',
        "description": "Direct filter on poline table using session authentication"
    }

    print(f"\n🧪 Test: {test_case['name']}")
    print(f"   Description: {test_case['description']}")
    print(f"   Where clause: {test_case['where']}")

    # OSLC endpoint (session auth typically uses OSLC)
    api_url = f"{BASE_URL}/oslc/os/mxapipo"

    # Parameters
    params = {
        "oslc.select": "ponum,status,siteid,vendor,orderdate,poline",
        "oslc.where": test_case['where'],
        "oslc.pageSize": "50",
        "lean": "1"
    }

    # Show curl command equivalent (would need session cookies)
    print(f"   Endpoint: {api_url}")
    print(f"   Auth: Session cookies (not shown in curl)")

    # Make request using session
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            headers={"Accept": "application/json"},
            timeout=(5, 30)
        )

        print(f"   Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            member_count = len(data.get('member', []))
            print(f"   ✅ Success: Found {member_count} records")

            # Count records with matching item in poline
            matching_records = 0
            for record in data.get('member', []):
                poline_records = record.get('poline', [])
                for poline in poline_records:
                    if poline.get('itemnum') == TARGET_ITEM:
                        matching_records += 1
                        break

            print(f"   📊 Records with target item in poline: {matching_records}")

            # Show sample record structure
            if member_count > 0:
                sample = data['member'][0]
                print(f"   📝 Sample record keys: {list(sample.keys())}")
                if 'poline' in sample and sample['poline']:
                    print(f"   📝 Sample poline keys: {list(sample['poline'][0].keys())}")

        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text[:500]}")

    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_mxapipr_session(token_manager):
    """Test MXAPIPR endpoint with session authentication."""

    # Test the recommended where clause with session auth
    test_case = {
        "name": "Nested prline filter with session auth",
        "where": f'prline.itemnum="{TARGET_ITEM}" and prline.siteid="{TARGET_SITE}"',
        "description": "Direct filter on prline table using session authentication"
    }

    print(f"\n🧪 Test: {test_case['name']}")
    print(f"   Description: {test_case['description']}")
    print(f"   Where clause: {test_case['where']}")

    # OSLC endpoint (session auth typically uses OSLC)
    api_url = f"{BASE_URL}/oslc/os/mxapipr"

    # Parameters
    params = {
        "oslc.select": "prnum,status,siteid,requestedby,requestdate,prline",
        "oslc.where": test_case['where'],
        "oslc.pageSize": "50",
        "lean": "1"
    }

    # Show curl command equivalent (would need session cookies)
    print(f"   Endpoint: {api_url}")
    print(f"   Auth: Session cookies (not shown in curl)")

    # Make request using session
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            headers={"Accept": "application/json"},
            timeout=(5, 30)
        )

        print(f"   Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            member_count = len(data.get('member', []))
            print(f"   ✅ Success: Found {member_count} records")

            # Count records with matching item in prline
            matching_records = 0
            for record in data.get('member', []):
                prline_records = record.get('prline', [])
                for prline in prline_records:
                    if prline.get('itemnum') == TARGET_ITEM:
                        matching_records += 1
                        break

            print(f"   📊 Records with target item in prline: {matching_records}")

            # Show sample record structure
            if member_count > 0:
                sample = data['member'][0]
                print(f"   📝 Sample record keys: {list(sample.keys())}")
                if 'prline' in sample and sample['prline']:
                    print(f"   📝 Sample prline keys: {list(sample['prline'][0].keys())}")

        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text[:500]}")

    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Testing MXAPIPO and MXAPIPR Endpoints")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Expected: 4 purchase order records")
    print(f"Timestamp: {datetime.now().isoformat()}")

    # Step 1: Show current where clauses
    show_current_where_clauses()

    # Step 2: Test with API Key authentication
    test_api_key_authentication()

    # Step 3: Test with Session Token authentication
    test_session_authentication()

    print(f"\n{'='*80}")
    print("🏁 Testing Complete")
    print(f"{'='*80}")
    print("\n📋 Next Steps:")
    print("1. Review the test results above")
    print("2. Identify which authentication method and where clause works best")
    print("3. Update the application code with the working approach")
    print("4. Test the updated application with the target item")
