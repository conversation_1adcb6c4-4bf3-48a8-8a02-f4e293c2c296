#!/usr/bin/env python3
"""
Test Session Token Authentication for MXAPIPO and MXAPIPR endpoints
"""

import os
import sys
import json
from datetime import datetime

# Add backend path for imports
sys.path.append('backend/auth')

try:
    from backend.auth.token_manager import MaximoTokenManager
except ImportError as e:
    print(f"❌ Cannot import MaximoTokenManager: {e}")
    sys.exit(1)

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
TARGET_ITEM = "5975-60-V00-0529"
TARGET_SITE = "LCVKWT"

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def print_subsection(title):
    """Print a formatted subsection header."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def test_session_authentication():
    """Test session token authentication with both endpoints."""
    print_section("SESSION TOKEN AUTHENTICATION TEST")
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    if not token_manager.is_logged_in():
        print("❌ Session authentication not available - user not logged in")
        print("   Please log in through the web interface first")
        return False
        
    print("✅ Session authentication available")
    print(f"   Username: {getattr(token_manager, 'username', 'Unknown')}")

    # First test whoami endpoint to verify session
    print_subsection("Testing Session Validity with /oslc/whoami")
    whoami_success = test_whoami_session(token_manager)

    if not whoami_success:
        print("❌ Session is not valid, skipping endpoint tests")
        return False

    # Test MXAPIPO with Session Auth
    print_subsection("MXAPIPO (Purchase Orders) - Session Auth")
    po_success = test_mxapipo_session(token_manager)

    # Test MXAPIPR with Session Auth
    print_subsection("MXAPIPR (Purchase Requisitions) - Session Auth")
    pr_success = test_mxapipr_session(token_manager)

    return po_success and pr_success

def test_whoami_session(token_manager):
    """Test session validity using the whoami endpoint."""

    # OSLC whoami endpoint
    api_url = f"{BASE_URL}/oslc/whoami"

    print(f"Endpoint: {api_url}")
    print(f"Auth: Session cookies")

    # Make request using session
    try:
        response = token_manager.session.get(
            api_url,
            headers={"Accept": "application/json"},
            timeout=(5, 30)
        )

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            # Check response content first
            response_text = response.text.strip()
            print(f"Response length: {len(response_text)}")

            if response_text.startswith('<!doctype html>'):
                print("❌ Received HTML login page - session expired or invalid")
                return False

            try:
                data = response.json()
                print(f"✅ Session valid - User: {data.get('userName', 'Unknown')}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response_text[:200]}")
                return False

        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False

    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_mxapipo_session(token_manager):
    """Test MXAPIPO endpoint with session authentication."""
    
    # Current where clause (will be enhanced later)
    where_clause = f'poline.itemnum="{TARGET_ITEM}" and poline.siteid="{TARGET_SITE}"'
    
    print(f"Where clause: {where_clause}")
    
    # OSLC endpoint for session auth
    api_url = f"{BASE_URL}/oslc/os/mxapipo"
    
    # Parameters
    params = {
        "oslc.select": "ponum,status,status_description,siteid,vendor,orderdate,poline",
        "oslc.where": where_clause,
        "oslc.pageSize": "20",
        "lean": "1"
    }
    
    print(f"Endpoint: {api_url}")
    print(f"Auth: Session cookies")
    
    # Make request using session
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            headers={"Accept": "application/json"},
            timeout=(5, 30)
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # Check response content first
            response_text = response.text.strip()
            print(f"Response length: {len(response_text)}")
            print(f"Response preview: {response_text[:200]}...")

            if not response_text:
                print("❌ Empty response")
                return False

            try:
                data = response.json()
                member_count = len(data.get('member', []))
                print(f"✅ Success: Found {member_count} records")

                # Analyze status distribution
                status_counts = {}
                for record in data.get('member', []):
                    status = record.get('status', 'UNKNOWN')
                    status_counts[status] = status_counts.get(status, 0) + 1

                print(f"📊 Status distribution: {status_counts}")

                # Show sample records
                if member_count > 0:
                    print("\nSample records:")
                    for i, record in enumerate(data['member'][:3], 1):
                        print(f"{i}. PO #{record.get('ponum')} - Status: {record.get('status')} - Vendor: {record.get('vendor')}")

                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response_text[:500]}")
                return False
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_mxapipr_session(token_manager):
    """Test MXAPIPR endpoint with session authentication."""
    
    # Current where clause (will be enhanced later)
    where_clause = f'prline.itemnum="{TARGET_ITEM}" and prline.siteid="{TARGET_SITE}"'
    
    print(f"Where clause: {where_clause}")
    
    # OSLC endpoint for session auth
    api_url = f"{BASE_URL}/oslc/os/mxapipr"
    
    # Parameters
    params = {
        "oslc.select": "prnum,status,status_description,siteid,requestedby,requestdate,prline",
        "oslc.where": where_clause,
        "oslc.pageSize": "20",
        "lean": "1"
    }
    
    print(f"Endpoint: {api_url}")
    print(f"Auth: Session cookies")
    
    # Make request using session
    try:
        response = token_manager.session.get(
            api_url,
            params=params,
            headers={"Accept": "application/json"},
            timeout=(5, 30)
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # Check response content first
            response_text = response.text.strip()
            print(f"Response length: {len(response_text)}")
            print(f"Response preview: {response_text[:200]}...")

            if not response_text:
                print("❌ Empty response")
                return False

            try:
                data = response.json()
                member_count = len(data.get('member', []))
                print(f"✅ Success: Found {member_count} records")

                # Analyze status distribution
                status_counts = {}
                for record in data.get('member', []):
                    status = record.get('status', 'UNKNOWN')
                    status_counts[status] = status_counts.get(status, 0) + 1

                print(f"📊 Status distribution: {status_counts}")

                # Show sample records
                if member_count > 0:
                    print("\nSample records:")
                    for i, record in enumerate(data['member'][:3], 1):
                        print(f"{i}. PR #{record.get('prnum')} - Status: {record.get('status')} - Requested By: {record.get('requestedby')}")

                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response_text[:500]}")
                return False
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

if __name__ == "__main__":
    print(f"🚀 Testing Session Token Authentication")
    print(f"Target Item: {TARGET_ITEM}")
    print(f"Target Site: {TARGET_SITE}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    success = test_session_authentication()
    
    print(f"\n{'='*80}")
    if success:
        print("🏁 Session Token Authentication: ✅ SUCCESS")
        print("Ready to implement enhanced filtering with status exclusions")
    else:
        print("🏁 Session Token Authentication: ❌ FAILED")
        print("Need to resolve authentication issues before proceeding")
    print(f"{'='*80}")
