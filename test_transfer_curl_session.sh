#!/bin/bash

# Test Transfer with Session Cookies - Multiple Attempts Until Success
# =====================================================================

echo "🚀 TESTING TRANSFER WITH SESSION COOKIES"
echo "========================================"

# Base URL and endpoint
BASE_URL="https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
ENDPOINT="${BASE_URL}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"

# Cookie file
COOKIE_FILE="cookies.txt"

echo "🔗 Endpoint: $ENDPOINT"
echo "🍪 Cookie file: $COOKIE_FILE"
echo ""

# Test Case 1: Fixed location reference (KWAJ-1058 should be in IKWAJ site)
echo "📋 TEST 1: Fixed location reference"
echo "=================================="

PAYLOAD1='[
  {
    "_action": "AddChange",
    "itemnum": "5975-60-V00-0529",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKWT",
    "location": "RIP001",
    "issueunit": "RO",
    "matrectrans": [
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058",
        "transdate": "2025-07-16T11:00:00+00:00",
        "issueunit": "RO",
        "frombinnum": "28-800-0004",
        "tobinnum": "58-A-A01-1",
        "fromlotnum": "TEST",
        "tolotnum": "TEST",
        "fromconditioncode": "A1",
        "toconditioncode": "A1"
      }
    ]
  }
]'

echo "🔄 Submitting Test 1..."
RESPONSE1=$(curl -X POST "$ENDPOINT" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "x-method-override: BULK" \
  --cookie "$COOKIE_FILE" \
  --cookie-jar "$COOKIE_FILE" \
  -d "$PAYLOAD1" \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s)

echo "📊 Response:"
echo "$RESPONSE1"
echo ""

# Extract status code
STATUS1=$(echo "$RESPONSE1" | grep "HTTP_STATUS:" | cut -d: -f2)
echo "📈 HTTP Status: $STATUS1"

# Check for success
if [[ "$STATUS1" == "204" ]]; then
    echo "🎉 SUCCESS! Test 1 returned 204"
    exit 0
fi

# Check response for errors
if echo "$RESPONSE1" | grep -q "204"; then
    echo "🎉 SUCCESS! Found 204 in response"
    exit 0
fi

echo ""
echo "❌ Test 1 failed, trying Test 2..."
echo ""

# Test Case 2: Minimal required fields only
echo "📋 TEST 2: Minimal required fields"
echo "================================="

PAYLOAD2='[
  {
    "_action": "AddChange",
    "itemnum": "5975-60-V00-0529",
    "siteid": "LCVKWT",
    "location": "RIP001",
    "matrectrans": [
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058"
      }
    ]
  }
]'

echo "🔄 Submitting Test 2..."
RESPONSE2=$(curl -X POST "$ENDPOINT" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "x-method-override: BULK" \
  --cookie "$COOKIE_FILE" \
  --cookie-jar "$COOKIE_FILE" \
  -d "$PAYLOAD2" \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s)

echo "📊 Response:"
echo "$RESPONSE2"
echo ""

STATUS2=$(echo "$RESPONSE2" | grep "HTTP_STATUS:" | cut -d: -f2)
echo "📈 HTTP Status: $STATUS2"

if [[ "$STATUS2" == "204" ]]; then
    echo "🎉 SUCCESS! Test 2 returned 204"
    exit 0
fi

if echo "$RESPONSE2" | grep -q "204"; then
    echo "🎉 SUCCESS! Found 204 in response"
    exit 0
fi

echo ""
echo "❌ Test 2 failed, trying Test 3..."
echo ""

# Test Case 3: Different bin numbers
echo "📋 TEST 3: Different bin numbers"
echo "==============================="

PAYLOAD3='[
  {
    "_action": "AddChange",
    "itemnum": "5975-60-V00-0529",
    "siteid": "LCVKWT",
    "location": "RIP001",
    "matrectrans": [
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058",
        "frombinnum": "28-800-0004",
        "tobinnum": "1058-TEMP"
      }
    ]
  }
]'

echo "🔄 Submitting Test 3..."
RESPONSE3=$(curl -X POST "$ENDPOINT" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "x-method-override: BULK" \
  --cookie "$COOKIE_FILE" \
  --cookie-jar "$COOKIE_FILE" \
  -d "$PAYLOAD3" \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s)

echo "📊 Response:"
echo "$RESPONSE3"
echo ""

STATUS3=$(echo "$RESPONSE3" | grep "HTTP_STATUS:" | cut -d: -f2)
echo "📈 HTTP Status: $STATUS3"

if [[ "$STATUS3" == "204" ]]; then
    echo "🎉 SUCCESS! Test 3 returned 204"
    exit 0
fi

if echo "$RESPONSE3" | grep -q "204"; then
    echo "🎉 SUCCESS! Found 204 in response"
    exit 0
fi

echo ""
echo "❌ Test 3 failed, trying Test 4..."
echo ""

# Test Case 4: Without lot numbers and condition codes
echo "📋 TEST 4: Without lot/condition codes"
echo "====================================="

PAYLOAD4='[
  {
    "_action": "AddChange",
    "itemnum": "5975-60-V00-0529",
    "siteid": "LCVKWT",
    "location": "RIP001",
    "issueunit": "RO",
    "matrectrans": [
      {
        "_action": "AddChange",
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058",
        "transdate": "2025-07-16T11:00:00+00:00",
        "issueunit": "RO"
      }
    ]
  }
]'

echo "🔄 Submitting Test 4..."
RESPONSE4=$(curl -X POST "$ENDPOINT" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "x-method-override: BULK" \
  --cookie "$COOKIE_FILE" \
  --cookie-jar "$COOKIE_FILE" \
  -d "$PAYLOAD4" \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s)

echo "📊 Response:"
echo "$RESPONSE4"
echo ""

STATUS4=$(echo "$RESPONSE4" | grep "HTTP_STATUS:" | cut -d: -f2)
echo "📈 HTTP Status: $STATUS4"

if [[ "$STATUS4" == "204" ]]; then
    echo "🎉 SUCCESS! Test 4 returned 204"
    exit 0
fi

if echo "$RESPONSE4" | grep -q "204"; then
    echo "🎉 SUCCESS! Found 204 in response"
    exit 0
fi

echo ""
echo "❌ Test 4 failed, trying Test 5..."
echo ""

# Test Case 5: Replace action instead of AddChange
echo "📋 TEST 5: Replace action"
echo "========================"

PAYLOAD5='[
  {
    "_action": "AddChange",
    "itemnum": "5975-60-V00-0529",
    "siteid": "LCVKWT",
    "location": "RIP001",
    "matrectrans": [
      {
        "_action": "Replace",
        "itemnum": "5975-60-V00-0529",
        "issuetype": "TRANSFER",
        "quantity": 1.0,
        "fromsiteid": "LCVKWT",
        "tositeid": "IKWAJ",
        "fromstoreloc": "RIP001",
        "tostoreloc": "KWAJ-1058",
        "frombinnum": "28-800-0004",
        "tobinnum": "1058-TEMP"
      }
    ]
  }
]'

echo "🔄 Submitting Test 5..."
RESPONSE5=$(curl -X POST "$ENDPOINT" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "x-method-override: BULK" \
  --cookie "$COOKIE_FILE" \
  --cookie-jar "$COOKIE_FILE" \
  -d "$PAYLOAD5" \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s)

echo "📊 Response:"
echo "$RESPONSE5"
echo ""

STATUS5=$(echo "$RESPONSE5" | grep "HTTP_STATUS:" | cut -d: -f2)
echo "📈 HTTP Status: $STATUS5"

if [[ "$STATUS5" == "204" ]]; then
    echo "🎉 SUCCESS! Test 5 returned 204"
    exit 0
fi

if echo "$RESPONSE5" | grep -q "204"; then
    echo "🎉 SUCCESS! Found 204 in response"
    exit 0
fi

echo ""
echo "📋 SUMMARY OF ALL TESTS"
echo "======================"
echo "Test 1 Status: $STATUS1"
echo "Test 2 Status: $STATUS2"
echo "Test 3 Status: $STATUS3"
echo "Test 4 Status: $STATUS4"
echo "Test 5 Status: $STATUS5"
echo ""
echo "❌ All tests failed to return 204. Check responses above for error details."
