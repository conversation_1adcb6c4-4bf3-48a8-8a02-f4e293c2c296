<!DOCTYPE html>
<html>
<head>
    <title>Debug Work Order Dropdown</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
</head>
<body>
    <h1>Debug Work Order Dropdown</h1>
    
    <div>
        <label>Work Order:</label>
        <select id="test_work_order" style="width: 300px;">
            <option value="">Select work order...</option>
        </select>
    </div>
    
    <br>
    <button onclick="testAPI()">Test API Call</button>
    <button onclick="testSelect2()">Test Select2</button>
    <button onclick="loadWorkOrders()">Load Work Orders</button>
    
    <div id="debug-output" style="margin-top: 20px; padding: 10px; background: #f0f0f0; white-space: pre-wrap;"></div>

    <script>
        function log(message) {
            const output = document.getElementById('debug-output');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }

        async function testAPI() {
            log('Testing API call...');
            try {
                const response = await fetch('/api/inventory/issue-current-item/work-orders?siteid=LCVKWT', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Response data: ${JSON.stringify(data, null, 2)}`);
                } else {
                    log(`Error response: ${response.statusText}`);
                    const text = await response.text();
                    log(`Error text: ${text}`);
                }
            } catch (error) {
                log(`Exception: ${error.message}`);
            }
        }

        function testSelect2() {
            log('Testing Select2 initialization...');
            const select = document.getElementById('test_work_order');
            
            if (typeof $ !== 'undefined' && $.fn.select2) {
                log('jQuery and Select2 are available');
                
                // Add some test options
                select.innerHTML = `
                    <option value="">Select work order...</option>
                    <option value="WO001">WO001 - Test Work Order 1 (WMATL)</option>
                    <option value="WO002">WO002 - Test Work Order 2 (PISSUE)</option>
                `;
                
                // Initialize Select2
                $(select).select2({
                    placeholder: 'Select work order...',
                    allowClear: true,
                    width: '100%'
                });
                
                log('Select2 initialized successfully');
            } else {
                log('jQuery or Select2 not available');
            }
        }

        async function loadWorkOrders() {
            log('Loading work orders...');
            
            try {
                const response = await fetch('/api/inventory/issue-current-item/work-orders?siteid=LCVKWT', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API returned: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success && data.work_orders) {
                        const select = document.getElementById('test_work_order');
                        
                        // Clear existing options
                        select.innerHTML = '<option value="">Select work order...</option>';
                        
                        // Add work orders
                        data.work_orders.forEach(wo => {
                            const description = wo.description || 'No description';
                            const displayText = `${wo.wonum} - ${description} (${wo.status})`;
                            select.innerHTML += `<option value="${wo.wonum}">${displayText}</option>`;
                        });
                        
                        log(`Added ${data.work_orders.length} work orders to dropdown`);
                        
                        // Reinitialize Select2
                        if (typeof $ !== 'undefined' && $.fn.select2) {
                            if ($(select).hasClass('select2-hidden-accessible')) {
                                $(select).select2('destroy');
                            }
                            $(select).select2({
                                placeholder: 'Select work order...',
                                allowClear: true,
                                width: '100%'
                            });
                            log('Select2 reinitialized');
                        }
                    } else {
                        log(`API error: ${data.error || 'Unknown error'}`);
                    }
                } else {
                    log(`HTTP error: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`Exception: ${error.message}`);
            }
        }

        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            log('Debug page loaded');
            log(`jQuery available: ${typeof $ !== 'undefined'}`);
            log(`Select2 available: ${typeof $ !== 'undefined' && $.fn.select2}`);
        });
    </script>
</body>
</html>
