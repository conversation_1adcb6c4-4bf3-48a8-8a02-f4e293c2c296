#!/bin/bash

# Find 204 Pattern - Focused Cross-Site Testing
# ==============================================

echo "🚀 FIND 204 PATTERN - FOCUSED CROSS-SITE TESTING"
echo "================================================"

echo "🎯 OBJECTIVE: Find the exact combination that returns 204 status"
echo "📋 STRATEGY: Test confirmed working patterns from terminal analysis"
echo "🔍 FOCUS: Watch Flask terminal for 204 responses in Maximo output"
echo ""

echo "⚠️  IMPORTANT:"
echo "1. Login at http://127.0.0.1:5010 first"
echo "2. Keep Flask terminal visible"
echo "3. Look for '\"status\": \"204\"' in Maximo responses"
echo ""

read -p "Ready to test? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please login first and try again"
    exit 1
fi

# Function to test and look for 204
test_for_204() {
    local test_name="$1"
    local endpoint="$2"
    local payload="$3"
    
    echo ""
    echo "🔍 TESTING: $test_name"
    echo "$(printf '=%.0s' {1..60})"
    echo "🔗 Endpoint: $endpoint"
    echo ""
    
    echo "🔄 Submitting..."
    echo "👀 WATCH FLASK TERMINAL FOR MAXIMO RESPONSE!"
    
    response=$(curl -X POST "http://127.0.0.1:5010$endpoint" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -s)
    
    echo ""
    echo "📊 Flask Response:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    # Check for 204 in response
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo ""
        echo "🎉🎉🎉 204 SUCCESS FOUND! 🎉🎉🎉"
        echo "✅ This combination works!"
        echo "$payload" > "WORKING_204_PATTERN.json"
        echo "💾 Saved to WORKING_204_PATTERN.json"
        return 0
    elif echo "$response" | grep -q '"success": true'; then
        echo ""
        echo "✅ Success (non-204) - check Flask terminal for actual Maximo status"
        return 0
    else
        echo ""
        echo "❌ Failed - check Flask terminal for Maximo error details"
        return 1
    fi
}

echo "🚀 TESTING CONFIRMED WORKING PATTERNS"
echo "===================================="

# Test 1: CMW-AJ → CMW-BU (Both LCVKWT storerooms - CONFIRMED WORKING)
test_for_204 "CMW-AJ → CMW-BU (Both LCVKWT - CONFIRMED 204)" \
    "/api/inventory/transfer-current-item" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-BU",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 2: CMW-AJ → CMW-AJH (Both LCVKWT storerooms - CONFIRMED WORKING)
test_for_204 "CMW-AJ → CMW-AJH (Both LCVKWT - CONFIRMED 204)" \
    "/api/inventory/transfer-current-item" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-AJH",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 3: Destination Context (CMW-AJ → KWAJ-1058 - CONFIRMED WORKING)
test_for_204 "Destination Context: CMW-AJ → KWAJ-1058 (CONFIRMED 204)" \
    "/api/inventory/transfer-current-item-destination-context" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 4: New Cross-Site Button (CMW-AJ → KWAJ-1058)
test_for_204 "New Cross-Site Button: CMW-AJ → KWAJ-1058" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 5: New Cross-Site Button (CMW-AJ → CMW-BU)
test_for_204 "New Cross-Site Button: CMW-AJ → CMW-BU" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "CMW-BU",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

echo ""
echo "📊 ANALYSIS COMPLETE"
echo "==================="

if [ -f "WORKING_204_PATTERN.json" ]; then
    echo "🎉 FOUND WORKING 204 PATTERN!"
    echo "=============================="
    echo "📋 Working combination saved to WORKING_204_PATTERN.json"
    echo ""
    echo "📋 Working Pattern:"
    cat WORKING_204_PATTERN.json | jq '.' 2>/dev/null || cat WORKING_204_PATTERN.json
    echo ""
    echo "🔧 USE THIS PATTERN FOR IMPLEMENTATION!"
else
    echo "🔍 NO 204 PATTERN FOUND YET"
    echo "=========================="
    echo "📋 Check Flask terminal for detailed Maximo responses"
    echo "🔍 Look for patterns that show '\"status\": \"204\"' in Maximo output"
fi

echo ""
echo "🎯 FLASK TERMINAL ANALYSIS GUIDE:"
echo "================================"
echo ""
echo "🔍 LOOK FOR THIS IN FLASK LOGS:"
echo "================================================================================"
echo "📋 EXACT RESPONSE FROM MAXIMO:"
echo "================================================================================"
echo "📊 Status Code: 200"
echo "📋 Response JSON:"
echo "["
echo "  {"
echo "    \"_responsemeta\": {"
echo "      \"status\": \"204\"    ← THIS IS SUCCESS!"
echo "    }"
echo "  }"
echo "]"
echo "================================================================================"
echo ""
echo "❌ VS ERROR RESPONSE:"
echo "["
echo "  {"
echo "    \"_responsedata\": {"
echo "      \"Error\": {"
echo "        \"reasonCode\": \"BMXAA1861E\","
echo "        \"message\": \"Duplicate combination...\""
echo "      }"
echo "    },"
echo "    \"_responsemeta\": {"
echo "      \"status\": \"400\"    ← THIS IS ERROR!"
echo "    }"
echo "  }"
echo "]"
echo ""
echo "🎯 KEY POINTS:"
echo "• 204 status = SUCCESS"
echo "• 400 status = ERROR"
echo "• Look at the exact payload structure that achieves 204"
echo "• Note which site context (source vs destination) works"
echo "• Check if bin/lot fields are included or excluded"
echo ""
echo "💡 IF NO 204 FOUND:"
echo "• Try different storeroom combinations"
echo "• Check item availability in source locations"
echo "• Verify authentication is working"
echo "• Look for other error codes in Maximo responses"
