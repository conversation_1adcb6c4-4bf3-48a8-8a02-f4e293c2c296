<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test No-Balance Inventory Buttons</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .inventory-item-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }
        .inventory-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .inventory-item-info {
            display: flex;
            align-items: center;
        }
        .inventory-item-icon {
            margin-right: 0.75rem;
            font-size: 1.5rem;
            color: #6c757d;
        }
        .inventory-status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            background-color: #28a745;
            color: white;
        }
        .no-balance-actions {
            border: 1px dashed #ffc107;
            border-radius: 0.375rem;
            padding: 0.75rem;
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-boxes me-2"></i>Test: No-Balance Inventory Buttons</h1>
        <p class="text-muted">This page demonstrates how the new buttons appear for inventory items with no balance records.</p>
        
        <div class="row">
            <div class="col-12">
                <h3>Inventory Item with No Balance Records</h3>
                <div class="inventory-item-card">
                    <!-- Header section -->
                    <div class="inventory-item-header">
                        <div class="inventory-item-info">
                            <div class="inventory-item-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <div class="inventory-item-details">
                                <h6>TEST-ITEM-001</h6>
                                <p>Test Item for No Balance Records</p>
                            </div>
                        </div>
                        <div class="inventory-status-badge">ACTIVE</div>
                    </div>

                    <!-- Fields section (simplified) -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Site ID:</strong> LCVKNT
                        </div>
                        <div class="col-md-4">
                            <strong>Item Type:</strong> ITEM
                        </div>
                        <div class="col-md-4">
                            <strong>Issue Unit:</strong> EA
                        </div>
                    </div>

                    <!-- Actions section -->
                    <div class="inventory-item-actions mt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="btn-group" role="group">
                                <!-- No balance actions -->
                                <div class="no-balance-actions">
                                    <small class="text-warning d-block mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>No inventory balances - Create initial records:
                                    </small>
                                    
                                    <!-- Physical Count button -->
                                    <button type="button" class="btn btn-primary btn-sm me-1 mb-1"
                                            onclick="alert('Physical Count modal would open here')"
                                            title="Create new inventory record with physical count">
                                        <i class="fas fa-clipboard-check me-1"></i>Physical Count
                                    </button>
                                    
                                    <!-- Current Balance button -->
                                    <button type="button" class="btn btn-warning btn-sm me-1 mb-1"
                                            onclick="alert('Current Balance modal would open here')"
                                            title="Create new inventory record with current balance">
                                        <i class="fas fa-balance-scale me-1"></i>Current Balance
                                    </button>
                                    
                                    <!-- QR Code button -->
                                    <button type="button" class="btn btn-outline-secondary btn-sm mb-1"
                                            onclick="alert('QR Code would be generated here')"
                                            title="Generate QR Code for TEST-ITEM-001">
                                        <i class="fas fa-qrcode me-1"></i>QR Code
                                    </button>
                                </div>
                            </div>

                            <!-- Cost Adjustment Buttons Section -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-warning btn-sm cost-adjustment-btn"
                                        onclick="alert('Average Cost modal would open here')"
                                        title="Adjust Average Cost">
                                    <i class="fas fa-dollar-sign me-1"></i>Avg Cost
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm cost-adjustment-btn"
                                        onclick="alert('Standard Cost modal would open here')"
                                        title="Adjust Standard Cost">
                                    <i class="fas fa-chart-line me-1"></i>Std Cost
                                </button>
                            </div>

                            <button type="button" class="btn btn-primary btn-sm details-btn"
                                    onclick="alert('Details modal would open here')"
                                    title="View Detailed Information">
                                <i class="fas fa-info-circle me-1"></i>Details
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>Inventory Item with Balance Records (for comparison)</h3>
                <div class="inventory-item-card">
                    <!-- Header section -->
                    <div class="inventory-item-header">
                        <div class="inventory-item-info">
                            <div class="inventory-item-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <div class="inventory-item-details">
                                <h6>TEST-ITEM-002</h6>
                                <p>Test Item with Balance Records</p>
                            </div>
                        </div>
                        <div class="inventory-status-badge">ACTIVE</div>
                    </div>

                    <!-- Fields section (simplified) -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Site ID:</strong> LCVKNT
                        </div>
                        <div class="col-md-4">
                            <strong>Item Type:</strong> ITEM
                        </div>
                        <div class="col-md-4">
                            <strong>Issue Unit:</strong> EA
                        </div>
                    </div>

                    <!-- Balance Records Section -->
                    <div class="balance-records-section mt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="text-primary mb-0">
                                <i class="fas fa-warehouse me-1"></i>Balance Records (2)
                            </h6>
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-chevron-down me-1"></i>Show Balances
                            </button>
                        </div>
                    </div>

                    <!-- Actions section -->
                    <div class="inventory-item-actions mt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="btn-group" role="group">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>QR codes available in balance details above
                                </small>
                            </div>

                            <!-- Cost Adjustment Buttons Section -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-warning btn-sm cost-adjustment-btn"
                                        title="Adjust Average Cost">
                                    <i class="fas fa-dollar-sign me-1"></i>Avg Cost
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm cost-adjustment-btn"
                                        title="Adjust Standard Cost">
                                    <i class="fas fa-chart-line me-1"></i>Std Cost
                                </button>
                            </div>

                            <button type="button" class="btn btn-primary btn-sm details-btn"
                                    title="View Detailed Information">
                                <i class="fas fa-info-circle me-1"></i>Details
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>Implementation Notes</h5>
            <ul class="mb-0">
                <li><strong>Physical Count Button:</strong> Creates new inventory record with initial physical count</li>
                <li><strong>Current Balance Button:</strong> Creates new inventory record with initial current balance</li>
                <li><strong>QR Code Button:</strong> Generates QR code for the item (existing functionality)</li>
                <li><strong>Visual Indicator:</strong> Warning message and highlighted box clearly indicate no balance records exist</li>
                <li><strong>Button Placement:</strong> Buttons appear in the main actions section of each inventory item card</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
