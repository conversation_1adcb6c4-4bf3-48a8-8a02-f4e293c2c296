#!/usr/bin/env python3
"""
Test Alternative Approaches for Transfer Operations

Since the nested object URLs are getting 502 errors, this script tests
alternative approaches for creating transfer operations in MXAPIINVENTORY.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

def test_direct_inventory_update_with_transfer():
    """Test updating inventory record directly with transfer data."""
    print("🔍 Testing Direct Inventory Update with Transfer Data")
    print("=" * 60)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # First, get an inventory record to work with
    try:
        response = requests.get(
            endpoint_url,
            params={
                "oslc.select": "inventoryid,itemnum,siteid,location,curbaltotal,transfercuritem",
                "oslc.where": 'siteid="LCVKWT" and curbaltotal>0',
                "oslc.pageSize": "1"
            },
            headers={"Accept": "application/json", "apikey": API_KEY},
            timeout=(3.05, 15)
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'rdfs:member' in data and data['rdfs:member']:
                record = data['rdfs:member'][0]
                inventory_id = record.get('spi:inventoryid')
                
                print(f"✅ Found inventory record:")
                print(f"   Item: {record.get('spi:itemnum')}")
                print(f"   Location: {record.get('spi:location')}")
                print(f"   Current Balance: {record.get('spi:curbaltotal')}")
                print(f"   Inventory ID: {inventory_id}")
                
                # Test updating the record with transfer data
                test_inventory_update_with_transfer(inventory_id, record, headers)
                
            else:
                print("❌ No inventory records with balance found")
        else:
            print(f"❌ Failed to get inventory records: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_inventory_update_with_transfer(inventory_id: int, record: Dict, headers: Dict):
    """Test updating inventory record with embedded transfer data."""
    print(f"\n📋 Testing Inventory Update with Transfer Data")
    print("-" * 40)
    
    # Try updating the inventory record with transfer data
    update_url = f"{BASE_URL}/api/os/mxapiinventory/{inventory_id}"
    
    # Create transfer data to embed in the inventory update
    transfer_data = {
        "transfercuritem": [
            {
                "fromstoreloc": record.get('spi:location'),
                "tositeid": "LCVKWT",
                "quantity": 1.0,
                "unitcost": 10.0
            }
        ]
    }
    
    try:
        response = requests.patch(
            update_url,
            json=transfer_data,
            headers=headers,
            timeout=(3.05, 15)
        )
        
        print(f"   PATCH Status: {response.status_code}")
        print(f"   URL: {update_url}")
        print(f"   Payload: {json.dumps(transfer_data, indent=2)}")
        
        if response.content:
            try:
                data = response.json()
                print(f"   Response: {json.dumps(data, indent=2)}")
            except:
                print(f"   Response (text): {response.text[:200]}...")
                
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

def test_post_to_main_endpoint_with_transfer():
    """Test POST to main endpoint with transfer data."""
    print(f"\n🔍 Testing POST to Main Endpoint with Transfer Data")
    print("=" * 60)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test creating a new inventory record with transfer data
    payload_with_transfer = {
        "itemnum": "TEST-TRANSFER-001",
        "siteid": "LCVKWT",
        "location": "LCVK-CMW-AJ",
        "transfercuritem": [
            {
                "fromstoreloc": "LCVK-CMW-AJ",
                "tositeid": "LCVKWT",
                "quantity": 1.0,
                "unitcost": 15.0
            }
        ]
    }
    
    try:
        response = requests.post(
            endpoint_url,
            json=payload_with_transfer,
            headers=headers,
            timeout=(3.05, 15)
        )
        
        print(f"   POST Status: {response.status_code}")
        print(f"   Payload: {json.dumps(payload_with_transfer, indent=2)}")
        
        if response.content:
            try:
                data = response.json()
                print(f"   Response: {json.dumps(data, indent=2)}")
            except:
                print(f"   Response (text): {response.text[:200]}...")
                
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

def test_matrectrans_endpoint():
    """Test if there's a separate MATRECTRANS endpoint for transfers."""
    print(f"\n🔍 Testing MATRECTRANS Endpoint")
    print("=" * 60)
    
    # Test if MATRECTRANS endpoint exists (material receipt transactions)
    matrectrans_endpoints = [
        f"{BASE_URL}/api/os/matrectrans",
        f"{BASE_URL}/api/os/mxapimatrectrans",
        f"{BASE_URL}/oslc/os/matrectrans"
    ]
    
    headers = {
        "Accept": "application/json",
        "apikey": API_KEY
    }
    
    for endpoint in matrectrans_endpoints:
        print(f"\n📋 Testing: {endpoint}")
        
        try:
            response = requests.get(
                endpoint,
                params={"oslc.pageSize": "1"},
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ Endpoint exists - Structure: {list(data.keys()) if isinstance(data, dict) else 'Array'}")
                    
                    if 'rdfs:member' in data and data['rdfs:member']:
                        first_record = data['rdfs:member'][0]
                        print(f"   Sample fields: {list(first_record.keys())[:10]}")
                        
                except:
                    print(f"   Response (text): {response.text[:100]}...")
            else:
                print(f"   ❌ Endpoint not accessible")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

def test_inventory_endpoint_actions():
    """Test if there are any working actions on the main inventory endpoint."""
    print(f"\n🔍 Testing Inventory Endpoint Actions")
    print("=" * 60)
    
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": API_KEY
    }
    
    # Test some potential actions that might work
    potential_actions = [
        "addtransfer",
        "createtransfer", 
        "transfer",
        "movetransfer",
        "addchange",
        "adjustbalance"
    ]
    
    for action in potential_actions:
        test_url = f"{endpoint_url}?action={action}"
        
        print(f"\n📋 Testing action: {action}")
        
        test_payload = {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT",
            "fromlocation": "LCVK-CMW-AJ",
            "tolocation": "LCVKWT-STORE",
            "quantity": 1.0
        }
        
        try:
            response = requests.post(
                test_url,
                json=test_payload,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.content:
                try:
                    data = response.json()
                    if 'oslc:Error' in data:
                        error_msg = data['oslc:Error'].get('oslc:message', '')
                        if 'not found' not in error_msg.lower():
                            print(f"   ✅ Action might exist - Error: {error_msg[:100]}...")
                        else:
                            print(f"   ❌ Action not found")
                    else:
                        print(f"   ✅ Success response: {str(data)[:100]}...")
                except:
                    print(f"   Response (text): {response.text[:100]}...")
                    
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

def test_url_encoding_approaches():
    """Test different URL encoding approaches for nested objects."""
    print(f"\n🔍 Testing URL Encoding Approaches")
    print("=" * 60)
    
    # Get a sample inventory record
    endpoint_url = f"{BASE_URL}/api/os/mxapiinventory"
    
    try:
        response = requests.get(
            endpoint_url,
            params={
                "oslc.select": "inventoryid,itemnum,siteid,location",
                "oslc.where": 'siteid="LCVKWT"',
                "oslc.pageSize": "1"
            },
            headers={"Accept": "application/json", "apikey": API_KEY},
            timeout=(3.05, 15)
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'rdfs:member' in data and data['rdfs:member']:
                record = data['rdfs:member'][0]
                inventory_id = record.get('spi:inventoryid')
                
                # Test different URL patterns
                url_patterns = [
                    f"{BASE_URL}/api/os/mxapiinventory/{inventory_id}/transfercuritem",
                    f"{BASE_URL}/api/os/mxapiinventory/{inventory_id}/spi:transfercuritem",
                    f"{BASE_URL}/api/os/mxapiinventory/{inventory_id}/transfercuritem/0",
                    f"{endpoint_url}?action=transfercuritem&inventoryid={inventory_id}"
                ]
                
                headers = {
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "apikey": API_KEY
                }
                
                for i, url in enumerate(url_patterns, 1):
                    print(f"\n📋 Pattern {i}: {url}")
                    
                    test_payload = {
                        "fromstoreloc": record.get('spi:location'),
                        "tositeid": "LCVKWT",
                        "quantity": 1.0
                    }
                    
                    try:
                        response = requests.post(
                            url,
                            json=test_payload,
                            headers=headers,
                            timeout=(3.05, 10)
                        )
                        
                        print(f"   Status: {response.status_code}")
                        
                        if response.status_code != 502:
                            if response.content:
                                try:
                                    data = response.json()
                                    print(f"   Response: {json.dumps(data, indent=2)}")
                                except:
                                    print(f"   Response (text): {response.text[:100]}...")
                        else:
                            print(f"   ❌ 502 Bad Gateway")
                            
                    except Exception as e:
                        print(f"   ❌ Error: {str(e)}")
                        
    except Exception as e:
        print(f"❌ Error getting inventory record: {str(e)}")

def main():
    """Main execution function."""
    print("🔍 Alternative Transfer Operation Approaches Testing")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Test different approaches
    test_direct_inventory_update_with_transfer()
    test_post_to_main_endpoint_with_transfer()
    test_matrectrans_endpoint()
    test_inventory_endpoint_actions()
    test_url_encoding_approaches()
    
    print("\n📊 Testing Summary")
    print("=" * 80)
    print("✅ Direct inventory update tested")
    print("✅ POST with transfer data tested")
    print("✅ MATRECTRANS endpoint tested")
    print("✅ Inventory actions tested")
    print("✅ URL encoding approaches tested")
    
    print("\n💡 Alternative Approaches Explored:")
    print("   • Direct PATCH to inventory with transfer data")
    print("   • POST to main endpoint with embedded transfer")
    print("   • Separate MATRECTRANS endpoint")
    print("   • Action parameters on main endpoint")
    print("   • Different URL encoding patterns")
    
    print("\n✅ Alternative testing completed")

if __name__ == "__main__":
    main()
