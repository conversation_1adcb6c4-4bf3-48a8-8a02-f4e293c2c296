{"investigation_date": "2025-07-16T10:47:20.002729", "endpoints_tested": ["https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory"], "authentication": "OSLC Token Session", "method_analysis": [{"method": "transfercurrentitem", "timestamp": "2025-07-16T10:47:21.031061", "test_results": [{"config_name": "API Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/transfercurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/transfercurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?wsmethod=transfercurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfercurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}], "working_patterns": [], "documentation": {}}, {"method": "transfercuritem", "timestamp": "2025-07-16T10:47:27.008146", "test_results": [{"config_name": "API Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/transfercuritem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercuritem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/transfercuritem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/transfercuritem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?wsmethod=transfercuritem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=transfercuritem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}], "working_patterns": [], "documentation": {}}, {"method": "issuecurrentitem", "timestamp": "2025-07-16T10:47:32.284109", "test_results": [{"config_name": "API Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/issuecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/issuecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/issuecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/issuecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?wsmethod=issuecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=issuecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}], "working_patterns": [], "documentation": {}}, {"method": "receivecurrentitem", "timestamp": "2025-07-16T10:47:38.168970", "test_results": [{"config_name": "API Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/receivecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - JSON", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/receivecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/receivecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Endpoint - Form Data", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory/receivecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}, "payload_type": "form", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}, {"config_name": "API Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory?wsmethod=receivecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 403, "response_type": "forbidden", "response_data": null, "response_text": "{\"Error\":{\"extendedError\":{\"moreInfo\":{\"href\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/oslc\\/error\\/messages\\/BMXAA7901E\"}},\"reasonCode\":\"BMXAA7901E\",\"message\":\"BMXAA7901E - You cannot log in at this time. Contact the system administrator.\",\"statusCode\":\"403\"}}", "error": null, "is_api_response": false}, {"config_name": "OSLC Query Parameter", "url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory?wsmethod=receivecurrentitem", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "payload_type": "json", "http_status": 200, "response_type": "html", "response_data": null, "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link", "error": null, "is_api_response": false}], "working_patterns": [], "documentation": {}}], "working_patterns": [], "api_documentation": {}}