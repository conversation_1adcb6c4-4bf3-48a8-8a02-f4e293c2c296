#!/bin/bash

# Test 4: Complete transfer
# Transfer with all optional fields

curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"itemnum":"5975-60-V00-0529","from_siteid":"LCVKWT","to_siteid":"IKWAJ","from_storeroom":"RIP001","to_storeroom":"KWAJ-1058","quantity":1.0,"from_issue_unit":"RO","from_bin":"28-800-0004","to_bin":"1058-TEMP","from_lot":"TEST","to_lot":"TEST","from_condition":"A1","to_condition":"A1"}' \
  -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
  -s
