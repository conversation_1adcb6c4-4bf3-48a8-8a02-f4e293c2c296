{"timestamp": "2025-07-16T09:33:52.453471", "base_url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo", "api_key_preview": "dj9sia0tu2...r0ahlsn70o", "summary": {"total_methods": 10, "soap_wsdl_available": false, "rest_api_available": true, "oslc_available": true, "wsmethods_available": false, "nested_objects_available": true}, "authentication": {"api_key": {"available": true, "header": "apikey", "example": "headers = {'Accept': 'application/json', 'apikey': 'your-api-key'}"}, "session": {"available": true, "method": "Browser session cookies", "example": "Use MaximoTokenManager for session authentication"}}, "endpoints": {"primary": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "oslc": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory", "status": "Both endpoints accessible with API key authentication"}, "working_methods": [{"name": "GET_Inventory_Records", "type": "REST/GET", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "method": "GET", "authentication": ["API Key", "Session"], "description": "Retrieve inventory records with OSLC filtering", "example": {"url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "method": "GET", "headers": {"Accept": "application/json", "apikey": "your-api-key"}, "params": {"oslc.select": "itemnum,siteid,location,curbaltotal,avblbalance", "oslc.where": "siteid=\"YOURSITE\" and status!=\"OBSOLETE\"", "oslc.pageSize": "50", "lean": "1"}}, "response_structure": ["member", "href", "responseInfo"], "status": "✅ CONFIRMED WORKING"}, {"name": "POST_Create_Inventory", "type": "REST/POST", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "method": "POST", "authentication": ["API Key", "Session"], "description": "Create new inventory records", "example": {"url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory", "method": "POST", "headers": {"Accept": "application/json", "Content-Type": "application/json", "apikey": "your-api-key"}, "payload": {"itemnum": "NEW-ITEM-001", "siteid": "YOURSITE", "location": "STOREROOM", "itemsetid": "ITEMSET"}}, "status": "⚠️ REQUIRES TESTING"}, {"name": "PATCH_Update_Inventory", "type": "REST/PATCH", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}", "method": "PATCH", "authentication": ["API Key", "Session"], "description": "Update existing inventory records", "example": {"url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_{base64_inventory_id}", "method": "PATCH", "headers": {"Accept": "application/json", "Content-Type": "application/json", "apikey": "your-api-key"}, "payload": {"curbaltotal": 100.0, "memo": "Updated via API"}}, "status": "⚠️ REQUIRES TESTING"}, {"name": "GET_Transfer_Current_Item", "type": "Nested Object", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/transfercuritem", "method": "GET", "authentication": ["API Key", "Session"], "description": "Access transfer current item nested object", "example": {"url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_{base64_inventory_id}/transfercuritem", "method": "GET", "headers": {"Accept": "application/json", "apikey": "your-api-key"}}, "status": "✅ CONFIRMED ACCESSIBLE"}, {"name": "GET_Inventory_Balances", "type": "Nested Object", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/invbalances", "method": "GET", "authentication": ["API Key", "Session"], "description": "Access inventory balances nested object", "example": {"url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/_{base64_inventory_id}/invbalances", "method": "GET", "headers": {"Accept": "application/json", "apikey": "your-api-key"}}, "status": "✅ CONFIRMED ACCESSIBLE"}, {"name": "GET_Inventory_Cost", "type": "Nested Object", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/invcost", "method": "GET", "authentication": ["API Key", "Session"], "description": "Access inventory cost nested object", "status": "✅ CONFIRMED ACCESSIBLE"}, {"name": "GET_Inventory_Vendor", "type": "Nested Object", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiinventory/{inventory_id}/invvendor", "method": "GET", "authentication": ["API Key", "Session"], "description": "Access inventory vendor nested object", "status": "✅ CONFIRMED ACCESSIBLE"}, {"name": "Current_Balance_Adjustment", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange", "method": "POST", "authentication": ["API Key", "Session"], "description": "Adjust current balance using MxLoader pattern", "example": {"url": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange", "method": "POST", "headers": {"Accept": "application/json", "Content-Type": "application/json", "x-method-override": "BULK", "apikey": "your-api-key"}, "payload": [{"_action": "AddChange", "itemnum": "ITEM-001", "siteid": "SITE01", "location": "STORE01", "invbalances": [{"curbal": 100.0, "conditioncode": "GOOD"}]}]}, "status": "✅ CONFIRMED WORKING"}, {"name": "Physical_Count_Adjustment", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange", "method": "POST", "authentication": ["API Key", "Session"], "description": "Adjust physical count using MxLoader pattern", "example": {"payload": [{"_action": "AddChange", "itemnum": "ITEM-001", "siteid": "SITE01", "location": "STORE01", "invbalances": [{"physcnt": 95.0, "conditioncode": "GOOD"}]}]}, "status": "✅ CONFIRMED WORKING"}, {"name": "OSLC_Query_Inventory", "type": "OSLC", "endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiinventory", "method": "GET", "authentication": ["Session"], "description": "Query inventory using OSLC endpoint", "status": "✅ CONFIRMED ACCESSIBLE"}]}