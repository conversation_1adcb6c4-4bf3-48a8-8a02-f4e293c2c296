#!/usr/bin/env python3
"""
Test mxapiinventory endpoint with curl using the EXACT same method as workorder task status update.
Uses session cookies, same headers, same URL pattern.
"""
import sys
import os
import subprocess
import tempfile
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.auth.token_manager import MaximoTokenManager

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

def extract_cookies_to_file(token_manager):
    """Extract cookies from token manager session and save to a file for curl."""
    if not hasattr(token_manager, 'session') or not token_manager.session.cookies:
        print("❌ No session or cookies found in token manager")
        return None
    
    # Create a temporary file for cookies
    cookie_fd, cookie_file = tempfile.mkstemp(suffix='.txt', prefix='maximo_cookies_')
    
    try:
        with os.fdopen(cookie_fd, 'w') as f:
            # Write cookies in Netscape format for curl
            f.write("# Netscape HTTP Cookie File\n")
            f.write("# This is a generated file! Do not edit.\n\n")
            
            for cookie in token_manager.session.cookies:
                # Format: domain, domain_specified, path, secure, expires, name, value
                domain = cookie.domain if cookie.domain else ".maximo.com"
                domain_specified = "TRUE" if cookie.domain_specified else "FALSE"
                path = cookie.path if cookie.path else "/"
                secure = "TRUE" if cookie.secure else "FALSE"
                expires = str(int(cookie.expires)) if cookie.expires else "0"
                
                f.write(f"{domain}\t{domain_specified}\t{path}\t{secure}\t{expires}\t{cookie.name}\t{cookie.value}\n")
        
        print(f"✅ Cookies extracted to: {cookie_file}")
        print(f"📊 Total cookies: {len(token_manager.session.cookies)}")
        
        return cookie_file
        
    except Exception as e:
        print(f"❌ Error writing cookies: {e}")
        os.unlink(cookie_file)
        return None

def test_mxapiinventory_like_workorder(cookie_file):
    """Test mxapiinventory using EXACT same pattern as workorder task status update."""
    
    print(f"\n🔧 Testing mxapiinventory using workorder pattern")
    print("=" * 60)
    
    # The exact payload structure from your example
    payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKNT",
            "location": "RIP001",
            "issueunit": "RO",
            "minlevel": 0,
            "orderqty": 1,
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 30,
                    "physcnt": 0,
                    "physcntdate": "2021-09-24T09:16:12",
                    "conditioncode": "A1",
                    "lotnum": "",
                    "reconciled": True,
                    "memo": "",
                    "controlacc": "",
                    "shrinkageacc": ""
                }
            ]
        }
    ]
    
    # Convert to JSON string
    payload_json = json.dumps(payload, separators=(',', ':'))
    
    print(f"📋 Payload:")
    print(json.dumps(payload, indent=2))
    
    # Use OSLC route like workorder (not /api route)
    url = f"{BASE_URL}/oslc/os/mxapiinventory"
    
    print(f"\n🌐 URL: {url}")
    print(f"🍪 Cookie File: {cookie_file}")
    print("")
    
    # Build curl command using EXACT same headers as workorder task status update
    cmd = [
        'curl', '-X', 'POST',
        url,
        '-H', 'Accept: application/json',
        '-H', 'Content-Type: application/json',
        '-d', payload_json,
        '-b', cookie_file,
        '-v',
        '--max-time', '30'
    ]
    
    print("🚀 Running curl command (using workorder pattern)...")
    print(f"Command: curl -X POST {url} [headers] [payload]")
    print("")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print(f"📊 Exit code: {result.returncode}")
        print(f"📤 STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print(f"📥 STDERR:")
            print(result.stderr)
        
        # Try to parse the response as JSON
        if result.stdout:
            try:
                response_data = json.loads(result.stdout)
                print(f"\n✅ Response parsed as JSON:")
                print(json.dumps(response_data, indent=2))
                
                # Check for success indicators
                if isinstance(response_data, list) and len(response_data) > 0:
                    first_item = response_data[0]
                    if 'inventoryid' in first_item or 'itemnum' in first_item:
                        print(f"\n🎉 SUCCESS: Inventory adjustment processed!")
                        print(f"📋 Item: {first_item.get('itemnum', 'Unknown')}")
                        if 'inventoryid' in first_item:
                            print(f"📋 Inventory ID: {first_item['inventoryid']}")
                        return True
                elif 'oslc:Error' in response_data:
                    error_info = response_data['oslc:Error']
                    print(f"\n❌ MAXIMO ERROR:")
                    print(f"📋 Status: {error_info.get('oslc:statusCode', 'Unknown')}")
                    print(f"📋 Reason: {error_info.get('spi:reasonCode', 'Unknown')}")
                    print(f"📋 Message: {error_info.get('oslc:message', 'Unknown')}")
                    return False
                elif 'Error' in response_data:
                    print(f"\n❌ API ERROR:")
                    print(json.dumps(response_data['Error'], indent=2))
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"\n⚠️ Response is not valid JSON: {e}")
                print("Raw response:")
                print(result.stdout[:500])
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 30 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running curl: {e}")
        return False

def test_workorder_pattern_first(cookie_file):
    """Test that the workorder pattern still works to verify our session is good."""
    
    print(f"\n🔧 Testing workorder pattern first (verification)")
    print("=" * 50)
    
    # Test a simple workorder status change to verify session works
    test_wonum = "15643629"  # Use a known work order
    test_status = "INPRG"
    
    # Use the exact same pattern as the working workorder task status update
    url = f"{BASE_URL}/oslc/os/mxapiwodetail"
    action_url = f"{url}?action=wsmethod:changeStatus"
    
    request_data = {
        'wonum': test_wonum,
        'status': test_status
    }
    
    payload_json = json.dumps(request_data)
    
    cmd = [
        'curl', '-X', 'POST',
        action_url,
        '-H', 'Accept: application/json',
        '-H', 'Content-Type: application/json',
        '-H', 'X-method-override: PATCH',
        '-d', payload_json,
        '-b', cookie_file,
        '-v',
        '--max-time', '15'
    ]
    
    print(f"🌐 URL: {action_url}")
    print(f"📋 Data: {payload_json}")
    print("🚀 Running workorder test...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        print(f"📊 Exit code: {result.returncode}")
        
        if result.stdout:
            try:
                response_data = json.loads(result.stdout)
                if isinstance(response_data, list) and len(response_data) > 0:
                    print(f"✅ Workorder pattern works! Session is valid")
                    return True
                elif 'oslc:Error' in response_data:
                    error_info = response_data['oslc:Error']
                    print(f"⚠️ Workorder error: {error_info.get('oslc:message', 'Unknown')}")
                else:
                    print(f"⚠️ Unexpected workorder response: {list(response_data.keys())}")
            except json.JSONDecodeError:
                print(f"⚠️ Workorder response not JSON: {result.stdout[:200]}")
        
        if result.stderr:
            print(f"📥 STDERR: {result.stderr}")
            
        return False
        
    except Exception as e:
        print(f"❌ Error in workorder test: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Starting mxapiinventory test using workorder pattern")
    print("=" * 60)
    
    # Initialize token manager
    token_manager = MaximoTokenManager(BASE_URL)
    
    # Check if logged in
    if not token_manager.is_logged_in():
        print("❌ Not logged in to Maximo. Please login first through the web app.")
        print("💡 Go to http://127.0.0.1:5010 and login, then run this script again.")
        return False
    
    print(f"✅ Logged in to Maximo as: {getattr(token_manager, 'username', 'Unknown')}")
    
    # Extract cookies
    cookie_file = extract_cookies_to_file(token_manager)
    
    if not cookie_file:
        print("❌ Failed to extract cookies")
        return False
    
    try:
        # Test workorder pattern first to verify session
        if test_workorder_pattern_first(cookie_file):
            print("\n" + "=" * 60)
            # If workorder works, test mxapiinventory with same pattern
            success = test_mxapiinventory_like_workorder(cookie_file)
            
            if success:
                print("\n🎉 CURL TEST USING WORKORDER PATTERN COMPLETED SUCCESSFULLY!")
                print("✅ The mxapiinventory endpoint works with session authentication")
                print("✅ Your payload structure is correct")
                print("✅ Ready to integrate into the QR scanner app")
            else:
                print("\n❌ CURL TEST USING WORKORDER PATTERN FAILED")
                print("🔍 Check the error messages above for details")
                
            return success
        else:
            print("\n❌ Workorder test failed - session authentication issue")
            return False
            
    finally:
        # Clean up cookie file
        try:
            os.unlink(cookie_file)
            print(f"\n🧹 Cleaned up cookie file: {cookie_file}")
        except:
            pass

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
