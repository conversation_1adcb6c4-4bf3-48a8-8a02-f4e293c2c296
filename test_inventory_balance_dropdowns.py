#!/usr/bin/env python3
"""
Test script to validate the inventory balance dropdown functionality.
This script tests the complete data flow from MXAPIINVENTORY endpoint 
through the backend service to ensure accurate real-time inventory 
balance information is displayed without hardcoded fallbacks.
"""

import requests
import json
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class InventoryBalanceDropdownTester:
    def __init__(self):
        self.base_url = os.getenv('MAXIMO_BASE_URL', 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
        self.flask_url = 'http://127.0.0.1:5010'
        self.session = requests.Session()
        self.test_results = []
        
    def log_result(self, test_name, success, message, details=None):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'details': details or {}
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name} - {message}")
        
        if details and not success:
            print(f"   Details: {json.dumps(details, indent=2)}")
    
    def test_mxapiinventory_endpoint_direct(self):
        """Test direct access to MXAPIINVENTORY endpoint"""
        print("\n🔍 Testing direct MXAPIINVENTORY endpoint access...")
        
        try:
            # Test with a known item and site
            test_params = {
                'itemnum': '5975-60-V00-0001',
                'siteid': 'LCVKWT',
                'location': 'RIP001'
            }
            
            api_url = f"{self.base_url}/oslc/os/mxapiinventory"
            params = {
                "oslc.select": "itemnum,siteid,location,status,invbalances",
                "oslc.where": f'itemnum="{test_params["itemnum"]}" and siteid="{test_params["siteid"]}" and location="{test_params["location"]}" and status="ACTIVE"',
                "oslc.pageSize": "10",
                "lean": "1"
            }
            
            # This would require authentication - for now just test the URL structure
            self.log_result(
                "MXAPIINVENTORY Endpoint Structure",
                True,
                f"Endpoint URL properly constructed: {api_url}",
                {'url': api_url, 'params': params}
            )
            
        except Exception as e:
            self.log_result(
                "MXAPIINVENTORY Endpoint Structure",
                False,
                f"Error constructing endpoint: {str(e)}"
            )
    
    def test_flask_inventory_balance_api(self):
        """Test Flask API endpoint for inventory balances"""
        print("\n🔍 Testing Flask inventory balance API endpoint...")
        
        try:
            # Test the Flask API endpoint
            api_url = f"{self.flask_url}/api/inventory/issue-current-item/inventory-balances"
            params = {
                'itemnum': '5975-60-V00-0001',
                'siteid': 'LCVKWT',
                'location': 'RIP001'
            }
            
            response = self.session.get(api_url, params=params, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Check response structure
                    required_fields = ['success', 'balances']
                    missing_fields = [field for field in required_fields if field not in data]
                    
                    if missing_fields:
                        self.log_result(
                            "Flask API Response Structure",
                            False,
                            f"Missing required fields: {missing_fields}",
                            {'response': data}
                        )
                        return
                    
                    if data.get('success'):
                        balances = data.get('balances', {})
                        balance_fields = ['bins', 'lots', 'conditions']
                        
                        # Check if balance fields exist
                        missing_balance_fields = [field for field in balance_fields if field not in balances]
                        
                        if missing_balance_fields:
                            self.log_result(
                                "Flask API Balance Fields",
                                False,
                                f"Missing balance fields: {missing_balance_fields}",
                                {'balances': balances}
                            )
                        else:
                            # Check if we have actual data (not hardcoded)
                            bins = balances.get('bins', [])
                            lots = balances.get('lots', [])
                            conditions = balances.get('conditions', [])
                            
                            # Check for hardcoded values
                            hardcoded_bins = ['BIN001', 'BIN002', 'BIN003']
                            hardcoded_lots = ['LOT001', 'LOT002', 'LOT003']
                            hardcoded_conditions = ['GOOD', 'FAIR', 'POOR']
                            
                            has_hardcoded_bins = any(bin_val in hardcoded_bins for bin_val in bins)
                            has_hardcoded_lots = any(lot_val in hardcoded_lots for lot_val in lots)
                            has_hardcoded_conditions = any(cond_val in hardcoded_conditions for cond_val in conditions)
                            
                            if has_hardcoded_bins or has_hardcoded_lots or has_hardcoded_conditions:
                                self.log_result(
                                    "Flask API Hardcoded Values Check",
                                    False,
                                    "Found hardcoded values in response",
                                    {
                                        'bins': bins,
                                        'lots': lots,
                                        'conditions': conditions,
                                        'hardcoded_detected': {
                                            'bins': has_hardcoded_bins,
                                            'lots': has_hardcoded_lots,
                                            'conditions': has_hardcoded_conditions
                                        }
                                    }
                                )
                            else:
                                self.log_result(
                                    "Flask API Dynamic Data",
                                    True,
                                    f"API returns dynamic data - {len(bins)} bins, {len(lots)} lots, {len(conditions)} conditions",
                                    {
                                        'bins_count': len(bins),
                                        'lots_count': len(lots),
                                        'conditions_count': len(conditions),
                                        'sample_bins': bins[:3] if bins else [],
                                        'sample_lots': lots[:3] if lots else [],
                                        'sample_conditions': conditions[:3] if conditions else []
                                    }
                                )
                    else:
                        self.log_result(
                            "Flask API Success Status",
                            False,
                            f"API returned success=false: {data.get('error', 'Unknown error')}",
                            {'response': data}
                        )
                        
                except json.JSONDecodeError as e:
                    self.log_result(
                        "Flask API JSON Response",
                        False,
                        f"Invalid JSON response: {str(e)}",
                        {'response_text': response.text[:200]}
                    )
            else:
                self.log_result(
                    "Flask API HTTP Status",
                    False,
                    f"HTTP {response.status_code}: {response.text[:100]}",
                    {'status_code': response.status_code}
                )
                
        except requests.exceptions.ConnectionError:
            self.log_result(
                "Flask API Connection",
                False,
                "Cannot connect to Flask application. Make sure it's running on http://127.0.0.1:5010"
            )
        except Exception as e:
            self.log_result(
                "Flask API General",
                False,
                f"Unexpected error: {str(e)}"
            )
    
    def test_frontend_integration(self):
        """Test frontend integration points"""
        print("\n🔍 Testing frontend integration...")
        
        # Check if JavaScript file exists and has been updated
        js_file_path = 'frontend/static/js/inventory_management.js'
        
        if os.path.exists(js_file_path):
            with open(js_file_path, 'r') as f:
                js_content = f.read()
                
            # Check for removal of hardcoded values
            hardcoded_patterns = [
                'BIN001', 'BIN002', 'BIN003',
                'LOT001', 'LOT002', 'LOT003',
                'addDefaultInventoryBalanceOptions'
            ]
            
            found_hardcoded = []
            for pattern in hardcoded_patterns:
                if pattern in js_content:
                    found_hardcoded.append(pattern)
            
            if found_hardcoded:
                self.log_result(
                    "Frontend Hardcoded Values Removal",
                    False,
                    f"Found remaining hardcoded patterns: {found_hardcoded}"
                )
            else:
                self.log_result(
                    "Frontend Hardcoded Values Removal",
                    True,
                    "No hardcoded values found in JavaScript"
                )
            
            # Check for Select2 integration
            select2_patterns = [
                'populateSearchableDropdown',
                'initializeIssueModalSelect2',
                'cleanupIssueModalSelect2',
                'select2-dropdown-mobile-friendly'
            ]
            
            found_select2 = []
            for pattern in select2_patterns:
                if pattern in js_content:
                    found_select2.append(pattern)
            
            if len(found_select2) >= 3:  # Should have most Select2 integration patterns
                self.log_result(
                    "Frontend Select2 Integration",
                    True,
                    f"Select2 integration found: {found_select2}"
                )
            else:
                self.log_result(
                    "Frontend Select2 Integration",
                    False,
                    f"Missing Select2 integration patterns. Found: {found_select2}"
                )
                
        else:
            self.log_result(
                "Frontend JavaScript File",
                False,
                f"JavaScript file not found: {js_file_path}"
            )
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting Inventory Balance Dropdown Tests...")
        print("=" * 60)
        
        self.test_mxapiinventory_endpoint_direct()
        self.test_flask_inventory_balance_api()
        self.test_frontend_integration()
        
        # Generate summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        
        return failed_tests == 0

if __name__ == "__main__":
    tester = InventoryBalanceDropdownTester()
    success = tester.run_all_tests()
    
    # Save detailed results
    with open('inventory_balance_test_results.json', 'w') as f:
        json.dump(tester.test_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: inventory_balance_test_results.json")
    
    sys.exit(0 if success else 1)
