#!/usr/bin/env python3
"""
Extract Session Cookies and Test Transfer via Curl
==================================================

Extract the actual session cookies from MaximoTokenManager and use them
to test transfer operations via curl until we get a 204 response.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import subprocess
import time
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager

def extract_session_cookies():
    """Extract session cookies from MaximoTokenManager."""
    print("🍪 EXTRACTING SESSION COOKIES")
    print("=" * 40)
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return None
    
    print("✅ Authenticated with session")
    
    # Extract cookies from session
    cookies = []
    for cookie in token_manager.session.cookies:
        cookie_str = f"{cookie.name}={cookie.value}"
        cookies.append(cookie_str)
        print(f"🍪 {cookie.name}: {cookie.value[:20]}...")
    
    if not cookies:
        print("❌ No cookies found in session")
        return None
    
    # Create cookie string for curl
    cookie_string = "; ".join(cookies)
    print(f"✅ Extracted {len(cookies)} cookies")
    
    return cookie_string, token_manager

def test_transfer_with_cookies(cookie_string):
    """Test transfer operations with extracted cookies."""
    print("\n🚀 TESTING TRANSFER WITH EXTRACTED COOKIES")
    print("=" * 50)
    
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    endpoint = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    print(f"🔗 Endpoint: {endpoint}")
    print(f"🍪 Using extracted session cookies")
    
    # Test payloads
    test_cases = [
        {
            "name": "Test 1: Minimal transfer",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 2: With bins",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 3: With lots and conditions",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP",
                            "fromlotnum": "TEST",
                            "tolotnum": "TEST",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 4: Replace action",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "Replace",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Test 5: Different bin combination",
            "payload": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "58-A-A01-1"
                        }
                    ]
                }
            ]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 {test_case['name']}")
        print("=" * 50)
        
        payload_json = json.dumps(test_case['payload'])
        
        # Prepare curl command
        cmd = [
            'curl', '-X', 'POST', endpoint,
            '-H', 'Accept: application/json',
            '-H', 'Content-Type: application/json',
            '-H', 'x-method-override: BULK',
            '-H', f'Cookie: {cookie_string}',
            '-d', payload_json,
            '-w', '\\nHTTP_STATUS:%{http_code}\\nTIME:%{time_total}\\n',
            '-s'
        ]
        
        try:
            print(f"🔄 Submitting {test_case['name']}...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            output = result.stdout
            
            # Extract HTTP status
            status_code = None
            if 'HTTP_STATUS:' in output:
                status_line = [line for line in output.split('\n') if 'HTTP_STATUS:' in line][0]
                status_code = int(status_line.split(':')[1])
            
            print(f"📊 HTTP Status: {status_code}")
            
            # Parse JSON response
            json_part = output.split('HTTP_STATUS:')[0].strip()
            if json_part:
                try:
                    response_data = json.loads(json_part)
                    print(f"📋 Response: {json.dumps(response_data, indent=2)}")
                    
                    # Check for success
                    if status_code == 204:
                        print("🎉 SUCCESS! Got 204 status!")
                        return True, test_case, response_data
                    
                    if isinstance(response_data, list) and len(response_data) > 0:
                        first_item = response_data[0]
                        if '_responsemeta' in first_item:
                            meta_status = first_item['_responsemeta'].get('status')
                            if meta_status == '204':
                                print("🎉 SUCCESS! Found 204 in response meta!")
                                return True, test_case, response_data
                            else:
                                print(f"❌ Meta status: {meta_status}")
                        
                        if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                            error = first_item['_responsedata']['Error']
                            print(f"❌ Error: {error.get('reasonCode')} - {error.get('message')}")
                
                except json.JSONDecodeError:
                    print(f"📄 Response (non-JSON): {json_part}")
            
            if status_code == 403:
                print("❌ 403 Forbidden - Authentication issue")
            elif status_code == 400:
                print("⚠️  400 Bad Request - Payload issue")
            else:
                print(f"⚠️  Unexpected status: {status_code}")
                
        except subprocess.TimeoutExpired:
            print("⏱️  Timeout")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Brief pause between tests
        time.sleep(2)
    
    return False, None, None

def test_with_python_session(token_manager):
    """Test the same transfer using Python session for comparison."""
    print("\n🐍 TESTING WITH PYTHON SESSION FOR COMPARISON")
    print("=" * 50)
    
    import requests
    
    endpoint = f"{token_manager.base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "siteid": "LCVKWT",
            "location": "RIP001",
            "matrectrans": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "issuetype": "TRANSFER",
                    "quantity": 1.0,
                    "fromsiteid": "LCVKWT",
                    "tositeid": "IKWAJ",
                    "fromstoreloc": "RIP001",
                    "tostoreloc": "KWAJ-1058",
                    "frombinnum": "28-800-0004",
                    "tobinnum": "1058-TEMP"
                }
            ]
        }
    ]
    
    try:
        response = token_manager.session.post(
            endpoint,
            json=payload,
            headers={
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'x-method-override': 'BULK'
            },
            timeout=(3.05, 30)
        )
        
        print(f"📊 Python Session Status: {response.status_code}")
        
        if response.text:
            try:
                data = response.json()
                print(f"📋 Python Response: {json.dumps(data, indent=2)}")
            except json.JSONDecodeError:
                print(f"📄 Python Response (non-JSON): {response.text}")
        
        return response.status_code == 200 or response.status_code == 204
        
    except Exception as e:
        print(f"❌ Python session error: {str(e)}")
        return False

def main():
    """Main function."""
    print("🚀 EXTRACT SESSION AND TEST TRANSFER")
    print("=" * 40)
    
    # Extract session cookies
    result = extract_session_cookies()
    if not result:
        print("❌ Failed to extract session cookies")
        return
    
    cookie_string, token_manager = result
    
    # Test with Python session first
    python_success = test_with_python_session(token_manager)
    print(f"\n🐍 Python session test: {'✅ Success' if python_success else '❌ Failed'}")
    
    # Test with curl using extracted cookies
    success, successful_test, response_data = test_transfer_with_cookies(cookie_string)
    
    if success:
        print(f"\n🎉 SUCCESS FOUND!")
        print("=" * 30)
        print(f"Successful test: {successful_test['name']}")
        print(f"Payload: {json.dumps(successful_test['payload'], indent=2)}")
        
        # Save successful payload
        with open('successful_curl_transfer_payload.json', 'w') as f:
            json.dump(successful_test['payload'], f, indent=2)
        print(f"\n💾 Successful payload saved to: successful_curl_transfer_payload.json")
    else:
        print(f"\n❌ NO SUCCESS FOUND")
        print("All curl tests failed. Check responses above for error details.")

if __name__ == "__main__":
    main()
