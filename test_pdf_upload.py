#!/usr/bin/env python3
"""
Test script to upload a PDF and then test downloading it
"""

import requests
import json
import os

def create_test_pdf():
    """Create a simple test PDF file"""
    # Create a minimal PDF content
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF Content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    
    with open('test_upload.pdf', 'wb') as f:
        f.write(pdf_content)
    
    return 'test_upload.pdf', len(pdf_content)

def test_pdf_upload_and_download():
    """Test uploading a PDF and then downloading it"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing PDF upload and download for {wonum}")
    print("=" * 60)
    
    # Create test PDF
    pdf_filename, pdf_size = create_test_pdf()
    print(f"📄 Created test PDF: {pdf_filename} ({pdf_size} bytes)")
    
    # Upload the PDF
    upload_url = f'{base_url}/api/workorder/{wonum}/attachments/upload'
    
    try:
        with open(pdf_filename, 'rb') as f:
            files = {'file': (pdf_filename, f, 'application/pdf')}
            data = {
                'description': 'Test PDF upload for debugging',
                'category': 'Attachments'
            }
            
            print(f"\n📤 Uploading PDF...")
            upload_response = requests.post(upload_url, files=files, data=data, timeout=60)
            
            print(f"   🔄 Upload Status: {upload_response.status_code}")
            
            if upload_response.status_code == 200:
                upload_data = upload_response.json()
                if upload_data.get('success'):
                    print(f"   ✅ Upload successful!")
                    print(f"   📋 Response: {upload_data}")
                    
                    # Wait a moment for the upload to be processed
                    import time
                    time.sleep(2)
                    
                    # Get the updated attachments list to find our PDF
                    attachments_url = f'{base_url}/api/workorder/{wonum}/attachments'
                    attachments_response = requests.get(attachments_url, timeout=30)
                    
                    if attachments_response.status_code == 200:
                        attachments_data = attachments_response.json()
                        if attachments_data.get('success') and attachments_data.get('attachments'):
                            attachments = attachments_data['attachments']
                            
                            # Find our uploaded PDF
                            uploaded_pdf = None
                            for attachment in attachments:
                                if (attachment.get('filename', '').lower() == pdf_filename.lower() and 
                                    attachment.get('changeby') == 'SOFG118757'):
                                    uploaded_pdf = attachment
                                    break
                            
                            if uploaded_pdf:
                                docinfoid = uploaded_pdf.get('docinfoid')
                                stored_size = uploaded_pdf.get('original_data', {}).get('describedBy', {}).get('attachmentSize', 0)
                                
                                print(f"\n📋 Found uploaded PDF:")
                                print(f"   📄 Filename: {uploaded_pdf.get('filename')}")
                                print(f"   🆔 DocInfoID: {docinfoid}")
                                print(f"   📊 Stored Size: {stored_size} bytes")
                                print(f"   📊 Original Size: {pdf_size} bytes")
                                
                                # Now test downloading it
                                print(f"\n📥 Testing download of uploaded PDF...")
                                download_url = f'{base_url}/api/workorder/{wonum}/attachments/{docinfoid}/download'
                                
                                download_response = requests.get(download_url, timeout=60)
                                print(f"   📤 Download URL: {download_url}")
                                print(f"   🔄 Status: {download_response.status_code}")
                                print(f"   📊 Content Length: {len(download_response.content)} bytes")
                                print(f"   📋 Content Type: {download_response.headers.get('content-type', 'Unknown')}")
                                
                                if download_response.status_code == 200:
                                    content = download_response.content
                                    
                                    if content.startswith(b'%PDF'):
                                        print(f"   ✅ SUCCESS! Downloaded PDF content")
                                        print(f"   📄 PDF version: {content[:8].decode('utf-8', errors='ignore')}")
                                        
                                        # Save the downloaded PDF
                                        downloaded_filename = f"downloaded_{pdf_filename}"
                                        with open(downloaded_filename, 'wb') as f:
                                            f.write(content)
                                        print(f"   💾 Saved to: {downloaded_filename}")
                                        
                                        # Compare sizes
                                        if len(content) == pdf_size:
                                            print(f"   🎉 PERFECT! Downloaded size matches original exactly")
                                            return True
                                        else:
                                            print(f"   ⚠️  Size mismatch: downloaded {len(content)} vs original {pdf_size}")
                                            return True  # Still success, just size difference
                                    else:
                                        print(f"   ❌ Downloaded content is not a PDF: {content[:50]}")
                                else:
                                    print(f"   ❌ Download failed")
                                    try:
                                        error_data = download_response.json()
                                        print(f"   📝 Error: {error_data.get('error')}")
                                    except:
                                        print(f"   📝 Response: {download_response.text[:200]}")
                            else:
                                print(f"   ❌ Could not find uploaded PDF in attachments list")
                        else:
                            print(f"   ❌ Failed to get updated attachments list")
                    else:
                        print(f"   ❌ Failed to get attachments: {attachments_response.status_code}")
                else:
                    print(f"   ❌ Upload failed: {upload_data}")
            else:
                print(f"   ❌ Upload failed with status {upload_response.status_code}")
                print(f"   📝 Response: {upload_response.text[:200]}")
    
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    finally:
        # Clean up test file
        if os.path.exists(pdf_filename):
            os.remove(pdf_filename)
            print(f"\n🧹 Cleaned up test file: {pdf_filename}")
    
    return False

if __name__ == "__main__":
    print("🧪 Testing PDF Upload and Download")
    print("=" * 60)
    
    success = test_pdf_upload_and_download()
    
    print("\n" + "=" * 60)
    print(f"🎯 PDF Test Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if success:
        print("🎉 PDF upload and download are working perfectly!")
        print("📋 The issue is with older PDFs that have attachmentSize: 0")
        print("📋 Our download logic works correctly for properly stored files")
    else:
        print("⚠️  PDF functionality needs investigation")
