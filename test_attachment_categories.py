#!/usr/bin/env python3
"""
Test script to verify all three Maximo attachment categories work correctly
"""

import requests
import os
import time

def test_attachment_categories():
    """Test uploading files to all three primary Maximo categories"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    # Test files for each category
    test_files = [
        {
            'name': 'test_attachment.txt',
            'content': b'This is a test attachment file for the Attachments category.',
            'content_type': 'text/plain',
            'doctype': 'Attachments',
            'description': 'Test file for Attachments category'
        },
        {
            'name': 'test_diagram.pdf',
            'content': b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF',
            'content_type': 'application/pdf',
            'doctype': 'Diagrams',
            'description': 'Test diagram for Diagrams category'
        },
        {
            'name': 'test_image.png',
            'content': b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82',
            'content_type': 'image/png',
            'doctype': 'Images',
            'description': 'Test image for Images category'
        }
    ]
    
    print(f"🧪 Testing attachment categories for work order {wonum}")
    print("=" * 60)
    
    success_count = 0
    total_tests = len(test_files)
    
    for i, test_file in enumerate(test_files, 1):
        print(f"\n📎 Test {i}/{total_tests}: Uploading {test_file['name']} to {test_file['doctype']} category")
        
        # Prepare the upload
        files = {
            'file': (test_file['name'], test_file['content'], test_file['content_type'])
        }
        
        data = {
            'description': test_file['description'],
            'doctype': test_file['doctype']
        }
        
        # Upload the file
        url = f'{base_url}/api/workorder/{wonum}/attachments'
        
        try:
            response = requests.post(url, files=files, data=data, timeout=30)
            
            print(f"   📤 Upload URL: {url}")
            print(f"   📋 Payload: {data}")
            print(f"   📄 File: {test_file['name']} ({len(test_file['content'])} bytes)")
            print(f"   🔄 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ SUCCESS: {test_file['doctype']} category upload successful!")
                    success_count += 1
                else:
                    print(f"   ❌ FAILED: {result.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ FAILED: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   📝 Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"   📝 Response: {response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
        
        # Small delay between uploads
        if i < total_tests:
            time.sleep(1)
    
    print("\n" + "=" * 60)
    print(f"🎯 SUMMARY: {success_count}/{total_tests} category tests passed")
    
    if success_count == total_tests:
        print("🎉 ALL TESTS PASSED! All three Maximo categories are working correctly.")
    else:
        print(f"⚠️  {total_tests - success_count} tests failed. Check the logs above for details.")
    
    print("\n📋 Next Steps:")
    print("1. Check the attachments tab in the web interface")
    print("2. Verify that category badges are displayed correctly")
    print("3. Test the auto-selection logic by uploading different file types")
    print("4. Verify download and view functionality")

if __name__ == "__main__":
    test_attachment_categories()
