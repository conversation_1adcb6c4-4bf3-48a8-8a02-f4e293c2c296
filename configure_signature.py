#!/usr/bin/env python3
"""
Configure signature system for COMP status
"""
import requests
import json

BASE_URL = "http://localhost:5010"

def configure_signature_for_comp():
    """Configure signature requirement for COMP status"""
    
    print("🔧 Configuring Signature System for COMP Status")
    print("=" * 50)
    
    # Configuration for COMP status
    config_data = {
        "statuses": ["COMP"],
        "scope": ["parent", "task"],
        "enabled": True
    }
    
    print(f"📝 Setting configuration: {json.dumps(config_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/admin/signature-config",
            json=config_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Configuration saved successfully!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ Failed to save configuration: {response.status_code}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_configuration():
    """Verify the configuration was saved"""
    print("\n🔍 Verifying Configuration...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/admin/signature-config")
        if response.status_code == 200:
            result = response.json()
            config = result.get('config', {})
            print(f"📄 Current config: {json.dumps(config, indent=2)}")
            
            if (config.get('enabled') and 
                'COMP' in config.get('statuses', []) and 
                'task' in config.get('scope', [])):
                print("✅ Configuration is correct!")
                return True
            else:
                print("❌ Configuration is not correct")
                return False
        else:
            print(f"❌ Failed to get configuration: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_signature_requirement():
    """Test if signature is required for COMP status"""
    print("\n🧪 Testing Signature Requirement...")
    
    test_data = {"status": "COMP", "wo_type": "task"}
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/admin/signature-required",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            required = result.get('signature_required', False)
            print(f"📄 Test result: {json.dumps(result, indent=2)}")
            
            if required:
                print("✅ Signature requirement is working!")
                return True
            else:
                print("❌ Signature requirement is NOT working")
                return False
        else:
            print(f"❌ Failed to test requirement: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = configure_signature_for_comp()
    if success:
        success = verify_configuration()
        if success:
            success = test_signature_requirement()
            if success:
                print("\n🎉 SUCCESS! Signature system is configured and working!")
                print("📝 Now when you change a task status to COMP, it should ask for signature.")
            else:
                print("\n💥 Configuration saved but signature requirement test failed.")
        else:
            print("\n💥 Configuration saved but verification failed.")
    else:
        print("\n💥 Failed to configure signature system.")
