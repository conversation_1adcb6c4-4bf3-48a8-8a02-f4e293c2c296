#!/usr/bin/env python3
"""
MXAPIINVENTORY Investigation using API Key Authentication

This script investigates the MXAPIINVENTORY endpoint using API key authentication
which has been shown to work in the existing documentation.

Author: Augment Agent
Date: 2025-01-15
"""

import sys
import os
import json
import requests
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.auth.token_manager import Maximo<PERSON><PERSON><PERSON><PERSON><PERSON>

def get_api_key():
    """Get API key from token manager."""
    token_manager = MaximoTokenManager('https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo')
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return None
        
    api_key = token_manager.get_api_key()
    if api_key:
        print(f"✅ API Key obtained: {api_key[:10]}...{api_key[-10:]}")
        return api_key
    else:
        print("❌ Failed to get API key")
        return None

def test_get_with_apikey(api_key):
    """Test GET operations using API key authentication."""
    print("\n🔍 Testing GET Operations with API Key")
    print("=" * 60)
    
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    
    # Test 1: Basic field discovery
    print("\n📋 Test 1: Field Discovery with Wildcard")
    api_url = f"{base_url}/api/os/mxapiinventory"
    
    params = {
        "oslc.select": "*",
        "oslc.where": 'siteid="LCVKWT"',
        "oslc.pageSize": "1",
        "lean": "0"
    }
    
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    try:
        response = requests.get(
            api_url,
            params=params,
            headers=headers,
            timeout=(3.05, 30)
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                members = data.get('member', [])
                print(f"Records Found: {len(members)}")
                
                if members:
                    first_record = members[0]
                    print(f"Total Fields: {len(first_record.keys())}")
                    
                    # Categorize fields
                    field_categories = {
                        'identifiers': [],
                        'quantities': [],
                        'dates': [],
                        'locations': [],
                        'financial': [],
                        'nested_arrays': [],
                        'other': []
                    }
                    
                    for field_name, value in first_record.items():
                        if any(keyword in field_name.lower() for keyword in ['id', 'num', 'code']):
                            field_categories['identifiers'].append(field_name)
                        elif any(keyword in field_name.lower() for keyword in ['qty', 'bal', 'count', 'level']):
                            field_categories['quantities'].append(field_name)
                        elif any(keyword in field_name.lower() for keyword in ['date', 'time']):
                            field_categories['dates'].append(field_name)
                        elif any(keyword in field_name.lower() for keyword in ['location', 'site', 'bin']):
                            field_categories['locations'].append(field_name)
                        elif any(keyword in field_name.lower() for keyword in ['cost', 'price', 'acc']):
                            field_categories['financial'].append(field_name)
                        elif isinstance(value, list):
                            field_categories['nested_arrays'].append(field_name)
                        else:
                            field_categories['other'].append(field_name)
                    
                    print("\n📊 Field Categories:")
                    for category, fields in field_categories.items():
                        if fields:
                            print(f"  {category.replace('_', ' ').title()}: {len(fields)} fields")
                            print(f"    Examples: {', '.join(fields[:3])}")
                            if len(fields) > 3:
                                print(f"    ... and {len(fields) - 3} more")
                    
                    # Analyze nested arrays
                    print("\n🔗 Nested Array Structures:")
                    for field_name, value in first_record.items():
                        if isinstance(value, list) and value:
                            if isinstance(value[0], dict):
                                print(f"  {field_name}: Array of objects")
                                print(f"    Object keys: {list(value[0].keys())}")
                            else:
                                print(f"  {field_name}: Array of {type(value[0]).__name__}")
                    
                    return {
                        'success': True,
                        'total_fields': len(first_record.keys()),
                        'field_categories': field_categories,
                        'sample_record': first_record
                    }
                else:
                    print("No records found")
                    return {'success': False, 'error': 'No records found'}
                    
            except json.JSONDecodeError as e:
                print(f"JSON Parse Error: {e}")
                print(f"Response preview: {response.text[:200]}")
                return {'success': False, 'error': f'JSON parse error: {e}'}
        else:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:300]}")
            return {'success': False, 'status_code': response.status_code, 'error': response.text[:300]}
            
    except Exception as e:
        print(f"Exception: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_post_with_apikey(api_key):
    """Test POST operations using API key authentication."""
    print("\n🔍 Testing POST Operations with API Key")
    print("=" * 60)
    
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    api_url = f"{base_url}/api/os/mxapiinventory"
    
    # Test different POST actions
    test_actions = [
        {
            'name': 'AddChange',
            'payload': [
                {
                    "_action": "AddChange",
                    "itemnum": "TEST-API-DISCOVERY",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "TEST-LOC"
                }
            ]
        },
        {
            'name': 'Create',
            'payload': [
                {
                    "_action": "Create",
                    "itemnum": "TEST-API-CREATE",
                    "siteid": "LCVKWT"
                }
            ]
        }
    ]
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "apikey": api_key
    }
    
    results = {}
    
    for test in test_actions:
        print(f"\n📋 Testing Action: {test['name']}")
        
        try:
            response = requests.post(
                api_url,
                json=test['payload'],
                headers=headers,
                timeout=(3.05, 30)
            )
            
            print(f"Status Code: {response.status_code}")
            
            result = {
                'status_code': response.status_code,
                'success': response.status_code in [200, 201, 204],
                'payload_sent': test['payload']
            }
            
            if response.content:
                try:
                    result['response_data'] = response.json()
                    print("Response Type: JSON")
                    
                    # Check for errors in response
                    if isinstance(result['response_data'], dict):
                        if 'Error' in result['response_data']:
                            print(f"API Error: {result['response_data']['Error']}")
                        elif 'error' in result['response_data']:
                            print(f"API Error: {result['response_data']['error']}")
                        else:
                            print("Response appears successful")
                    
                except json.JSONDecodeError:
                    result['response_text'] = response.text[:500]
                    print("Response Type: Text")
                    print(f"Response Preview: {response.text[:100]}")
            
            results[test['name']] = result
            
        except Exception as e:
            print(f"Exception: {str(e)}")
            results[test['name']] = {
                'success': False,
                'error': str(e),
                'payload_sent': test['payload']
            }
    
    return results

def test_options_with_apikey(api_key):
    """Test OPTIONS method to discover allowed operations."""
    print("\n🔍 Testing OPTIONS Method with API Key")
    print("=" * 60)
    
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    
    endpoints = [
        f"{base_url}/api/os/mxapiinventory",
        f"{base_url}/oslc/os/mxapiinventory"
    ]
    
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    results = {}
    
    for endpoint in endpoints:
        endpoint_name = "api" if "/api/" in endpoint else "oslc"
        print(f"\n📋 Testing {endpoint_name.upper()} endpoint")
        
        try:
            response = requests.options(
                endpoint,
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"Status Code: {response.status_code}")
            
            # Check for Allow header
            allow_header = response.headers.get('Allow', '')
            if allow_header:
                print(f"Allowed Methods: {allow_header}")
            else:
                print("No Allow header found")
            
            # Check for other relevant headers
            relevant_headers = ['Allow', 'Access-Control-Allow-Methods', 'X-Supported-Methods']
            found_headers = {}
            for header in relevant_headers:
                value = response.headers.get(header)
                if value:
                    found_headers[header] = value
                    print(f"{header}: {value}")
            
            results[endpoint_name] = {
                'status_code': response.status_code,
                'allowed_methods': allow_header.split(', ') if allow_header else [],
                'relevant_headers': found_headers,
                'success': response.status_code == 200
            }
            
        except Exception as e:
            print(f"Exception: {str(e)}")
            results[endpoint_name] = {
                'success': False,
                'error': str(e)
            }
    
    return results

def test_filtering_capabilities(api_key):
    """Test various filtering and query capabilities."""
    print("\n🔍 Testing Filtering Capabilities with API Key")
    print("=" * 60)
    
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    api_url = f"{base_url}/api/os/mxapiinventory"
    
    headers = {
        "Accept": "application/json",
        "apikey": api_key
    }
    
    # Test different filtering scenarios
    filter_tests = [
        {
            'name': 'site_filter',
            'params': {
                "oslc.select": "itemnum,siteid,location",
                "oslc.where": 'siteid="LCVKWT"',
                "oslc.pageSize": "5",
                "lean": "1"
            }
        },
        {
            'name': 'status_filter',
            'params': {
                "oslc.select": "itemnum,status",
                "oslc.where": 'status="ACTIVE"',
                "oslc.pageSize": "5",
                "lean": "1"
            }
        },
        {
            'name': 'item_pattern',
            'params': {
                "oslc.select": "itemnum,description",
                "oslc.where": 'itemnum like "5975%"',
                "oslc.pageSize": "5",
                "lean": "1"
            }
        },
        {
            'name': 'combined_filter',
            'params': {
                "oslc.select": "itemnum,siteid,status",
                "oslc.where": 'siteid="LCVKWT" and status="ACTIVE"',
                "oslc.pageSize": "5",
                "lean": "1"
            }
        }
    ]
    
    results = {}
    
    for test in filter_tests:
        print(f"\n📋 Testing Filter: {test['name']}")
        
        try:
            response = requests.get(
                api_url,
                params=test['params'],
                headers=headers,
                timeout=(3.05, 15)
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                record_count = len(data.get('member', []))
                print(f"Records Found: {record_count}")
                
                results[test['name']] = {
                    'success': True,
                    'status_code': response.status_code,
                    'record_count': record_count,
                    'params_used': test['params']
                }
            else:
                print(f"Error: {response.text[:200]}")
                results[test['name']] = {
                    'success': False,
                    'status_code': response.status_code,
                    'error': response.text[:200],
                    'params_used': test['params']
                }
                
        except Exception as e:
            print(f"Exception: {str(e)}")
            results[test['name']] = {
                'success': False,
                'error': str(e),
                'params_used': test['params']
            }
    
    return results

def main():
    """Main execution function."""
    print("🔍 MXAPIINVENTORY Investigation with API Key")
    print("=" * 80)
    
    # Get API key
    api_key = get_api_key()
    if not api_key:
        print("❌ Cannot proceed without API key")
        return
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'authentication_method': 'api_key',
        'api_key_preview': f"{api_key[:10]}...{api_key[-10:]}",
        'get_operations': None,
        'post_operations': None,
        'options_method': None,
        'filtering_capabilities': None
    }
    
    # Test GET operations
    get_results = test_get_with_apikey(api_key)
    results['get_operations'] = get_results
    
    # Test POST operations
    post_results = test_post_with_apikey(api_key)
    results['post_operations'] = post_results
    
    # Test OPTIONS method
    options_results = test_options_with_apikey(api_key)
    results['options_method'] = options_results
    
    # Test filtering capabilities
    filter_results = test_filtering_capabilities(api_key)
    results['filtering_capabilities'] = filter_results
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"mxapiinventory_apikey_investigation_{timestamp}.json"
    
    try:
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n✅ Results saved to: {filename}")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")
    
    # Print summary
    print("\n" + "=" * 80)
    print("🎯 INVESTIGATION SUMMARY")
    print("=" * 80)
    
    if get_results and get_results.get('success'):
        print(f"✅ GET Operations: Working - {get_results.get('total_fields', 0)} fields discovered")
        field_cats = get_results.get('field_categories', {})
        for cat, fields in field_cats.items():
            if fields:
                print(f"   {cat.replace('_', ' ').title()}: {len(fields)} fields")
    else:
        print("❌ GET Operations: Failed")
    
    if post_results:
        successful_actions = [action for action, result in post_results.items() if result.get('success')]
        print(f"📊 POST Operations: {len(successful_actions)} successful out of {len(post_results)}")
        if successful_actions:
            print(f"   Successful: {', '.join(successful_actions)}")
    
    if options_results:
        for endpoint, result in options_results.items():
            if result.get('success') and result.get('allowed_methods'):
                print(f"✅ {endpoint.upper()} OPTIONS: {', '.join(result['allowed_methods'])}")
    
    if filter_results:
        successful_filters = [name for name, result in filter_results.items() if result.get('success')]
        print(f"🔍 Filtering: {len(successful_filters)} successful filters out of {len(filter_results)}")
    
    return results

if __name__ == "__main__":
    main()
