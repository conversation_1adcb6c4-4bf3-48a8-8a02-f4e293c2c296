#!/usr/bin/env python3
"""
Replicate App Transfer Call
===========================

Replicate exactly what your Flask application is doing for the transfer call.
Based on the logs, your app is successfully making the API call but getting
a business logic error about the location.

Author: Maximo Architect
Date: 2025-07-16
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.auth.token_manager import MaximoTokenManager
from backend.services.inventory_transfer_service import InventoryTransferService

# Configure logging to match your app
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_exact_app_replication():
    """Test using the exact same service and parameters as your app."""
    print("🚀 REPLICATING EXACT APP TRANSFER CALL")
    print("=" * 50)
    
    # Initialize token manager (same as your app)
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated - need to login first")
        return
    
    print("✅ Authenticated with token manager")
    
    # Initialize the same service your app uses
    transfer_service = InventoryTransferService(token_manager)
    
    # Test the exact same parameters that caused the location error
    print("\n📋 TEST 1: Exact same parameters from your error log")
    print("=" * 50)
    
    # From your error log, the issue was location KWAJ-1058 doesn't exist in site LCVKWT
    # Let's test with corrected parameters
    test_params = {
        'itemnum': '5975-60-V00-0529',
        'from_siteid': 'LCVKWT',
        'to_siteid': 'IKWAJ',
        'from_storeroom': 'RIP001',
        'to_storeroom': 'KWAJ-1058',  # This should be valid in IKWAJ site
        'quantity': 1.0,
        'from_issue_unit': 'RO',
        'from_bin': '28-800-0004',
        'to_bin': '58-A-A01-1',
        'from_lot': 'LOT123',
        'to_lot': 'TEST',
        'from_condition': 'A1',
        'to_condition': 'A1'
    }
    
    print(f"Parameters: {json.dumps(test_params, indent=2)}")
    
    # Call the service method (correct method name)
    result = transfer_service.submit_transfer_current_item(test_params)
    
    print(f"\n📊 Result: {json.dumps(result, indent=2)}")
    
    if result.get('success'):
        print("🎉 SUCCESS! Transfer completed")
        return True
    else:
        print(f"❌ Transfer failed: {result.get('error', 'Unknown error')}")
    
    # Test 2: Minimal parameters
    print("\n📋 TEST 2: Minimal parameters")
    print("=" * 50)
    
    minimal_params = {
        'itemnum': '5975-60-V00-0529',
        'from_siteid': 'LCVKWT',
        'to_siteid': 'IKWAJ',
        'from_storeroom': 'RIP001',
        'to_storeroom': 'KWAJ-1058',
        'quantity': 1.0,
        'from_issue_unit': 'RO'
    }
    
    print(f"Parameters: {json.dumps(minimal_params, indent=2)}")
    
    result2 = transfer_service.submit_transfer_current_item(minimal_params)
    
    print(f"\n📊 Result: {json.dumps(result2, indent=2)}")
    
    if result2.get('success'):
        print("🎉 SUCCESS! Minimal transfer completed")
        return True
    else:
        print(f"❌ Minimal transfer failed: {result2.get('error', 'Unknown error')}")
    
    # Test 3: Different bin combination
    print("\n📋 TEST 3: Different bin combination")
    print("=" * 50)
    
    alt_params = {
        'itemnum': '5975-60-V00-0529',
        'from_siteid': 'LCVKWT',
        'to_siteid': 'IKWAJ',
        'from_storeroom': 'RIP001',
        'to_storeroom': 'KWAJ-1058',
        'quantity': 1.0,
        'from_issue_unit': 'RO',
        'from_bin': '28-800-0004',
        'to_bin': '1058-TEMP'  # Different target bin
    }
    
    print(f"Parameters: {json.dumps(alt_params, indent=2)}")
    
    result3 = transfer_service.submit_transfer_current_item(alt_params)
    
    print(f"\n📊 Result: {json.dumps(result3, indent=2)}")
    
    if result3.get('success'):
        print("🎉 SUCCESS! Alternative transfer completed")
        return True
    else:
        print(f"❌ Alternative transfer failed: {result3.get('error', 'Unknown error')}")
    
    return False

def test_direct_api_call():
    """Test making the direct API call like the service does."""
    print("\n🔧 TESTING DIRECT API CALL")
    print("=" * 40)
    
    base_url = 'https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo'
    token_manager = MaximoTokenManager(base_url)
    
    if not token_manager.is_logged_in():
        print("❌ Not authenticated")
        return False
    
    # Use the exact same endpoint and payload structure as the service
    api_url = f"{base_url}/api/os/MXAPIINVENTORY?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange"
    
    # Test payload variations
    payloads = [
        {
            "name": "Minimal payload",
            "data": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058"
                        }
                    ]
                }
            ]
        },
        {
            "name": "With bins",
            "data": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP"
                        }
                    ]
                }
            ]
        },
        {
            "name": "Complete payload",
            "data": [
                {
                    "_action": "AddChange",
                    "itemnum": "5975-60-V00-0529",
                    "itemsetid": "ITEMSET",
                    "siteid": "LCVKWT",
                    "location": "RIP001",
                    "issueunit": "RO",
                    "matrectrans": [
                        {
                            "_action": "AddChange",
                            "itemnum": "5975-60-V00-0529",
                            "issuetype": "TRANSFER",
                            "quantity": 1.0,
                            "fromsiteid": "LCVKWT",
                            "tositeid": "IKWAJ",
                            "fromstoreloc": "RIP001",
                            "tostoreloc": "KWAJ-1058",
                            "transdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S+00:00"),
                            "issueunit": "RO",
                            "frombinnum": "28-800-0004",
                            "tobinnum": "1058-TEMP",
                            "fromlotnum": "TEST",
                            "tolotnum": "TEST",
                            "fromconditioncode": "A1",
                            "toconditioncode": "A1"
                        }
                    ]
                }
            ]
        }
    ]
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "x-method-override": "BULK"
    }
    
    for i, payload_test in enumerate(payloads, 1):
        print(f"\n🧪 Direct API Test {i}: {payload_test['name']}")
        print("-" * 40)
        
        try:
            response = token_manager.session.post(
                api_url,
                json=payload_test['data'],
                headers=headers,
                timeout=(5.0, 30)
            )
            
            print(f"📊 HTTP Status: {response.status_code}")
            
            if response.text:
                try:
                    response_data = response.json()
                    print(f"📋 Response: {json.dumps(response_data, indent=2)}")
                    
                    # Check for success patterns
                    if response.status_code == 200:
                        if isinstance(response_data, list) and len(response_data) > 0:
                            first_item = response_data[0]
                            if '_responsemeta' in first_item:
                                meta_status = first_item['_responsemeta'].get('status')
                                if meta_status == '204':
                                    print("🎉 SUCCESS! Found 204 in response meta!")
                                    
                                    # Save successful payload
                                    filename = f"successful_direct_api_payload_{i}.json"
                                    with open(filename, 'w') as f:
                                        json.dump(payload_test['data'], f, indent=2)
                                    print(f"💾 Successful payload saved to: {filename}")
                                    return True
                                else:
                                    print(f"❌ Meta status: {meta_status}")
                            
                            if '_responsedata' in first_item and 'Error' in first_item['_responsedata']:
                                error = first_item['_responsedata']['Error']
                                print(f"❌ Error: {error.get('reasonCode')} - {error.get('message')}")
                    
                except json.JSONDecodeError:
                    print(f"📄 Response (non-JSON): {response.text}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    return False

def main():
    """Main function."""
    print("🚀 REPLICATE APP TRANSFER CALL")
    print("=" * 40)
    
    # Test using the service (like your app)
    service_success = test_exact_app_replication()
    
    if service_success:
        print("\n🎉 SERVICE TEST SUCCESSFUL!")
        return
    
    # Test direct API calls
    api_success = test_direct_api_call()
    
    if api_success:
        print("\n🎉 DIRECT API TEST SUCCESSFUL!")
    else:
        print("\n❌ ALL TESTS FAILED")
        print("Check the error messages above for details.")

if __name__ == "__main__":
    main()
