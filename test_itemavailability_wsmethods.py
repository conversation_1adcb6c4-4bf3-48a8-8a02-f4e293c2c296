#!/usr/bin/env python3
"""
Test itemavailability WSMethods on Discovered Endpoints

This script tests the itemavailability wsmethod on all discovered endpoints
with proper payloads and documents the complete API structure.

Author: Augment Agent
Date: 2025-01-15
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
API_KEY = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"

class ItemAvailabilityTester:
    """Tests itemavailability wsmethod across multiple endpoints."""
    
    def __init__(self):
        """Initialize the tester."""
        self.base_url = BASE_URL
        self.api_key = API_KEY
        self.discovered_endpoints = [
            "item",
            "inventory", 
            "matrectrans",
            "invbalances",
            "locations"
        ]
        
    def test_all_itemavailability_endpoints(self):
        """Test itemavailability on all discovered endpoints."""
        print("🔍 Testing itemavailability WSMethod on All Endpoints")
        print("=" * 80)
        
        results = {}
        
        for endpoint_name in self.discovered_endpoints:
            print(f"\n📋 Testing Endpoint: {endpoint_name}")
            print("-" * 50)
            
            endpoint_url = f"{self.base_url}/api/os/{endpoint_name}"
            result = self._test_itemavailability_detailed(endpoint_url, endpoint_name)
            results[endpoint_name] = result
            
        return results
        
    def _test_itemavailability_detailed(self, endpoint_url: str, endpoint_name: str) -> Dict:
        """Test itemavailability with detailed analysis."""
        
        # Test different payload variations
        test_payloads = [
            {
                "name": "minimal",
                "payload": {
                    "itemnum": "5975-60-V00-0001",
                    "siteid": "LCVKWT"
                }
            },
            {
                "name": "with_location",
                "payload": {
                    "itemnum": "5975-60-V00-0001",
                    "siteid": "LCVKWT",
                    "location": "LCVK-CMW-AJ"
                }
            },
            {
                "name": "with_storeroom",
                "payload": {
                    "itemnum": "5975-60-V00-0001",
                    "siteid": "LCVKWT",
                    "storeroom": "LCVKWT-STORE"
                }
            },
            {
                "name": "array_format",
                "payload": [
                    {
                        "itemnum": "5975-60-V00-0001",
                        "siteid": "LCVKWT"
                    }
                ]
            }
        ]
        
        results = {
            "endpoint": endpoint_url,
            "tests": {},
            "working_payload": None,
            "response_structure": None
        }
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        for test in test_payloads:
            test_url = f"{endpoint_url}?action=wsmethod:itemavailability"
            
            print(f"   🧪 Testing {test['name']} payload:")
            print(f"      Payload: {json.dumps(test['payload'], indent=6)}")
            
            try:
                response = requests.post(
                    test_url,
                    json=test['payload'],
                    headers=headers,
                    timeout=(3.05, 15)
                )
                
                print(f"      Status: {response.status_code}")
                
                if response.content:
                    try:
                        data = response.json()
                        
                        # Store the response
                        results["tests"][test['name']] = {
                            "status": response.status_code,
                            "response": data,
                            "success": response.status_code == 200
                        }
                        
                        # Check if this is a working payload
                        if response.status_code == 200:
                            results["working_payload"] = test['payload']
                            results["response_structure"] = data
                            print(f"      ✅ SUCCESS - Working payload found!")
                            print(f"      Response: {json.dumps(data, indent=6)}")
                        else:
                            # Analyze error for insights
                            if 'oslc:Error' in data:
                                error = data['oslc:Error']
                                reason_code = error.get('spi:reasonCode', 'Unknown')
                                message = error.get('oslc:message', 'No message')
                                print(f"      ❌ Error {reason_code}: {message}")
                            else:
                                print(f"      ⚠️ Unexpected response structure")
                                
                    except Exception as e:
                        print(f"      ❌ JSON parse error: {str(e)}")
                        results["tests"][test['name']] = {
                            "status": response.status_code,
                            "response": response.text,
                            "success": False
                        }
                else:
                    print(f"      ❌ No response content")
                    results["tests"][test['name']] = {
                        "status": response.status_code,
                        "response": "",
                        "success": False
                    }
                    
            except Exception as e:
                print(f"      ❌ Request error: {str(e)}")
                results["tests"][test['name']] = {
                    "status": None,
                    "response": str(e),
                    "success": False
                }
                
        return results
        
    def test_with_session_auth(self, endpoint_name: str, working_payload: Dict):
        """Test itemavailability with session authentication."""
        print(f"\n🔑 Testing {endpoint_name} with Session Authentication")
        print("-" * 50)
        
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
            from backend.auth.token_manager import MaximoTokenManager
            
            token_manager = MaximoTokenManager(self.base_url)
            
            if not token_manager.is_logged_in():
                print("❌ Session authentication not available")
                return None
                
            print("✅ Session authentication available")
            
            endpoint_url = f"{self.base_url}/oslc/os/{endpoint_name}"
            test_url = f"{endpoint_url}?action=wsmethod:itemavailability"
            
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
            
            try:
                response = token_manager.session.post(
                    test_url,
                    json=working_payload,
                    headers=headers,
                    timeout=(3.05, 15)
                )
                
                print(f"   Status: {response.status_code}")
                print(f"   URL: {test_url}")
                print(f"   Payload: {json.dumps(working_payload, indent=2)}")
                
                if response.content:
                    try:
                        data = response.json()
                        print(f"   Response: {json.dumps(data, indent=2)}")
                        return data
                    except:
                        print(f"   Response (text): {response.text[:200]}...")
                        return response.text
                else:
                    print(f"   No response content")
                    return None
                    
            except Exception as e:
                print(f"   ❌ Session request error: {str(e)}")
                return None
                
        except ImportError:
            print("❌ Token manager not available for session testing")
            return None
            
    def generate_comprehensive_documentation(self, results: Dict):
        """Generate comprehensive documentation for working wsmethods."""
        print(f"\n📚 Comprehensive WSMethod Documentation")
        print("=" * 80)
        
        working_endpoints = []
        
        for endpoint_name, result in results.items():
            if result.get("working_payload"):
                working_endpoints.append(endpoint_name)
                
        if working_endpoints:
            print(f"✅ Working itemavailability endpoints: {', '.join(working_endpoints)}")
            
            for endpoint_name in working_endpoints:
                result = results[endpoint_name]
                self._document_working_endpoint(endpoint_name, result)
        else:
            print("❌ No working itemavailability endpoints found")
            
            # Document the error patterns for debugging
            print(f"\n📋 Error Analysis:")
            for endpoint_name, result in results.items():
                print(f"\n🔸 {endpoint_name}:")
                for test_name, test_result in result["tests"].items():
                    if not test_result["success"] and isinstance(test_result["response"], dict):
                        if 'oslc:Error' in test_result["response"]:
                            error = test_result["response"]['oslc:Error']
                            reason_code = error.get('spi:reasonCode', 'Unknown')
                            message = error.get('oslc:message', 'No message')
                            print(f"   {test_name}: {reason_code} - {message[:100]}...")
                            
    def _document_working_endpoint(self, endpoint_name: str, result: Dict):
        """Document a working endpoint in detail."""
        print(f"\n🔸 WSMethod: itemavailability")
        print(f"   Endpoint: {endpoint_name}")
        print(f"   URL: {result['endpoint']}")
        print(f"   Working Payload: {json.dumps(result['working_payload'], indent=2)}")
        print(f"   Response Structure: {json.dumps(result['response_structure'], indent=2)}")
        
        # Test with session auth if available
        session_result = self.test_with_session_auth(endpoint_name, result['working_payload'])
        
    def test_transfer_related_wsmethods(self):
        """Test other transfer-related wsmethods on discovered endpoints."""
        print(f"\n🔍 Testing Transfer-Related WSMethods")
        print("=" * 80)
        
        transfer_methods = [
            "transfercurrentitem",
            "issuecurrentitem", 
            "receivecurrentitem",
            "addchange"
        ]
        
        for endpoint_name in self.discovered_endpoints:
            print(f"\n📋 Testing {endpoint_name}:")
            endpoint_url = f"{self.base_url}/api/os/{endpoint_name}"
            
            for method in transfer_methods:
                self._test_wsmethod_existence(endpoint_url, method)
                
    def _test_wsmethod_existence(self, endpoint_url: str, method: str):
        """Test if a wsmethod exists on an endpoint."""
        test_url = f"{endpoint_url}?action=wsmethod:{method}"
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        test_payload = {
            "itemnum": "5975-60-V00-0001",
            "siteid": "LCVKWT"
        }
        
        try:
            response = requests.post(
                test_url,
                json=test_payload,
                headers=headers,
                timeout=(3.05, 10)
            )
            
            if response.status_code in [200, 400, 422]:
                try:
                    data = response.json()
                    if 'oslc:Error' in data:
                        error_msg = data['oslc:Error'].get('oslc:message', '').lower()
                        if 'not found' not in error_msg and 'method' not in error_msg:
                            print(f"   ✅ {method} - Exists")
                        else:
                            print(f"   ❌ {method} - Not found")
                    else:
                        print(f"   ✅ {method} - Success")
                except:
                    print(f"   ⚠️ {method} - Non-JSON response")
            else:
                print(f"   ❌ {method} - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {method} - Error: {str(e)[:30]}")

def main():
    """Main execution function."""
    print("🔍 itemavailability WSMethod Comprehensive Testing")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Target: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}...{API_KEY[-10:]}")
    print("=" * 80)
    
    # Initialize tester
    tester = ItemAvailabilityTester()
    
    # Test itemavailability on all endpoints
    results = tester.test_all_itemavailability_endpoints()
    
    # Generate comprehensive documentation
    tester.generate_comprehensive_documentation(results)
    
    # Test other transfer-related wsmethods
    tester.test_transfer_related_wsmethods()
    
    print(f"\n📊 Testing Summary")
    print("=" * 80)
    print("✅ itemavailability tested on all discovered endpoints")
    print("✅ Multiple payload formats tested")
    print("✅ Session authentication tested where available")
    print("✅ Transfer-related wsmethods tested")
    
    print(f"\n✅ Comprehensive testing completed")

if __name__ == "__main__":
    main()
