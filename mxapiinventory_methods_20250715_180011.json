{"timestamp": "2025-07-15T17:59:59.140525", "get_operations": {"success": false, "error": "Expecting value: line 1 column 1 (char 0)"}, "post_operations": {"AddChange": {"status_code": 200, "success": true, "payload_sent": [{"_action": "AddChange", "itemnum": "TEST-DISCOVERY-001", "itemsetid": "ITEMSET", "siteid": "LCVKWT", "location": "TEST-LOC"}], "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link"}, "Create": {"status_code": 200, "success": true, "payload_sent": [{"_action": "Create", "itemnum": "TEST-DISCOVERY-002", "siteid": "LCVKWT"}], "response_text": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"/><meta name=\"generator\" content=\"IBM Graphite\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,shrink-to-fit=no,viewport-fit=auto\"/><meta name=\"theme-color\" content=\"#000000\"/><title>mas-login</title><link"}}, "options_method": {"oslc": {"status_code": 200, "allowed_methods": [], "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:30:11 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "content-security-policy": "default-src 'self' https:;font-src 'self' data: https://1.www.s81c.com;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'", "x-dns-prefetch-control": "off", "expect-ct": "max-age=0", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "referrer-policy": "no-referrer", "x-xss-protection": "0", "x-masidp": "true", "cache-control": "private, no-cache, no-store, must-revalidate", "expires": "-1", "pragma": "no-cache", "accept-ranges": "bytes", "last-modified": "<PERSON><PERSON>, 12 Mar 2024 18:59:37 GMT", "etag": "W/\"619-18e3408d1a8\"", "vary": "Accept-Encoding", "content-encoding": "gzip"}, "success": true}, "api": {"status_code": 200, "allowed_methods": [], "headers": {"Date": "<PERSON><PERSON>, 15 Jul 2025 12:30:11 GMT", "Content-Length": "0", "Connection": "keep-alive", "x-frame-options": "SAMEORIGIN", "x-content-type-options": "nosniff", "access-control-allow-credentials": "true", "x-xss-protection": "1", "content-security-policy": "font-src 'self' data: https://1.www.s81c.com *.walkme.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' *.walkme.com; img-src 'self' d2qhvajt3imc89.cloudfront.net data: *.walkme.com; object-src 'self' *.walkme.com;", "access-control-allow-origin": "https://com.ibm.iot.maximo.mobile", "access-control-allow-methods": "GET,POST,PUT,DELETE", "access-control-allow-headers": "maxauth,x-method-override,patchtype,content-type,accept,x-public-uri,properties", "access-control-expose-headers": "csrftoken,Location,maxrowstamp", "content-language": "en-US", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Accept-Encoding"}, "success": true}}}