2025-06-16 12:18:48 [DEBUG] M<PERSON><PERSON><PERSON><PERSON> started
2025-06-16 12:18:48 [DEBUG] ------------------------------------------------------------
2025-06-16 12:18:48 [DEBUG] Configuration
2025-06-16 12:18:48 [DEBUG] Operating System  : Windows (64-bit) NT 10.00
2025-06-16 12:18:48 [DEBUG] Excel version     : 16.0 - Excel 2016
2025-06-16 12:18:48 [DEBUG] Workbook Name     : MxLoader.xlsm
2025-06-16 12:18:48 [INFO]  MxLoader version  : 8.4.1
2025-06-16 12:18:48 [INFO]  Server address    : https://vectrustst01.manage.v2x.maximotest.gov2x.com
2025-06-16 12:18:48 [INFO]  Service           : REST
2025-06-16 12:18:48 [INFO]  Authentication    : API
2025-06-16 12:18:48 [DEBUG] User              : 
2025-06-16 12:18:48 [DEBUG] Proxy             : 
2025-06-16 12:18:48 [DEBUG] Proxy user        : 
2025-06-16 12:18:48 [DEBUG] Resolve Timeout   : 5000
2025-06-16 12:18:48 [DEBUG] Connect Timeout   : 10000
2025-06-16 12:18:48 [DEBUG] Send Timeout      : 10000
2025-06-16 12:18:48 [DEBUG] Receive Timeout   : 120000
2025-06-16 12:18:48 [DEBUG] OS Context Root   : /meaweb/os/
2025-06-16 12:18:48 [DEBUG] OSLC Context Root : /maximo_aw/oslc/
2025-06-16 12:18:48 [DEBUG] API Context Root  : /maximo/api/
2025-06-16 12:18:48 [DEBUG] Timezone          : 0
2025-06-16 12:18:48 [DEBUG] DateTime Format   : yyyy-mm-dd hh:mm
2025-06-16 12:18:48 [DEBUG] Date Format       : yyyy-mm-dd
2025-06-16 12:18:48 [DEBUG] Time Format       : hh:mm
2025-06-16 12:18:48 [DEBUG] Log Level         : Trace
2025-06-16 12:18:48 [DEBUG] Log file name     : C:\Users\<USER>\OneDrive\Documents\Mxloader.log
2025-06-16 12:18:48 [DEBUG] Full filename     : C:\Users\<USER>\OneDrive\Documents\Mxloader.log
2025-06-16 12:18:48 [DEBUG] Maximum objects   : 0
2025-06-16 12:18:48 [DEBUG] Batch size        : 1
2025-06-16 12:18:48 [DEBUG] Split child rows  : False
2025-06-16 12:18:48 [DEBUG] Start Row         : 3
2025-06-16 12:18:48 [DEBUG] Stop on error     : True
2025-06-16 12:18:48 [DEBUG] Continue sync     : True
2025-06-16 12:18:48 [DEBUG] Display summary   : True
2025-06-16 12:18:48 [INFO]  ------------------------------------------------------------
2025-06-16 12:18:48 [INFO]  Sheet name      : WO.
2025-06-16 12:18:48 [INFO]  Object service  : MXAPIWODETAIL
2025-06-16 12:18:48 [INFO]  Object name     : WORKORDER
2025-06-16 12:18:48 [INFO]  Action          : AddChange
2025-06-16 12:18:48 [INFO]  Where           : WONUM="2021-1744762"
2025-06-16 12:18:48 [DEBUG] Order By        : 
2025-06-16 12:18:48 [DEBUG] OSLC Directives : 
2025-06-16 12:18:48 [DEBUG] Attr(1)         : WONUM ()
2025-06-16 12:18:48 [DEBUG] Attr(2)         : SITEID ()
2025-06-16 12:18:48 [DEBUG] Attr(3)         : LABTRANS.LABORCODE ()
2025-06-16 12:18:48 [DEBUG] Attr(4)         : LABTRANS.TASKID (INTEGER)
2025-06-16 12:18:48 [DEBUG] Attr(5)         : LABTRANS.GENAPPRSERVRECEIPT (YORN)
2025-06-16 12:18:48 [DEBUG] Attr(6)         : LABTRANS.STARTDATE (DATE)
2025-06-16 12:18:48 [DEBUG] Attr(7)         : LABTRANS.STARTTIME (TIME)
2025-06-16 12:18:48 [DEBUG] Attr(8)         : LABTRANS.FINISHTIME (TIME)
2025-06-16 12:18:48 [DEBUG] Attr(9)         : LABTRANS.REGULARHRS (DURATION)
2025-06-16 12:18:48 [DEBUG] Attr(10)         : LABTRANS.PAYRATE (AMOUNT)
2025-06-16 12:18:48 [INFO]  ------------------------------------------------------------
2025-06-16 12:18:48 [DEBUG] Parsing row 3
2025-06-16 12:18:48 [TRACE] >> WORKORDER.WONUM = 2021-1744762
2025-06-16 12:18:48 [TRACE] >> WORKORDER.SITEID = LCVKWT
2025-06-16 12:18:48 [TRACE] >>>> Object name changed WORKORDER > WORKORDER.LABTRANS
2025-06-16 12:18:48 [TRACE] >>>> Adding current object 2021-1744762 to array WORKORDER
2025-06-16 12:18:48 [TRACE] >>>> Creating child array
2025-06-16 12:18:48 [TRACE] >> WORKORDER.LABTRANS.LABORCODE = SINGAMA1555
2025-06-16 12:18:48 [TRACE] >> WORKORDER.LABTRANS.TASKID = 40
2025-06-16 12:18:48 [TRACE] >> WORKORDER.LABTRANS.GENAPPRSERVRECEIPT = 1
2025-06-16 12:18:48 [TRACE] >> WORKORDER.LABTRANS.STARTDATE = 2025-06-15T00:00:00
2025-06-16 12:18:48 [TRACE] >> WORKORDER.LABTRANS.STARTTIME = 1970-01-01T07:15:00
2025-06-16 12:18:48 [TRACE] >> WORKORDER.LABTRANS.FINISHTIME = 1970-01-01T11:00:00
2025-06-16 12:18:48 [TRACE] >>>> Adding current object SINGAMA1555 to array labtrans
2025-06-16 12:18:48 [DEBUG] Objects parsed from table: 1
2025-06-16 12:18:48 [DEBUG] HTTP call POST: /maximo/api/os/MXAPIWODETAIL?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange
2025-06-16 12:18:48 [DEBUG] Setting header: apikey
2025-06-16 12:18:48 [DEBUG] Setting header: x-method-override=BULK
2025-06-16 12:18:48 [INFO]  HTTP POST request: https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/MXAPIWODETAIL?lean=1&ignorecollectionref=1&ignorekeyref=1&ignorers=1&mxlaction=addchange
2025-06-16 12:18:48 [INFO]  HTTP request body size: 372 bytes
2025-06-16 12:18:48 [DEBUG] HTTP request body : 
[
  {
    "_action": "AddChange",
    "wonum": "2021-1744762",
    "siteid": "LCVKWT",
    "labtrans" : [
      {
        "laborcode": "SINGAMA1555",
        "taskid": 40,
        "genapprservreceipt": 1,
        "startdate": "2025-06-15T00:00:00",
        "starttime": "1970-01-01T07:15:00",
        "finishtime": "1970-01-01T11:00:00"
      }
    ]
  }
]
2025-06-16 12:18:56 [INFO]  HTTP response received in 7.99 seconds, status=200, len=36 bytes
2025-06-16 12:18:56 [DEBUG] HTTP statusText  : OK
2025-06-16 12:18:56 [DEBUG] HTTP responseText: [{"_responsemeta":{"status":"204"}}]
2025-06-16 12:18:56 [INFO]  Parsing JSON
2025-06-16 12:18:56 [DEBUG] Parsing JSON: 
2025-06-16 12:18:56 [TRACE]   _responsemeta
2025-06-16 12:18:56 [TRACE]     status
2025-06-16 12:18:56 [DEBUG] Parsing JSON: 
2025-06-16 12:18:56 [TRACE]   _responsemeta
2025-06-16 12:18:56 [TRACE]     status
2025-06-16 12:18:56 [TRACE] Parsed response

  {
,

    {
      "STATUS": "204"
    }
  }
]
2025-06-16 12:18:56 [TRACE] >> 3 0 >> 204 - 
2025-06-16 12:18:56 [INFO]  Finished synching 1 objects in 8.03 seconds - 0.12 (objs/s)
