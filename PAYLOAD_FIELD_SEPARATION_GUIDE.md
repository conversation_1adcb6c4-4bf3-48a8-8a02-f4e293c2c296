# Payload Field Separation Guide

## Overview

This document explains the field separation logic implemented for Physical Count and Current Balance adjustments when no existing inventory balances exist.

## Field Separation Rules

### 🔵 Physical Count Adjustments

**Standard Behavior (Most Reason Codes):**
- ✅ Include: `physcnt`, `physcntdate`
- ❌ Omit: `curbal`

**Exception Reason Codes:**
- ✅ Include: `physcnt`, `curbal` (equal values), `physcntdate`

### 🟡 Current Balance Adjustments

**Standard Behavior (Most Reason Codes):**
- ✅ Include: `curbal`
- ❌ Omit: `physcnt`, `physcntdate`

**Exception Reason Codes:**
- ✅ Include: `curbal`, `physcnt` (equal values), `physcntdate`

## Exception Reason Codes

When the reason code (memo field) is one of these values, both `physcnt` and `curbal` are populated with equal values:

- **"INITIAL_BALANCE"**
- **"NEW_INVENTORY"**
- **"OPENING_BALANCE"**
- **"INITIAL_COUNT"**

## Payload Examples

### Physical Count - Standard Reason Code

```json
[{
    "_action": "AddChange",
    "itemnum": "TEST-ITEM-001",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKNT",
    "location": "RIP001",
    "issueunit": "EA",
    "minlevel": 0,
    "orderqty": 1,
    "invbalances": [{
        "binnum": "BIN-001",
        "physcnt": 25.0,
        "physcntdate": "2024-01-15T14:30:00",
        "conditioncode": "A1",
        "memo": "CYCLE_COUNT",
        "reconciled": true
        // curbal field OMITTED
    }]
}]
```

### Physical Count - Exception Reason Code

```json
[{
    "_action": "AddChange",
    "itemnum": "TEST-ITEM-001",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKNT",
    "location": "RIP001",
    "issueunit": "EA",
    "minlevel": 0,
    "orderqty": 1,
    "invbalances": [{
        "binnum": "BIN-001",
        "physcnt": 25.0,
        "curbal": 25.0,  // Added because of exception reason code
        "physcntdate": "2024-01-15T14:30:00",
        "conditioncode": "A1",
        "memo": "INITIAL_COUNT",
        "reconciled": true
    }]
}]
```

### Current Balance - Standard Reason Code

```json
[{
    "_action": "AddChange",
    "itemnum": "TEST-ITEM-001",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKNT",
    "location": "RIP001",
    "issueunit": "EA",
    "minlevel": 0,
    "orderqty": 1,
    "invbalances": [{
        "binnum": "BIN-002",
        "curbal": 15.0,
        "conditioncode": "A1",
        "memo": "ADJUSTMENT",
        "reconciled": true
        // physcnt and physcntdate fields OMITTED
    }]
}]
```

### Current Balance - Exception Reason Code

```json
[{
    "_action": "AddChange",
    "itemnum": "TEST-ITEM-001",
    "itemsetid": "ITEMSET",
    "siteid": "LCVKNT",
    "location": "RIP001",
    "issueunit": "EA",
    "minlevel": 0,
    "orderqty": 1,
    "invbalances": [{
        "binnum": "BIN-002",
        "curbal": 15.0,
        "physcnt": 15.0,  // Added because of exception reason code
        "physcntdate": "2024-01-15T14:30:00",  // Added because of exception reason code
        "conditioncode": "A1",
        "memo": "INITIAL_BALANCE",
        "reconciled": true
    }]
}]
```

## Implementation Logic

### JavaScript Implementation

The logic is implemented in the submission functions:

```javascript
// Physical Count
const reasonCodesRequiringBothFields = ["INITIAL_BALANCE", "NEW_INVENTORY", "OPENING_BALANCE", "INITIAL_COUNT"];
const shouldPopulateBothFields = reasonCodesRequiringBothFields.includes(reasonCode);

const invbalanceRecord = {
    "binnum": binnum,
    "physcnt": physicalCount,
    "physcntdate": currentTimestamp,
    "conditioncode": conditioncode,
    "memo": reasonCode,
    "reconciled": true
};

// Only add curbal if reason code requires both fields
if (shouldPopulateBothFields) {
    invbalanceRecord.curbal = physicalCount;
}
```

```javascript
// Current Balance
const reasonCodesRequiringBothFields = ["INITIAL_BALANCE", "NEW_INVENTORY", "OPENING_BALANCE", "INITIAL_COUNT"];
const shouldPopulateBothFields = reasonCodesRequiringBothFields.includes(reasonCode);

const invbalanceRecord = {
    "binnum": binnum,
    "curbal": currentBalance,
    "conditioncode": conditioncode,
    "memo": reasonCode,
    "reconciled": true
};

// Only add physcnt and physcntdate if reason code requires both fields
if (shouldPopulateBothFields) {
    invbalanceRecord.physcnt = currentBalance;
    invbalanceRecord.physcntdate = new Date().toISOString().slice(0, 19);
}
```

## Reason Code Mapping

### Standard Reason Codes (Field Separation)
- "CYCLE_COUNT" → Physical Count only
- "PHYSICAL_INVENTORY" → Physical Count only
- "CORRECTION" → Physical Count only
- "ADJUSTMENT" → Current Balance only
- "OTHER" → Depends on adjustment type

### Exception Reason Codes (Both Fields)
- "INITIAL_COUNT" → Both fields equal
- "INITIAL_BALANCE" → Both fields equal
- "NEW_INVENTORY" → Both fields equal
- "OPENING_BALANCE" → Both fields equal

## Testing

### Test Cases Covered

1. **Physical Count + Standard Reason**: Only `physcnt` and `physcntdate`
2. **Physical Count + Exception Reason**: Both `physcnt` and `curbal` equal
3. **Current Balance + Standard Reason**: Only `curbal`
4. **Current Balance + Exception Reason**: Both `curbal` and `physcnt` equal

### Console Logging

The implementation includes detailed console logging:

```
🔍 NO-BALANCE PHYSICAL COUNT: Reason code 'CYCLE_COUNT' - omitting curbal field
🔍 NO-BALANCE PHYSICAL COUNT: Reason code 'INITIAL_COUNT' requires both fields - adding curbal: 25.0
🔍 NO-BALANCE CURRENT BALANCE: Reason code 'ADJUSTMENT' - omitting physcnt and physcntdate fields
🔍 NO-BALANCE CURRENT BALANCE: Reason code 'INITIAL_BALANCE' requires both fields - adding physcnt: 15.0
```

## Benefits

1. **Proper Field Separation**: Maintains clear distinction between adjustment types
2. **Exception Handling**: Supports special cases for initial/opening records
3. **Maximo Compatibility**: Follows Maximo's expected field patterns
4. **Debugging**: Clear logging shows which fields are included/omitted
5. **Flexibility**: Easy to add new exception reason codes if needed

## Maintenance

To add new exception reason codes, update the `reasonCodesRequiringBothFields` array in both functions:

```javascript
const reasonCodesRequiringBothFields = [
    "INITIAL_BALANCE", 
    "NEW_INVENTORY", 
    "OPENING_BALANCE", 
    "INITIAL_COUNT",
    "NEW_EXCEPTION_CODE"  // Add new codes here
];
```
