#!/bin/bash

# Test Cross-Site with Actual LCVKWT Locations
# =============================================

echo "🚀 TESTING CROSS-SITE WITH ACTUAL LCVKWT LOCATIONS"
echo "=================================================="

echo "🎯 OBJECTIVE: Get 204 success using actual LCVKWT storeroom locations"
echo "📋 STRATEGY: Use the actual locations we found: CMW-AJ, CMW-AJH, CMW-BU, etc."
echo "🔍 HYPOTHESIS: Cross-site works when destination location exists in source site"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_cross_site_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting cross-site transfer..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Cross-site transfer worked with 204 status!"
        echo "$payload" > "successful_cross_site_actual_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: successful_cross_site_actual_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING WITH ACTUAL LCVKWT STOREROOM LOCATIONS"
echo "================================================"

# Test 1: LCVKWT → IKWAJ with CMW-AJ destination
test_cross_site_payload "LCVKWT → IKWAJ with CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: LCVKWT → IKWAJ with CMW-AJH destination
test_cross_site_payload "LCVKWT → IKWAJ with CMW-AJH" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJH",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: LCVKWT → IKWAJ with CMW-BU destination
test_cross_site_payload "LCVKWT → IKWAJ with CMW-BU" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: LCVKWT → IKWAJ with CMW-BUH destination
test_cross_site_payload "LCVKWT → IKWAJ with CMW-BUH" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-BUH",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Different source and destination within LCVKWT locations
test_cross_site_payload "CMW-AJ → IKWAJ with CMW-BU" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 6: Minimal fields with valid LCVKWT location
test_cross_site_payload "Minimal with CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO"
}'

# Test 7: Small quantity with valid LCVKWT location
test_cross_site_payload "Small Quantity with CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 0.1,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 8: Different bins with valid LCVKWT location
test_cross_site_payload "Different Bins with CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "28-800-0004",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 9: Different lots with valid LCVKWT location
test_cross_site_payload "Different Lots with CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "LOT123",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 10: Without condition codes
test_cross_site_payload "No Conditions with CMW-AJ" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT"
}'

echo ""
echo "📊 CROSS-SITE TRANSFER TEST SUMMARY"
echo "==================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found working cross-site transfer patterns!"
    echo "======================================================"
    echo "💾 Check successful_cross_site_actual_*.json files for working patterns"
    echo ""
    echo "📋 Working curl commands:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "successful_cross_site_actual_$i.json" ]; then
            echo ""
            echo "# Working Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat successful_cross_site_actual_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
    
    echo ""
    echo "🎯 IMPLEMENTATION NOTES:"
    echo "• Cross-site transfers work when destination location exists in source site"
    echo "• Use proper storeroom filtering by site in your application"
    echo "• Validate destination locations against source site before transfer"
    
else
    echo ""
    echo "❌ Still no 204 success responses found"
    echo "🔄 This confirms the validation logic is checking destination against source site"
    echo ""
    echo "💡 NEXT STEPS:"
    echo "• Modify Flask service to use destination site context"
    echo "• Or implement two-step transfer process"
    echo "• Or create locations in both sites"
fi

echo ""
echo "🎯 KEY INSIGHT: Maximo validates destination location against SOURCE site context"
