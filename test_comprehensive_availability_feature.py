#!/usr/bin/env python3
"""
Comprehensive test for the enhanced View Item Availability feature.

This script tests the complete multi-tab availability feature including:
- All Locations tab
- All Lots tab  
- Purchasing tab
- Reservations tab
- Alternate Items tab

Author: Augment Agent
Date: 2025-07-16
"""

import sys
import os
import json
import requests
from datetime import datetime

def test_comprehensive_availability_api():
    """Test the enhanced comprehensive availability API endpoint."""
    print("🔍 TESTING COMPREHENSIVE AVAILABILITY FEATURE")
    print("=" * 80)
    
    # Test parameters
    test_itemnum = "5975-60-V00-0001"
    test_siteid = "LCVKWT"
    
    print(f"📋 Testing with Item: {test_itemnum}, Site: {test_siteid}")
    
    # Test the enhanced API endpoint
    api_url = f"http://127.0.0.1:5010/api/inventory/availability/{test_itemnum}"
    params = {"siteid": test_siteid}
    
    try:
        print(f"🔗 Making request to: {api_url}")
        
        response = requests.get(api_url, params=params, timeout=(3.05, 30))
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ API call successful!")
                
                # Test comprehensive summary
                print("\n📊 COMPREHENSIVE AVAILABILITY SUMMARY:")
                summary = data.get('availability_summary', {})
                print(f"  • Total Available Balance: {summary.get('total_available_balance', 'N/A')}")
                print(f"  • Total Current Balance: {summary.get('total_current_balance', 'N/A')}")
                print(f"  • Total Reserved Quantity: {summary.get('total_reserved_quantity', 'N/A')}")
                print(f"  • Total Locations: {summary.get('total_locations', 'N/A')}")
                print(f"  • Total Lots/Bins: {summary.get('total_lots_bins', 'N/A')}")
                print(f"  • Total Reservation Records: {summary.get('total_reservation_records', 'N/A')}")
                print(f"  • Total Purchase Records: {summary.get('total_purchase_records', 'N/A')}")
                print(f"  • Total Vendor Records: {summary.get('total_vendor_records', 'N/A')}")
                
                # Test detailed records structure
                print("\n📋 DETAILED RECORDS ANALYSIS:")
                records = data.get('inventory_records', [])
                if records:
                    print(f"  Found {len(records)} inventory records")
                    
                    # Analyze first record for comprehensive data
                    record = records[0]
                    print(f"\n  📍 Sample Record (Location: {record.get('location', 'N/A')}):")
                    
                    # Test lots data
                    lots_info = record.get('lots_info', [])
                    print(f"    • Lots/Bins Data: {len(lots_info)} records")
                    if lots_info:
                        lot = lots_info[0]
                        print(f"      - Sample Lot: Bin={lot.get('binnum', 'N/A')}, Balance={lot.get('curbal', 'N/A')}")
                    
                    # Test purchasing data
                    purchasing_data = record.get('purchasing_data', [])
                    print(f"    • Purchasing Data: {len(purchasing_data)} records")
                    if purchasing_data:
                        purchase = purchasing_data[0]
                        print(f"      - Sample Purchase: PO={purchase.get('ponum', 'N/A')}, Qty={purchase.get('receiptquantity', 'N/A')}")
                    
                    # Test reservations data
                    reservations_usage = record.get('reservations_usage', [])
                    print(f"    • Reservations/Usage Data: {len(reservations_usage)} records")
                    if reservations_usage:
                        reservation = reservations_usage[0]
                        print(f"      - Sample Reservation: WO={reservation.get('wonum', 'N/A')}, Qty={reservation.get('quantity', 'N/A')}")
                    
                    # Test alternate items data
                    alternate_items = record.get('alternate_items', [])
                    print(f"    • Alternate Items Data: {len(alternate_items)} records")
                    if alternate_items:
                        alternate = alternate_items[0]
                        print(f"      - Sample Alternate: Vendor={alternate.get('vendor', 'N/A')}, Model={alternate.get('modelnum', 'N/A')}")
                
                # Test data completeness for all tabs
                print("\n📊 TAB DATA COMPLETENESS:")
                total_lots = sum(len(record.get('lots_info', [])) for record in records)
                total_purchases = sum(len(record.get('purchasing_data', [])) for record in records)
                total_reservations = sum(len(record.get('reservations_usage', [])) for record in records)
                total_alternates = sum(len(record.get('alternate_items', [])) for record in records)
                
                print(f"  • All Locations Tab: {len(records)} locations ✅")
                print(f"  • All Lots Tab: {total_lots} lots/bins {'✅' if total_lots > 0 else '⚠️'}")
                print(f"  • Purchasing Tab: {total_purchases} purchase records {'✅' if total_purchases > 0 else '⚠️'}")
                print(f"  • Reservations Tab: {total_reservations} reservation records {'✅' if total_reservations > 0 else '⚠️'}")
                print(f"  • Alternate Items Tab: {total_alternates} alternate records {'✅' if total_alternates > 0 else '⚠️'}")
                
                return True
            else:
                print(f"❌ API returned error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        return False

def test_ui_comprehensive_integration():
    """Test comprehensive UI integration."""
    print("\n🖥️  TESTING COMPREHENSIVE UI INTEGRATION")
    print("=" * 60)
    
    # Check if the JavaScript file contains all the new comprehensive methods
    js_file_path = "frontend/static/js/inventory_management.js"
    
    try:
        with open(js_file_path, 'r') as f:
            js_content = f.read()
        
        # Check for required comprehensive elements
        required_elements = [
            # Tab structure
            "nav-tabs",
            "availabilityTabs",
            "locations-tab",
            "lots-tab", 
            "purchasing-tab",
            "reservations-tab",
            "alternates-tab",
            
            # Tab building functions
            "buildLocationsTab",
            "buildLotsTab",
            "buildPurchasingTab", 
            "buildReservationsTab",
            "buildAlternatesTab",
            
            # Comprehensive data fields
            "total_lots_bins",
            "total_reservation_records",
            "total_purchase_records",
            "total_vendor_records",
            "lots_info",
            "purchasing_data",
            "reservations_usage",
            "alternate_items"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in js_content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing comprehensive UI elements: {missing_elements}")
            return False
        else:
            print("✅ All required comprehensive UI elements found")
            
        # Check tab functionality
        tab_functions = [
            "buildLocationsTab",
            "buildLotsTab", 
            "buildPurchasingTab",
            "buildReservationsTab",
            "buildAlternatesTab"
        ]
        
        for func in tab_functions:
            if func in js_content:
                print(f"✅ {func} properly implemented")
            else:
                print(f"❌ {func} not found")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Error checking comprehensive UI integration: {str(e)}")
        return False

def test_data_structure_completeness():
    """Test that the data structure supports all Maximo availability tabs."""
    print("\n📊 TESTING DATA STRUCTURE COMPLETENESS")
    print("=" * 60)
    
    # Test with a known item
    api_url = "http://127.0.0.1:5010/api/inventory/availability/5975-60-V00-0001"
    params = {"siteid": "LCVKWT"}
    
    try:
        response = requests.get(api_url, params=params, timeout=(3.05, 15))
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                records = data.get('inventory_records', [])
                
                # Check data structure completeness
                required_fields = [
                    'lots_info',
                    'purchasing_data', 
                    'reservations_usage',
                    'alternate_items',
                    'balance_details',
                    'cost_info'
                ]
                
                print("📋 Checking data structure for Maximo availability tabs:")
                
                for field in required_fields:
                    found_in_records = any(field in record for record in records)
                    print(f"  • {field}: {'✅ Present' if found_in_records else '⚠️ Missing'}")
                
                # Check summary completeness
                summary = data.get('availability_summary', {})
                required_summary_fields = [
                    'total_available_balance',
                    'total_current_balance', 
                    'total_reserved_quantity',
                    'total_locations',
                    'total_lots_bins',
                    'total_reservation_records',
                    'total_purchase_records',
                    'total_vendor_records'
                ]
                
                print("\n📊 Checking summary completeness:")
                for field in required_summary_fields:
                    present = field in summary
                    print(f"  • {field}: {'✅ Present' if present else '❌ Missing'}")
                
                return True
            else:
                print(f"❌ API error: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing data structure: {str(e)}")
        return False

def main():
    """Run all comprehensive tests."""
    print(f"🚀 STARTING COMPREHENSIVE AVAILABILITY FEATURE TESTS")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test comprehensive API functionality
    api_success = test_comprehensive_availability_api()
    
    # Test comprehensive UI integration
    ui_success = test_ui_comprehensive_integration()
    
    # Test data structure completeness
    data_success = test_data_structure_completeness()
    
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"Comprehensive API Test: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"Comprehensive UI Test: {'✅ PASSED' if ui_success else '❌ FAILED'}")
    print(f"Data Structure Test: {'✅ PASSED' if data_success else '❌ FAILED'}")
    
    overall_success = api_success and ui_success and data_success
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 The Comprehensive View Item Availability feature is ready!")
        print("📋 Features available:")
        print("   • All Locations tab - Shows availability by location")
        print("   • All Lots tab - Shows lot/bin level details")
        print("   • Purchasing tab - Shows receipt transactions")
        print("   • Reservations tab - Shows usage and reservations")
        print("   • Alternate Items tab - Shows vendor and alternate data")
        print("\n📋 To test manually:")
        print("   1. Go to http://127.0.0.1:5010/inventory-management")
        print("   2. Search for an item (e.g., '5975-60-V00-0001')")
        print("   3. Click the green 'View Availability' button")
        print("   4. Navigate through all 5 tabs to see comprehensive data")
    else:
        print("\n⚠️  Please fix the failing tests before using the feature.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
