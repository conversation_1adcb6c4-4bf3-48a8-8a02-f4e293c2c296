#!/bin/bash

# Test New Transfer Button Implementation
# =======================================

echo "🚀 TESTING NEW TRANSFER BUTTON IMPLEMENTATION"
echo "============================================="

echo "🎯 OBJECTIVE: Test both Same Site and Cross Site transfer buttons"
echo "📋 STRATEGY: Test the new endpoints with different transfer scenarios"
echo "🔍 IMPLEMENTATION: Two button approach with destination context"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test transfer endpoint
test_transfer_endpoint() {
    local test_name="$1"
    local endpoint="$2"
    local payload="$3"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    echo "🔗 Endpoint: $endpoint"
    
    echo "🔄 Submitting transfer..."
    
    response=$(curl -X POST "http://127.0.0.1:5010$endpoint" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Transfer worked with 204 status!"
        echo "$payload" > "successful_new_implementation_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: successful_new_implementation_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING NEW TRANSFER IMPLEMENTATION"
echo "====================================="

# Test 1: Same Site Transfer (IKWAJ → IKWAJ)
test_transfer_endpoint "Same Site Transfer (IKWAJ → IKWAJ)" \
    "/api/inventory/transfer-same-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 2: Cross Site Transfer (LCVKWT → IKWAJ) with Destination Context
test_transfer_endpoint "Cross Site Transfer (LCVKWT → IKWAJ) - Destination Context" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 3: Cross Site Transfer with Different Units
test_transfer_endpoint "Cross Site Transfer - RO to RO (Same Units)" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "RO",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 4: Same Site Transfer with Conversion Factor
test_transfer_endpoint "Same Site Transfer with Conversion Factor" \
    "/api/inventory/transfer-same-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 2.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 5: Cross Site Transfer - Small Quantity
test_transfer_endpoint "Cross Site Transfer - Small Quantity" \
    "/api/inventory/transfer-cross-site" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 0.1,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 6: Auto-Detection via Original Endpoint (Same Site)
test_transfer_endpoint "Auto-Detection - Same Site via Original Endpoint" \
    "/api/inventory/transfer-current-item" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "IKWAJ",
        "to_siteid": "IKWAJ",
        "from_storeroom": "KWAJ-1058",
        "to_storeroom": "KWAJ-1115",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "conversion_factor": 1.0,
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

# Test 7: Auto-Detection via Original Endpoint (Cross Site)
test_transfer_endpoint "Auto-Detection - Cross Site via Original Endpoint" \
    "/api/inventory/transfer-current-item" \
    '{
        "itemnum": "5975-60-V00-0529",
        "from_siteid": "LCVKWT",
        "to_siteid": "IKWAJ",
        "from_storeroom": "CMW-AJ",
        "to_storeroom": "KWAJ-1058",
        "quantity": 1.0,
        "from_issue_unit": "RO",
        "to_issue_unit": "EA",
        "from_bin": "DEFAULT",
        "to_bin": "DEFAULT",
        "from_lot": "DEFAULT",
        "to_lot": "DEFAULT",
        "from_condition": "A1",
        "to_condition": "A1"
    }'

echo ""
echo "📊 NEW TRANSFER IMPLEMENTATION TEST SUMMARY"
echo "=========================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! New transfer implementation is working!"
    echo "==============================================="
    echo "💾 Check successful_new_implementation_*.json files for working patterns"
    echo ""
    echo "📋 Working endpoints:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "successful_new_implementation_$i.json" ]; then
            echo "✅ Test $i: Working pattern found"
        fi
    done
    
    echo ""
    echo "🎯 IMPLEMENTATION SUCCESS FACTORS:"
    echo "• Same Site Transfer: Uses source site context with conversion factor"
    echo "• Cross Site Transfer: Uses destination site context with toissueunit"
    echo "• Auto-Detection: Routes to appropriate method based on site comparison"
    echo "• Unit Conversion: Properly handled in both approaches"
    echo "• Field Validation: Complete validation with DEFAULT values"
    
else
    echo ""
    echo "❌ No successful transfers found"
    echo "🔄 Check Flask application logs for errors"
    echo ""
    echo "💡 TROUBLESHOOTING:"
    echo "• Ensure Flask app is running on port 5010"
    echo "• Check authentication status"
    echo "• Verify inventory service implementation"
    echo "• Check Maximo API connectivity"
fi

echo ""
echo "🎯 NEXT STEPS:"
echo "• Test the UI at http://127.0.0.1:5010/inventory-management"
echo "• Verify the two transfer buttons appear correctly"
echo "• Test transfer functionality through the web interface"
echo "• Confirm button visibility changes based on site selection"
