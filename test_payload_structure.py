#!/usr/bin/env python3
"""
Test script to verify the payload structures match the requirements exactly
"""

import json
from datetime import datetime

def test_physical_count_payload():
    """Test that physical count payload matches the required structure"""
    print("🔍 Testing Physical Count Payload Structure...")
    
    # Expected structure from requirements
    expected_structure = [
        {
            "_action": "AddChange",
            "itemnum": "<current_item_number>",
            "itemsetid": "<current_ITEMSETID>",
            "siteid": "<current_site_id>",
            "location": "<current_location>",
            "invbalances": [
                {
                    "binnum": "<current_bin_number>",
                    "physcnt": "<user_entered_physical_count>",
                    "physcntdate": "<current_timestamp_in_ISO_format>",
                    "conditioncode": "<current_condition_code>",
                    "memo": "<current_reason_code>"
                }
            ]
        }
    ]
    
    # Actual payload with sample data
    actual_payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKWT",
            "location": "RIP001",
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "physcnt": 25,
                    "physcntdate": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
                    "conditioncode": "A1",
                    "memo": "CYCLE_COUNT"
                }
            ]
        }
    ]
    
    # Check structure
    required_fields = ["_action", "itemnum", "itemsetid", "siteid", "location", "invbalances"]
    balance_fields = ["binnum", "physcnt", "physcntdate", "conditioncode", "memo"]
    
    payload = actual_payload[0]
    
    # Check top-level fields
    for field in required_fields:
        if field not in payload:
            print(f"❌ Missing field: {field}")
            return False
        else:
            print(f"✅ Found field: {field}")
    
    # Check invbalances structure
    if not isinstance(payload["invbalances"], list) or len(payload["invbalances"]) == 0:
        print("❌ invbalances is not a valid array")
        return False
    
    balance = payload["invbalances"][0]
    for field in balance_fields:
        if field not in balance:
            print(f"❌ Missing balance field: {field}")
            return False
        else:
            print(f"✅ Found balance field: {field}")
    
    print("✅ Physical Count payload structure is correct!")
    print(f"Sample payload: {json.dumps(actual_payload, indent=2)}")
    return True

def test_current_balance_payload():
    """Test that current balance payload matches the required structure"""
    print("\n🔍 Testing Current Balance Payload Structure...")
    
    # Expected structure from requirements
    expected_structure = [
        {
            "_action": "AddChange",
            "itemnum": "<current_item_number>",
            "itemsetid": "<current_ITEMSETID>",
            "siteid": "<current_site_id>",
            "location": "<current_location>",
            "invbalances": [
                {
                    "binnum": "<current_bin_number>",
                    "curbal": "<user_entered_new_balance>",
                    "conditioncode": "<current_condition_code>",
                    "memo": "<current_reason_code>"
                }
            ]
        }
    ]
    
    # Actual payload with sample data
    actual_payload = [
        {
            "_action": "AddChange",
            "itemnum": "5975-60-V00-0529",
            "itemsetid": "ITEMSET",
            "siteid": "LCVKWT",
            "location": "RIP001",
            "invbalances": [
                {
                    "binnum": "28-800-0004",
                    "curbal": 25,
                    "conditioncode": "A1",
                    "memo": "ADJUSTMENT"
                }
            ]
        }
    ]
    
    # Check structure
    required_fields = ["_action", "itemnum", "itemsetid", "siteid", "location", "invbalances"]
    balance_fields = ["binnum", "curbal", "conditioncode", "memo"]
    
    payload = actual_payload[0]
    
    # Check top-level fields
    for field in required_fields:
        if field not in payload:
            print(f"❌ Missing field: {field}")
            return False
        else:
            print(f"✅ Found field: {field}")
    
    # Check invbalances structure
    if not isinstance(payload["invbalances"], list) or len(payload["invbalances"]) == 0:
        print("❌ invbalances is not a valid array")
        return False
    
    balance = payload["invbalances"][0]
    for field in balance_fields:
        if field not in balance:
            print(f"❌ Missing balance field: {field}")
            return False
        else:
            print(f"✅ Found balance field: {field}")
    
    print("✅ Current Balance payload structure is correct!")
    print(f"Sample payload: {json.dumps(actual_payload, indent=2)}")
    return True

def main():
    """Run payload structure tests"""
    print("🚀 Testing Payload Structures...\n")
    
    tests = [
        ("Physical Count Payload", test_physical_count_payload),
        ("Current Balance Payload", test_current_balance_payload),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("PAYLOAD STRUCTURE TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All payload structure tests passed!")
        print("✅ Payloads match the exact requirements specified!")
    else:
        print("⚠️ Some tests failed - check payload structures")

if __name__ == "__main__":
    main()
