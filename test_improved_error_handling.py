#!/usr/bin/env python3
"""
Test script to verify the improved error handling for problematic files
"""

import requests
import json

def test_improved_error_handling():
    """Test the improved error handling for files that can't be downloaded"""
    
    base_url = 'http://127.0.0.1:5010'
    wonum = '2021-1744762'
    
    print(f"🔍 Testing improved error handling for {wonum}")
    print("=" * 60)
    
    # Test cases: working file vs problematic file
    test_cases = [
        {
            'name': 'Working Text File',
            'docinfoid': '1926176',  # test_upload.txt - should work
            'expected': 'success'
        },
        {
            'name': 'Working PNG Image',
            'docinfoid': '1926181',  # Maximo.png - should work
            'expected': 'success'
        },
        {
            'name': 'Problematic PDF (attachmentSize: 0)',
            'docinfoid': '175708',   # ACO-LOTD-21-0157Erect4Tents-signed.pdf - should fail gracefully
            'expected': 'error'
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📋 Test {i+1}: {test_case['name']}")
        print(f"   🆔 DocInfoID: {test_case['docinfoid']}")
        
        # Test download
        download_url = f"{base_url}/api/workorder/{wonum}/attachments/{test_case['docinfoid']}/download"
        
        try:
            response = requests.get(download_url, timeout=60)
            print(f"   📤 Download URL: {download_url}")
            print(f"   🔄 Status: {response.status_code}")
            print(f"   📊 Content Length: {len(response.content)} bytes")
            print(f"   📋 Content Type: {response.headers.get('content-type', 'Unknown')}")
            
            if response.status_code == 200:
                # Success case
                content = response.content
                
                if test_case['expected'] == 'success':
                    print(f"   ✅ SUCCESS: Downloaded successfully as expected")
                    
                    # Check content type
                    if content.startswith(b'%PDF'):
                        print(f"   📄 Content: PDF file")
                    elif content.startswith(b'\x89PNG'):
                        print(f"   🖼️ Content: PNG image")
                    elif test_case['docinfoid'] == '1926176':
                        print(f"   📝 Content: Text file - {content.decode('utf-8', errors='ignore')[:50]}...")
                    else:
                        print(f"   📄 Content: Other file type")
                else:
                    print(f"   ⚠️  UNEXPECTED: File downloaded but was expected to fail")
                    
            elif response.status_code == 400:
                # Expected error case
                try:
                    error_data = response.json()
                    print(f"   📝 Error Response: {error_data}")
                    
                    if test_case['expected'] == 'error':
                        if 'File not accessible' in error_data.get('error', ''):
                            print(f"   ✅ SUCCESS: Got expected 'File not accessible' error")
                            print(f"   📋 Details: {error_data.get('details', 'No details')}")
                            print(f"   💡 Suggestion: {error_data.get('suggestion', 'No suggestion')}")
                        else:
                            print(f"   ⚠️  Got error but not the expected 'File not accessible' error")
                    else:
                        print(f"   ❌ UNEXPECTED: Got error but file was expected to work")
                        
                except json.JSONDecodeError:
                    print(f"   📝 Non-JSON error response: {response.text[:200]}")
                    
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   📝 Response: {response.text[:200]}")
        
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print(f"\n" + "=" * 60)
    print("🎯 Error Handling Test Complete")
    
    # Test view functionality as well
    print(f"\n👁️ Testing VIEW functionality with improved error handling")
    print("=" * 40)
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📋 View Test {i+1}: {test_case['name']}")
        
        view_url = f"{base_url}/api/workorder/{wonum}/attachments/{test_case['docinfoid']}/view"
        
        try:
            response = requests.get(view_url, timeout=60)
            print(f"   📤 View URL: {view_url}")
            print(f"   🔄 Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ View successful")
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    if 'File not accessible' in error_data.get('error', ''):
                        print(f"   ✅ Got expected 'File not accessible' error for view")
                    else:
                        print(f"   📝 View error: {error_data.get('error', 'Unknown')}")
                except:
                    print(f"   📝 View error response: {response.text[:100]}")
            else:
                print(f"   ❌ Unexpected view status: {response.status_code}")
        
        except Exception as e:
            print(f"   ❌ View exception: {e}")

if __name__ == "__main__":
    print("🧪 Testing Improved Error Handling")
    print("=" * 60)
    
    test_improved_error_handling()
    
    print("\n" + "=" * 60)
    print("🎉 Testing Complete!")
    print("\n📋 Summary:")
    print("✅ Working files should download successfully")
    print("✅ Problematic files should show helpful error messages")
    print("✅ Both download and view should handle errors gracefully")
