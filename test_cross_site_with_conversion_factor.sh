#!/bin/bash

# Test Cross-Site with Conversion Factor
# =======================================

echo "🚀 TESTING CROSS-SITE TRANSFERS WITH CONVERSION FACTOR = 1"
echo "=========================================================="

echo "🎯 OBJECTIVE: Get multiple 204 success responses for cross-site transfers"
echo "📋 STRATEGY: Include conversion factor = 1 to solve unit of measure issues"
echo "🔍 HYPOTHESIS: Setting conversion factor = 1 will resolve EA/RO conversion errors"
echo ""

# Counter for successful tests
SUCCESS_COUNT=0
TEST_COUNT=0

# Function to test payload and check for 204 success
test_cross_site_payload() {
    local test_name="$1"
    local payload="$2"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    echo "📋 TEST $TEST_COUNT: $test_name"
    echo "$(printf '=%.0s' {1..80})"
    
    echo "🔄 Submitting cross-site transfer with conversion factor..."
    
    response=$(curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$payload" \
        -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}\n" \
        -s)
    
    echo "📊 Response:"
    echo "$response"
    
    # Check for 204 success
    if echo "$response" | grep -q '"status": "204"' || echo "$response" | grep -q '"status":"204"'; then
        echo "🎉 SUCCESS! Cross-site transfer worked with 204 status!"
        echo "$payload" > "working_cross_site_conversion_$TEST_COUNT.json"
        echo "💾 Successful payload saved to: working_cross_site_conversion_$TEST_COUNT.json"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        return 0
    elif echo "$response" | grep -q '"Error"'; then
        error_msg=$(echo "$response" | grep -o '"message": "[^"]*"' | head -1)
        echo "❌ Business logic error: $error_msg"
        return 1
    else
        echo "⚠️  Unexpected response format"
        return 1
    fi
    
    echo ""
}

echo "🚀 TESTING CROSS-SITE TRANSFERS WITH CONVERSION FACTOR"
echo "====================================================="

# Test 1: LCVKWT → IKWAJ with CMW-AJ and conversion factor
test_cross_site_payload "LCVKWT → IKWAJ with CMW-AJ + Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 2: LCVKWT → IKWAJ with CMW-AJH and conversion factor
test_cross_site_payload "LCVKWT → IKWAJ with CMW-AJH + Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJH",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 3: LCVKWT → IKWAJ with CMW-BU and conversion factor
test_cross_site_payload "LCVKWT → IKWAJ with CMW-BU + Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 4: LCVKWT → IKWAJ with CMW-BUH and conversion factor
test_cross_site_payload "LCVKWT → IKWAJ with CMW-BUH + Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-BUH",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 5: Minimal fields with conversion factor
test_cross_site_payload "Minimal with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1
}'

# Test 6: Different quantities with conversion factor
test_cross_site_payload "Different Quantity with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 0.5,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 7: Different source storeroom with conversion factor
test_cross_site_payload "Different Source with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "CMW-AJ",
    "to_storeroom": "CMW-BU",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 8: Reverse direction with conversion factor
test_cross_site_payload "IKWAJ → LCVKWT with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "IKWAJ",
    "to_siteid": "LCVKWT",
    "from_storeroom": "KWAJ-1058",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 9: Different bins with conversion factor
test_cross_site_payload "Different Bins with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "28-800-0004",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 10: Different lots with conversion factor
test_cross_site_payload "Different Lots with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "LOT123",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 11: Original KWAJ-1058 destination with conversion factor
test_cross_site_payload "Original KWAJ-1058 with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "KWAJ-1058",
    "quantity": 1.0,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

# Test 12: Small quantity with conversion factor
test_cross_site_payload "Small Quantity with Conversion Factor" '{
    "itemnum": "5975-60-V00-0529",
    "from_siteid": "LCVKWT",
    "to_siteid": "IKWAJ",
    "from_storeroom": "RIP001",
    "to_storeroom": "CMW-AJ",
    "quantity": 0.1,
    "from_issue_unit": "RO",
    "conversion_factor": 1,
    "from_bin": "DEFAULT",
    "to_bin": "DEFAULT",
    "from_lot": "DEFAULT",
    "to_lot": "DEFAULT",
    "from_condition": "A1",
    "to_condition": "A1"
}'

echo ""
echo "📊 CROSS-SITE TRANSFER WITH CONVERSION FACTOR SUMMARY"
echo "===================================================="
echo "✅ Successful transfers (204 status): $SUCCESS_COUNT"
echo "❌ Failed transfers: $((TEST_COUNT - SUCCESS_COUNT))"
echo "📝 Total tests completed: $TEST_COUNT"

if [ $SUCCESS_COUNT -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Found multiple working cross-site transfer patterns!"
    echo "============================================================="
    echo "💾 Check working_cross_site_conversion_*.json files for working patterns"
    echo ""
    echo "📋 Working curl commands:"
    for i in $(seq 1 $TEST_COUNT); do
        if [ -f "working_cross_site_conversion_$i.json" ]; then
            echo ""
            echo "# Working Pattern $i:"
            echo "curl -X POST http://127.0.0.1:5010/api/inventory/transfer-current-item \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"Accept: application/json\" \\"
            echo "  -d '$(cat working_cross_site_conversion_$i.json | tr -d '\n' | tr -s ' ')' \\"
            echo "  -s"
        fi
    done
    
    echo ""
    echo "🎯 KEY SUCCESS FACTORS:"
    echo "• Include conversion_factor: 1 to resolve unit of measure issues"
    echo "• Use LCVKWT storerooms as destination (CMW-AJ, CMW-AJH, CMW-BU, etc.)"
    echo "• Cross-site transfers work when destination location exists in source site"
    echo "• Complete field validation with DEFAULT values for bins/lots"
    echo "• A1 condition codes work properly"
    
else
    echo ""
    echo "❌ No 204 success responses found"
    echo "🔄 Need to investigate further..."
fi

echo ""
echo "🎯 IMPLEMENTATION NOTES:"
echo "• Always include conversion_factor: 1 for cross-site transfers"
echo "• Validate destination storerooms exist in source site"
echo "• Use proper storeroom filtering by site in application UI"
